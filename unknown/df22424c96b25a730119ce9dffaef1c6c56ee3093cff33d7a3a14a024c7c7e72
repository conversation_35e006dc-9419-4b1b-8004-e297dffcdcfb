import {
  SubmittedCharge,
  SubmittedChargeLocationLocationTypeEnum,
} from '@experience/shared/axios/data-platform-api-client';
import {
  formatDateToTimestamp,
  formatPenceAsCurrencyString,
} from '@experience/shared/typescript/utils';

class SubmittedChargeTransformer {
  public toCsv(charge: SubmittedCharge): Record<string, string | number> {
    return {
      Started: formatDateToTimestamp(
        new Date(charge.startTime),
        'DD MMM YYYY - HH:mm'
      ) as string,
      Ended: formatDateToTimestamp(
        new Date(charge.endTime),
        'DD MMM YYYY - HH:mm'
      ) as string,
      'Submitted date time': formatDateToTimestamp(
        new Date(charge.submittedTime),
        'DD MMM YYYY - HH:mm'
      ) as string,
      Cost: formatPenceAsCurrencyString({ amount: charge.chargeCost }),
      'kWh used': `${charge.energyUsage} kWh`,
      'Location type': charge.location.locationType,
      'Charger name': charge.chargerName,
      Location:
        charge.location.locationType ===
        SubmittedChargeLocationLocationTypeEnum.Public
          ? charge.location.address.prettyPrint
          : '-',
      'Processed date time': charge.processedTime
        ? (formatDateToTimestamp(
            new Date(charge.processedTime),
            'DD MMM YYYY - HH:mm'
          ) as string)
        : '-',
      'Processed by': charge.processedByFullName ?? '-',
    };
  }
}

export const submittedChargeTransformer: SubmittedChargeTransformer =
  new SubmittedChargeTransformer();
