# How to add an in-app advert

## The purpose of this article

This article describes how to create a new advertisement to be shown to users of the Pod Point mobile application after
they have claimed a charge.

## Trigger

This is not something which commercial customers are able to self serve at this time. Requests will make their way to
the team from customers via product.

## Before you begin

In order to complete this task you will require the following:

- An image 855 pixels wide and 975 pixels high. The customer should provide this.
- A URL to which the app user will be navigated when they click the image. The customer should provide this.
- A list of pods to which the advert should be applied.
- Access to the main Pod Point AWS account (************).
- Access to the production instance of the podadmin database. The connection details and credentials can be obtained
  from the AWS Secrets Manager in the main Pod Point account.

## Step 1 - Resize image

Create required size variations of the 855 x 975 image which has been provided. It is important that the naming
convention for the files and their corresponding dimensions match the following:

BMW_In-App+Banner.jpg
width: 285 px
height: 325 px

<EMAIL>
width: 428 px
height: 488 px

<EMAIL>
width: 570 px
height: 650 px

<EMAIL>
width: 855 px
height: 975 px

Note that api3 has an API which is supposed to support this resizing process, but it does not have to be used. It was
attempted before writing this article but a successful request could not be made.

https://podpoint.atlassian.net/wiki/spaces/SKB/pages/**********/Adding+adverts+to+the+mobile+app+for+specific+locations

## Step 2 - Upload images

Upload the 4 images to the podpoint-admin-tool bucket in the main Pod Point account. Use the `adverts` folder.

https://s3.console.aws.amazon.com/s3/buckets/podpoint-admin-tool?region=eu-west-1&prefix=adverts/&showversions=false

## Step 3 - Make images public

Edit the access control list for the 4 images and ensure that Everyone (public access) is able to read the object. You
will have to tick a checkbox to acknowledge that you are aware that you are making the file available to anyone in the
world.

## Step 4 - Copy image URL

Copy the object URL for the image which does _not_ declare a size multiplier.

For example:

https://s3.console.aws.amazon.com/s3/object/podpoint-admin-tool?region=eu-west-1&prefix=adverts/BMW_In-App+Banner.jpg

## Step 5 - Create advert in database

Create the advert in the podpoint database. The first URL should be the one from step 4. The second URL should the one
provided by the customer for the advert to link off to when the user clicks it.

```
INSERT INTO podpoint.adverts(image, url, created_at, updated_at)
VALUES('https://podpoint-admin-tool.s3.eu-west-1.amazonaws.com/adverts/BMW_In-App+Banner.jpg', 'https://discover.bmw.co.uk/article/bmw-and-national-parks-partnership', '2022-01-13 10:26:00', '2022-01-13 10:26:00');
```

Take a note of the ID of the newly created row in the database.

## Step 6 - Link pods to advert

Apply the advert to the required pods in the podpoint database. Ensure that the list of IDs you are working with
correspond to the pod_locations table in the database. It is possible that you will have been provided with IDs for the
pod_units table or PPID's/names of units.

To find the ids of the records in pod_locations and update them, use the below steps:

1. Run this SQL, replacing the question mark with the PPID or name you have to find the pod_units record. If you already have the unit IDs you can skip this step.

```
# For PPID
SELECT * FROM podpoint.pod_units WHERE ppid = ?
# For name
SELECT * FROM podpoint.pod_units WHERE name = ?
```

2. Run this SQL, replacing the question mark with the pod unit id of the record from the previous step to find the pod_location_unit record:

```
SELECT * FROM podpoint.pod_location_unit WHERE unit_id = ?
```

3. Run this SQL, replacing the question mark with the location id of the record from the previous step to find the pod_location to be updated:

```
SELECT * FROM podpoint.pod_locations WHERE id = ?
```

4. Once you have found the records that you need to update via the previous steps you can update the pod_locations records
   to assign the adverts to those chargers.

Run this SQL, replacing the first question mark
with the advert id and the rest of the question marks with the ids of the pod_location records to be updated.

```
UPDATE podpoint.pod_locations
SET advert_id = ?
WHERE ID IN (?, ?, ?)
```
