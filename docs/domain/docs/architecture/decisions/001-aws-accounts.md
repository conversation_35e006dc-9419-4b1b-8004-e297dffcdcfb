# EXP-001: AWS accounts will be shared across domain

- **Status**: Accepted
- **Deciders**: <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>
- **Date**: 2023-03-02

Extended by: [002](002-service-access-control.md).

Extended by: [002](002-service-access-control.md).

Extended by: [002](002-service-access-control.md).

## Context

Should the subdomain teams have separate or shared AWS account structures?

## Decision Drivers

- Cost of provisioning and management of multiple accounts
- Complexity of the network connectivity model
- Complexity of the security model
- Current account structure
- Developer productivity
- Security risks
- Cost modelling
- Deployments impact
- Decision is changeable in the future

## Considered Options

- Subdomains share the same AWS account structure
- Subdomains each have their own AWS account structure
- Separate AWS account structures to support Commercial and Mobile separation

## Decision

Chosen option: **Subdomains share the same AWS account structure**

## Consequences

### Positive Consequences

- No need for Mobile to provision dedicated accounts
- Makes environment alignment within domain trivial
- Consistent connectivity and security model across subdomain
- Defers a number of decisions on connectivity and security controls that still require more detailed design
- Integration overhead between Mobile and Experience Data Platform is low
- Increases opportunties of sharing within domain (delivery pipeline, terraform infra, repositories, practices)
- Shared maintenance and support across account structure and environments
- Commercial and Experience Data Platform already sharing an account structure
- Assumption that Mobile will also deploy onto AWS Fargate clusters and can therefore utilise security groups as the
  main security control
- Provides a consistent baseline position that can be extended and hardened over time across the domain

### Negative Consequences

- Future cost of moving to separate account structure, if required
- Greater potential for one teams changes to affect others in the domain
- Account limits may need greater consideration with more services/teams in same account
- Potentially harder to implement and demonstrate the least privilege access policy
- Destructive commands and/or a security event blast radius affecting multiple services/applications
- Shared production environment
- Harder cost attribution model
