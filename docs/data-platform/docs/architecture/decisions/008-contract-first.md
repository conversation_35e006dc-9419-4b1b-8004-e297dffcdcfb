# XDP-008: Adopt contract first development approach

- **Status**: Accepted
- **Deciders**: <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON>, <PERSON>
- **Date**: 2022-11-25

Based on: [001](001-purpose.md)

Extended by: [009](009-rest.md), and
[012](012-monolith.md).

## Context

As a team providing API based services to others, whether that be web, message, or event based integrations,
there needs to be a well-defined approach to the development of these APIs to ensure that it is consistently applied.

## Decision Drivers

- Contract first is an established good practice
- As a team delivering APIs to others, establishing and agreeing consistent practice is important
- Provides clarity on purpose and behaviour of APIs being developed which is expected to reduce development time and
  cost
- Having APIs well documented reduces support overheads by enabling self-service options
- Having APIs well documented increases reuse by others
- Enables decoupling of delivery

## Considered Options

- Code first
- Contract first (aka API or Design first)

## Decision

Chosen option: **Contract first**, as it is an established good practice, and whilst there are complexities to adopting
the practice of providing well documented APIs early in the development cycle the benefits are considerable.
Additionally, having well understood and documented APIs will aid in the longer term support and management of the
services that are developed through the full publish, maintain and retirement phases.

## Consequences

### Positive Consequences

- Development teams can work in parallel, using mocks and stubs to release early
- APIs are documented at the earliest point
- Reduction in development costs as issues identified at design not implementation stage
- Promotes collaboration with teams who are clients of API
- Improved Developer Experience through contract documentation
- Promotes establishing repeatable API design decisions and adoption of standards, such as OpenAPI
- Enforces that the system boundary is the API provided not the underlying implementation

### Negative Consequences

- False assumptions, or changes of requirements will still mean changing the contract
- For simplest APIs may not be required
- Care has to be taken to ensure collaborative sessions are necessary and focused
- Care has to be taken to ensure best practices followed when agreeing contracts and not point in time opinions of
  those involved
