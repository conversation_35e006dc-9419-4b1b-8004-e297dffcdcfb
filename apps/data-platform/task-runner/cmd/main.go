package main

import (
	"context"
	"experience/apps/data-platform/task-runner/pkg/config"
	"experience/apps/data-platform/task-runner/pkg/runner"
	dbmigrate "experience/libs/data-platform/golang-migrate"
	"experience/libs/data-platform/tasks"
	"experience/libs/shared/go/db/postgres"
	"experience/libs/shared/go/sqs"
	"os"
)

const (
	appName = "data-platform-task-runner"
)

func main() {
	ctx := context.Background()
	c, logger := config.NewTaskRunnerConfig(ctx, appName)
	taskRunner := runner.NewRunner(os.Args)
	taskName := taskRunner.TaskName()
	logger.Printf("Starting task %q", taskName)

	rwDB := postgres.NewReadWriteDB(&c.ServiceDatasource, c.IsL<PERSON>al(), postgres.WithXray())
	flags := taskRunner.CommandLineFlags()
	sqsClient, _ := sqs.NewSQSClient(ctx)
	taskRunner.SetSqsClient(sqsClient, c.SQS)
	task := taskRunner.TaskInstance(logger, rwDB)

	mc := dbmigrate.NewMigrateConfig(c.Config, c.MigrateDatasource)
	dbmigrate.MigrateUp(mc, logger)

	result, err := task.Run(&tasks.TaskParams{Mapping: flags})
	if err != nil {
		logger.Printf("Error occurred while running %q task: %v", taskName, err)
		return
	}

	logger.Printf("Result: %s", result.Result)
	logger.Printf("Running time: %d secs", result.Meta.RunningTimeSecs)
}
