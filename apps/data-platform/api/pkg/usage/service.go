package usage

import (
	"context"
	"errors"
	"experience/apps/data-platform/api/pkg/common/addresses"
	"experience/apps/data-platform/api/pkg/common/groups"
	"experience/apps/data-platform/api/pkg/common/locations"
	"experience/apps/data-platform/api/pkg/usage/models"
	"log"
	"time"

	"github.com/google/uuid"
)

type serviceImpl struct {
	logger              *log.Logger
	repository          Repository
	groupsRepository    groups.Repository
	addressesRepository addresses.Repository
	locationsRepository locations.Repository
}

func NewService(logger *log.Logger, repository Repository, groupsRepository groups.Repository, addressesRepository addresses.Repository, locationsRepository locations.Repository) Service {
	return serviceImpl{
		logger:              logger,
		repository:          repository,
		groupsRepository:    groupsRepository,
		addressesRepository: addressesRepository,
		locationsRepository: locationsRepository,
	}
}

func (s serviceImpl) RetrieveBasicUsageByOrganisation(ctx context.Context, groupID uuid.UUID, from, to time.Time, interval string) ([]models.BasicUsage, error) {
	_, err := s.groupsRepository.FindGroupByUUID(ctx, groupID)
	if err != nil {
		if errors.Is(err, groups.ErrGroupNotFound) {
			return nil, ErrOrganisationNotFound
		}
		return nil, err
	}

	return s.repository.RetrieveBasicUsageByOrganisation(ctx, groupID, from, to, interval)
}

func (s serviceImpl) RetrieveBasicUsageByOrganisationAndSite(ctx context.Context, groupID uuid.UUID, siteID int32, from, to time.Time, interval string) ([]models.BasicUsage, error) {
	_, err := s.addressesRepository.FindByID(ctx, siteID)
	if err != nil {
		if errors.Is(err, addresses.ErrAddressNotFound) {
			return nil, ErrSiteNotFound
		}
		return nil, err
	}
	_, err = s.groupsRepository.FindGroupByUUID(ctx, groupID)
	if err != nil {
		if errors.Is(err, groups.ErrGroupNotFound) {
			return nil, ErrOrganisationNotFound
		}
		return nil, err
	}

	return s.repository.RetrieveBasicUsageByOrganisationAndSite(ctx, groupID, siteID, from, to, interval)
}

func (s serviceImpl) RetrieveBasicUsageByOrganisationAndLocation(ctx context.Context, groupID uuid.UUID, podID int32, from, to time.Time, interval string) ([]models.BasicUsage, error) {
	_, err := s.locationsRepository.FindLocationByID(ctx, podID)
	if err != nil {
		if errors.Is(err, locations.ErrLocationNotFound) {
			return nil, ErrLocationNotFound
		}
		return nil, err
	}
	_, err = s.groupsRepository.FindGroupByUUID(ctx, groupID)
	if err != nil {
		if errors.Is(err, groups.ErrGroupNotFound) {
			return nil, ErrOrganisationNotFound
		}
		return nil, err
	}

	return s.repository.RetrieveBasicUsageByOrganisationAndLocation(ctx, groupID, podID, from, to, interval)
}
