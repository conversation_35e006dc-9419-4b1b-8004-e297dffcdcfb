version: 1
packages:
  - name: 'sqlc'
    path: './sqlc'
    engine: 'postgresql'
    schema: '../../../../../libs/data-platform/golang-migrate/migration/'
    queries: 'queries/'
    emit_json_tags: true
    emit_prepared_queries: false
    emit_interface: false
    emit_exact_table_names: false
    strict_order_by: false
  - name: 'sqlcpodadmin'
    path: './sqlc-podadmin'
    engine: 'mysql'
    schema: '../../../../../libs/shared/test/db/podadmin/migrations/_init.sql'
    queries: 'queries-podadmin/'
    emit_json_tags: true
    emit_prepared_queries: false
    emit_interface: false
    emit_exact_table_names: false
rename:
  charge_uuid: 'ChargeUUID'
