# Data Platform API

Service to provide API endpoints and functionality to other experience domain teams services.

## Prerequisites

1. Please ensure you have completed the prerequisites in the root [README](/README.md)

## Lint

Run `npx nx lint data-platform-api`

## Build

Run `npx nx build data-platform-api`

## Run - Local Development

1. From the repository root directory
1. Start localstack, podadmin and data_platform databases by running: `npx nx compose data-platform-api -c up`
1. Either:

- within Goland select and run the data-platform-api run configuration, or
- run `npx nx serve data-platform-api`

### Running the database and localstack

Run `npx nx compose data-platform-api -c up` to start the podadamin databases and localstack containers.

Run `npx nx compose data-platform-api -c down` to stop the podadamin databases and localstack containers.

Run `npx nx compose data-platform-api -c remove` to remove the podadamin databases and localstack containers.

Run `npx nx compose data-platform-api -c recreate` to recreate the podadamin databases and localstack containers.

## Test

Run `npx nx test data-platform-api`

---

<img src="https://d3h256n3bzippp.cloudfront.net/pod-point-logo.svg" alt="Pod Point logo" style="float: right;" />

Driving shouldn’t cost the earth 🌍

Made with ❤️&nbsp;&nbsp;at [Pod Point](https://pod-point.com)
