// Jest Snapshot v1, https://goo.gl/fbAQLP

exports[`Loyalty card service api api should expose open api specification 1`] = `
{
  "components": {
    "schemas": {
      "CreateTescoClubcardRequest": {
        "properties": {
          "authId": {
            "format": "uuid",
            "pattern": "^([0-9a-fA-F]{8}-[0-9a-fA-F]{4}-[1-8][0-9a-fA-F]{3}-[89abAB][0-9a-fA-F]{3}-[0-9a-fA-F]{12}|00000000-0000-0000-0000-000000000000)$",
            "type": "string",
          },
          "customerId": {
            "format": "uuid",
            "pattern": "^([0-9a-fA-F]{8}-[0-9a-fA-F]{4}-[1-8][0-9a-fA-F]{3}-[89abAB][0-9a-fA-F]{3}-[0-9a-fA-F]{12}|00000000-0000-0000-0000-000000000000)$",
            "type": "string",
          },
        },
        "required": [
          "authId",
          "customerId",
        ],
        "type": "object",
      },
      "TescoClubcard": {
        "properties": {
          "authId": {
            "format": "uuid",
            "pattern": "^([0-9a-fA-F]{8}-[0-9a-fA-F]{4}-[1-8][0-9a-fA-F]{3}-[89abAB][0-9a-fA-F]{3}-[0-9a-fA-F]{12}|00000000-0000-0000-0000-000000000000)$",
            "type": "string",
          },
          "customerId": {
            "format": "uuid",
            "pattern": "^([0-9a-fA-F]{8}-[0-9a-fA-F]{4}-[1-8][0-9a-fA-F]{3}-[89abAB][0-9a-fA-F]{3}-[0-9a-fA-F]{12}|00000000-0000-0000-0000-000000000000)$",
            "type": "string",
          },
        },
        "required": [
          "authId",
          "customerId",
        ],
        "type": "object",
      },
    },
  },
  "info": {
    "contact": {},
    "description": "",
    "title": "",
    "version": "1.0.0",
  },
  "openapi": "3.0.0",
  "paths": {
    "/health": {
      "get": {
        "operationId": "HealthController_check",
        "parameters": [],
        "responses": {
          "200": {
            "content": {
              "application/json": {
                "schema": {
                  "properties": {
                    "details": {
                      "additionalProperties": {
                        "additionalProperties": true,
                        "properties": {
                          "status": {
                            "type": "string",
                          },
                        },
                        "required": [
                          "status",
                        ],
                        "type": "object",
                      },
                      "example": {
                        "database": {
                          "status": "up",
                        },
                      },
                      "type": "object",
                    },
                    "error": {
                      "additionalProperties": {
                        "additionalProperties": true,
                        "properties": {
                          "status": {
                            "type": "string",
                          },
                        },
                        "required": [
                          "status",
                        ],
                        "type": "object",
                      },
                      "example": {},
                      "nullable": true,
                      "type": "object",
                    },
                    "info": {
                      "additionalProperties": {
                        "additionalProperties": true,
                        "properties": {
                          "status": {
                            "type": "string",
                          },
                        },
                        "required": [
                          "status",
                        ],
                        "type": "object",
                      },
                      "example": {
                        "database": {
                          "status": "up",
                        },
                      },
                      "nullable": true,
                      "type": "object",
                    },
                    "status": {
                      "example": "ok",
                      "type": "string",
                    },
                  },
                  "type": "object",
                },
              },
            },
            "description": "The Health Check is successful",
          },
          "503": {
            "content": {
              "application/json": {
                "schema": {
                  "properties": {
                    "details": {
                      "additionalProperties": {
                        "additionalProperties": true,
                        "properties": {
                          "status": {
                            "type": "string",
                          },
                        },
                        "required": [
                          "status",
                        ],
                        "type": "object",
                      },
                      "example": {
                        "database": {
                          "status": "up",
                        },
                        "redis": {
                          "message": "Could not connect",
                          "status": "down",
                        },
                      },
                      "type": "object",
                    },
                    "error": {
                      "additionalProperties": {
                        "additionalProperties": true,
                        "properties": {
                          "status": {
                            "type": "string",
                          },
                        },
                        "required": [
                          "status",
                        ],
                        "type": "object",
                      },
                      "example": {
                        "redis": {
                          "message": "Could not connect",
                          "status": "down",
                        },
                      },
                      "nullable": true,
                      "type": "object",
                    },
                    "info": {
                      "additionalProperties": {
                        "additionalProperties": true,
                        "properties": {
                          "status": {
                            "type": "string",
                          },
                        },
                        "required": [
                          "status",
                        ],
                        "type": "object",
                      },
                      "example": {
                        "database": {
                          "status": "up",
                        },
                      },
                      "nullable": true,
                      "type": "object",
                    },
                    "status": {
                      "example": "error",
                      "type": "string",
                    },
                  },
                  "type": "object",
                },
              },
            },
            "description": "The Health Check is not successful",
          },
        },
        "tags": [
          "Health",
        ],
      },
    },
    "/loyalty-cards/tesco": {
      "post": {
        "operationId": "TescoClubcardController_create",
        "parameters": [],
        "requestBody": {
          "content": {
            "application/json": {
              "schema": {
                "$ref": "#/components/schemas/CreateTescoClubcardRequest",
              },
            },
          },
          "required": true,
        },
        "responses": {
          "201": {
            "description": "",
          },
        },
        "tags": [
          "TescoClubcard",
        ],
      },
    },
    "/loyalty-cards/tesco/{authId}": {
      "delete": {
        "operationId": "TescoClubcardController_delete",
        "parameters": [
          {
            "in": "path",
            "name": "authId",
            "required": true,
            "schema": {
              "format": "uuid",
              "pattern": "^([0-9a-fA-F]{8}-[0-9a-fA-F]{4}-[1-8][0-9a-fA-F]{3}-[89abAB][0-9a-fA-F]{3}-[0-9a-fA-F]{12}|00000000-0000-0000-0000-000000000000)$",
              "type": "string",
            },
          },
        ],
        "responses": {
          "204": {
            "description": "",
          },
        },
        "tags": [
          "TescoClubcard",
        ],
      },
      "get": {
        "operationId": "TescoClubcardController_get",
        "parameters": [
          {
            "in": "path",
            "name": "authId",
            "required": true,
            "schema": {
              "format": "uuid",
              "pattern": "^([0-9a-fA-F]{8}-[0-9a-fA-F]{4}-[1-8][0-9a-fA-F]{3}-[89abAB][0-9a-fA-F]{3}-[0-9a-fA-F]{12}|00000000-0000-0000-0000-000000000000)$",
              "type": "string",
            },
          },
        ],
        "responses": {
          "200": {
            "content": {
              "application/json": {
                "schema": {
                  "$ref": "#/components/schemas/TescoClubcard",
                },
              },
            },
            "description": "",
          },
        },
        "tags": [
          "TescoClubcard",
        ],
      },
    },
  },
  "servers": [],
  "tags": [],
}
`;

exports[`Loyalty card service api api should perform a health check 1`] = `
{
  "details": {
    "loyalty-card": {
      "status": "up",
    },
  },
  "error": {},
  "info": {
    "loyalty-card": {
      "status": "up",
    },
  },
  "status": "ok",
}
`;

exports[`Loyalty card service api tesco clubcard module tesco clubcard controller should delete a tesco clubcard 1`] = `""`;

exports[`Loyalty card service api tesco clubcard module tesco clubcard controller should get a tesco clubcard 1`] = `
{
  "authId": "79a7ca3e-bb8e-4904-b312-9c3c970b0538",
  "customerId": "5ceb29c2-b3b8-46ab-abe6-9e97782be291",
}
`;
