import { NextRequest } from 'next/server';
import { STATEMENT_SERVICE_API_URL } from '@experience/commercial/statement-service/shared';
import { appRequestHandler } from '@experience/commercial/next/app-request-utils';
import { redirect } from 'next/navigation';

export const GET = async (
  _: NextRequest,
  props: {
    params: Promise<{ invoiceId: string }>;
  }
): Promise<Response> => {
  const { invoiceId } = await props.params;
  return await appRequestHandler(
    `${STATEMENT_SERVICE_API_URL}/invoices/${invoiceId}/pdf`,
    {
      headers: {
        'content-type': 'application/pdf',
      },
      method: 'GET',
    },
    { 404: () => redirect(`/statements/invoices/${invoiceId}`) },
    true
  );
};
