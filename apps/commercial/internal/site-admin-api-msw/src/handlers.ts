/**
 * This file is AUTO GENERATED by [msw-auto-mock](https://github.com/zoubingwu/msw-auto-mock)
 * Feel free to commit/edit it as you need.
 */
/* eslint-disable */
/* tslint:disable */
// @ts-nocheck
import { HttpResponse, http } from 'msw';
import { faker } from '@faker-js/faker';

faker.seed(1);

const baseURL = '';
const MAX_ARRAY_LENGTH = 20;

// Map to store counters for each API endpoint
const apiCounters = new Map();

const next = (apiKey) => {
  let currentCount = apiCounters.get(apiKey) ?? 0;
  if (currentCount === Number.MAX_SAFE_INTEGER - 1) {
    currentCount = 0;
  }
  apiCounters.set(apiKey, currentCount + 1);
  return currentCount;
};

export const handlers = [
  http.get(`${baseURL}/admins`, async () => {
    const resultArray = [
      [getAdminControllerFindByGroupId200Response(), { status: 200 }],
    ];

    return HttpResponse.json(
      ...resultArray[next(`get /admins`) % resultArray.length]
    );
  }),
  http.post(`${baseURL}/admins`, async () => {
    const resultArray = [[undefined, { status: 201 }]];

    return HttpResponse.json(
      ...resultArray[next(`post /admins`) % resultArray.length]
    );
  }),
  http.post(`${baseURL}/admins/:id/status`, async () => {
    const resultArray = [[undefined, { status: 200 }]];

    return HttpResponse.json(
      ...resultArray[next(`post /admins/:id/status`) % resultArray.length]
    );
  }),
  http.delete(`${baseURL}/admins/:id`, async () => {
    const resultArray = [[undefined, { status: 204 }]];

    return HttpResponse.json(
      ...resultArray[next(`delete /admins/:id`) % resultArray.length]
    );
  }),
  http.post(`${baseURL}/admins/:id/invitation`, async () => {
    const resultArray = [[undefined, { status: 204 }]];

    return HttpResponse.json(
      ...resultArray[next(`post /admins/:id/invitation`) % resultArray.length]
    );
  }),
  http.post(`${baseURL}/admins/email-verification`, async () => {
    const resultArray = [[undefined, { status: 202 }]];

    return HttpResponse.json(
      ...resultArray[
        next(`post /admins/email-verification`) % resultArray.length
      ]
    );
  }),
  http.get(`${baseURL}/user/group`, async () => {
    const resultArray = [
      [getGroupControllerFindByGroupId200Response(), { status: 200 }],
    ];

    return HttpResponse.json(
      ...resultArray[next(`get /user/group`) % resultArray.length]
    );
  }),
  http.get(`${baseURL}/user/groups`, async () => {
    const resultArray = [
      [getGroupControllerFindAllByGroupId200Response(), { status: 200 }],
    ];

    return HttpResponse.json(
      ...resultArray[next(`get /user/groups`) % resultArray.length]
    );
  }),
  http.get(`${baseURL}/user/group/charges/csv`, async () => {
    const resultArray = [[undefined, { status: 200 }]];

    return HttpResponse.json(
      ...resultArray[next(`get /user/group/charges/csv`) % resultArray.length]
    );
  }),
  http.post(`${baseURL}/admins/password-reset`, async () => {
    const resultArray = [[undefined, { status: 202 }]];

    return HttpResponse.json(
      ...resultArray[next(`post /admins/password-reset`) % resultArray.length]
    );
  }),
  http.post(`${baseURL}/admins/sign-in-with-email`, async () => {
    const resultArray = [[undefined, { status: 202 }]];

    return HttpResponse.json(
      ...resultArray[
        next(`post /admins/sign-in-with-email`) % resultArray.length
      ]
    );
  }),
  http.get(`${baseURL}/drivers`, async () => {
    const resultArray = [
      [getDriverControllerFindByGroupId200Response(), { status: 200 }],
    ];

    return HttpResponse.json(
      ...resultArray[next(`get /drivers`) % resultArray.length]
    );
  }),
  http.post(`${baseURL}/drivers`, async () => {
    const resultArray = [
      [getDriverControllerCreateByGroupId201Response(), { status: 201 }],
    ];

    return HttpResponse.json(
      ...resultArray[next(`post /drivers`) % resultArray.length]
    );
  }),
  http.put(`${baseURL}/drivers/:driverId`, async () => {
    const resultArray = [[undefined, { status: 204 }]];

    return HttpResponse.json(
      ...resultArray[next(`put /drivers/:driverId`) % resultArray.length]
    );
  }),
  http.delete(`${baseURL}/drivers/:driverId`, async () => {
    const resultArray = [[undefined, { status: 204 }]];

    return HttpResponse.json(
      ...resultArray[next(`delete /drivers/:driverId`) % resultArray.length]
    );
  }),
  http.post(`${baseURL}/drivers/upload`, async () => {
    const resultArray = [
      [getDriverControllerBulkCreateByGroupId201Response(), { status: 201 }],
    ];

    return HttpResponse.json(
      ...resultArray[next(`post /drivers/upload`) % resultArray.length]
    );
  }),
  http.post(`${baseURL}/drivers/:driverId/invitation`, async () => {
    const resultArray = [[undefined, { status: 204 }]];

    return HttpResponse.json(
      ...resultArray[
        next(`post /drivers/:driverId/invitation`) % resultArray.length
      ]
    );
  }),
  http.get(`${baseURL}/drivers/:driverId/charges`, async () => {
    const resultArray = [
      [
        getDriverChargesControllerFindByGroupIdAndDriverId200Response(),
        { status: 200 },
      ],
    ];

    return HttpResponse.json(
      ...resultArray[
        next(`get /drivers/:driverId/charges`) % resultArray.length
      ]
    );
  }),
  http.get(`${baseURL}/drivers/:driverId/charges/csv`, async () => {
    const resultArray = [[undefined, { status: 200 }]];

    return HttpResponse.json(
      ...resultArray[
        next(`get /drivers/:driverId/charges/csv`) % resultArray.length
      ]
    );
  }),
  http.get(`${baseURL}/adverts`, async () => {
    const resultArray = [
      [getAdvertControllerFindAll200Response(), { status: 200 }],
    ];

    return HttpResponse.json(
      ...resultArray[next(`get /adverts`) % resultArray.length]
    );
  }),
  http.get(`${baseURL}/adverts/:advertId`, async () => {
    const resultArray = [
      [getAdvertControllerFindByAdvertId200Response(), { status: 200 }],
    ];

    return HttpResponse.json(
      ...resultArray[next(`get /adverts/:advertId`) % resultArray.length]
    );
  }),
  http.post(`${baseURL}/adverts/:advertId/pods`, async () => {
    const resultArray = [[undefined, { status: 204 }]];

    return HttpResponse.json(
      ...resultArray[next(`post /adverts/:advertId/pods`) % resultArray.length]
    );
  }),
  http.delete(`${baseURL}/adverts/:advertId/pods/:podId`, async () => {
    const resultArray = [[undefined, { status: 204 }]];

    return HttpResponse.json(
      ...resultArray[
        next(`delete /adverts/:advertId/pods/:podId`) % resultArray.length
      ]
    );
  }),
  http.get(`${baseURL}/billing`, async () => {
    const resultArray = [
      [
        getBillingControllerFindBillingInformationByGroupUid200Response(),
        { status: 200 },
      ],
    ];

    return HttpResponse.json(
      ...resultArray[next(`get /billing`) % resultArray.length]
    );
  }),
  http.get(`${baseURL}/billing/invoices/:invoiceId/pdf`, async () => {
    const resultArray = [[undefined, { status: 200 }]];

    return HttpResponse.json(
      ...resultArray[
        next(`get /billing/invoices/:invoiceId/pdf`) % resultArray.length
      ]
    );
  }),
  http.get(`${baseURL}/billing/statements/:statementId/pdf`, async () => {
    const resultArray = [[undefined, { status: 200 }]];

    return HttpResponse.json(
      ...resultArray[
        next(`get /billing/statements/:statementId/pdf`) % resultArray.length
      ]
    );
  }),
  http.get(`${baseURL}/domains`, async () => {
    const resultArray = [
      [getDomainControllerFindByGroupId200Response(), { status: 200 }],
    ];

    return HttpResponse.json(
      ...resultArray[next(`get /domains`) % resultArray.length]
    );
  }),
  http.post(`${baseURL}/domains`, async () => {
    const resultArray = [
      [getDomainControllerCreateByGroupId201Response(), { status: 201 }],
    ];

    return HttpResponse.json(
      ...resultArray[next(`post /domains`) % resultArray.length]
    );
  }),
  http.put(`${baseURL}/domains/:domainId`, async () => {
    const resultArray = [[undefined, { status: 204 }]];

    return HttpResponse.json(
      ...resultArray[next(`put /domains/:domainId`) % resultArray.length]
    );
  }),
  http.delete(`${baseURL}/domains/:domainId`, async () => {
    const resultArray = [[undefined, { status: 204 }]];

    return HttpResponse.json(
      ...resultArray[next(`delete /domains/:domainId`) % resultArray.length]
    );
  }),
  http.get(`${baseURL}/expenses/new`, async () => {
    const resultArray = [
      [getExpenseControllerFindNewByGroup200Response(), { status: 200 }],
    ];

    return HttpResponse.json(
      ...resultArray[next(`get /expenses/new`) % resultArray.length]
    );
  }),
  http.get(`${baseURL}/expenses/processed`, async () => {
    const resultArray = [
      [getExpenseControllerFindProcessedByGroup200Response(), { status: 200 }],
    ];

    return HttpResponse.json(
      ...resultArray[next(`get /expenses/processed`) % resultArray.length]
    );
  }),
  http.get(`${baseURL}/expenses/new/grouped`, async () => {
    const resultArray = [
      [
        getExpenseControllerFindNewByGroupGroupedByDriver200Response(),
        { status: 200 },
      ],
    ];

    return HttpResponse.json(
      ...resultArray[next(`get /expenses/new/grouped`) % resultArray.length]
    );
  }),
  http.get(`${baseURL}/expenses/drivers/:driverId`, async () => {
    const resultArray = [[undefined, { status: 200 }]];

    return HttpResponse.json(
      ...resultArray[
        next(`get /expenses/drivers/:driverId`) % resultArray.length
      ]
    );
  }),
  http.post(`${baseURL}/expenses/process`, async () => {
    const resultArray = [[undefined, { status: 201 }]];

    return HttpResponse.json(
      ...resultArray[next(`post /expenses/process`) % resultArray.length]
    );
  }),
  http.get(`${baseURL}/expenses/stats/monthly`, async () => {
    const resultArray = [
      [
        getExpenseControllerGetMonthlyUsageForOrganisation200Response(),
        { status: 200 },
      ],
    ];

    return HttpResponse.json(
      ...resultArray[next(`get /expenses/stats/monthly`) % resultArray.length]
    );
  }),
  http.get(`${baseURL}/health`, async () => {
    const resultArray = [
      [getHealthControllerCheck200Response(), { status: 200 }],
      [getHealthControllerCheck503Response(), { status: 503 }],
    ];

    return HttpResponse.json(
      ...resultArray[next(`get /health`) % resultArray.length]
    );
  }),
  http.get(`${baseURL}/insights`, async () => {
    const resultArray = [
      [getInsightsControllerFindByGroupUid200Response(), { status: 200 }],
    ];

    return HttpResponse.json(
      ...resultArray[next(`get /insights`) % resultArray.length]
    );
  }),
  http.get(`${baseURL}/insights/csv`, async () => {
    const resultArray = [[undefined, { status: 200 }]];

    return HttpResponse.json(
      ...resultArray[next(`get /insights/csv`) % resultArray.length]
    );
  }),
  http.get(`${baseURL}/insights/sites/:siteId`, async () => {
    const resultArray = [
      [
        getInsightsControllerFindByGroupUidAndSiteId200Response(),
        { status: 200 },
      ],
    ];

    return HttpResponse.json(
      ...resultArray[next(`get /insights/sites/:siteId`) % resultArray.length]
    );
  }),
  http.get(`${baseURL}/insights/sites/:siteId/csv`, async () => {
    const resultArray = [[undefined, { status: 200 }]];

    return HttpResponse.json(
      ...resultArray[
        next(`get /insights/sites/:siteId/csv`) % resultArray.length
      ]
    );
  }),
  http.get(`${baseURL}/insights/chargers/:chargerId`, async () => {
    const resultArray = [
      [
        getInsightsControllerFindByGroupUidAndChargerId200Response(),
        { status: 200 },
      ],
    ];

    return HttpResponse.json(
      ...resultArray[
        next(`get /insights/chargers/:chargerId`) % resultArray.length
      ]
    );
  }),
  http.get(`${baseURL}/insights/chargers/:chargerId/csv`, async () => {
    const resultArray = [[undefined, { status: 200 }]];

    return HttpResponse.json(
      ...resultArray[
        next(`get /insights/chargers/:chargerId/csv`) % resultArray.length
      ]
    );
  }),
  http.get(`${baseURL}/pods`, async () => {
    const resultArray = [
      [getPodControllerFindByGroupId200Response(), { status: 200 }],
    ];

    return HttpResponse.json(
      ...resultArray[next(`get /pods`) % resultArray.length]
    );
  }),
  http.get(`${baseURL}/pods/:podId`, async () => {
    const resultArray = [
      [getPodControllerFindByGroupIdAndPodId200Response(), { status: 200 }],
    ];

    return HttpResponse.json(
      ...resultArray[next(`get /pods/:podId`) % resultArray.length]
    );
  }),
  http.get(`${baseURL}/pods/:podId/charges`, async () => {
    const resultArray = [[undefined, { status: 200 }]];

    return HttpResponse.json(
      ...resultArray[next(`get /pods/:podId/charges`) % resultArray.length]
    );
  }),
  http.post(`${baseURL}/pods/:podId/charges`, async () => {
    const resultArray = [[undefined, { status: 204 }]];

    return HttpResponse.json(
      ...resultArray[next(`post /pods/:podId/charges`) % resultArray.length]
    );
  }),
  http.get(`${baseURL}/pods/:podId/events/security`, async () => {
    const resultArray = [
      [
        getPodControllerFindSecurityEventsByGroupIdAndPodId200Response(),
        { status: 200 },
      ],
    ];

    return HttpResponse.json(
      ...resultArray[
        next(`get /pods/:podId/events/security`) % resultArray.length
      ]
    );
  }),
  http.put(`${baseURL}/pods/:podId/tariff`, async () => {
    const resultArray = [[undefined, { status: 204 }]];

    return HttpResponse.json(
      ...resultArray[next(`put /pods/:podId/tariff`) % resultArray.length]
    );
  }),
  http.delete(`${baseURL}/pods/:podId/tariff`, async () => {
    const resultArray = [[undefined, { status: 204 }]];

    return HttpResponse.json(
      ...resultArray[next(`delete /pods/:podId/tariff`) % resultArray.length]
    );
  }),
  http.get(`${baseURL}/tariffs`, async () => {
    const resultArray = [
      [getTariffControllerFindAll200Response(), { status: 200 }],
    ];

    return HttpResponse.json(
      ...resultArray[next(`get /tariffs`) % resultArray.length]
    );
  }),
  http.post(`${baseURL}/tariffs`, async () => {
    const resultArray = [
      [getTariffControllerCreateByGroupId201Response(), { status: 201 }],
    ];

    return HttpResponse.json(
      ...resultArray[next(`post /tariffs`) % resultArray.length]
    );
  }),
  http.get(`${baseURL}/tariffs/:tariffId`, async () => {
    const resultArray = [
      [
        getTariffControllerFindByGroupIdAndTariffId200Response(),
        { status: 200 },
      ],
    ];

    return HttpResponse.json(
      ...resultArray[next(`get /tariffs/:tariffId`) % resultArray.length]
    );
  }),
  http.put(`${baseURL}/tariffs/:tariffId`, async () => {
    const resultArray = [[undefined, { status: 204 }]];

    return HttpResponse.json(
      ...resultArray[next(`put /tariffs/:tariffId`) % resultArray.length]
    );
  }),
  http.delete(`${baseURL}/tariffs/:tariffId`, async () => {
    const resultArray = [[undefined, { status: 204 }]];

    return HttpResponse.json(
      ...resultArray[next(`delete /tariffs/:tariffId`) % resultArray.length]
    );
  }),
  http.post(`${baseURL}/tariffs/:tariffId/schedules`, async () => {
    const resultArray = [[undefined, { status: 201 }]];

    return HttpResponse.json(
      ...resultArray[
        next(`post /tariffs/:tariffId/schedules`) % resultArray.length
      ]
    );
  }),
  http.delete(`${baseURL}/tariffs/:tariffId/schedules`, async () => {
    const resultArray = [[undefined, { status: 204 }]];

    return HttpResponse.json(
      ...resultArray[
        next(`delete /tariffs/:tariffId/schedules`) % resultArray.length
      ]
    );
  }),
  http.post(`${baseURL}/tariffs/:tariffId/schedules/daynight`, async () => {
    const resultArray = [[undefined, { status: 201 }]];

    return HttpResponse.json(
      ...resultArray[
        next(`post /tariffs/:tariffId/schedules/daynight`) % resultArray.length
      ]
    );
  }),
  http.post(`${baseURL}/tariffs/:tariffId/schedules/workweek`, async () => {
    const resultArray = [[undefined, { status: 201 }]];

    return HttpResponse.json(
      ...resultArray[
        next(`post /tariffs/:tariffId/schedules/workweek`) % resultArray.length
      ]
    );
  }),
  http.put(`${baseURL}/tariffs/:tariffId/schedules/:scheduleId`, async () => {
    const resultArray = [[undefined, { status: 204 }]];

    return HttpResponse.json(
      ...resultArray[
        next(`put /tariffs/:tariffId/schedules/:scheduleId`) %
          resultArray.length
      ]
    );
  }),
  http.put(`${baseURL}/tariffs/:tariffId/pods`, async () => {
    const resultArray = [[undefined, { status: 204 }]];

    return HttpResponse.json(
      ...resultArray[next(`put /tariffs/:tariffId/pods`) % resultArray.length]
    );
  }),
  http.post(`${baseURL}/sites/:siteId/additional-information`, async () => {
    const resultArray = [[undefined, { status: 201 }]];

    return HttpResponse.json(
      ...resultArray[
        next(`post /sites/:siteId/additional-information`) % resultArray.length
      ]
    );
  }),
  http.put(`${baseURL}/sites/:siteId/contact-details`, async () => {
    const resultArray = [[undefined, { status: 204 }]];

    return HttpResponse.json(
      ...resultArray[
        next(`put /sites/:siteId/contact-details`) % resultArray.length
      ]
    );
  }),
  http.put(`${baseURL}/sites/:siteId/energy-cost`, async () => {
    const resultArray = [[undefined, { status: 204 }]];

    return HttpResponse.json(
      ...resultArray[
        next(`put /sites/:siteId/energy-cost`) % resultArray.length
      ]
    );
  }),
  http.post(`${baseURL}/sites/:siteId/opening-times`, async () => {
    const resultArray = [[undefined, { status: 201 }]];

    return HttpResponse.json(
      ...resultArray[
        next(`post /sites/:siteId/opening-times`) % resultArray.length
      ]
    );
  }),
  http.get(`${baseURL}/sites`, async () => {
    const resultArray = [
      [getSiteControllerFindByGroupUid200Response(), { status: 200 }],
    ];

    return HttpResponse.json(
      ...resultArray[next(`get /sites`) % resultArray.length]
    );
  }),
  http.get(`${baseURL}/sites/:siteId`, async () => {
    const resultArray = [
      [getSiteControllerFindByGroupUidAndSiteId200Response(), { status: 200 }],
    ];

    return HttpResponse.json(
      ...resultArray[next(`get /sites/:siteId`) % resultArray.length]
    );
  }),
  http.get(`${baseURL}/sites/:siteId/charges`, async () => {
    const resultArray = [[undefined, { status: 200 }]];

    return HttpResponse.json(
      ...resultArray[next(`get /sites/:siteId/charges`) % resultArray.length]
    );
  }),
  http.get(`${baseURL}/sites/:siteId/statement`, async () => {
    const resultArray = [
      [
        getStatementControllerFindByGroupUidAndSiteId200Response(),
        { status: 200 },
      ],
    ];

    return HttpResponse.json(
      ...resultArray[next(`get /sites/:siteId/statement`) % resultArray.length]
    );
  }),
  http.post(`${baseURL}/sites/:siteId/statement`, async () => {
    const resultArray = [
      [
        getStatementControllerCreateByGroupUidAndSiteId201Response(),
        { status: 201 },
      ],
    ];

    return HttpResponse.json(
      ...resultArray[next(`post /sites/:siteId/statement`) % resultArray.length]
    );
  }),
  http.get(`${baseURL}/sites/stats/csv`, async () => {
    const resultArray = [[undefined, { status: 200 }]];

    return HttpResponse.json(
      ...resultArray[next(`get /sites/stats/csv`) % resultArray.length]
    );
  }),
  http.get(`${baseURL}/rfid/cards`, async () => {
    const resultArray = [
      [getRfidCardControllerFindByGroupId200Response(), { status: 200 }],
    ];

    return HttpResponse.json(
      ...resultArray[next(`get /rfid/cards`) % resultArray.length]
    );
  }),
  http.post(`${baseURL}/rfid/cards`, async () => {
    const resultArray = [[undefined, { status: 201 }]];

    return HttpResponse.json(
      ...resultArray[next(`post /rfid/cards`) % resultArray.length]
    );
  }),
  http.put(`${baseURL}/rfid/cards/:cardUid`, async () => {
    const resultArray = [[undefined, { status: 204 }]];

    return HttpResponse.json(
      ...resultArray[next(`put /rfid/cards/:cardUid`) % resultArray.length]
    );
  }),
  http.get(`${baseURL}/support/emails/bounced`, async () => {
    const resultArray = [
      [getBouncedEmailsControllerFindAll200Response(), { status: 200 }],
    ];

    return HttpResponse.json(
      ...resultArray[next(`get /support/emails/bounced`) % resultArray.length]
    );
  }),
  http.delete(`${baseURL}/support/emails/bounced/:emailAddress`, async () => {
    const resultArray = [[undefined, { status: 204 }]];

    return HttpResponse.json(
      ...resultArray[
        next(`delete /support/emails/bounced/:emailAddress`) %
          resultArray.length
      ]
    );
  }),
  http.get(`${baseURL}/support/chargers/:identifier`, async () => {
    const resultArray = [
      [getChargerControllerFindByIdentifier200Response(), { status: 200 }],
    ];

    return HttpResponse.json(
      ...resultArray[
        next(`get /support/chargers/:identifier`) % resultArray.length
      ]
    );
  }),
  http.get(`${baseURL}/support/chargers/:name/ppid`, async () => {
    const resultArray = [
      [getChargerControllerFindPpidByChargerName200Response(), { status: 200 }],
    ];

    return HttpResponse.json(
      ...resultArray[
        next(`get /support/chargers/:name/ppid`) % resultArray.length
      ]
    );
  }),
  http.get(`${baseURL}/support/chargers/:ppid/name`, async () => {
    const resultArray = [
      [getChargerControllerFindChargerNameByPpid200Response(), { status: 200 }],
    ];

    return HttpResponse.json(
      ...resultArray[
        next(`get /support/chargers/:ppid/name`) % resultArray.length
      ]
    );
  }),
  http.get(`${baseURL}/user`, async () => {
    const resultArray = [
      [getUserControllerFindByUserId200Response(), { status: 200 }],
    ];

    return HttpResponse.json(
      ...resultArray[next(`get /user`) % resultArray.length]
    );
  }),
  http.put(`${baseURL}/user`, async () => {
    const resultArray = [[undefined, { status: 204 }]];

    return HttpResponse.json(
      ...resultArray[next(`put /user`) % resultArray.length]
    );
  }),
  http.put(`${baseURL}/user/terms-and-conditions/acceptance`, async () => {
    const resultArray = [[undefined, { status: 204 }]];

    return HttpResponse.json(
      ...resultArray[
        next(`put /user/terms-and-conditions/acceptance`) % resultArray.length
      ]
    );
  }),
  http.get(`${baseURL}/version`, async () => {
    const resultArray = [[undefined, { status: 200 }]];

    return HttpResponse.json(
      ...resultArray[next(`get /version`) % resultArray.length]
    );
  }),
];

export function getAdminControllerFindByGroupId200Response() {
  return [
    ...new Array(faker.number.int({ min: 1, max: MAX_ARRAY_LENGTH })).keys(),
  ].map((_) => ({
    activatedOn: faker.date.past(),
    authId: faker.string.uuid(),
    email: faker.internet.email(),
    emailBounced: faker.datatype.boolean(),
    firstName: faker.person.fullName(),
    groupId: faker.number.int(),
    groupName: faker.person.fullName(),
    groupUid: faker.string.uuid(),
    id: faker.number.int(),
    lastLogin: faker.date.past(),
    lastName: faker.person.fullName(),
    status: faker.helpers.arrayElement([
      'Registered',
      'Pending',
      'Deactivated',
    ]),
  }));
}

export function getGroupControllerFindByGroupId200Response() {
  return {
    activatedOn: faker.date.past(),
    contactName: faker.person.fullName(),
    contactNumber: faker.lorem.words(),
    id: faker.number.int(),
    name: faker.person.fullName(),
    readOnly: false,
    stats: {
      chargingDuration: faker.number.int(),
      co2Savings: faker.number.int(),
      energyCost: faker.number.int(),
      energyDelivered: faker.number.int(),
      numberOfChargers: faker.number.int(),
      numberOfCharges: faker.number.int(),
      numberOfDrivers: faker.number.int(),
      numberOfSites: faker.number.int(),
      revenueGenerated: faker.number.int(),
    },
    type: faker.lorem.words(),
    uid: faker.string.uuid(),
  };
}

export function getGroupControllerFindAllByGroupId200Response() {
  return [
    ...new Array(faker.number.int({ min: 1, max: MAX_ARRAY_LENGTH })).keys(),
  ].map((_) => ({
    activatedOn: faker.date.past(),
    contactName: faker.person.fullName(),
    contactNumber: faker.lorem.words(),
    id: faker.number.int(),
    name: faker.person.fullName(),
    readOnly: false,
    stats: {
      chargingDuration: faker.number.int(),
      co2Savings: faker.number.int(),
      energyCost: faker.number.int(),
      energyDelivered: faker.number.int(),
      numberOfChargers: faker.number.int(),
      numberOfCharges: faker.number.int(),
      numberOfDrivers: faker.number.int(),
      numberOfSites: faker.number.int(),
      revenueGenerated: faker.number.int(),
    },
    type: faker.lorem.words(),
    uid: faker.string.uuid(),
  }));
}

export function getDriverControllerFindByGroupId200Response() {
  return [
    ...new Array(faker.number.int({ min: 1, max: MAX_ARRAY_LENGTH })).keys(),
  ].map((_) => ({
    canExpense: faker.datatype.boolean(),
    chargingStats: {
      charges: [
        ...new Array(
          faker.number.int({ min: 1, max: MAX_ARRAY_LENGTH })
        ).keys(),
      ].map((_) => ({
        chargingDuration: faker.lorem.words(),
        confirmed: faker.datatype.boolean(),
        confirmedBy: faker.helpers.arrayElement(['driver', 'other']),
        co2Savings: faker.lorem.words(),
        door: faker.lorem.words(),
        endedAt: faker.lorem.words(),
        energyCost: faker.lorem.words(),
        energyUsage: faker.lorem.words(),
        locationId: faker.number.int(),
        pluggedIn: faker.lorem.words(),
        podName: faker.person.fullName(),
        ppid: faker.string.uuid(),
        revenueGenerated: faker.lorem.words(),
        startedAt: faker.lorem.words(),
        siteName: faker.person.fullName(),
        totalDuration: faker.lorem.words(),
        userEmail: faker.internet.email(),
        userName: faker.person.fullName(),
        vehicle: faker.lorem.words(),
      })),
      co2Avoided: faker.lorem.words(),
      energyCost: faker.lorem.words(),
      energyDelivered: faker.lorem.words(),
      revenue: faker.lorem.words(),
    },
    email: faker.internet.email(),
    emailBounced: faker.datatype.boolean(),
    firstName: faker.person.fullName(),
    fullName: faker.person.fullName(),
    group: {
      activatedOn: faker.date.past(),
      contactName: faker.person.fullName(),
      contactNumber: faker.lorem.words(),
      id: faker.number.int(),
      name: faker.person.fullName(),
      readOnly: false,
      stats: {
        chargingDuration: faker.number.int(),
        co2Savings: faker.number.int(),
        energyCost: faker.number.int(),
        energyDelivered: faker.number.int(),
        numberOfChargers: faker.number.int(),
        numberOfCharges: faker.number.int(),
        numberOfDrivers: faker.number.int(),
        numberOfSites: faker.number.int(),
        revenueGenerated: faker.number.int(),
      },
      type: faker.lorem.words(),
      uid: faker.string.uuid(),
    },
    id: faker.number.int(),
    lastName: faker.person.fullName(),
    registeredDate: faker.lorem.words(),
    status: faker.helpers.arrayElement([
      'Deleted',
      'Deactivated',
      'Pending',
      'Registered',
    ]),
    tariffTier: faker.helpers.arrayElement(['Driver', 'Member']),
  }));
}

export function getDriverControllerCreateByGroupId201Response() {
  return {
    canExpense: faker.datatype.boolean(),
    chargingStats: {
      charges: [
        ...new Array(
          faker.number.int({ min: 1, max: MAX_ARRAY_LENGTH })
        ).keys(),
      ].map((_) => ({
        chargingDuration: faker.lorem.words(),
        confirmed: faker.datatype.boolean(),
        confirmedBy: faker.helpers.arrayElement(['driver', 'other']),
        co2Savings: faker.lorem.words(),
        door: faker.lorem.words(),
        endedAt: faker.lorem.words(),
        energyCost: faker.lorem.words(),
        energyUsage: faker.lorem.words(),
        locationId: faker.number.int(),
        pluggedIn: faker.lorem.words(),
        podName: faker.person.fullName(),
        ppid: faker.string.uuid(),
        revenueGenerated: faker.lorem.words(),
        startedAt: faker.lorem.words(),
        siteName: faker.person.fullName(),
        totalDuration: faker.lorem.words(),
        userEmail: faker.internet.email(),
        userName: faker.person.fullName(),
        vehicle: faker.lorem.words(),
      })),
      co2Avoided: faker.lorem.words(),
      energyCost: faker.lorem.words(),
      energyDelivered: faker.lorem.words(),
      revenue: faker.lorem.words(),
    },
    email: faker.internet.email(),
    emailBounced: faker.datatype.boolean(),
    firstName: faker.person.fullName(),
    fullName: faker.person.fullName(),
    group: {
      activatedOn: faker.date.past(),
      contactName: faker.person.fullName(),
      contactNumber: faker.lorem.words(),
      id: faker.number.int(),
      name: faker.person.fullName(),
      readOnly: false,
      stats: {
        chargingDuration: faker.number.int(),
        co2Savings: faker.number.int(),
        energyCost: faker.number.int(),
        energyDelivered: faker.number.int(),
        numberOfChargers: faker.number.int(),
        numberOfCharges: faker.number.int(),
        numberOfDrivers: faker.number.int(),
        numberOfSites: faker.number.int(),
        revenueGenerated: faker.number.int(),
      },
      type: faker.lorem.words(),
      uid: faker.string.uuid(),
    },
    id: faker.number.int(),
    lastName: faker.person.fullName(),
    registeredDate: faker.lorem.words(),
    status: faker.helpers.arrayElement([
      'Deleted',
      'Deactivated',
      'Pending',
      'Registered',
    ]),
    tariffTier: faker.helpers.arrayElement(['Driver', 'Member']),
  };
}

export function getDriverControllerBulkCreateByGroupId201Response() {
  return [
    ...new Array(faker.number.int({ min: 1, max: MAX_ARRAY_LENGTH })).keys(),
  ].map((_) => ({
    canExpense: faker.datatype.boolean(),
    chargingStats: {
      charges: [
        ...new Array(
          faker.number.int({ min: 1, max: MAX_ARRAY_LENGTH })
        ).keys(),
      ].map((_) => ({
        chargingDuration: faker.lorem.words(),
        confirmed: faker.datatype.boolean(),
        confirmedBy: faker.helpers.arrayElement(['driver', 'other']),
        co2Savings: faker.lorem.words(),
        door: faker.lorem.words(),
        endedAt: faker.lorem.words(),
        energyCost: faker.lorem.words(),
        energyUsage: faker.lorem.words(),
        locationId: faker.number.int(),
        pluggedIn: faker.lorem.words(),
        podName: faker.person.fullName(),
        ppid: faker.string.uuid(),
        revenueGenerated: faker.lorem.words(),
        startedAt: faker.lorem.words(),
        siteName: faker.person.fullName(),
        totalDuration: faker.lorem.words(),
        userEmail: faker.internet.email(),
        userName: faker.person.fullName(),
        vehicle: faker.lorem.words(),
      })),
      co2Avoided: faker.lorem.words(),
      energyCost: faker.lorem.words(),
      energyDelivered: faker.lorem.words(),
      revenue: faker.lorem.words(),
    },
    email: faker.internet.email(),
    emailBounced: faker.datatype.boolean(),
    firstName: faker.person.fullName(),
    fullName: faker.person.fullName(),
    group: {
      activatedOn: faker.date.past(),
      contactName: faker.person.fullName(),
      contactNumber: faker.lorem.words(),
      id: faker.number.int(),
      name: faker.person.fullName(),
      readOnly: false,
      stats: {
        chargingDuration: faker.number.int(),
        co2Savings: faker.number.int(),
        energyCost: faker.number.int(),
        energyDelivered: faker.number.int(),
        numberOfChargers: faker.number.int(),
        numberOfCharges: faker.number.int(),
        numberOfDrivers: faker.number.int(),
        numberOfSites: faker.number.int(),
        revenueGenerated: faker.number.int(),
      },
      type: faker.lorem.words(),
      uid: faker.string.uuid(),
    },
    id: faker.number.int(),
    lastName: faker.person.fullName(),
    registeredDate: faker.lorem.words(),
    status: faker.helpers.arrayElement([
      'Deleted',
      'Deactivated',
      'Pending',
      'Registered',
    ]),
    tariffTier: faker.helpers.arrayElement(['Driver', 'Member']),
  }));
}

export function getDriverChargesControllerFindByGroupIdAndDriverId200Response() {
  return {
    canExpense: faker.datatype.boolean(),
    chargingStats: {
      charges: [
        ...new Array(
          faker.number.int({ min: 1, max: MAX_ARRAY_LENGTH })
        ).keys(),
      ].map((_) => ({
        chargingDuration: faker.lorem.words(),
        confirmed: faker.datatype.boolean(),
        confirmedBy: faker.helpers.arrayElement(['driver', 'other']),
        co2Savings: faker.lorem.words(),
        door: faker.lorem.words(),
        endedAt: faker.lorem.words(),
        energyCost: faker.lorem.words(),
        energyUsage: faker.lorem.words(),
        locationId: faker.number.int(),
        pluggedIn: faker.lorem.words(),
        podName: faker.person.fullName(),
        ppid: faker.string.uuid(),
        revenueGenerated: faker.lorem.words(),
        startedAt: faker.lorem.words(),
        siteName: faker.person.fullName(),
        totalDuration: faker.lorem.words(),
        userEmail: faker.internet.email(),
        userName: faker.person.fullName(),
        vehicle: faker.lorem.words(),
      })),
      co2Avoided: faker.lorem.words(),
      energyCost: faker.lorem.words(),
      energyDelivered: faker.lorem.words(),
      revenue: faker.lorem.words(),
    },
    email: faker.internet.email(),
    emailBounced: faker.datatype.boolean(),
    firstName: faker.person.fullName(),
    fullName: faker.person.fullName(),
    group: {
      activatedOn: faker.date.past(),
      contactName: faker.person.fullName(),
      contactNumber: faker.lorem.words(),
      id: faker.number.int(),
      name: faker.person.fullName(),
      readOnly: false,
      stats: {
        chargingDuration: faker.number.int(),
        co2Savings: faker.number.int(),
        energyCost: faker.number.int(),
        energyDelivered: faker.number.int(),
        numberOfChargers: faker.number.int(),
        numberOfCharges: faker.number.int(),
        numberOfDrivers: faker.number.int(),
        numberOfSites: faker.number.int(),
        revenueGenerated: faker.number.int(),
      },
      type: faker.lorem.words(),
      uid: faker.string.uuid(),
    },
    id: faker.number.int(),
    lastName: faker.person.fullName(),
    registeredDate: faker.lorem.words(),
    status: faker.helpers.arrayElement([
      'Deleted',
      'Deactivated',
      'Pending',
      'Registered',
    ]),
    tariffTier: faker.helpers.arrayElement(['Driver', 'Member']),
  };
}

export function getAdvertControllerFindAll200Response() {
  return [
    ...new Array(faker.number.int({ min: 1, max: MAX_ARRAY_LENGTH })).keys(),
  ].map((_) => ({
    id: faker.number.int(),
    image: faker.lorem.words(),
    pods: [
      ...new Array(faker.number.int({ min: 1, max: MAX_ARRAY_LENGTH })).keys(),
    ].map((_) => ({
      ageYears: faker.number.int(),
      confirmChargeEnabled: faker.datatype.boolean(),
      chargeSummary: {
        chargingDuration: faker.number.int(),
        claimedEnergyUsage: faker.number.int(),
        co2Savings: faker.number.int(),
        energyCost: faker.number.int(),
        energyDelivered: faker.number.int(),
        energyUsage: faker.number.int(),
        numberOfCharges: faker.number.int(),
        revenueGenerated: faker.number.int(),
        revenueGeneratingClaimedUsage: faker.number.int(),
      },
      coordinates: {
        latitude: faker.number.int(),
        longitude: faker.number.int(),
      },
      description: faker.lorem.words(),
      id: faker.number.int(),
      installDate: faker.lorem.words(),
      isEvZone: faker.datatype.boolean(),
      isPublic: faker.datatype.boolean(),
      lastContact: faker.lorem.words(),
      model: faker.lorem.words(),
      mostRecentCharge: {
        chargingDuration: faker.lorem.words(),
        confirmed: faker.datatype.boolean(),
        confirmedBy: faker.helpers.arrayElement(['driver', 'other']),
        co2Savings: faker.lorem.words(),
        door: faker.lorem.words(),
        endedAt: faker.lorem.words(),
        energyCost: faker.lorem.words(),
        energyUsage: faker.lorem.words(),
        locationId: faker.number.int(),
        pluggedIn: faker.lorem.words(),
        podName: faker.person.fullName(),
        ppid: faker.string.uuid(),
        revenueGenerated: faker.lorem.words(),
        startedAt: faker.lorem.words(),
        siteName: faker.person.fullName(),
        totalDuration: faker.lorem.words(),
        userEmail: faker.internet.email(),
        userName: faker.person.fullName(),
        vehicle: faker.lorem.words(),
      },
      name: faker.person.fullName(),
      ppid: faker.string.uuid(),
      recentCharges: [
        ...new Array(
          faker.number.int({ min: 1, max: MAX_ARRAY_LENGTH })
        ).keys(),
      ].map((_) => ({
        chargingDuration: faker.lorem.words(),
        confirmed: faker.datatype.boolean(),
        confirmedBy: faker.helpers.arrayElement(['driver', 'other']),
        co2Savings: faker.lorem.words(),
        door: faker.lorem.words(),
        endedAt: faker.lorem.words(),
        energyCost: faker.lorem.words(),
        energyUsage: faker.lorem.words(),
        locationId: faker.number.int(),
        pluggedIn: faker.lorem.words(),
        podName: faker.person.fullName(),
        ppid: faker.string.uuid(),
        revenueGenerated: faker.lorem.words(),
        startedAt: faker.lorem.words(),
        siteName: faker.person.fullName(),
        totalDuration: faker.lorem.words(),
        userEmail: faker.internet.email(),
        userName: faker.person.fullName(),
        vehicle: faker.lorem.words(),
      })),
      schedules: [
        ...new Array(
          faker.number.int({ min: 1, max: MAX_ARRAY_LENGTH })
        ).keys(),
      ].map((_) => ({
        endDay: faker.number.int({ min: 0, max: 6 }),
        endTime: faker.lorem.words(),
        isActive: faker.datatype.boolean(),
        startDay: faker.number.int({ min: 0, max: 6 }),
        startTime: faker.lorem.words(),
      })),
      schemes: [
        ...new Array(
          faker.number.int({ min: 1, max: MAX_ARRAY_LENGTH })
        ).keys(),
      ].map((_) => ({
        activatedOn: faker.date.past(),
        contactName: faker.person.fullName(),
        contactNumber: faker.lorem.words(),
        id: faker.number.int(),
        name: faker.person.fullName(),
        readOnly: false,
        stats: {
          chargingDuration: faker.number.int(),
          co2Savings: faker.number.int(),
          energyCost: faker.number.int(),
          energyDelivered: faker.number.int(),
          numberOfChargers: faker.number.int(),
          numberOfCharges: faker.number.int(),
          numberOfDrivers: faker.number.int(),
          numberOfSites: faker.number.int(),
          revenueGenerated: faker.number.int(),
        },
        type: faker.lorem.words(),
        uid: faker.string.uuid(),
      })),
      site: {
        address: {
          country: faker.lorem.words(),
          line1: faker.lorem.words(),
          line2: faker.lorem.words(),
          name: faker.person.fullName(),
          postcode: faker.lorem.words(),
          prettyPrint: faker.lorem.words(),
          town: faker.lorem.words(),
        },
        chargeSummary: {
          chargingDuration: faker.number.int(),
          claimedEnergyUsage: faker.number.int(),
          co2Savings: faker.number.int(),
          energyCost: faker.number.int(),
          energyDelivered: faker.number.int(),
          energyUsage: faker.number.int(),
          numberOfCharges: faker.number.int(),
          revenueGenerated: faker.number.int(),
          revenueGeneratingClaimedUsage: faker.number.int(),
        },
        contactDetails: {
          email: faker.internet.email(),
          name: faker.person.fullName(),
          telephone: faker.lorem.words(),
        },
        description: faker.lorem.words(),
        energyCost: faker.number.int(),
        group: {
          activatedOn: faker.date.past(),
          contactName: faker.person.fullName(),
          contactNumber: faker.lorem.words(),
          id: faker.number.int(),
          name: faker.person.fullName(),
          readOnly: false,
          stats: {
            chargingDuration: faker.number.int(),
            co2Savings: faker.number.int(),
            energyCost: faker.number.int(),
            energyDelivered: faker.number.int(),
            numberOfChargers: faker.number.int(),
            numberOfCharges: faker.number.int(),
            numberOfDrivers: faker.number.int(),
            numberOfSites: faker.number.int(),
            revenueGenerated: faker.number.int(),
          },
          type: faker.lorem.words(),
          uid: faker.string.uuid(),
        },
        id: faker.number.int(),
        parking: {
          openingTimes: {
            notes: [
              ...new Array(
                faker.number.int({ min: 1, max: MAX_ARRAY_LENGTH })
              ).keys(),
            ].map((_) => faker.lorem.words()),
          },
        },
        pods: [
          ...new Array(
            faker.number.int({ min: 1, max: MAX_ARRAY_LENGTH })
          ).keys(),
        ].map((_) => null),
      },
      sockets: [
        ...new Array(
          faker.number.int({ min: 1, max: MAX_ARRAY_LENGTH })
        ).keys(),
      ].map((_) => ({
        door: faker.lorem.words(),
        firmwareVersion: faker.lorem.words(),
        isUpdateAvailable: faker.datatype.boolean(),
        lastContact: faker.lorem.words(),
        serialNumber: faker.lorem.words(),
        status: faker.helpers.arrayElement([
          'Available',
          'Charging',
          'Offline',
          'Unavailable',
        ]),
      })),
      status: faker.lorem.words(),
      supportsConfirmCharge: true,
      supportsContactless: faker.datatype.boolean(),
      supportsEnergyTariff: true,
      supportsOcpp: false,
      supportsPerKwh: true,
      supportsRfid: faker.datatype.boolean(),
      supportsTariffs: true,
      tariff: {
        id: faker.number.int(),
        name: faker.person.fullName(),
      },
    })),
    url: faker.internet.url(),
  }));
}

export function getAdvertControllerFindByAdvertId200Response() {
  return {
    id: faker.number.int(),
    image: faker.lorem.words(),
    pods: [
      ...new Array(faker.number.int({ min: 1, max: MAX_ARRAY_LENGTH })).keys(),
    ].map((_) => ({
      ageYears: faker.number.int(),
      confirmChargeEnabled: faker.datatype.boolean(),
      chargeSummary: {
        chargingDuration: faker.number.int(),
        claimedEnergyUsage: faker.number.int(),
        co2Savings: faker.number.int(),
        energyCost: faker.number.int(),
        energyDelivered: faker.number.int(),
        energyUsage: faker.number.int(),
        numberOfCharges: faker.number.int(),
        revenueGenerated: faker.number.int(),
        revenueGeneratingClaimedUsage: faker.number.int(),
      },
      coordinates: {
        latitude: faker.number.int(),
        longitude: faker.number.int(),
      },
      description: faker.lorem.words(),
      id: faker.number.int(),
      installDate: faker.lorem.words(),
      isEvZone: faker.datatype.boolean(),
      isPublic: faker.datatype.boolean(),
      lastContact: faker.lorem.words(),
      model: faker.lorem.words(),
      mostRecentCharge: {
        chargingDuration: faker.lorem.words(),
        confirmed: faker.datatype.boolean(),
        confirmedBy: faker.helpers.arrayElement(['driver', 'other']),
        co2Savings: faker.lorem.words(),
        door: faker.lorem.words(),
        endedAt: faker.lorem.words(),
        energyCost: faker.lorem.words(),
        energyUsage: faker.lorem.words(),
        locationId: faker.number.int(),
        pluggedIn: faker.lorem.words(),
        podName: faker.person.fullName(),
        ppid: faker.string.uuid(),
        revenueGenerated: faker.lorem.words(),
        startedAt: faker.lorem.words(),
        siteName: faker.person.fullName(),
        totalDuration: faker.lorem.words(),
        userEmail: faker.internet.email(),
        userName: faker.person.fullName(),
        vehicle: faker.lorem.words(),
      },
      name: faker.person.fullName(),
      ppid: faker.string.uuid(),
      recentCharges: [
        ...new Array(
          faker.number.int({ min: 1, max: MAX_ARRAY_LENGTH })
        ).keys(),
      ].map((_) => ({
        chargingDuration: faker.lorem.words(),
        confirmed: faker.datatype.boolean(),
        confirmedBy: faker.helpers.arrayElement(['driver', 'other']),
        co2Savings: faker.lorem.words(),
        door: faker.lorem.words(),
        endedAt: faker.lorem.words(),
        energyCost: faker.lorem.words(),
        energyUsage: faker.lorem.words(),
        locationId: faker.number.int(),
        pluggedIn: faker.lorem.words(),
        podName: faker.person.fullName(),
        ppid: faker.string.uuid(),
        revenueGenerated: faker.lorem.words(),
        startedAt: faker.lorem.words(),
        siteName: faker.person.fullName(),
        totalDuration: faker.lorem.words(),
        userEmail: faker.internet.email(),
        userName: faker.person.fullName(),
        vehicle: faker.lorem.words(),
      })),
      schedules: [
        ...new Array(
          faker.number.int({ min: 1, max: MAX_ARRAY_LENGTH })
        ).keys(),
      ].map((_) => ({
        endDay: faker.number.int({ min: 0, max: 6 }),
        endTime: faker.lorem.words(),
        isActive: faker.datatype.boolean(),
        startDay: faker.number.int({ min: 0, max: 6 }),
        startTime: faker.lorem.words(),
      })),
      schemes: [
        ...new Array(
          faker.number.int({ min: 1, max: MAX_ARRAY_LENGTH })
        ).keys(),
      ].map((_) => ({
        activatedOn: faker.date.past(),
        contactName: faker.person.fullName(),
        contactNumber: faker.lorem.words(),
        id: faker.number.int(),
        name: faker.person.fullName(),
        readOnly: false,
        stats: {
          chargingDuration: faker.number.int(),
          co2Savings: faker.number.int(),
          energyCost: faker.number.int(),
          energyDelivered: faker.number.int(),
          numberOfChargers: faker.number.int(),
          numberOfCharges: faker.number.int(),
          numberOfDrivers: faker.number.int(),
          numberOfSites: faker.number.int(),
          revenueGenerated: faker.number.int(),
        },
        type: faker.lorem.words(),
        uid: faker.string.uuid(),
      })),
      site: {
        address: {
          country: faker.lorem.words(),
          line1: faker.lorem.words(),
          line2: faker.lorem.words(),
          name: faker.person.fullName(),
          postcode: faker.lorem.words(),
          prettyPrint: faker.lorem.words(),
          town: faker.lorem.words(),
        },
        chargeSummary: {
          chargingDuration: faker.number.int(),
          claimedEnergyUsage: faker.number.int(),
          co2Savings: faker.number.int(),
          energyCost: faker.number.int(),
          energyDelivered: faker.number.int(),
          energyUsage: faker.number.int(),
          numberOfCharges: faker.number.int(),
          revenueGenerated: faker.number.int(),
          revenueGeneratingClaimedUsage: faker.number.int(),
        },
        contactDetails: {
          email: faker.internet.email(),
          name: faker.person.fullName(),
          telephone: faker.lorem.words(),
        },
        description: faker.lorem.words(),
        energyCost: faker.number.int(),
        group: {
          activatedOn: faker.date.past(),
          contactName: faker.person.fullName(),
          contactNumber: faker.lorem.words(),
          id: faker.number.int(),
          name: faker.person.fullName(),
          readOnly: false,
          stats: {
            chargingDuration: faker.number.int(),
            co2Savings: faker.number.int(),
            energyCost: faker.number.int(),
            energyDelivered: faker.number.int(),
            numberOfChargers: faker.number.int(),
            numberOfCharges: faker.number.int(),
            numberOfDrivers: faker.number.int(),
            numberOfSites: faker.number.int(),
            revenueGenerated: faker.number.int(),
          },
          type: faker.lorem.words(),
          uid: faker.string.uuid(),
        },
        id: faker.number.int(),
        parking: {
          openingTimes: {
            notes: [
              ...new Array(
                faker.number.int({ min: 1, max: MAX_ARRAY_LENGTH })
              ).keys(),
            ].map((_) => faker.lorem.words()),
          },
        },
        pods: [
          ...new Array(
            faker.number.int({ min: 1, max: MAX_ARRAY_LENGTH })
          ).keys(),
        ].map((_) => null),
      },
      sockets: [
        ...new Array(
          faker.number.int({ min: 1, max: MAX_ARRAY_LENGTH })
        ).keys(),
      ].map((_) => ({
        door: faker.lorem.words(),
        firmwareVersion: faker.lorem.words(),
        isUpdateAvailable: faker.datatype.boolean(),
        lastContact: faker.lorem.words(),
        serialNumber: faker.lorem.words(),
        status: faker.helpers.arrayElement([
          'Available',
          'Charging',
          'Offline',
          'Unavailable',
        ]),
      })),
      status: faker.lorem.words(),
      supportsConfirmCharge: true,
      supportsContactless: faker.datatype.boolean(),
      supportsEnergyTariff: true,
      supportsOcpp: false,
      supportsPerKwh: true,
      supportsRfid: faker.datatype.boolean(),
      supportsTariffs: true,
      tariff: {
        id: faker.number.int(),
        name: faker.person.fullName(),
      },
    })),
    url: faker.internet.url(),
  };
}

export function getBillingControllerFindBillingInformationByGroupUid200Response() {
  return {
    customerDetails: {
      address: {
        line1: faker.lorem.words(),
        line2: faker.lorem.words(),
        town: faker.lorem.words(),
        county: faker.lorem.words(),
        postcode: faker.lorem.words(),
      },
      email: faker.internet.email(),
      name: faker.person.fullName(),
      accountReference: faker.lorem.words(),
      poNumber: faker.lorem.words(),
    },
    onboardingLink: faker.lorem.words(),
    statements: [
      ...new Array(faker.number.int({ min: 1, max: MAX_ARRAY_LENGTH })).keys(),
    ].map((_) => ({
      siteName: faker.person.fullName(),
      month: faker.lorem.words(),
      feeInvoiceNumber: faker.lorem.words(),
      feeInvoiceStatus: faker.lorem.words(),
      hostedInvoiceUrl: faker.internet.url(),
      invoiceId: faker.string.uuid(),
      revenuePayoutStatus: faker.lorem.words(),
      statementId: faker.string.uuid(),
    })),
    subscription: {
      chargers: [
        ...new Array(
          faker.number.int({ min: 1, max: MAX_ARRAY_LENGTH })
        ).keys(),
      ].map((_) => ({
        ppid: faker.string.uuid(),
        socket: {},
      })),
      status: faker.lorem.words(),
      invoices: [
        ...new Array(
          faker.number.int({ min: 1, max: MAX_ARRAY_LENGTH })
        ).keys(),
      ].map((_) => ({
        status: faker.lorem.words(),
        created: faker.lorem.words(),
        amount: faker.number.int(),
        due: faker.lorem.words(),
        hostedInvoiceUrl: faker.internet.url(),
        invoiceNumber: faker.lorem.words(),
        invoicePdfUrl: faker.internet.url(),
        customerEmail: faker.internet.email(),
      })),
    },
    subscriptionChargers: [
      ...new Array(faker.number.int({ min: 1, max: MAX_ARRAY_LENGTH })).keys(),
    ].map((_) => ({
      ppid: faker.string.uuid(),
      socket: faker.lorem.words(),
    })),
  };
}

export function getDomainControllerFindByGroupId200Response() {
  return [
    ...new Array(faker.number.int({ min: 1, max: MAX_ARRAY_LENGTH })).keys(),
  ].map((_) => ({
    activatedOn: faker.date.past(),
    domainName: faker.person.fullName(),
    id: faker.number.int(),
    groupId: faker.number.int(),
  }));
}

export function getDomainControllerCreateByGroupId201Response() {
  return {
    activatedOn: faker.date.past(),
    domainName: faker.person.fullName(),
    id: faker.number.int(),
    groupId: faker.number.int(),
  };
}

export function getExpenseControllerFindNewByGroup200Response() {
  return [
    ...new Array(faker.number.int({ min: 1, max: MAX_ARRAY_LENGTH })).keys(),
  ].map((_) => ({}));
}

export function getExpenseControllerFindProcessedByGroup200Response() {
  return [
    ...new Array(faker.number.int({ min: 1, max: MAX_ARRAY_LENGTH })).keys(),
  ].map((_) => ({}));
}

export function getExpenseControllerFindNewByGroupGroupedByDriver200Response() {
  return [
    ...new Array(faker.number.int({ min: 1, max: MAX_ARRAY_LENGTH })).keys(),
  ].map((_) => ({}));
}

export function getExpenseControllerGetMonthlyUsageForOrganisation200Response() {
  return {};
}

export function getHealthControllerCheck200Response() {
  return {
    status: 'ok',
    info: { database: { status: 'up' } },
    error: {},
    details: { database: { status: 'up' } },
  };
}

export function getHealthControllerCheck503Response() {
  return {
    status: 'error',
    info: { database: { status: 'up' } },
    error: { redis: { status: 'down', message: 'Could not connect' } },
    details: {
      database: { status: 'up' },
      redis: { status: 'down', message: 'Could not connect' },
    },
  };
}

export function getInsightsControllerFindByGroupUid200Response() {
  return [
    ...new Array(faker.number.int({ min: 1, max: MAX_ARRAY_LENGTH })).keys(),
  ].map((_) => ({
    co2Savings: faker.number.int(),
    cost: faker.number.int(),
    intervalStartDate: faker.lorem.words(),
    revenueGenerated: faker.number.int(),
    totalUsage: faker.number.int(),
  }));
}

export function getInsightsControllerFindByGroupUidAndSiteId200Response() {
  return [
    ...new Array(faker.number.int({ min: 1, max: MAX_ARRAY_LENGTH })).keys(),
  ].map((_) => ({
    co2Savings: faker.number.int(),
    cost: faker.number.int(),
    intervalStartDate: faker.lorem.words(),
    revenueGenerated: faker.number.int(),
    totalUsage: faker.number.int(),
  }));
}

export function getInsightsControllerFindByGroupUidAndChargerId200Response() {
  return [
    ...new Array(faker.number.int({ min: 1, max: MAX_ARRAY_LENGTH })).keys(),
  ].map((_) => ({
    co2Savings: faker.number.int(),
    cost: faker.number.int(),
    intervalStartDate: faker.lorem.words(),
    revenueGenerated: faker.number.int(),
    totalUsage: faker.number.int(),
  }));
}

export function getPodControllerFindByGroupId200Response() {
  return [
    ...new Array(faker.number.int({ min: 1, max: MAX_ARRAY_LENGTH })).keys(),
  ].map((_) => ({
    ageYears: faker.number.int(),
    confirmChargeEnabled: faker.datatype.boolean(),
    chargeSummary: {
      chargingDuration: faker.number.int(),
      claimedEnergyUsage: faker.number.int(),
      co2Savings: faker.number.int(),
      energyCost: faker.number.int(),
      energyDelivered: faker.number.int(),
      energyUsage: faker.number.int(),
      numberOfCharges: faker.number.int(),
      revenueGenerated: faker.number.int(),
      revenueGeneratingClaimedUsage: faker.number.int(),
    },
    coordinates: {
      latitude: faker.number.int(),
      longitude: faker.number.int(),
    },
    description: faker.lorem.words(),
    id: faker.number.int(),
    installDate: faker.lorem.words(),
    isEvZone: faker.datatype.boolean(),
    isPublic: faker.datatype.boolean(),
    lastContact: faker.lorem.words(),
    model: faker.lorem.words(),
    mostRecentCharge: {
      chargingDuration: faker.lorem.words(),
      confirmed: faker.datatype.boolean(),
      confirmedBy: faker.helpers.arrayElement(['driver', 'other']),
      co2Savings: faker.lorem.words(),
      door: faker.lorem.words(),
      endedAt: faker.lorem.words(),
      energyCost: faker.lorem.words(),
      energyUsage: faker.lorem.words(),
      locationId: faker.number.int(),
      pluggedIn: faker.lorem.words(),
      podName: faker.person.fullName(),
      ppid: faker.string.uuid(),
      revenueGenerated: faker.lorem.words(),
      startedAt: faker.lorem.words(),
      siteName: faker.person.fullName(),
      totalDuration: faker.lorem.words(),
      userEmail: faker.internet.email(),
      userName: faker.person.fullName(),
      vehicle: faker.lorem.words(),
    },
    name: faker.person.fullName(),
    ppid: faker.string.uuid(),
    recentCharges: [
      ...new Array(faker.number.int({ min: 1, max: MAX_ARRAY_LENGTH })).keys(),
    ].map((_) => ({
      chargingDuration: faker.lorem.words(),
      confirmed: faker.datatype.boolean(),
      confirmedBy: faker.helpers.arrayElement(['driver', 'other']),
      co2Savings: faker.lorem.words(),
      door: faker.lorem.words(),
      endedAt: faker.lorem.words(),
      energyCost: faker.lorem.words(),
      energyUsage: faker.lorem.words(),
      locationId: faker.number.int(),
      pluggedIn: faker.lorem.words(),
      podName: faker.person.fullName(),
      ppid: faker.string.uuid(),
      revenueGenerated: faker.lorem.words(),
      startedAt: faker.lorem.words(),
      siteName: faker.person.fullName(),
      totalDuration: faker.lorem.words(),
      userEmail: faker.internet.email(),
      userName: faker.person.fullName(),
      vehicle: faker.lorem.words(),
    })),
    schedules: [
      ...new Array(faker.number.int({ min: 1, max: MAX_ARRAY_LENGTH })).keys(),
    ].map((_) => ({
      endDay: faker.number.int({ min: 0, max: 6 }),
      endTime: faker.lorem.words(),
      isActive: faker.datatype.boolean(),
      startDay: faker.number.int({ min: 0, max: 6 }),
      startTime: faker.lorem.words(),
    })),
    schemes: [
      ...new Array(faker.number.int({ min: 1, max: MAX_ARRAY_LENGTH })).keys(),
    ].map((_) => ({
      activatedOn: faker.date.past(),
      contactName: faker.person.fullName(),
      contactNumber: faker.lorem.words(),
      id: faker.number.int(),
      name: faker.person.fullName(),
      readOnly: false,
      stats: {
        chargingDuration: faker.number.int(),
        co2Savings: faker.number.int(),
        energyCost: faker.number.int(),
        energyDelivered: faker.number.int(),
        numberOfChargers: faker.number.int(),
        numberOfCharges: faker.number.int(),
        numberOfDrivers: faker.number.int(),
        numberOfSites: faker.number.int(),
        revenueGenerated: faker.number.int(),
      },
      type: faker.lorem.words(),
      uid: faker.string.uuid(),
    })),
    site: {
      address: {
        country: faker.lorem.words(),
        line1: faker.lorem.words(),
        line2: faker.lorem.words(),
        name: faker.person.fullName(),
        postcode: faker.lorem.words(),
        prettyPrint: faker.lorem.words(),
        town: faker.lorem.words(),
      },
      chargeSummary: {
        chargingDuration: faker.number.int(),
        claimedEnergyUsage: faker.number.int(),
        co2Savings: faker.number.int(),
        energyCost: faker.number.int(),
        energyDelivered: faker.number.int(),
        energyUsage: faker.number.int(),
        numberOfCharges: faker.number.int(),
        revenueGenerated: faker.number.int(),
        revenueGeneratingClaimedUsage: faker.number.int(),
      },
      contactDetails: {
        email: faker.internet.email(),
        name: faker.person.fullName(),
        telephone: faker.lorem.words(),
      },
      description: faker.lorem.words(),
      energyCost: faker.number.int(),
      group: {
        activatedOn: faker.date.past(),
        contactName: faker.person.fullName(),
        contactNumber: faker.lorem.words(),
        id: faker.number.int(),
        name: faker.person.fullName(),
        readOnly: false,
        stats: {
          chargingDuration: faker.number.int(),
          co2Savings: faker.number.int(),
          energyCost: faker.number.int(),
          energyDelivered: faker.number.int(),
          numberOfChargers: faker.number.int(),
          numberOfCharges: faker.number.int(),
          numberOfDrivers: faker.number.int(),
          numberOfSites: faker.number.int(),
          revenueGenerated: faker.number.int(),
        },
        type: faker.lorem.words(),
        uid: faker.string.uuid(),
      },
      id: faker.number.int(),
      parking: {
        openingTimes: {
          notes: [
            ...new Array(
              faker.number.int({ min: 1, max: MAX_ARRAY_LENGTH })
            ).keys(),
          ].map((_) => faker.lorem.words()),
        },
      },
      pods: [
        ...new Array(
          faker.number.int({ min: 1, max: MAX_ARRAY_LENGTH })
        ).keys(),
      ].map((_) => null),
    },
    sockets: [
      ...new Array(faker.number.int({ min: 1, max: MAX_ARRAY_LENGTH })).keys(),
    ].map((_) => ({
      door: faker.lorem.words(),
      firmwareVersion: faker.lorem.words(),
      isUpdateAvailable: faker.datatype.boolean(),
      lastContact: faker.lorem.words(),
      serialNumber: faker.lorem.words(),
      status: faker.helpers.arrayElement([
        'Available',
        'Charging',
        'Offline',
        'Unavailable',
      ]),
    })),
    status: faker.lorem.words(),
    supportsConfirmCharge: true,
    supportsContactless: faker.datatype.boolean(),
    supportsEnergyTariff: true,
    supportsOcpp: false,
    supportsPerKwh: true,
    supportsRfid: faker.datatype.boolean(),
    supportsTariffs: true,
    tariff: {
      id: faker.number.int(),
      name: faker.person.fullName(),
    },
  }));
}

export function getPodControllerFindByGroupIdAndPodId200Response() {
  return {
    ageYears: faker.number.int(),
    confirmChargeEnabled: faker.datatype.boolean(),
    chargeSummary: {
      chargingDuration: faker.number.int(),
      claimedEnergyUsage: faker.number.int(),
      co2Savings: faker.number.int(),
      energyCost: faker.number.int(),
      energyDelivered: faker.number.int(),
      energyUsage: faker.number.int(),
      numberOfCharges: faker.number.int(),
      revenueGenerated: faker.number.int(),
      revenueGeneratingClaimedUsage: faker.number.int(),
    },
    coordinates: {
      latitude: faker.number.int(),
      longitude: faker.number.int(),
    },
    description: faker.lorem.words(),
    id: faker.number.int(),
    installDate: faker.lorem.words(),
    isEvZone: faker.datatype.boolean(),
    isPublic: faker.datatype.boolean(),
    lastContact: faker.lorem.words(),
    model: faker.lorem.words(),
    mostRecentCharge: {
      chargingDuration: faker.lorem.words(),
      confirmed: faker.datatype.boolean(),
      confirmedBy: faker.helpers.arrayElement(['driver', 'other']),
      co2Savings: faker.lorem.words(),
      door: faker.lorem.words(),
      endedAt: faker.lorem.words(),
      energyCost: faker.lorem.words(),
      energyUsage: faker.lorem.words(),
      locationId: faker.number.int(),
      pluggedIn: faker.lorem.words(),
      podName: faker.person.fullName(),
      ppid: faker.string.uuid(),
      revenueGenerated: faker.lorem.words(),
      startedAt: faker.lorem.words(),
      siteName: faker.person.fullName(),
      totalDuration: faker.lorem.words(),
      userEmail: faker.internet.email(),
      userName: faker.person.fullName(),
      vehicle: faker.lorem.words(),
    },
    name: faker.person.fullName(),
    ppid: faker.string.uuid(),
    recentCharges: [
      ...new Array(faker.number.int({ min: 1, max: MAX_ARRAY_LENGTH })).keys(),
    ].map((_) => ({
      chargingDuration: faker.lorem.words(),
      confirmed: faker.datatype.boolean(),
      confirmedBy: faker.helpers.arrayElement(['driver', 'other']),
      co2Savings: faker.lorem.words(),
      door: faker.lorem.words(),
      endedAt: faker.lorem.words(),
      energyCost: faker.lorem.words(),
      energyUsage: faker.lorem.words(),
      locationId: faker.number.int(),
      pluggedIn: faker.lorem.words(),
      podName: faker.person.fullName(),
      ppid: faker.string.uuid(),
      revenueGenerated: faker.lorem.words(),
      startedAt: faker.lorem.words(),
      siteName: faker.person.fullName(),
      totalDuration: faker.lorem.words(),
      userEmail: faker.internet.email(),
      userName: faker.person.fullName(),
      vehicle: faker.lorem.words(),
    })),
    schedules: [
      ...new Array(faker.number.int({ min: 1, max: MAX_ARRAY_LENGTH })).keys(),
    ].map((_) => ({
      endDay: faker.number.int({ min: 0, max: 6 }),
      endTime: faker.lorem.words(),
      isActive: faker.datatype.boolean(),
      startDay: faker.number.int({ min: 0, max: 6 }),
      startTime: faker.lorem.words(),
    })),
    schemes: [
      ...new Array(faker.number.int({ min: 1, max: MAX_ARRAY_LENGTH })).keys(),
    ].map((_) => ({
      activatedOn: faker.date.past(),
      contactName: faker.person.fullName(),
      contactNumber: faker.lorem.words(),
      id: faker.number.int(),
      name: faker.person.fullName(),
      readOnly: false,
      stats: {
        chargingDuration: faker.number.int(),
        co2Savings: faker.number.int(),
        energyCost: faker.number.int(),
        energyDelivered: faker.number.int(),
        numberOfChargers: faker.number.int(),
        numberOfCharges: faker.number.int(),
        numberOfDrivers: faker.number.int(),
        numberOfSites: faker.number.int(),
        revenueGenerated: faker.number.int(),
      },
      type: faker.lorem.words(),
      uid: faker.string.uuid(),
    })),
    site: {
      address: {
        country: faker.lorem.words(),
        line1: faker.lorem.words(),
        line2: faker.lorem.words(),
        name: faker.person.fullName(),
        postcode: faker.lorem.words(),
        prettyPrint: faker.lorem.words(),
        town: faker.lorem.words(),
      },
      chargeSummary: {
        chargingDuration: faker.number.int(),
        claimedEnergyUsage: faker.number.int(),
        co2Savings: faker.number.int(),
        energyCost: faker.number.int(),
        energyDelivered: faker.number.int(),
        energyUsage: faker.number.int(),
        numberOfCharges: faker.number.int(),
        revenueGenerated: faker.number.int(),
        revenueGeneratingClaimedUsage: faker.number.int(),
      },
      contactDetails: {
        email: faker.internet.email(),
        name: faker.person.fullName(),
        telephone: faker.lorem.words(),
      },
      description: faker.lorem.words(),
      energyCost: faker.number.int(),
      group: {
        activatedOn: faker.date.past(),
        contactName: faker.person.fullName(),
        contactNumber: faker.lorem.words(),
        id: faker.number.int(),
        name: faker.person.fullName(),
        readOnly: false,
        stats: {
          chargingDuration: faker.number.int(),
          co2Savings: faker.number.int(),
          energyCost: faker.number.int(),
          energyDelivered: faker.number.int(),
          numberOfChargers: faker.number.int(),
          numberOfCharges: faker.number.int(),
          numberOfDrivers: faker.number.int(),
          numberOfSites: faker.number.int(),
          revenueGenerated: faker.number.int(),
        },
        type: faker.lorem.words(),
        uid: faker.string.uuid(),
      },
      id: faker.number.int(),
      parking: {
        openingTimes: {
          notes: [
            ...new Array(
              faker.number.int({ min: 1, max: MAX_ARRAY_LENGTH })
            ).keys(),
          ].map((_) => faker.lorem.words()),
        },
      },
      pods: [
        ...new Array(
          faker.number.int({ min: 1, max: MAX_ARRAY_LENGTH })
        ).keys(),
      ].map((_) => null),
    },
    sockets: [
      ...new Array(faker.number.int({ min: 1, max: MAX_ARRAY_LENGTH })).keys(),
    ].map((_) => ({
      door: faker.lorem.words(),
      firmwareVersion: faker.lorem.words(),
      isUpdateAvailable: faker.datatype.boolean(),
      lastContact: faker.lorem.words(),
      serialNumber: faker.lorem.words(),
      status: faker.helpers.arrayElement([
        'Available',
        'Charging',
        'Offline',
        'Unavailable',
      ]),
    })),
    status: faker.lorem.words(),
    supportsConfirmCharge: true,
    supportsContactless: faker.datatype.boolean(),
    supportsEnergyTariff: true,
    supportsOcpp: false,
    supportsPerKwh: true,
    supportsRfid: faker.datatype.boolean(),
    supportsTariffs: true,
    tariff: {
      id: faker.number.int(),
      name: faker.person.fullName(),
    },
  };
}

export function getPodControllerFindSecurityEventsByGroupIdAndPodId200Response() {
  return {};
}

export function getTariffControllerFindAll200Response() {
  return [
    ...new Array(faker.number.int({ min: 1, max: MAX_ARRAY_LENGTH })).keys(),
  ].map((_) => ({
    id: faker.number.int(),
    name: faker.person.fullName(),
    currency: faker.lorem.words(),
    pods: [
      ...new Array(faker.number.int({ min: 1, max: MAX_ARRAY_LENGTH })).keys(),
    ].map((_) => ({
      ageYears: faker.number.int(),
      confirmChargeEnabled: faker.datatype.boolean(),
      chargeSummary: {
        chargingDuration: faker.number.int(),
        claimedEnergyUsage: faker.number.int(),
        co2Savings: faker.number.int(),
        energyCost: faker.number.int(),
        energyDelivered: faker.number.int(),
        energyUsage: faker.number.int(),
        numberOfCharges: faker.number.int(),
        revenueGenerated: faker.number.int(),
        revenueGeneratingClaimedUsage: faker.number.int(),
      },
      coordinates: {
        latitude: faker.number.int(),
        longitude: faker.number.int(),
      },
      description: faker.lorem.words(),
      id: faker.number.int(),
      installDate: faker.lorem.words(),
      isEvZone: faker.datatype.boolean(),
      isPublic: faker.datatype.boolean(),
      lastContact: faker.lorem.words(),
      model: faker.lorem.words(),
      mostRecentCharge: {
        chargingDuration: faker.lorem.words(),
        confirmed: faker.datatype.boolean(),
        confirmedBy: faker.helpers.arrayElement(['driver', 'other']),
        co2Savings: faker.lorem.words(),
        door: faker.lorem.words(),
        endedAt: faker.lorem.words(),
        energyCost: faker.lorem.words(),
        energyUsage: faker.lorem.words(),
        locationId: faker.number.int(),
        pluggedIn: faker.lorem.words(),
        podName: faker.person.fullName(),
        ppid: faker.string.uuid(),
        revenueGenerated: faker.lorem.words(),
        startedAt: faker.lorem.words(),
        siteName: faker.person.fullName(),
        totalDuration: faker.lorem.words(),
        userEmail: faker.internet.email(),
        userName: faker.person.fullName(),
        vehicle: faker.lorem.words(),
      },
      name: faker.person.fullName(),
      ppid: faker.string.uuid(),
      recentCharges: [
        ...new Array(
          faker.number.int({ min: 1, max: MAX_ARRAY_LENGTH })
        ).keys(),
      ].map((_) => ({
        chargingDuration: faker.lorem.words(),
        confirmed: faker.datatype.boolean(),
        confirmedBy: faker.helpers.arrayElement(['driver', 'other']),
        co2Savings: faker.lorem.words(),
        door: faker.lorem.words(),
        endedAt: faker.lorem.words(),
        energyCost: faker.lorem.words(),
        energyUsage: faker.lorem.words(),
        locationId: faker.number.int(),
        pluggedIn: faker.lorem.words(),
        podName: faker.person.fullName(),
        ppid: faker.string.uuid(),
        revenueGenerated: faker.lorem.words(),
        startedAt: faker.lorem.words(),
        siteName: faker.person.fullName(),
        totalDuration: faker.lorem.words(),
        userEmail: faker.internet.email(),
        userName: faker.person.fullName(),
        vehicle: faker.lorem.words(),
      })),
      schedules: [
        ...new Array(
          faker.number.int({ min: 1, max: MAX_ARRAY_LENGTH })
        ).keys(),
      ].map((_) => ({
        endDay: faker.number.int({ min: 0, max: 6 }),
        endTime: faker.lorem.words(),
        isActive: faker.datatype.boolean(),
        startDay: faker.number.int({ min: 0, max: 6 }),
        startTime: faker.lorem.words(),
      })),
      schemes: [
        ...new Array(
          faker.number.int({ min: 1, max: MAX_ARRAY_LENGTH })
        ).keys(),
      ].map((_) => ({
        activatedOn: faker.date.past(),
        contactName: faker.person.fullName(),
        contactNumber: faker.lorem.words(),
        id: faker.number.int(),
        name: faker.person.fullName(),
        readOnly: false,
        stats: {
          chargingDuration: faker.number.int(),
          co2Savings: faker.number.int(),
          energyCost: faker.number.int(),
          energyDelivered: faker.number.int(),
          numberOfChargers: faker.number.int(),
          numberOfCharges: faker.number.int(),
          numberOfDrivers: faker.number.int(),
          numberOfSites: faker.number.int(),
          revenueGenerated: faker.number.int(),
        },
        type: faker.lorem.words(),
        uid: faker.string.uuid(),
      })),
      site: {
        address: {
          country: faker.lorem.words(),
          line1: faker.lorem.words(),
          line2: faker.lorem.words(),
          name: faker.person.fullName(),
          postcode: faker.lorem.words(),
          prettyPrint: faker.lorem.words(),
          town: faker.lorem.words(),
        },
        chargeSummary: {
          chargingDuration: faker.number.int(),
          claimedEnergyUsage: faker.number.int(),
          co2Savings: faker.number.int(),
          energyCost: faker.number.int(),
          energyDelivered: faker.number.int(),
          energyUsage: faker.number.int(),
          numberOfCharges: faker.number.int(),
          revenueGenerated: faker.number.int(),
          revenueGeneratingClaimedUsage: faker.number.int(),
        },
        contactDetails: {
          email: faker.internet.email(),
          name: faker.person.fullName(),
          telephone: faker.lorem.words(),
        },
        description: faker.lorem.words(),
        energyCost: faker.number.int(),
        group: {
          activatedOn: faker.date.past(),
          contactName: faker.person.fullName(),
          contactNumber: faker.lorem.words(),
          id: faker.number.int(),
          name: faker.person.fullName(),
          readOnly: false,
          stats: {
            chargingDuration: faker.number.int(),
            co2Savings: faker.number.int(),
            energyCost: faker.number.int(),
            energyDelivered: faker.number.int(),
            numberOfChargers: faker.number.int(),
            numberOfCharges: faker.number.int(),
            numberOfDrivers: faker.number.int(),
            numberOfSites: faker.number.int(),
            revenueGenerated: faker.number.int(),
          },
          type: faker.lorem.words(),
          uid: faker.string.uuid(),
        },
        id: faker.number.int(),
        parking: {
          openingTimes: {
            notes: [
              ...new Array(
                faker.number.int({ min: 1, max: MAX_ARRAY_LENGTH })
              ).keys(),
            ].map((_) => faker.lorem.words()),
          },
        },
        pods: [
          ...new Array(
            faker.number.int({ min: 1, max: MAX_ARRAY_LENGTH })
          ).keys(),
        ].map((_) => null),
      },
      sockets: [
        ...new Array(
          faker.number.int({ min: 1, max: MAX_ARRAY_LENGTH })
        ).keys(),
      ].map((_) => ({
        door: faker.lorem.words(),
        firmwareVersion: faker.lorem.words(),
        isUpdateAvailable: faker.datatype.boolean(),
        lastContact: faker.lorem.words(),
        serialNumber: faker.lorem.words(),
        status: faker.helpers.arrayElement([
          'Available',
          'Charging',
          'Offline',
          'Unavailable',
        ]),
      })),
      status: faker.lorem.words(),
      supportsConfirmCharge: true,
      supportsContactless: faker.datatype.boolean(),
      supportsEnergyTariff: true,
      supportsOcpp: false,
      supportsPerKwh: true,
      supportsRfid: faker.datatype.boolean(),
      supportsTariffs: true,
      tariff: {
        id: faker.number.int(),
        name: faker.person.fullName(),
      },
    })),
    schedule: {
      drivers: [
        ...new Array(
          faker.number.int({ min: 1, max: MAX_ARRAY_LENGTH })
        ).keys(),
      ].map((_) => ({
        id: faker.number.int(),
        bands: [
          ...new Array(
            faker.number.int({ min: 1, max: MAX_ARRAY_LENGTH })
          ).keys(),
        ].map((_) => ({
          id: faker.number.int(),
          after: faker.number.int(),
          cost: faker.number.int(),
          prettyPrint: faker.lorem.words(),
        })),
        endDay: faker.number.int({ min: 0, max: 6 }),
        endTime: faker.lorem.words(),
        pricingModel: faker.helpers.arrayElement([
          'duration',
          'energy',
          'fixed',
        ]),
        startDay: faker.number.int({ min: 0, max: 6 }),
        startTime: faker.lorem.words(),
        tariffTier: faker.helpers.arrayElement([
          'public',
          'members',
          'drivers',
        ]),
      })),
      members: [
        ...new Array(
          faker.number.int({ min: 1, max: MAX_ARRAY_LENGTH })
        ).keys(),
      ].map((_) => ({
        id: faker.number.int(),
        bands: [
          ...new Array(
            faker.number.int({ min: 1, max: MAX_ARRAY_LENGTH })
          ).keys(),
        ].map((_) => ({
          id: faker.number.int(),
          after: faker.number.int(),
          cost: faker.number.int(),
          prettyPrint: faker.lorem.words(),
        })),
        endDay: faker.number.int({ min: 0, max: 6 }),
        endTime: faker.lorem.words(),
        pricingModel: faker.helpers.arrayElement([
          'duration',
          'energy',
          'fixed',
        ]),
        startDay: faker.number.int({ min: 0, max: 6 }),
        startTime: faker.lorem.words(),
        tariffTier: faker.helpers.arrayElement([
          'public',
          'members',
          'drivers',
        ]),
      })),
      public: [
        ...new Array(
          faker.number.int({ min: 1, max: MAX_ARRAY_LENGTH })
        ).keys(),
      ].map((_) => ({
        id: faker.number.int(),
        bands: [
          ...new Array(
            faker.number.int({ min: 1, max: MAX_ARRAY_LENGTH })
          ).keys(),
        ].map((_) => ({
          id: faker.number.int(),
          after: faker.number.int(),
          cost: faker.number.int(),
          prettyPrint: faker.lorem.words(),
        })),
        endDay: faker.number.int({ min: 0, max: 6 }),
        endTime: faker.lorem.words(),
        pricingModel: faker.helpers.arrayElement([
          'duration',
          'energy',
          'fixed',
        ]),
        startDay: faker.number.int({ min: 0, max: 6 }),
        startTime: faker.lorem.words(),
        tariffTier: faker.helpers.arrayElement([
          'public',
          'members',
          'drivers',
        ]),
      })),
    },
  }));
}

export function getTariffControllerCreateByGroupId201Response() {
  return {
    id: faker.number.int(),
    name: faker.person.fullName(),
    currency: faker.lorem.words(),
    pods: [
      ...new Array(faker.number.int({ min: 1, max: MAX_ARRAY_LENGTH })).keys(),
    ].map((_) => ({
      ageYears: faker.number.int(),
      confirmChargeEnabled: faker.datatype.boolean(),
      chargeSummary: {
        chargingDuration: faker.number.int(),
        claimedEnergyUsage: faker.number.int(),
        co2Savings: faker.number.int(),
        energyCost: faker.number.int(),
        energyDelivered: faker.number.int(),
        energyUsage: faker.number.int(),
        numberOfCharges: faker.number.int(),
        revenueGenerated: faker.number.int(),
        revenueGeneratingClaimedUsage: faker.number.int(),
      },
      coordinates: {
        latitude: faker.number.int(),
        longitude: faker.number.int(),
      },
      description: faker.lorem.words(),
      id: faker.number.int(),
      installDate: faker.lorem.words(),
      isEvZone: faker.datatype.boolean(),
      isPublic: faker.datatype.boolean(),
      lastContact: faker.lorem.words(),
      model: faker.lorem.words(),
      mostRecentCharge: {
        chargingDuration: faker.lorem.words(),
        confirmed: faker.datatype.boolean(),
        confirmedBy: faker.helpers.arrayElement(['driver', 'other']),
        co2Savings: faker.lorem.words(),
        door: faker.lorem.words(),
        endedAt: faker.lorem.words(),
        energyCost: faker.lorem.words(),
        energyUsage: faker.lorem.words(),
        locationId: faker.number.int(),
        pluggedIn: faker.lorem.words(),
        podName: faker.person.fullName(),
        ppid: faker.string.uuid(),
        revenueGenerated: faker.lorem.words(),
        startedAt: faker.lorem.words(),
        siteName: faker.person.fullName(),
        totalDuration: faker.lorem.words(),
        userEmail: faker.internet.email(),
        userName: faker.person.fullName(),
        vehicle: faker.lorem.words(),
      },
      name: faker.person.fullName(),
      ppid: faker.string.uuid(),
      recentCharges: [
        ...new Array(
          faker.number.int({ min: 1, max: MAX_ARRAY_LENGTH })
        ).keys(),
      ].map((_) => ({
        chargingDuration: faker.lorem.words(),
        confirmed: faker.datatype.boolean(),
        confirmedBy: faker.helpers.arrayElement(['driver', 'other']),
        co2Savings: faker.lorem.words(),
        door: faker.lorem.words(),
        endedAt: faker.lorem.words(),
        energyCost: faker.lorem.words(),
        energyUsage: faker.lorem.words(),
        locationId: faker.number.int(),
        pluggedIn: faker.lorem.words(),
        podName: faker.person.fullName(),
        ppid: faker.string.uuid(),
        revenueGenerated: faker.lorem.words(),
        startedAt: faker.lorem.words(),
        siteName: faker.person.fullName(),
        totalDuration: faker.lorem.words(),
        userEmail: faker.internet.email(),
        userName: faker.person.fullName(),
        vehicle: faker.lorem.words(),
      })),
      schedules: [
        ...new Array(
          faker.number.int({ min: 1, max: MAX_ARRAY_LENGTH })
        ).keys(),
      ].map((_) => ({
        endDay: faker.number.int({ min: 0, max: 6 }),
        endTime: faker.lorem.words(),
        isActive: faker.datatype.boolean(),
        startDay: faker.number.int({ min: 0, max: 6 }),
        startTime: faker.lorem.words(),
      })),
      schemes: [
        ...new Array(
          faker.number.int({ min: 1, max: MAX_ARRAY_LENGTH })
        ).keys(),
      ].map((_) => ({
        activatedOn: faker.date.past(),
        contactName: faker.person.fullName(),
        contactNumber: faker.lorem.words(),
        id: faker.number.int(),
        name: faker.person.fullName(),
        readOnly: false,
        stats: {
          chargingDuration: faker.number.int(),
          co2Savings: faker.number.int(),
          energyCost: faker.number.int(),
          energyDelivered: faker.number.int(),
          numberOfChargers: faker.number.int(),
          numberOfCharges: faker.number.int(),
          numberOfDrivers: faker.number.int(),
          numberOfSites: faker.number.int(),
          revenueGenerated: faker.number.int(),
        },
        type: faker.lorem.words(),
        uid: faker.string.uuid(),
      })),
      site: {
        address: {
          country: faker.lorem.words(),
          line1: faker.lorem.words(),
          line2: faker.lorem.words(),
          name: faker.person.fullName(),
          postcode: faker.lorem.words(),
          prettyPrint: faker.lorem.words(),
          town: faker.lorem.words(),
        },
        chargeSummary: {
          chargingDuration: faker.number.int(),
          claimedEnergyUsage: faker.number.int(),
          co2Savings: faker.number.int(),
          energyCost: faker.number.int(),
          energyDelivered: faker.number.int(),
          energyUsage: faker.number.int(),
          numberOfCharges: faker.number.int(),
          revenueGenerated: faker.number.int(),
          revenueGeneratingClaimedUsage: faker.number.int(),
        },
        contactDetails: {
          email: faker.internet.email(),
          name: faker.person.fullName(),
          telephone: faker.lorem.words(),
        },
        description: faker.lorem.words(),
        energyCost: faker.number.int(),
        group: {
          activatedOn: faker.date.past(),
          contactName: faker.person.fullName(),
          contactNumber: faker.lorem.words(),
          id: faker.number.int(),
          name: faker.person.fullName(),
          readOnly: false,
          stats: {
            chargingDuration: faker.number.int(),
            co2Savings: faker.number.int(),
            energyCost: faker.number.int(),
            energyDelivered: faker.number.int(),
            numberOfChargers: faker.number.int(),
            numberOfCharges: faker.number.int(),
            numberOfDrivers: faker.number.int(),
            numberOfSites: faker.number.int(),
            revenueGenerated: faker.number.int(),
          },
          type: faker.lorem.words(),
          uid: faker.string.uuid(),
        },
        id: faker.number.int(),
        parking: {
          openingTimes: {
            notes: [
              ...new Array(
                faker.number.int({ min: 1, max: MAX_ARRAY_LENGTH })
              ).keys(),
            ].map((_) => faker.lorem.words()),
          },
        },
        pods: [
          ...new Array(
            faker.number.int({ min: 1, max: MAX_ARRAY_LENGTH })
          ).keys(),
        ].map((_) => null),
      },
      sockets: [
        ...new Array(
          faker.number.int({ min: 1, max: MAX_ARRAY_LENGTH })
        ).keys(),
      ].map((_) => ({
        door: faker.lorem.words(),
        firmwareVersion: faker.lorem.words(),
        isUpdateAvailable: faker.datatype.boolean(),
        lastContact: faker.lorem.words(),
        serialNumber: faker.lorem.words(),
        status: faker.helpers.arrayElement([
          'Available',
          'Charging',
          'Offline',
          'Unavailable',
        ]),
      })),
      status: faker.lorem.words(),
      supportsConfirmCharge: true,
      supportsContactless: faker.datatype.boolean(),
      supportsEnergyTariff: true,
      supportsOcpp: false,
      supportsPerKwh: true,
      supportsRfid: faker.datatype.boolean(),
      supportsTariffs: true,
      tariff: {
        id: faker.number.int(),
        name: faker.person.fullName(),
      },
    })),
    schedule: {
      drivers: [
        ...new Array(
          faker.number.int({ min: 1, max: MAX_ARRAY_LENGTH })
        ).keys(),
      ].map((_) => ({
        id: faker.number.int(),
        bands: [
          ...new Array(
            faker.number.int({ min: 1, max: MAX_ARRAY_LENGTH })
          ).keys(),
        ].map((_) => ({
          id: faker.number.int(),
          after: faker.number.int(),
          cost: faker.number.int(),
          prettyPrint: faker.lorem.words(),
        })),
        endDay: faker.number.int({ min: 0, max: 6 }),
        endTime: faker.lorem.words(),
        pricingModel: faker.helpers.arrayElement([
          'duration',
          'energy',
          'fixed',
        ]),
        startDay: faker.number.int({ min: 0, max: 6 }),
        startTime: faker.lorem.words(),
        tariffTier: faker.helpers.arrayElement([
          'public',
          'members',
          'drivers',
        ]),
      })),
      members: [
        ...new Array(
          faker.number.int({ min: 1, max: MAX_ARRAY_LENGTH })
        ).keys(),
      ].map((_) => ({
        id: faker.number.int(),
        bands: [
          ...new Array(
            faker.number.int({ min: 1, max: MAX_ARRAY_LENGTH })
          ).keys(),
        ].map((_) => ({
          id: faker.number.int(),
          after: faker.number.int(),
          cost: faker.number.int(),
          prettyPrint: faker.lorem.words(),
        })),
        endDay: faker.number.int({ min: 0, max: 6 }),
        endTime: faker.lorem.words(),
        pricingModel: faker.helpers.arrayElement([
          'duration',
          'energy',
          'fixed',
        ]),
        startDay: faker.number.int({ min: 0, max: 6 }),
        startTime: faker.lorem.words(),
        tariffTier: faker.helpers.arrayElement([
          'public',
          'members',
          'drivers',
        ]),
      })),
      public: [
        ...new Array(
          faker.number.int({ min: 1, max: MAX_ARRAY_LENGTH })
        ).keys(),
      ].map((_) => ({
        id: faker.number.int(),
        bands: [
          ...new Array(
            faker.number.int({ min: 1, max: MAX_ARRAY_LENGTH })
          ).keys(),
        ].map((_) => ({
          id: faker.number.int(),
          after: faker.number.int(),
          cost: faker.number.int(),
          prettyPrint: faker.lorem.words(),
        })),
        endDay: faker.number.int({ min: 0, max: 6 }),
        endTime: faker.lorem.words(),
        pricingModel: faker.helpers.arrayElement([
          'duration',
          'energy',
          'fixed',
        ]),
        startDay: faker.number.int({ min: 0, max: 6 }),
        startTime: faker.lorem.words(),
        tariffTier: faker.helpers.arrayElement([
          'public',
          'members',
          'drivers',
        ]),
      })),
    },
  };
}

export function getTariffControllerFindByGroupIdAndTariffId200Response() {
  return {
    id: faker.number.int(),
    name: faker.person.fullName(),
    currency: faker.lorem.words(),
    pods: [
      ...new Array(faker.number.int({ min: 1, max: MAX_ARRAY_LENGTH })).keys(),
    ].map((_) => ({
      ageYears: faker.number.int(),
      confirmChargeEnabled: faker.datatype.boolean(),
      chargeSummary: {
        chargingDuration: faker.number.int(),
        claimedEnergyUsage: faker.number.int(),
        co2Savings: faker.number.int(),
        energyCost: faker.number.int(),
        energyDelivered: faker.number.int(),
        energyUsage: faker.number.int(),
        numberOfCharges: faker.number.int(),
        revenueGenerated: faker.number.int(),
        revenueGeneratingClaimedUsage: faker.number.int(),
      },
      coordinates: {
        latitude: faker.number.int(),
        longitude: faker.number.int(),
      },
      description: faker.lorem.words(),
      id: faker.number.int(),
      installDate: faker.lorem.words(),
      isEvZone: faker.datatype.boolean(),
      isPublic: faker.datatype.boolean(),
      lastContact: faker.lorem.words(),
      model: faker.lorem.words(),
      mostRecentCharge: {
        chargingDuration: faker.lorem.words(),
        confirmed: faker.datatype.boolean(),
        confirmedBy: faker.helpers.arrayElement(['driver', 'other']),
        co2Savings: faker.lorem.words(),
        door: faker.lorem.words(),
        endedAt: faker.lorem.words(),
        energyCost: faker.lorem.words(),
        energyUsage: faker.lorem.words(),
        locationId: faker.number.int(),
        pluggedIn: faker.lorem.words(),
        podName: faker.person.fullName(),
        ppid: faker.string.uuid(),
        revenueGenerated: faker.lorem.words(),
        startedAt: faker.lorem.words(),
        siteName: faker.person.fullName(),
        totalDuration: faker.lorem.words(),
        userEmail: faker.internet.email(),
        userName: faker.person.fullName(),
        vehicle: faker.lorem.words(),
      },
      name: faker.person.fullName(),
      ppid: faker.string.uuid(),
      recentCharges: [
        ...new Array(
          faker.number.int({ min: 1, max: MAX_ARRAY_LENGTH })
        ).keys(),
      ].map((_) => ({
        chargingDuration: faker.lorem.words(),
        confirmed: faker.datatype.boolean(),
        confirmedBy: faker.helpers.arrayElement(['driver', 'other']),
        co2Savings: faker.lorem.words(),
        door: faker.lorem.words(),
        endedAt: faker.lorem.words(),
        energyCost: faker.lorem.words(),
        energyUsage: faker.lorem.words(),
        locationId: faker.number.int(),
        pluggedIn: faker.lorem.words(),
        podName: faker.person.fullName(),
        ppid: faker.string.uuid(),
        revenueGenerated: faker.lorem.words(),
        startedAt: faker.lorem.words(),
        siteName: faker.person.fullName(),
        totalDuration: faker.lorem.words(),
        userEmail: faker.internet.email(),
        userName: faker.person.fullName(),
        vehicle: faker.lorem.words(),
      })),
      schedules: [
        ...new Array(
          faker.number.int({ min: 1, max: MAX_ARRAY_LENGTH })
        ).keys(),
      ].map((_) => ({
        endDay: faker.number.int({ min: 0, max: 6 }),
        endTime: faker.lorem.words(),
        isActive: faker.datatype.boolean(),
        startDay: faker.number.int({ min: 0, max: 6 }),
        startTime: faker.lorem.words(),
      })),
      schemes: [
        ...new Array(
          faker.number.int({ min: 1, max: MAX_ARRAY_LENGTH })
        ).keys(),
      ].map((_) => ({
        activatedOn: faker.date.past(),
        contactName: faker.person.fullName(),
        contactNumber: faker.lorem.words(),
        id: faker.number.int(),
        name: faker.person.fullName(),
        readOnly: false,
        stats: {
          chargingDuration: faker.number.int(),
          co2Savings: faker.number.int(),
          energyCost: faker.number.int(),
          energyDelivered: faker.number.int(),
          numberOfChargers: faker.number.int(),
          numberOfCharges: faker.number.int(),
          numberOfDrivers: faker.number.int(),
          numberOfSites: faker.number.int(),
          revenueGenerated: faker.number.int(),
        },
        type: faker.lorem.words(),
        uid: faker.string.uuid(),
      })),
      site: {
        address: {
          country: faker.lorem.words(),
          line1: faker.lorem.words(),
          line2: faker.lorem.words(),
          name: faker.person.fullName(),
          postcode: faker.lorem.words(),
          prettyPrint: faker.lorem.words(),
          town: faker.lorem.words(),
        },
        chargeSummary: {
          chargingDuration: faker.number.int(),
          claimedEnergyUsage: faker.number.int(),
          co2Savings: faker.number.int(),
          energyCost: faker.number.int(),
          energyDelivered: faker.number.int(),
          energyUsage: faker.number.int(),
          numberOfCharges: faker.number.int(),
          revenueGenerated: faker.number.int(),
          revenueGeneratingClaimedUsage: faker.number.int(),
        },
        contactDetails: {
          email: faker.internet.email(),
          name: faker.person.fullName(),
          telephone: faker.lorem.words(),
        },
        description: faker.lorem.words(),
        energyCost: faker.number.int(),
        group: {
          activatedOn: faker.date.past(),
          contactName: faker.person.fullName(),
          contactNumber: faker.lorem.words(),
          id: faker.number.int(),
          name: faker.person.fullName(),
          readOnly: false,
          stats: {
            chargingDuration: faker.number.int(),
            co2Savings: faker.number.int(),
            energyCost: faker.number.int(),
            energyDelivered: faker.number.int(),
            numberOfChargers: faker.number.int(),
            numberOfCharges: faker.number.int(),
            numberOfDrivers: faker.number.int(),
            numberOfSites: faker.number.int(),
            revenueGenerated: faker.number.int(),
          },
          type: faker.lorem.words(),
          uid: faker.string.uuid(),
        },
        id: faker.number.int(),
        parking: {
          openingTimes: {
            notes: [
              ...new Array(
                faker.number.int({ min: 1, max: MAX_ARRAY_LENGTH })
              ).keys(),
            ].map((_) => faker.lorem.words()),
          },
        },
        pods: [
          ...new Array(
            faker.number.int({ min: 1, max: MAX_ARRAY_LENGTH })
          ).keys(),
        ].map((_) => null),
      },
      sockets: [
        ...new Array(
          faker.number.int({ min: 1, max: MAX_ARRAY_LENGTH })
        ).keys(),
      ].map((_) => ({
        door: faker.lorem.words(),
        firmwareVersion: faker.lorem.words(),
        isUpdateAvailable: faker.datatype.boolean(),
        lastContact: faker.lorem.words(),
        serialNumber: faker.lorem.words(),
        status: faker.helpers.arrayElement([
          'Available',
          'Charging',
          'Offline',
          'Unavailable',
        ]),
      })),
      status: faker.lorem.words(),
      supportsConfirmCharge: true,
      supportsContactless: faker.datatype.boolean(),
      supportsEnergyTariff: true,
      supportsOcpp: false,
      supportsPerKwh: true,
      supportsRfid: faker.datatype.boolean(),
      supportsTariffs: true,
      tariff: {
        id: faker.number.int(),
        name: faker.person.fullName(),
      },
    })),
    schedule: {
      drivers: [
        ...new Array(
          faker.number.int({ min: 1, max: MAX_ARRAY_LENGTH })
        ).keys(),
      ].map((_) => ({
        id: faker.number.int(),
        bands: [
          ...new Array(
            faker.number.int({ min: 1, max: MAX_ARRAY_LENGTH })
          ).keys(),
        ].map((_) => ({
          id: faker.number.int(),
          after: faker.number.int(),
          cost: faker.number.int(),
          prettyPrint: faker.lorem.words(),
        })),
        endDay: faker.number.int({ min: 0, max: 6 }),
        endTime: faker.lorem.words(),
        pricingModel: faker.helpers.arrayElement([
          'duration',
          'energy',
          'fixed',
        ]),
        startDay: faker.number.int({ min: 0, max: 6 }),
        startTime: faker.lorem.words(),
        tariffTier: faker.helpers.arrayElement([
          'public',
          'members',
          'drivers',
        ]),
      })),
      members: [
        ...new Array(
          faker.number.int({ min: 1, max: MAX_ARRAY_LENGTH })
        ).keys(),
      ].map((_) => ({
        id: faker.number.int(),
        bands: [
          ...new Array(
            faker.number.int({ min: 1, max: MAX_ARRAY_LENGTH })
          ).keys(),
        ].map((_) => ({
          id: faker.number.int(),
          after: faker.number.int(),
          cost: faker.number.int(),
          prettyPrint: faker.lorem.words(),
        })),
        endDay: faker.number.int({ min: 0, max: 6 }),
        endTime: faker.lorem.words(),
        pricingModel: faker.helpers.arrayElement([
          'duration',
          'energy',
          'fixed',
        ]),
        startDay: faker.number.int({ min: 0, max: 6 }),
        startTime: faker.lorem.words(),
        tariffTier: faker.helpers.arrayElement([
          'public',
          'members',
          'drivers',
        ]),
      })),
      public: [
        ...new Array(
          faker.number.int({ min: 1, max: MAX_ARRAY_LENGTH })
        ).keys(),
      ].map((_) => ({
        id: faker.number.int(),
        bands: [
          ...new Array(
            faker.number.int({ min: 1, max: MAX_ARRAY_LENGTH })
          ).keys(),
        ].map((_) => ({
          id: faker.number.int(),
          after: faker.number.int(),
          cost: faker.number.int(),
          prettyPrint: faker.lorem.words(),
        })),
        endDay: faker.number.int({ min: 0, max: 6 }),
        endTime: faker.lorem.words(),
        pricingModel: faker.helpers.arrayElement([
          'duration',
          'energy',
          'fixed',
        ]),
        startDay: faker.number.int({ min: 0, max: 6 }),
        startTime: faker.lorem.words(),
        tariffTier: faker.helpers.arrayElement([
          'public',
          'members',
          'drivers',
        ]),
      })),
    },
  };
}

export function getSiteControllerFindByGroupUid200Response() {
  return [
    ...new Array(faker.number.int({ min: 1, max: MAX_ARRAY_LENGTH })).keys(),
  ].map((_) => ({
    address: {
      country: faker.lorem.words(),
      line1: faker.lorem.words(),
      line2: faker.lorem.words(),
      name: faker.person.fullName(),
      postcode: faker.lorem.words(),
      prettyPrint: faker.lorem.words(),
      town: faker.lorem.words(),
    },
    chargeSummary: {
      chargingDuration: faker.number.int(),
      claimedEnergyUsage: faker.number.int(),
      co2Savings: faker.number.int(),
      energyCost: faker.number.int(),
      energyDelivered: faker.number.int(),
      energyUsage: faker.number.int(),
      numberOfCharges: faker.number.int(),
      revenueGenerated: faker.number.int(),
      revenueGeneratingClaimedUsage: faker.number.int(),
    },
    contactDetails: {
      email: faker.internet.email(),
      name: faker.person.fullName(),
      telephone: faker.lorem.words(),
    },
    description: faker.lorem.words(),
    energyCost: faker.number.int(),
    group: {
      activatedOn: faker.date.past(),
      contactName: faker.person.fullName(),
      contactNumber: faker.lorem.words(),
      id: faker.number.int(),
      name: faker.person.fullName(),
      readOnly: false,
      stats: {
        chargingDuration: faker.number.int(),
        co2Savings: faker.number.int(),
        energyCost: faker.number.int(),
        energyDelivered: faker.number.int(),
        numberOfChargers: faker.number.int(),
        numberOfCharges: faker.number.int(),
        numberOfDrivers: faker.number.int(),
        numberOfSites: faker.number.int(),
        revenueGenerated: faker.number.int(),
      },
      type: faker.lorem.words(),
      uid: faker.string.uuid(),
    },
    id: faker.number.int(),
    parking: {
      openingTimes: {
        notes: [
          ...new Array(
            faker.number.int({ min: 1, max: MAX_ARRAY_LENGTH })
          ).keys(),
        ].map((_) => faker.lorem.words()),
      },
    },
    pods: [
      ...new Array(faker.number.int({ min: 1, max: MAX_ARRAY_LENGTH })).keys(),
    ].map((_) => ({
      ageYears: faker.number.int(),
      confirmChargeEnabled: faker.datatype.boolean(),
      chargeSummary: {
        chargingDuration: faker.number.int(),
        claimedEnergyUsage: faker.number.int(),
        co2Savings: faker.number.int(),
        energyCost: faker.number.int(),
        energyDelivered: faker.number.int(),
        energyUsage: faker.number.int(),
        numberOfCharges: faker.number.int(),
        revenueGenerated: faker.number.int(),
        revenueGeneratingClaimedUsage: faker.number.int(),
      },
      coordinates: {
        latitude: faker.number.int(),
        longitude: faker.number.int(),
      },
      description: faker.lorem.words(),
      id: faker.number.int(),
      installDate: faker.lorem.words(),
      isEvZone: faker.datatype.boolean(),
      isPublic: faker.datatype.boolean(),
      lastContact: faker.lorem.words(),
      model: faker.lorem.words(),
      mostRecentCharge: {
        chargingDuration: faker.lorem.words(),
        confirmed: faker.datatype.boolean(),
        confirmedBy: faker.helpers.arrayElement(['driver', 'other']),
        co2Savings: faker.lorem.words(),
        door: faker.lorem.words(),
        endedAt: faker.lorem.words(),
        energyCost: faker.lorem.words(),
        energyUsage: faker.lorem.words(),
        locationId: faker.number.int(),
        pluggedIn: faker.lorem.words(),
        podName: faker.person.fullName(),
        ppid: faker.string.uuid(),
        revenueGenerated: faker.lorem.words(),
        startedAt: faker.lorem.words(),
        siteName: faker.person.fullName(),
        totalDuration: faker.lorem.words(),
        userEmail: faker.internet.email(),
        userName: faker.person.fullName(),
        vehicle: faker.lorem.words(),
      },
      name: faker.person.fullName(),
      ppid: faker.string.uuid(),
      recentCharges: [
        ...new Array(
          faker.number.int({ min: 1, max: MAX_ARRAY_LENGTH })
        ).keys(),
      ].map((_) => ({
        chargingDuration: faker.lorem.words(),
        confirmed: faker.datatype.boolean(),
        confirmedBy: faker.helpers.arrayElement(['driver', 'other']),
        co2Savings: faker.lorem.words(),
        door: faker.lorem.words(),
        endedAt: faker.lorem.words(),
        energyCost: faker.lorem.words(),
        energyUsage: faker.lorem.words(),
        locationId: faker.number.int(),
        pluggedIn: faker.lorem.words(),
        podName: faker.person.fullName(),
        ppid: faker.string.uuid(),
        revenueGenerated: faker.lorem.words(),
        startedAt: faker.lorem.words(),
        siteName: faker.person.fullName(),
        totalDuration: faker.lorem.words(),
        userEmail: faker.internet.email(),
        userName: faker.person.fullName(),
        vehicle: faker.lorem.words(),
      })),
      schedules: [
        ...new Array(
          faker.number.int({ min: 1, max: MAX_ARRAY_LENGTH })
        ).keys(),
      ].map((_) => ({
        endDay: faker.number.int({ min: 0, max: 6 }),
        endTime: faker.lorem.words(),
        isActive: faker.datatype.boolean(),
        startDay: faker.number.int({ min: 0, max: 6 }),
        startTime: faker.lorem.words(),
      })),
      schemes: [
        ...new Array(
          faker.number.int({ min: 1, max: MAX_ARRAY_LENGTH })
        ).keys(),
      ].map((_) => ({
        activatedOn: faker.date.past(),
        contactName: faker.person.fullName(),
        contactNumber: faker.lorem.words(),
        id: faker.number.int(),
        name: faker.person.fullName(),
        readOnly: false,
        stats: {
          chargingDuration: faker.number.int(),
          co2Savings: faker.number.int(),
          energyCost: faker.number.int(),
          energyDelivered: faker.number.int(),
          numberOfChargers: faker.number.int(),
          numberOfCharges: faker.number.int(),
          numberOfDrivers: faker.number.int(),
          numberOfSites: faker.number.int(),
          revenueGenerated: faker.number.int(),
        },
        type: faker.lorem.words(),
        uid: faker.string.uuid(),
      })),
      site: null,
      sockets: [
        ...new Array(
          faker.number.int({ min: 1, max: MAX_ARRAY_LENGTH })
        ).keys(),
      ].map((_) => ({
        door: faker.lorem.words(),
        firmwareVersion: faker.lorem.words(),
        isUpdateAvailable: faker.datatype.boolean(),
        lastContact: faker.lorem.words(),
        serialNumber: faker.lorem.words(),
        status: faker.helpers.arrayElement([
          'Available',
          'Charging',
          'Offline',
          'Unavailable',
        ]),
      })),
      status: faker.lorem.words(),
      supportsConfirmCharge: true,
      supportsContactless: faker.datatype.boolean(),
      supportsEnergyTariff: true,
      supportsOcpp: false,
      supportsPerKwh: true,
      supportsRfid: faker.datatype.boolean(),
      supportsTariffs: true,
      tariff: {
        id: faker.number.int(),
        name: faker.person.fullName(),
      },
    })),
  }));
}

export function getSiteControllerFindByGroupUidAndSiteId200Response() {
  return {
    address: {
      country: faker.lorem.words(),
      line1: faker.lorem.words(),
      line2: faker.lorem.words(),
      name: faker.person.fullName(),
      postcode: faker.lorem.words(),
      prettyPrint: faker.lorem.words(),
      town: faker.lorem.words(),
    },
    chargeSummary: {
      chargingDuration: faker.number.int(),
      claimedEnergyUsage: faker.number.int(),
      co2Savings: faker.number.int(),
      energyCost: faker.number.int(),
      energyDelivered: faker.number.int(),
      energyUsage: faker.number.int(),
      numberOfCharges: faker.number.int(),
      revenueGenerated: faker.number.int(),
      revenueGeneratingClaimedUsage: faker.number.int(),
    },
    contactDetails: {
      email: faker.internet.email(),
      name: faker.person.fullName(),
      telephone: faker.lorem.words(),
    },
    description: faker.lorem.words(),
    energyCost: faker.number.int(),
    group: {
      activatedOn: faker.date.past(),
      contactName: faker.person.fullName(),
      contactNumber: faker.lorem.words(),
      id: faker.number.int(),
      name: faker.person.fullName(),
      readOnly: false,
      stats: {
        chargingDuration: faker.number.int(),
        co2Savings: faker.number.int(),
        energyCost: faker.number.int(),
        energyDelivered: faker.number.int(),
        numberOfChargers: faker.number.int(),
        numberOfCharges: faker.number.int(),
        numberOfDrivers: faker.number.int(),
        numberOfSites: faker.number.int(),
        revenueGenerated: faker.number.int(),
      },
      type: faker.lorem.words(),
      uid: faker.string.uuid(),
    },
    id: faker.number.int(),
    parking: {
      openingTimes: {
        notes: [
          ...new Array(
            faker.number.int({ min: 1, max: MAX_ARRAY_LENGTH })
          ).keys(),
        ].map((_) => faker.lorem.words()),
      },
    },
    pods: [
      ...new Array(faker.number.int({ min: 1, max: MAX_ARRAY_LENGTH })).keys(),
    ].map((_) => ({
      ageYears: faker.number.int(),
      confirmChargeEnabled: faker.datatype.boolean(),
      chargeSummary: {
        chargingDuration: faker.number.int(),
        claimedEnergyUsage: faker.number.int(),
        co2Savings: faker.number.int(),
        energyCost: faker.number.int(),
        energyDelivered: faker.number.int(),
        energyUsage: faker.number.int(),
        numberOfCharges: faker.number.int(),
        revenueGenerated: faker.number.int(),
        revenueGeneratingClaimedUsage: faker.number.int(),
      },
      coordinates: {
        latitude: faker.number.int(),
        longitude: faker.number.int(),
      },
      description: faker.lorem.words(),
      id: faker.number.int(),
      installDate: faker.lorem.words(),
      isEvZone: faker.datatype.boolean(),
      isPublic: faker.datatype.boolean(),
      lastContact: faker.lorem.words(),
      model: faker.lorem.words(),
      mostRecentCharge: {
        chargingDuration: faker.lorem.words(),
        confirmed: faker.datatype.boolean(),
        confirmedBy: faker.helpers.arrayElement(['driver', 'other']),
        co2Savings: faker.lorem.words(),
        door: faker.lorem.words(),
        endedAt: faker.lorem.words(),
        energyCost: faker.lorem.words(),
        energyUsage: faker.lorem.words(),
        locationId: faker.number.int(),
        pluggedIn: faker.lorem.words(),
        podName: faker.person.fullName(),
        ppid: faker.string.uuid(),
        revenueGenerated: faker.lorem.words(),
        startedAt: faker.lorem.words(),
        siteName: faker.person.fullName(),
        totalDuration: faker.lorem.words(),
        userEmail: faker.internet.email(),
        userName: faker.person.fullName(),
        vehicle: faker.lorem.words(),
      },
      name: faker.person.fullName(),
      ppid: faker.string.uuid(),
      recentCharges: [
        ...new Array(
          faker.number.int({ min: 1, max: MAX_ARRAY_LENGTH })
        ).keys(),
      ].map((_) => ({
        chargingDuration: faker.lorem.words(),
        confirmed: faker.datatype.boolean(),
        confirmedBy: faker.helpers.arrayElement(['driver', 'other']),
        co2Savings: faker.lorem.words(),
        door: faker.lorem.words(),
        endedAt: faker.lorem.words(),
        energyCost: faker.lorem.words(),
        energyUsage: faker.lorem.words(),
        locationId: faker.number.int(),
        pluggedIn: faker.lorem.words(),
        podName: faker.person.fullName(),
        ppid: faker.string.uuid(),
        revenueGenerated: faker.lorem.words(),
        startedAt: faker.lorem.words(),
        siteName: faker.person.fullName(),
        totalDuration: faker.lorem.words(),
        userEmail: faker.internet.email(),
        userName: faker.person.fullName(),
        vehicle: faker.lorem.words(),
      })),
      schedules: [
        ...new Array(
          faker.number.int({ min: 1, max: MAX_ARRAY_LENGTH })
        ).keys(),
      ].map((_) => ({
        endDay: faker.number.int({ min: 0, max: 6 }),
        endTime: faker.lorem.words(),
        isActive: faker.datatype.boolean(),
        startDay: faker.number.int({ min: 0, max: 6 }),
        startTime: faker.lorem.words(),
      })),
      schemes: [
        ...new Array(
          faker.number.int({ min: 1, max: MAX_ARRAY_LENGTH })
        ).keys(),
      ].map((_) => ({
        activatedOn: faker.date.past(),
        contactName: faker.person.fullName(),
        contactNumber: faker.lorem.words(),
        id: faker.number.int(),
        name: faker.person.fullName(),
        readOnly: false,
        stats: {
          chargingDuration: faker.number.int(),
          co2Savings: faker.number.int(),
          energyCost: faker.number.int(),
          energyDelivered: faker.number.int(),
          numberOfChargers: faker.number.int(),
          numberOfCharges: faker.number.int(),
          numberOfDrivers: faker.number.int(),
          numberOfSites: faker.number.int(),
          revenueGenerated: faker.number.int(),
        },
        type: faker.lorem.words(),
        uid: faker.string.uuid(),
      })),
      site: null,
      sockets: [
        ...new Array(
          faker.number.int({ min: 1, max: MAX_ARRAY_LENGTH })
        ).keys(),
      ].map((_) => ({
        door: faker.lorem.words(),
        firmwareVersion: faker.lorem.words(),
        isUpdateAvailable: faker.datatype.boolean(),
        lastContact: faker.lorem.words(),
        serialNumber: faker.lorem.words(),
        status: faker.helpers.arrayElement([
          'Available',
          'Charging',
          'Offline',
          'Unavailable',
        ]),
      })),
      status: faker.lorem.words(),
      supportsConfirmCharge: true,
      supportsContactless: faker.datatype.boolean(),
      supportsEnergyTariff: true,
      supportsOcpp: false,
      supportsPerKwh: true,
      supportsRfid: faker.datatype.boolean(),
      supportsTariffs: true,
      tariff: {
        id: faker.number.int(),
        name: faker.person.fullName(),
      },
    })),
  };
}

export function getStatementControllerFindByGroupUidAndSiteId200Response() {
  return {
    breakdown: [
      ...new Array(faker.number.int({ min: 1, max: MAX_ARRAY_LENGTH })).keys(),
    ].map((_) => ({
      date: faker.lorem.words(),
      groupName: faker.person.fullName(),
      reference: faker.lorem.words(),
      siteAddress: faker.lorem.words(),
      siteName: faker.person.fullName(),
      claimedEnergyDelivered: faker.number.int(),
      energyDelivered: faker.number.int(),
      fees: {
        gross: faker.number.int(),
        net: faker.number.int(),
        vat: faker.number.int(),
      },
      numberOfCharges: faker.number.int(),
      paidEnergyDelivered: faker.number.int(),
      revenue: {
        gross: faker.number.int(),
        net: faker.number.int(),
        vat: faker.number.int(),
      },
    })),
    date: faker.lorem.words(),
    groupName: faker.person.fullName(),
    reference: faker.lorem.words(),
    siteAddress: faker.lorem.words(),
    siteName: faker.person.fullName(),
    claimedEnergyDelivered: faker.number.int(),
    energyDelivered: faker.number.int(),
    fees: {
      gross: faker.number.int(),
      net: faker.number.int(),
      vat: faker.number.int(),
    },
    numberOfCharges: faker.number.int(),
    paidEnergyDelivered: faker.number.int(),
    revenue: {
      gross: faker.number.int(),
      net: faker.number.int(),
      vat: faker.number.int(),
    },
  };
}

export function getStatementControllerCreateByGroupUidAndSiteId201Response() {
  return {
    breakdown: [
      ...new Array(faker.number.int({ min: 1, max: MAX_ARRAY_LENGTH })).keys(),
    ].map((_) => ({
      date: faker.lorem.words(),
      groupName: faker.person.fullName(),
      reference: faker.lorem.words(),
      siteAddress: faker.lorem.words(),
      siteName: faker.person.fullName(),
      claimedEnergyDelivered: faker.number.int(),
      energyDelivered: faker.number.int(),
      fees: {
        gross: faker.number.int(),
        net: faker.number.int(),
        vat: faker.number.int(),
      },
      numberOfCharges: faker.number.int(),
      paidEnergyDelivered: faker.number.int(),
      revenue: {
        gross: faker.number.int(),
        net: faker.number.int(),
        vat: faker.number.int(),
      },
    })),
    date: faker.lorem.words(),
    groupName: faker.person.fullName(),
    reference: faker.lorem.words(),
    siteAddress: faker.lorem.words(),
    siteName: faker.person.fullName(),
    claimedEnergyDelivered: faker.number.int(),
    energyDelivered: faker.number.int(),
    fees: {
      gross: faker.number.int(),
      net: faker.number.int(),
      vat: faker.number.int(),
    },
    numberOfCharges: faker.number.int(),
    paidEnergyDelivered: faker.number.int(),
    revenue: {
      gross: faker.number.int(),
      net: faker.number.int(),
      vat: faker.number.int(),
    },
  };
}

export function getRfidCardControllerFindByGroupId200Response() {
  return [
    ...new Array(faker.number.int({ min: 1, max: MAX_ARRAY_LENGTH })).keys(),
  ].map((_) => ({
    active: faker.datatype.boolean(),
    reference: faker.lorem.words(),
    tags: [
      ...new Array(faker.number.int({ min: 1, max: MAX_ARRAY_LENGTH })).keys(),
    ].map((_) => faker.lorem.words()),
    uid: faker.string.uuid(),
  }));
}

export function getBouncedEmailsControllerFindAll200Response() {
  return [
    ...new Array(faker.number.int({ min: 1, max: MAX_ARRAY_LENGTH })).keys(),
  ].map((_) => faker.lorem.words());
}

export function getChargerControllerFindByIdentifier200Response() {
  return {
    admins: [
      ...new Array(faker.number.int({ min: 1, max: MAX_ARRAY_LENGTH })).keys(),
    ].map((_) => ({
      activatedOn: faker.date.past(),
      authId: faker.string.uuid(),
      email: faker.internet.email(),
      emailBounced: faker.datatype.boolean(),
      firstName: faker.person.fullName(),
      groupId: faker.number.int(),
      groupName: faker.person.fullName(),
      groupUid: faker.string.uuid(),
      id: faker.number.int(),
      lastLogin: faker.date.past(),
      lastName: faker.person.fullName(),
      status: faker.helpers.arrayElement([
        'Registered',
        'Pending',
        'Deactivated',
      ]),
    })),
    billing: {
      customerDetails: {
        address: {
          line1: faker.lorem.words(),
          line2: faker.lorem.words(),
          town: faker.lorem.words(),
          county: faker.lorem.words(),
          postcode: faker.lorem.words(),
        },
        email: faker.internet.email(),
        name: faker.person.fullName(),
        accountReference: faker.lorem.words(),
        poNumber: faker.lorem.words(),
      },
      onboardingLink: faker.lorem.words(),
      statements: [
        ...new Array(
          faker.number.int({ min: 1, max: MAX_ARRAY_LENGTH })
        ).keys(),
      ].map((_) => ({
        siteName: faker.person.fullName(),
        month: faker.lorem.words(),
        feeInvoiceNumber: faker.lorem.words(),
        feeInvoiceStatus: faker.lorem.words(),
        hostedInvoiceUrl: faker.internet.url(),
        invoiceId: faker.string.uuid(),
        revenuePayoutStatus: faker.lorem.words(),
        statementId: faker.string.uuid(),
      })),
      subscription: {
        status: faker.lorem.words(),
        invoices: [
          ...new Array(
            faker.number.int({ min: 1, max: MAX_ARRAY_LENGTH })
          ).keys(),
        ].map((_) => ({
          status: faker.lorem.words(),
          created: faker.lorem.words(),
          amount: faker.number.int(),
          due: faker.lorem.words(),
          hostedInvoiceUrl: faker.internet.url(),
          invoiceNumber: faker.lorem.words(),
          invoicePdfUrl: faker.internet.url(),
          customerEmail: faker.internet.email(),
        })),
      },
      subscriptionChargers: [
        ...new Array(
          faker.number.int({ min: 1, max: MAX_ARRAY_LENGTH })
        ).keys(),
      ].map((_) => ({
        ppid: faker.string.uuid(),
        socket: faker.lorem.words(),
      })),
    },
    domains: [
      ...new Array(faker.number.int({ min: 1, max: MAX_ARRAY_LENGTH })).keys(),
    ].map((_) => ({
      activatedOn: faker.date.past(),
      domainName: faker.person.fullName(),
      id: faker.number.int(),
      groupId: faker.number.int(),
    })),
    drivers: [
      ...new Array(faker.number.int({ min: 1, max: MAX_ARRAY_LENGTH })).keys(),
    ].map((_) => ({
      canExpense: faker.datatype.boolean(),
      chargingStats: {
        charges: [
          ...new Array(
            faker.number.int({ min: 1, max: MAX_ARRAY_LENGTH })
          ).keys(),
        ].map((_) => ({
          chargingDuration: faker.lorem.words(),
          confirmed: faker.datatype.boolean(),
          confirmedBy: faker.helpers.arrayElement(['driver', 'other']),
          co2Savings: faker.lorem.words(),
          door: faker.lorem.words(),
          endedAt: faker.lorem.words(),
          energyCost: faker.lorem.words(),
          energyUsage: faker.lorem.words(),
          locationId: faker.number.int(),
          pluggedIn: faker.lorem.words(),
          podName: faker.person.fullName(),
          ppid: faker.string.uuid(),
          revenueGenerated: faker.lorem.words(),
          startedAt: faker.lorem.words(),
          siteName: faker.person.fullName(),
          totalDuration: faker.lorem.words(),
          userEmail: faker.internet.email(),
          userName: faker.person.fullName(),
          vehicle: faker.lorem.words(),
        })),
        co2Avoided: faker.lorem.words(),
        energyCost: faker.lorem.words(),
        energyDelivered: faker.lorem.words(),
        revenue: faker.lorem.words(),
      },
      email: faker.internet.email(),
      emailBounced: faker.datatype.boolean(),
      firstName: faker.person.fullName(),
      fullName: faker.person.fullName(),
      group: {
        activatedOn: faker.date.past(),
        contactName: faker.person.fullName(),
        contactNumber: faker.lorem.words(),
        id: faker.number.int(),
        name: faker.person.fullName(),
        readOnly: false,
        stats: {
          chargingDuration: faker.number.int(),
          co2Savings: faker.number.int(),
          energyCost: faker.number.int(),
          energyDelivered: faker.number.int(),
          numberOfChargers: faker.number.int(),
          numberOfCharges: faker.number.int(),
          numberOfDrivers: faker.number.int(),
          numberOfSites: faker.number.int(),
          revenueGenerated: faker.number.int(),
        },
        type: faker.lorem.words(),
        uid: faker.string.uuid(),
      },
      id: faker.number.int(),
      lastName: faker.person.fullName(),
      registeredDate: faker.lorem.words(),
      status: faker.helpers.arrayElement([
        'Deleted',
        'Deactivated',
        'Pending',
        'Registered',
      ]),
      tariffTier: faker.helpers.arrayElement(['Driver', 'Member']),
    })),
    group: {
      activatedOn: faker.date.past(),
      contactName: faker.person.fullName(),
      contactNumber: faker.lorem.words(),
      id: faker.number.int(),
      name: faker.person.fullName(),
      readOnly: false,
      stats: {
        chargingDuration: faker.number.int(),
        co2Savings: faker.number.int(),
        energyCost: faker.number.int(),
        energyDelivered: faker.number.int(),
        numberOfChargers: faker.number.int(),
        numberOfCharges: faker.number.int(),
        numberOfDrivers: faker.number.int(),
        numberOfSites: faker.number.int(),
        revenueGenerated: faker.number.int(),
      },
      type: faker.lorem.words(),
      uid: faker.string.uuid(),
    },
    name: faker.person.fullName(),
    ppid: faker.string.uuid(),
    rfidCards: [
      ...new Array(faker.number.int({ min: 1, max: MAX_ARRAY_LENGTH })).keys(),
    ].map((_) => ({
      active: faker.datatype.boolean(),
      reference: faker.lorem.words(),
      tags: [
        ...new Array(
          faker.number.int({ min: 1, max: MAX_ARRAY_LENGTH })
        ).keys(),
      ].map((_) => faker.lorem.words()),
      uid: faker.string.uuid(),
    })),
    settings: {
      confirmCharge: faker.datatype.boolean(),
    },
    site: {
      address: {
        country: faker.lorem.words(),
        line1: faker.lorem.words(),
        line2: faker.lorem.words(),
        name: faker.person.fullName(),
        postcode: faker.lorem.words(),
        prettyPrint: faker.lorem.words(),
        town: faker.lorem.words(),
      },
      chargeSummary: {
        chargingDuration: faker.number.int(),
        claimedEnergyUsage: faker.number.int(),
        co2Savings: faker.number.int(),
        energyCost: faker.number.int(),
        energyDelivered: faker.number.int(),
        energyUsage: faker.number.int(),
        numberOfCharges: faker.number.int(),
        revenueGenerated: faker.number.int(),
        revenueGeneratingClaimedUsage: faker.number.int(),
      },
      contactDetails: {
        email: faker.internet.email(),
        name: faker.person.fullName(),
        telephone: faker.lorem.words(),
      },
      description: faker.lorem.words(),
      energyCost: faker.number.int(),
      group: {
        activatedOn: faker.date.past(),
        contactName: faker.person.fullName(),
        contactNumber: faker.lorem.words(),
        id: faker.number.int(),
        name: faker.person.fullName(),
        readOnly: false,
        stats: {
          chargingDuration: faker.number.int(),
          co2Savings: faker.number.int(),
          energyCost: faker.number.int(),
          energyDelivered: faker.number.int(),
          numberOfChargers: faker.number.int(),
          numberOfCharges: faker.number.int(),
          numberOfDrivers: faker.number.int(),
          numberOfSites: faker.number.int(),
          revenueGenerated: faker.number.int(),
        },
        type: faker.lorem.words(),
        uid: faker.string.uuid(),
      },
      id: faker.number.int(),
      parking: {
        openingTimes: {
          notes: [
            ...new Array(
              faker.number.int({ min: 1, max: MAX_ARRAY_LENGTH })
            ).keys(),
          ].map((_) => faker.lorem.words()),
        },
      },
      pods: [
        ...new Array(
          faker.number.int({ min: 1, max: MAX_ARRAY_LENGTH })
        ).keys(),
      ].map((_) => ({
        ageYears: faker.number.int(),
        confirmChargeEnabled: faker.datatype.boolean(),
        chargeSummary: {
          chargingDuration: faker.number.int(),
          claimedEnergyUsage: faker.number.int(),
          co2Savings: faker.number.int(),
          energyCost: faker.number.int(),
          energyDelivered: faker.number.int(),
          energyUsage: faker.number.int(),
          numberOfCharges: faker.number.int(),
          revenueGenerated: faker.number.int(),
          revenueGeneratingClaimedUsage: faker.number.int(),
        },
        coordinates: {
          latitude: faker.number.int(),
          longitude: faker.number.int(),
        },
        description: faker.lorem.words(),
        id: faker.number.int(),
        installDate: faker.lorem.words(),
        isEvZone: faker.datatype.boolean(),
        isPublic: faker.datatype.boolean(),
        lastContact: faker.lorem.words(),
        model: faker.lorem.words(),
        mostRecentCharge: {
          chargingDuration: faker.lorem.words(),
          confirmed: faker.datatype.boolean(),
          confirmedBy: faker.helpers.arrayElement(['driver', 'other']),
          co2Savings: faker.lorem.words(),
          door: faker.lorem.words(),
          endedAt: faker.lorem.words(),
          energyCost: faker.lorem.words(),
          energyUsage: faker.lorem.words(),
          locationId: faker.number.int(),
          pluggedIn: faker.lorem.words(),
          podName: faker.person.fullName(),
          ppid: faker.string.uuid(),
          revenueGenerated: faker.lorem.words(),
          startedAt: faker.lorem.words(),
          siteName: faker.person.fullName(),
          totalDuration: faker.lorem.words(),
          userEmail: faker.internet.email(),
          userName: faker.person.fullName(),
          vehicle: faker.lorem.words(),
        },
        name: faker.person.fullName(),
        ppid: faker.string.uuid(),
        recentCharges: [
          ...new Array(
            faker.number.int({ min: 1, max: MAX_ARRAY_LENGTH })
          ).keys(),
        ].map((_) => ({
          chargingDuration: faker.lorem.words(),
          confirmed: faker.datatype.boolean(),
          confirmedBy: faker.helpers.arrayElement(['driver', 'other']),
          co2Savings: faker.lorem.words(),
          door: faker.lorem.words(),
          endedAt: faker.lorem.words(),
          energyCost: faker.lorem.words(),
          energyUsage: faker.lorem.words(),
          locationId: faker.number.int(),
          pluggedIn: faker.lorem.words(),
          podName: faker.person.fullName(),
          ppid: faker.string.uuid(),
          revenueGenerated: faker.lorem.words(),
          startedAt: faker.lorem.words(),
          siteName: faker.person.fullName(),
          totalDuration: faker.lorem.words(),
          userEmail: faker.internet.email(),
          userName: faker.person.fullName(),
          vehicle: faker.lorem.words(),
        })),
        schedules: [
          ...new Array(
            faker.number.int({ min: 1, max: MAX_ARRAY_LENGTH })
          ).keys(),
        ].map((_) => ({
          endDay: faker.number.int({ min: 0, max: 6 }),
          endTime: faker.lorem.words(),
          isActive: faker.datatype.boolean(),
          startDay: faker.number.int({ min: 0, max: 6 }),
          startTime: faker.lorem.words(),
        })),
        schemes: [
          ...new Array(
            faker.number.int({ min: 1, max: MAX_ARRAY_LENGTH })
          ).keys(),
        ].map((_) => ({
          activatedOn: faker.date.past(),
          contactName: faker.person.fullName(),
          contactNumber: faker.lorem.words(),
          id: faker.number.int(),
          name: faker.person.fullName(),
          readOnly: false,
          stats: {
            chargingDuration: faker.number.int(),
            co2Savings: faker.number.int(),
            energyCost: faker.number.int(),
            energyDelivered: faker.number.int(),
            numberOfChargers: faker.number.int(),
            numberOfCharges: faker.number.int(),
            numberOfDrivers: faker.number.int(),
            numberOfSites: faker.number.int(),
            revenueGenerated: faker.number.int(),
          },
          type: faker.lorem.words(),
          uid: faker.string.uuid(),
        })),
        site: null,
        sockets: [
          ...new Array(
            faker.number.int({ min: 1, max: MAX_ARRAY_LENGTH })
          ).keys(),
        ].map((_) => ({
          door: faker.lorem.words(),
          firmwareVersion: faker.lorem.words(),
          isUpdateAvailable: faker.datatype.boolean(),
          lastContact: faker.lorem.words(),
          serialNumber: faker.lorem.words(),
          status: faker.helpers.arrayElement([
            'Available',
            'Charging',
            'Offline',
            'Unavailable',
          ]),
        })),
        status: faker.lorem.words(),
        supportsConfirmCharge: true,
        supportsContactless: faker.datatype.boolean(),
        supportsEnergyTariff: true,
        supportsOcpp: false,
        supportsPerKwh: true,
        supportsRfid: faker.datatype.boolean(),
        supportsTariffs: true,
        tariff: {
          id: faker.number.int(),
          name: faker.person.fullName(),
        },
      })),
    },
    tariff: {
      id: faker.number.int(),
      name: faker.person.fullName(),
      currency: faker.lorem.words(),
      pods: [
        ...new Array(
          faker.number.int({ min: 1, max: MAX_ARRAY_LENGTH })
        ).keys(),
      ].map((_) => ({
        ageYears: faker.number.int(),
        confirmChargeEnabled: faker.datatype.boolean(),
        chargeSummary: {
          chargingDuration: faker.number.int(),
          claimedEnergyUsage: faker.number.int(),
          co2Savings: faker.number.int(),
          energyCost: faker.number.int(),
          energyDelivered: faker.number.int(),
          energyUsage: faker.number.int(),
          numberOfCharges: faker.number.int(),
          revenueGenerated: faker.number.int(),
          revenueGeneratingClaimedUsage: faker.number.int(),
        },
        coordinates: {
          latitude: faker.number.int(),
          longitude: faker.number.int(),
        },
        description: faker.lorem.words(),
        id: faker.number.int(),
        installDate: faker.lorem.words(),
        isEvZone: faker.datatype.boolean(),
        isPublic: faker.datatype.boolean(),
        lastContact: faker.lorem.words(),
        model: faker.lorem.words(),
        mostRecentCharge: {
          chargingDuration: faker.lorem.words(),
          confirmed: faker.datatype.boolean(),
          confirmedBy: faker.helpers.arrayElement(['driver', 'other']),
          co2Savings: faker.lorem.words(),
          door: faker.lorem.words(),
          endedAt: faker.lorem.words(),
          energyCost: faker.lorem.words(),
          energyUsage: faker.lorem.words(),
          locationId: faker.number.int(),
          pluggedIn: faker.lorem.words(),
          podName: faker.person.fullName(),
          ppid: faker.string.uuid(),
          revenueGenerated: faker.lorem.words(),
          startedAt: faker.lorem.words(),
          siteName: faker.person.fullName(),
          totalDuration: faker.lorem.words(),
          userEmail: faker.internet.email(),
          userName: faker.person.fullName(),
          vehicle: faker.lorem.words(),
        },
        name: faker.person.fullName(),
        ppid: faker.string.uuid(),
        recentCharges: [
          ...new Array(
            faker.number.int({ min: 1, max: MAX_ARRAY_LENGTH })
          ).keys(),
        ].map((_) => ({
          chargingDuration: faker.lorem.words(),
          confirmed: faker.datatype.boolean(),
          confirmedBy: faker.helpers.arrayElement(['driver', 'other']),
          co2Savings: faker.lorem.words(),
          door: faker.lorem.words(),
          endedAt: faker.lorem.words(),
          energyCost: faker.lorem.words(),
          energyUsage: faker.lorem.words(),
          locationId: faker.number.int(),
          pluggedIn: faker.lorem.words(),
          podName: faker.person.fullName(),
          ppid: faker.string.uuid(),
          revenueGenerated: faker.lorem.words(),
          startedAt: faker.lorem.words(),
          siteName: faker.person.fullName(),
          totalDuration: faker.lorem.words(),
          userEmail: faker.internet.email(),
          userName: faker.person.fullName(),
          vehicle: faker.lorem.words(),
        })),
        schedules: [
          ...new Array(
            faker.number.int({ min: 1, max: MAX_ARRAY_LENGTH })
          ).keys(),
        ].map((_) => ({
          endDay: faker.number.int({ min: 0, max: 6 }),
          endTime: faker.lorem.words(),
          isActive: faker.datatype.boolean(),
          startDay: faker.number.int({ min: 0, max: 6 }),
          startTime: faker.lorem.words(),
        })),
        schemes: [
          ...new Array(
            faker.number.int({ min: 1, max: MAX_ARRAY_LENGTH })
          ).keys(),
        ].map((_) => ({
          activatedOn: faker.date.past(),
          contactName: faker.person.fullName(),
          contactNumber: faker.lorem.words(),
          id: faker.number.int(),
          name: faker.person.fullName(),
          readOnly: false,
          stats: {
            chargingDuration: faker.number.int(),
            co2Savings: faker.number.int(),
            energyCost: faker.number.int(),
            energyDelivered: faker.number.int(),
            numberOfChargers: faker.number.int(),
            numberOfCharges: faker.number.int(),
            numberOfDrivers: faker.number.int(),
            numberOfSites: faker.number.int(),
            revenueGenerated: faker.number.int(),
          },
          type: faker.lorem.words(),
          uid: faker.string.uuid(),
        })),
        site: {
          address: {
            country: faker.lorem.words(),
            line1: faker.lorem.words(),
            line2: faker.lorem.words(),
            name: faker.person.fullName(),
            postcode: faker.lorem.words(),
            prettyPrint: faker.lorem.words(),
            town: faker.lorem.words(),
          },
          chargeSummary: {
            chargingDuration: faker.number.int(),
            claimedEnergyUsage: faker.number.int(),
            co2Savings: faker.number.int(),
            energyCost: faker.number.int(),
            energyDelivered: faker.number.int(),
            energyUsage: faker.number.int(),
            numberOfCharges: faker.number.int(),
            revenueGenerated: faker.number.int(),
            revenueGeneratingClaimedUsage: faker.number.int(),
          },
          contactDetails: {
            email: faker.internet.email(),
            name: faker.person.fullName(),
            telephone: faker.lorem.words(),
          },
          description: faker.lorem.words(),
          energyCost: faker.number.int(),
          group: {
            activatedOn: faker.date.past(),
            contactName: faker.person.fullName(),
            contactNumber: faker.lorem.words(),
            id: faker.number.int(),
            name: faker.person.fullName(),
            readOnly: false,
            stats: {
              chargingDuration: faker.number.int(),
              co2Savings: faker.number.int(),
              energyCost: faker.number.int(),
              energyDelivered: faker.number.int(),
              numberOfChargers: faker.number.int(),
              numberOfCharges: faker.number.int(),
              numberOfDrivers: faker.number.int(),
              numberOfSites: faker.number.int(),
              revenueGenerated: faker.number.int(),
            },
            type: faker.lorem.words(),
            uid: faker.string.uuid(),
          },
          id: faker.number.int(),
          parking: {
            openingTimes: {
              notes: [
                ...new Array(
                  faker.number.int({ min: 1, max: MAX_ARRAY_LENGTH })
                ).keys(),
              ].map((_) => faker.lorem.words()),
            },
          },
          pods: [
            ...new Array(
              faker.number.int({ min: 1, max: MAX_ARRAY_LENGTH })
            ).keys(),
          ].map((_) => null),
        },
        sockets: [
          ...new Array(
            faker.number.int({ min: 1, max: MAX_ARRAY_LENGTH })
          ).keys(),
        ].map((_) => ({
          door: faker.lorem.words(),
          firmwareVersion: faker.lorem.words(),
          isUpdateAvailable: faker.datatype.boolean(),
          lastContact: faker.lorem.words(),
          serialNumber: faker.lorem.words(),
          status: faker.helpers.arrayElement([
            'Available',
            'Charging',
            'Offline',
            'Unavailable',
          ]),
        })),
        status: faker.lorem.words(),
        supportsConfirmCharge: true,
        supportsContactless: faker.datatype.boolean(),
        supportsEnergyTariff: true,
        supportsOcpp: false,
        supportsPerKwh: true,
        supportsRfid: faker.datatype.boolean(),
        supportsTariffs: true,
        tariff: {
          id: faker.number.int(),
          name: faker.person.fullName(),
        },
      })),
      schedule: {
        drivers: [
          ...new Array(
            faker.number.int({ min: 1, max: MAX_ARRAY_LENGTH })
          ).keys(),
        ].map((_) => ({
          id: faker.number.int(),
          bands: [
            ...new Array(
              faker.number.int({ min: 1, max: MAX_ARRAY_LENGTH })
            ).keys(),
          ].map((_) => ({
            id: faker.number.int(),
            after: faker.number.int(),
            cost: faker.number.int(),
            prettyPrint: faker.lorem.words(),
          })),
          endDay: faker.number.int({ min: 0, max: 6 }),
          endTime: faker.lorem.words(),
          pricingModel: faker.helpers.arrayElement([
            'duration',
            'energy',
            'fixed',
          ]),
          startDay: faker.number.int({ min: 0, max: 6 }),
          startTime: faker.lorem.words(),
          tariffTier: faker.helpers.arrayElement([
            'public',
            'members',
            'drivers',
          ]),
        })),
        members: [
          ...new Array(
            faker.number.int({ min: 1, max: MAX_ARRAY_LENGTH })
          ).keys(),
        ].map((_) => ({
          id: faker.number.int(),
          bands: [
            ...new Array(
              faker.number.int({ min: 1, max: MAX_ARRAY_LENGTH })
            ).keys(),
          ].map((_) => ({
            id: faker.number.int(),
            after: faker.number.int(),
            cost: faker.number.int(),
            prettyPrint: faker.lorem.words(),
          })),
          endDay: faker.number.int({ min: 0, max: 6 }),
          endTime: faker.lorem.words(),
          pricingModel: faker.helpers.arrayElement([
            'duration',
            'energy',
            'fixed',
          ]),
          startDay: faker.number.int({ min: 0, max: 6 }),
          startTime: faker.lorem.words(),
          tariffTier: faker.helpers.arrayElement([
            'public',
            'members',
            'drivers',
          ]),
        })),
        public: [
          ...new Array(
            faker.number.int({ min: 1, max: MAX_ARRAY_LENGTH })
          ).keys(),
        ].map((_) => ({
          id: faker.number.int(),
          bands: [
            ...new Array(
              faker.number.int({ min: 1, max: MAX_ARRAY_LENGTH })
            ).keys(),
          ].map((_) => ({
            id: faker.number.int(),
            after: faker.number.int(),
            cost: faker.number.int(),
            prettyPrint: faker.lorem.words(),
          })),
          endDay: faker.number.int({ min: 0, max: 6 }),
          endTime: faker.lorem.words(),
          pricingModel: faker.helpers.arrayElement([
            'duration',
            'energy',
            'fixed',
          ]),
          startDay: faker.number.int({ min: 0, max: 6 }),
          startTime: faker.lorem.words(),
          tariffTier: faker.helpers.arrayElement([
            'public',
            'members',
            'drivers',
          ]),
        })),
      },
    },
  };
}

export function getChargerControllerFindPpidByChargerName200Response() {
  return faker.lorem.words();
}

export function getChargerControllerFindChargerNameByPpid200Response() {
  return faker.lorem.words();
}

export function getUserControllerFindByUserId200Response() {
  return {
    activatedOn: faker.date.past(),
    authId: faker.string.uuid(),
    billingAccount: {
      balance: faker.number.int(),
      id: faker.number.int(),
      uid: faker.string.uuid(),
    },
    email: faker.internet.email(),
    emailVerified: faker.datatype.boolean(),
    firstName: faker.person.fullName(),
    groupId: faker.number.int(),
    groupName: faker.person.fullName(),
    groupUid: faker.string.uuid(),
    groupType: faker.helpers.arrayElement([
      'podPoint',
      'host',
      'scheme',
      'manufacturer',
    ]),
    id: faker.number.int(),
    lastName: faker.person.fullName(),
    status: faker.helpers.arrayElement(['Activated', 'Deactivated']),
    termsAndConditions: {},
  };
}
