{"name": "ocpi-service-webapp", "$schema": "../../../../node_modules/nx/schemas/project-schema.json", "sourceRoot": "apps/commercial/ocpi-service/webapp", "projectType": "application", "tags": ["commercial", "ocpi-service"], "targets": {"build": {"dependsOn": ["copy-shared-assets"], "executor": "@nx/next:build", "outputs": ["{options.outputPath}"], "defaultConfiguration": "production", "options": {"assets": [{"input": "./assets/shared/", "output": "./", "glob": "**/*.*"}], "outputPath": "dist/apps/commercial/ocpi-service/webapp", "generateLockfile": true}, "configurations": {"development": {"outputPath": "apps/commercial/ocpi-service/webapp"}, "production": {}}}, "copy-shared-assets": {"executor": "nx:run-commands", "options": {"command": "cp -r ./assets/shared/* ./apps/commercial/ocpi-service/webapp/public"}}, "start": {"executor": "@nx/next:server", "defaultConfiguration": "development", "options": {"buildTarget": "ocpi-service-webapp:build", "dev": true}, "configurations": {"development": {"buildTarget": "ocpi-service-webapp:build:development", "dev": true, "port": 3000}, "production": {"buildTarget": "ocpi-service-webapp:build:production", "dev": false}}}, "test": {"executor": "@nx/jest:jest", "outputs": ["{workspaceRoot}/coverage/{projectRoot}"], "options": {"jestConfig": "apps/commercial/ocpi-service/webapp/jest.config.ts"}}}}