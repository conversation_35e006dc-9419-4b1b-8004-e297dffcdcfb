networks:
  pod-point:
    name: pod-point
services:
  ocpi-service-api:
    build:
      context: ../../../..
      dockerfile: apps/commercial/ocpi-service/api/Dockerfile
    container_name: ocpi-service-api
    deploy:
      resources:
        limits:
          cpus: '0.5'
          memory: 512M
    env_file: .env.local
    environment:
      - COMMERCIAL_DB_URL=****************************************************/commercial
      - COMMERCIAL_DB_RO_URL=****************************************************/commercial
      - COMMERCIAL_DB_ADMIN_URL=****************************************************/commercial
      - COMMERCIAL_DB_ADMIN_RO_URL=****************************************************/commercial
    networks:
      - pod-point
    platform: linux/amd64
    ports:
      - 6102:6102
