// Jest Snapshot v1, https://goo.gl/fbAQLP

exports[`destination site admin api admin module admin controller should create an admin 1`] = `
[
  {
    "activatedOn": StringMatching /\\^\\\\d\\{4\\}-\\\\d\\{2\\}-\\\\d\\{2\\}T\\\\d\\{2\\}:\\\\d\\{2\\}:\\\\d\\{2\\}\\\\\\.\\\\d\\{3\\}Z\\$/,
    "authId": "35af3664-271b-4a15-b62b-c73c6d3a31c1",
    "email": "<EMAIL>",
    "emailBounced": false,
    "firstName": "",
    "groupId": 1,
    "groupName": "Pod Point - Software Team",
    "groupUid": "45c9ef22-b44b-4edd-bb66-392e923950af",
    "id": 50176,
    "lastLogin": StringMatching /\\^\\\\d\\{4\\}-\\\\d\\{2\\}-\\\\d\\{2\\}T\\\\d\\{2\\}:\\\\d\\{2\\}:\\\\d\\{2\\}\\\\\\.\\\\d\\{3\\}Z\\$/,
    "lastName": "",
    "status": "Registered",
  },
  {
    "activatedOn": StringMatching /\\^\\\\d\\{4\\}-\\\\d\\{2\\}-\\\\d\\{2\\}T\\\\d\\{2\\}:\\\\d\\{2\\}:\\\\d\\{2\\}\\\\\\.\\\\d\\{3\\}Z\\$/,
    "authId": "2abf5712-c7a9-4ee6-8692-ac3326466a87",
    "email": "<EMAIL>",
    "emailBounced": false,
    "firstName": "John",
    "groupId": 1,
    "groupName": "Pod Point - Software Team",
    "groupUid": "45c9ef22-b44b-4edd-bb66-392e923950af",
    "id": 9,
    "lastName": "Peet",
    "status": "Registered",
  },
  {
    "activatedOn": StringMatching /\\^\\\\d\\{4\\}-\\\\d\\{2\\}-\\\\d\\{2\\}T\\\\d\\{2\\}:\\\\d\\{2\\}:\\\\d\\{2\\}\\\\\\.\\\\d\\{3\\}Z\\$/,
    "authId": "0be756cb-fc95-48ef-80ad-7ce9c777ab29",
    "email": "<EMAIL>",
    "emailBounced": false,
    "firstName": "Parth",
    "groupId": 1,
    "groupName": "Pod Point - Software Team",
    "groupUid": "45c9ef22-b44b-4edd-bb66-392e923950af",
    "id": 4,
    "lastName": "Chandratreya",
    "status": "Registered",
  },
  {
    "activatedOn": StringMatching /\\^\\\\d\\{4\\}-\\\\d\\{2\\}-\\\\d\\{2\\}T\\\\d\\{2\\}:\\\\d\\{2\\}:\\\\d\\{2\\}\\\\\\.\\\\d\\{3\\}Z\\$/,
    "authId": "63200cfe-c2b0-4264-ab4d-c6654abf78c5",
    "email": "<EMAIL>",
    "emailBounced": false,
    "firstName": "Ewen",
    "groupId": 1,
    "groupName": "Pod Point - Software Team",
    "groupUid": "45c9ef22-b44b-4edd-bb66-392e923950af",
    "id": 3,
    "lastName": "Carr",
    "status": "Registered",
  },
  {
    "activatedOn": StringMatching /\\^\\\\d\\{4\\}-\\\\d\\{2\\}-\\\\d\\{2\\}T\\\\d\\{2\\}:\\\\d\\{2\\}:\\\\d\\{2\\}\\\\\\.\\\\d\\{3\\}Z\\$/,
    "authId": "41c07d75-105f-433d-816c-bc4f66f94700",
    "email": "<EMAIL>",
    "emailBounced": false,
    "firstName": "James",
    "groupId": 1,
    "groupName": "Pod Point - Software Team",
    "groupUid": "45c9ef22-b44b-4edd-bb66-392e923950af",
    "id": 2,
    "lastName": "Hardy",
    "status": "Registered",
  },
  {
    "activatedOn": StringMatching /\\^\\\\d\\{4\\}-\\\\d\\{2\\}-\\\\d\\{2\\}T\\\\d\\{2\\}:\\\\d\\{2\\}:\\\\d\\{2\\}\\\\\\.\\\\d\\{3\\}Z\\$/,
    "authId": "973b5195-c2b8-4de0-9eab-381618e3ff74",
    "email": "<EMAIL>",
    "emailBounced": false,
    "firstName": "Stuart",
    "groupId": 1,
    "groupName": "Pod Point - Software Team",
    "groupUid": "45c9ef22-b44b-4edd-bb66-392e923950af",
    "id": 1,
    "lastName": "McGregor",
    "status": "Registered",
  },
]
`;

exports[`destination site admin api admin module admin controller should delete an admin 1`] = `
[
  {
    "activatedOn": StringMatching /\\^\\\\d\\{4\\}-\\\\d\\{2\\}-\\\\d\\{2\\}T\\\\d\\{2\\}:\\\\d\\{2\\}:\\\\d\\{2\\}\\\\\\.\\\\d\\{3\\}Z\\$/,
    "authId": "2abf5712-c7a9-4ee6-8692-ac3326466a87",
    "email": "<EMAIL>",
    "emailBounced": false,
    "firstName": "John",
    "groupId": 1,
    "groupName": "Pod Point - Software Team",
    "groupUid": "45c9ef22-b44b-4edd-bb66-392e923950af",
    "id": 9,
    "lastName": "Peet",
    "status": "Registered",
  },
  {
    "activatedOn": StringMatching /\\^\\\\d\\{4\\}-\\\\d\\{2\\}-\\\\d\\{2\\}T\\\\d\\{2\\}:\\\\d\\{2\\}:\\\\d\\{2\\}\\\\\\.\\\\d\\{3\\}Z\\$/,
    "authId": "0be756cb-fc95-48ef-80ad-7ce9c777ab29",
    "email": "<EMAIL>",
    "emailBounced": false,
    "firstName": "Parth",
    "groupId": 1,
    "groupName": "Pod Point - Software Team",
    "groupUid": "45c9ef22-b44b-4edd-bb66-392e923950af",
    "id": 4,
    "lastName": "Chandratreya",
    "status": "Registered",
  },
  {
    "activatedOn": StringMatching /\\^\\\\d\\{4\\}-\\\\d\\{2\\}-\\\\d\\{2\\}T\\\\d\\{2\\}:\\\\d\\{2\\}:\\\\d\\{2\\}\\\\\\.\\\\d\\{3\\}Z\\$/,
    "authId": "63200cfe-c2b0-4264-ab4d-c6654abf78c5",
    "email": "<EMAIL>",
    "emailBounced": false,
    "firstName": "Ewen",
    "groupId": 1,
    "groupName": "Pod Point - Software Team",
    "groupUid": "45c9ef22-b44b-4edd-bb66-392e923950af",
    "id": 3,
    "lastName": "Carr",
    "status": "Registered",
  },
  {
    "activatedOn": StringMatching /\\^\\\\d\\{4\\}-\\\\d\\{2\\}-\\\\d\\{2\\}T\\\\d\\{2\\}:\\\\d\\{2\\}:\\\\d\\{2\\}\\\\\\.\\\\d\\{3\\}Z\\$/,
    "authId": "41c07d75-105f-433d-816c-bc4f66f94700",
    "email": "<EMAIL>",
    "emailBounced": false,
    "firstName": "James",
    "groupId": 1,
    "groupName": "Pod Point - Software Team",
    "groupUid": "45c9ef22-b44b-4edd-bb66-392e923950af",
    "id": 2,
    "lastName": "Hardy",
    "status": "Registered",
  },
  {
    "activatedOn": StringMatching /\\^\\\\d\\{4\\}-\\\\d\\{2\\}-\\\\d\\{2\\}T\\\\d\\{2\\}:\\\\d\\{2\\}:\\\\d\\{2\\}\\\\\\.\\\\d\\{3\\}Z\\$/,
    "authId": "973b5195-c2b8-4de0-9eab-381618e3ff74",
    "email": "<EMAIL>",
    "emailBounced": false,
    "firstName": "Stuart",
    "groupId": 1,
    "groupName": "Pod Point - Software Team",
    "groupUid": "45c9ef22-b44b-4edd-bb66-392e923950af",
    "id": 1,
    "lastName": "McGregor",
    "status": "Registered",
  },
]
`;

exports[`destination site admin api admin module admin controller should find a list of admins 1`] = `
[
  {
    "activatedOn": StringMatching /\\^\\\\d\\{4\\}-\\\\d\\{2\\}-\\\\d\\{2\\}T\\\\d\\{2\\}:\\\\d\\{2\\}:\\\\d\\{2\\}\\\\\\.\\\\d\\{3\\}Z\\$/,
    "authId": "2abf5712-c7a9-4ee6-8692-ac3326466a87",
    "email": "<EMAIL>",
    "emailBounced": false,
    "firstName": "John",
    "groupId": 1,
    "groupName": "Pod Point - Software Team",
    "groupUid": "45c9ef22-b44b-4edd-bb66-392e923950af",
    "id": 9,
    "lastName": "Peet",
    "status": "Registered",
  },
  {
    "activatedOn": StringMatching /\\^\\\\d\\{4\\}-\\\\d\\{2\\}-\\\\d\\{2\\}T\\\\d\\{2\\}:\\\\d\\{2\\}:\\\\d\\{2\\}\\\\\\.\\\\d\\{3\\}Z\\$/,
    "authId": "0be756cb-fc95-48ef-80ad-7ce9c777ab29",
    "email": "<EMAIL>",
    "emailBounced": false,
    "firstName": "Parth",
    "groupId": 1,
    "groupName": "Pod Point - Software Team",
    "groupUid": "45c9ef22-b44b-4edd-bb66-392e923950af",
    "id": 4,
    "lastName": "Chandratreya",
    "status": "Registered",
  },
  {
    "activatedOn": StringMatching /\\^\\\\d\\{4\\}-\\\\d\\{2\\}-\\\\d\\{2\\}T\\\\d\\{2\\}:\\\\d\\{2\\}:\\\\d\\{2\\}\\\\\\.\\\\d\\{3\\}Z\\$/,
    "authId": "63200cfe-c2b0-4264-ab4d-c6654abf78c5",
    "email": "<EMAIL>",
    "emailBounced": false,
    "firstName": "Ewen",
    "groupId": 1,
    "groupName": "Pod Point - Software Team",
    "groupUid": "45c9ef22-b44b-4edd-bb66-392e923950af",
    "id": 3,
    "lastName": "Carr",
    "status": "Registered",
  },
  {
    "activatedOn": StringMatching /\\^\\\\d\\{4\\}-\\\\d\\{2\\}-\\\\d\\{2\\}T\\\\d\\{2\\}:\\\\d\\{2\\}:\\\\d\\{2\\}\\\\\\.\\\\d\\{3\\}Z\\$/,
    "authId": "41c07d75-105f-433d-816c-bc4f66f94700",
    "email": "<EMAIL>",
    "emailBounced": false,
    "firstName": "James",
    "groupId": 1,
    "groupName": "Pod Point - Software Team",
    "groupUid": "45c9ef22-b44b-4edd-bb66-392e923950af",
    "id": 2,
    "lastName": "Hardy",
    "status": "Registered",
  },
  {
    "activatedOn": StringMatching /\\^\\\\d\\{4\\}-\\\\d\\{2\\}-\\\\d\\{2\\}T\\\\d\\{2\\}:\\\\d\\{2\\}:\\\\d\\{2\\}\\\\\\.\\\\d\\{3\\}Z\\$/,
    "authId": "973b5195-c2b8-4de0-9eab-381618e3ff74",
    "email": "<EMAIL>",
    "emailBounced": false,
    "firstName": "Stuart",
    "groupId": 1,
    "groupName": "Pod Point - Software Team",
    "groupUid": "45c9ef22-b44b-4edd-bb66-392e923950af",
    "id": 1,
    "lastName": "McGregor",
    "status": "Registered",
  },
]
`;

exports[`destination site admin api admin module admin controller should find all user groups by auth id 1`] = `
[
  {
    "activatedOn": "2014-12-17T17:07:44.000Z",
    "contactName": "Cora Boyle",
    "contactNumber": "+44(0)026802495",
    "id": 188,
    "name": "Savills UK",
    "readOnly": false,
    "type": "scheme",
    "uid": "780d9f10-47e7-472a-8367-88a852458efe",
  },
  {
    "activatedOn": "2016-04-27T15:22:15.000Z",
    "contactName": "Ella O'Reilly",
    "contactNumber": "06718 264434",
    "id": 336,
    "name": "Coliseum Shopping Park",
    "readOnly": false,
    "type": "host",
    "uid": "b2de66af-834c-4dd6-b119-99d94ffc2abe",
  },
  {
    "activatedOn": "2016-10-24T13:51:37.000Z",
    "contactName": "Aidan",
    "contactNumber": "04932 856856",
    "id": 451,
    "name": "Gallagher Shopping",
    "readOnly": false,
    "type": "host",
    "uid": "087e9b6b-1d60-47a0-89e9-87ff0d54f719",
  },
]
`;

exports[`destination site admin api admin module admin controller with projections feature flag false should find user group by auth id 1`] = `
{
  "activatedOn": "2010-07-29T13:00:00.000Z",
  "contactName": "Alfie Oâ€™Connell",
  "contactNumber": "01788845456",
  "id": 2,
  "name": "Tesco Stores Ltd",
  "readOnly": false,
  "stats": {
    "chargingDuration": 22245,
    "co2Savings": 4325.62,
    "energyCost": 324550,
    "energyDelivered": 567.89,
    "numberOfChargers": 32,
    "numberOfCharges": 2048,
    "numberOfDrivers": 64,
    "numberOfSites": 16,
    "revenueGenerated": 457600,
  },
  "type": "host",
  "uid": "c9c5d39c-23ba-4a21-ac45-75968fdc055f",
}
`;

exports[`destination site admin api admin module admin controller with projections feature flag true should find user group by auth id 1`] = `
{
  "activatedOn": "2010-07-29T13:00:00.000Z",
  "contactName": "Alfie Oâ€™Connell",
  "contactNumber": "01788845456",
  "id": 2,
  "name": "Tesco Stores Ltd",
  "readOnly": false,
  "stats": {
    "chargingDuration": 22245,
    "co2Savings": 4325.62,
    "energyCost": 324550,
    "energyDelivered": 567.89,
    "numberOfChargers": 32,
    "numberOfCharges": 2048,
    "numberOfDrivers": 64,
    "numberOfSites": 16,
    "revenueGenerated": 457600,
  },
  "type": "host",
  "uid": "c9c5d39c-23ba-4a21-ac45-75968fdc055f",
}
`;

exports[`destination site admin api api should expose open api specification 1`] = `
{
  "components": {
    "schemas": {
      "AssignTariffPodsRequest": {
        "properties": {},
        "type": "object",
      },
      "ConfirmChargeRequest": {
        "properties": {},
        "type": "object",
      },
      "CreateAdminRequest": {
        "properties": {},
        "type": "object",
      },
      "CreateDayNightTariffScheduleRequest": {
        "properties": {},
        "type": "object",
      },
      "CreateDomainRequest": {
        "properties": {},
        "type": "object",
      },
      "CreateDriverRequest": {
        "properties": {},
        "type": "object",
      },
      "CreateSiteStatementRequest": {
        "properties": {},
        "type": "object",
      },
      "CreateTariffRequest": {
        "properties": {},
        "type": "object",
      },
      "CreateTariffScheduleRequest": {
        "properties": {},
        "type": "object",
      },
      "CreateWorkWeekTariffScheduleRequest": {
        "properties": {},
        "type": "object",
      },
      "DeleteDriverRequest": {
        "properties": {},
        "type": "object",
      },
      "DeleteTariffScheduleRequest": {
        "properties": {},
        "type": "object",
      },
      "InviteDriverRequest": {
        "properties": {},
        "type": "object",
      },
      "ProcessExpensesRequest": {
        "properties": {},
        "type": "object",
      },
      "SendEmailVerificationRequest": {
        "properties": {},
        "type": "object",
      },
      "SendPasswordResetRequest": {
        "properties": {},
        "type": "object",
      },
      "SendSignInWithEmailRequest": {
        "properties": {},
        "type": "object",
      },
      "UpdateAdditionalInfoRequest": {
        "properties": {},
        "type": "object",
      },
      "UpdateContactDetailsRequest": {
        "properties": {},
        "type": "object",
      },
      "UpdateDriverRequest": {
        "properties": {},
        "type": "object",
      },
      "UpdateEnergyCostRequest": {
        "properties": {},
        "type": "object",
      },
      "UpdateTariffRequest": {
        "properties": {},
        "type": "object",
      },
      "UpdateUserRequest": {
        "properties": {},
        "type": "object",
      },
      "UpsertOpeningTimesRequest": {
        "properties": {},
        "type": "object",
      },
    },
  },
  "info": {
    "contact": {},
    "description": "",
    "title": "",
    "version": "1.0.0",
  },
  "openapi": "3.0.0",
  "paths": {
    "/admins": {
      "get": {
        "operationId": "AdminController_findByGroupId",
        "parameters": [
          {
            "in": "query",
            "name": "groupId",
            "required": true,
            "schema": {
              "type": "number",
            },
          },
        ],
        "responses": {
          "200": {
            "description": "",
          },
        },
        "tags": [
          "Admin",
        ],
      },
      "post": {
        "operationId": "AdminController_createByGroupId",
        "parameters": [
          {
            "in": "query",
            "name": "groupId",
            "required": true,
            "schema": {
              "type": "number",
            },
          },
          {
            "in": "query",
            "name": "adminUid",
            "required": true,
            "schema": {
              "type": "string",
            },
          },
        ],
        "requestBody": {
          "content": {
            "application/json": {
              "schema": {
                "$ref": "#/components/schemas/CreateAdminRequest",
              },
            },
          },
          "required": true,
        },
        "responses": {
          "201": {
            "description": "",
          },
        },
        "tags": [
          "Admin",
        ],
      },
    },
    "/admins/email-verification": {
      "post": {
        "operationId": "EmailVerificationController_sendEmailVerification",
        "parameters": [],
        "requestBody": {
          "content": {
            "application/json": {
              "schema": {
                "$ref": "#/components/schemas/SendEmailVerificationRequest",
              },
            },
          },
          "required": true,
        },
        "responses": {
          "202": {
            "description": "",
          },
        },
        "tags": [
          "EmailVerification",
        ],
      },
    },
    "/admins/password-reset": {
      "post": {
        "operationId": "PasswordResetController_sendPasswordReset",
        "parameters": [],
        "requestBody": {
          "content": {
            "application/json": {
              "schema": {
                "$ref": "#/components/schemas/SendPasswordResetRequest",
              },
            },
          },
          "required": true,
        },
        "responses": {
          "202": {
            "description": "",
          },
        },
        "tags": [
          "PasswordReset",
        ],
      },
    },
    "/admins/sign-in-with-email": {
      "post": {
        "operationId": "SignInWithEmailController_sendSignInWithEmail",
        "parameters": [],
        "requestBody": {
          "content": {
            "application/json": {
              "schema": {
                "$ref": "#/components/schemas/SendSignInWithEmailRequest",
              },
            },
          },
          "required": true,
        },
        "responses": {
          "202": {
            "description": "",
          },
        },
        "tags": [
          "SignInWithEmail",
        ],
      },
    },
    "/admins/{id}": {
      "delete": {
        "operationId": "AdminController_deleteByGroupId",
        "parameters": [
          {
            "in": "path",
            "name": "id",
            "required": true,
            "schema": {
              "type": "number",
            },
          },
          {
            "in": "query",
            "name": "groupId",
            "required": true,
            "schema": {
              "type": "number",
            },
          },
          {
            "in": "query",
            "name": "adminId",
            "required": true,
            "schema": {
              "type": "number",
            },
          },
        ],
        "responses": {
          "204": {
            "description": "",
          },
        },
        "tags": [
          "Admin",
        ],
      },
    },
    "/admins/{id}/invitation": {
      "post": {
        "operationId": "AdminController_inviteByGroupIdAndDriverId",
        "parameters": [
          {
            "in": "path",
            "name": "id",
            "required": true,
            "schema": {
              "type": "number",
            },
          },
          {
            "in": "query",
            "name": "groupId",
            "required": true,
            "schema": {
              "type": "number",
            },
          },
          {
            "in": "query",
            "name": "adminUid",
            "required": true,
            "schema": {
              "type": "string",
            },
          },
        ],
        "responses": {
          "204": {
            "description": "",
          },
        },
        "tags": [
          "Admin",
        ],
      },
    },
    "/admins/{id}/status": {
      "post": {
        "operationId": "AdminController_reactivateByGroupIdAndAdminId",
        "parameters": [
          {
            "in": "path",
            "name": "id",
            "required": true,
            "schema": {
              "type": "number",
            },
          },
          {
            "in": "query",
            "name": "groupId",
            "required": true,
            "schema": {
              "type": "number",
            },
          },
          {
            "in": "query",
            "name": "adminId",
            "required": true,
            "schema": {
              "type": "number",
            },
          },
        ],
        "responses": {
          "200": {
            "description": "",
          },
        },
        "tags": [
          "Admin",
        ],
      },
    },
    "/billing": {
      "get": {
        "operationId": "BillingController_findBillingInformationByGroupUid",
        "parameters": [
          {
            "in": "query",
            "name": "groupUid",
            "required": true,
            "schema": {
              "type": "string",
            },
          },
        ],
        "responses": {
          "200": {
            "description": "",
          },
        },
        "tags": [
          "Billing",
        ],
      },
    },
    "/billing/invoices/{invoiceId}/pdf": {
      "get": {
        "operationId": "BillingInvoiceController_getInvoicePdf",
        "parameters": [
          {
            "in": "path",
            "name": "invoiceId",
            "required": true,
            "schema": {
              "type": "string",
            },
          },
        ],
        "responses": {
          "200": {
            "description": "",
          },
        },
        "tags": [
          "BillingInvoice",
        ],
      },
    },
    "/billing/statements/{statementId}/pdf": {
      "get": {
        "operationId": "BillingStatementController_getStatementPdf",
        "parameters": [
          {
            "in": "path",
            "name": "statementId",
            "required": true,
            "schema": {
              "type": "string",
            },
          },
        ],
        "responses": {
          "200": {
            "description": "",
          },
        },
        "tags": [
          "BillingStatement",
        ],
      },
    },
    "/domains": {
      "get": {
        "operationId": "DomainController_findByGroupId",
        "parameters": [
          {
            "in": "query",
            "name": "groupId",
            "required": true,
            "schema": {
              "type": "number",
            },
          },
        ],
        "responses": {
          "200": {
            "description": "",
          },
        },
        "tags": [
          "Domain",
        ],
      },
      "post": {
        "operationId": "DomainController_createByGroupId",
        "parameters": [
          {
            "in": "query",
            "name": "groupId",
            "required": true,
            "schema": {
              "type": "number",
            },
          },
        ],
        "requestBody": {
          "content": {
            "application/json": {
              "schema": {
                "$ref": "#/components/schemas/CreateDomainRequest",
              },
            },
          },
          "required": true,
        },
        "responses": {
          "201": {
            "description": "",
          },
        },
        "tags": [
          "Domain",
        ],
      },
    },
    "/domains/{domainId}": {
      "delete": {
        "operationId": "DomainController_deleteByGroupId",
        "parameters": [
          {
            "in": "query",
            "name": "groupId",
            "required": true,
            "schema": {
              "type": "number",
            },
          },
          {
            "in": "path",
            "name": "domainId",
            "required": true,
            "schema": {
              "type": "number",
            },
          },
        ],
        "responses": {
          "204": {
            "description": "",
          },
        },
        "tags": [
          "Domain",
        ],
      },
      "put": {
        "operationId": "DomainController_updateByGroupId",
        "parameters": [
          {
            "in": "query",
            "name": "groupId",
            "required": true,
            "schema": {
              "type": "number",
            },
          },
          {
            "in": "path",
            "name": "domainId",
            "required": true,
            "schema": {
              "type": "number",
            },
          },
        ],
        "requestBody": {
          "content": {
            "application/json": {
              "schema": {
                "$ref": "#/components/schemas/CreateDomainRequest",
              },
            },
          },
          "required": true,
        },
        "responses": {
          "204": {
            "description": "",
          },
        },
        "tags": [
          "Domain",
        ],
      },
    },
    "/drivers": {
      "get": {
        "operationId": "DriverController_findByGroupId",
        "parameters": [
          {
            "in": "query",
            "name": "groupId",
            "required": true,
            "schema": {
              "type": "number",
            },
          },
        ],
        "responses": {
          "200": {
            "description": "",
          },
        },
        "tags": [
          "Driver",
        ],
      },
      "post": {
        "operationId": "DriverController_createByGroupId",
        "parameters": [
          {
            "in": "query",
            "name": "groupId",
            "required": true,
            "schema": {
              "type": "number",
            },
          },
          {
            "in": "query",
            "name": "adminId",
            "required": true,
            "schema": {
              "type": "number",
            },
          },
        ],
        "requestBody": {
          "content": {
            "application/json": {
              "schema": {
                "$ref": "#/components/schemas/CreateDriverRequest",
              },
            },
          },
          "required": true,
        },
        "responses": {
          "201": {
            "description": "",
          },
        },
        "tags": [
          "Driver",
        ],
      },
    },
    "/drivers/upload": {
      "post": {
        "operationId": "DriverController_bulkCreateByGroupId",
        "parameters": [
          {
            "in": "query",
            "name": "groupId",
            "required": true,
            "schema": {
              "type": "number",
            },
          },
        ],
        "responses": {
          "201": {
            "description": "",
          },
        },
        "tags": [
          "Driver",
        ],
      },
    },
    "/drivers/{driverId}": {
      "delete": {
        "operationId": "DriverController_deleteByGroupId",
        "parameters": [
          {
            "in": "query",
            "name": "groupId",
            "required": true,
            "schema": {
              "type": "number",
            },
          },
          {
            "in": "path",
            "name": "driverId",
            "required": true,
            "schema": {
              "type": "number",
            },
          },
        ],
        "requestBody": {
          "content": {
            "application/json": {
              "schema": {
                "$ref": "#/components/schemas/DeleteDriverRequest",
              },
            },
          },
          "required": true,
        },
        "responses": {
          "204": {
            "description": "",
          },
        },
        "tags": [
          "Driver",
        ],
      },
      "put": {
        "operationId": "DriverController_updateByGroupId",
        "parameters": [
          {
            "in": "query",
            "name": "groupId",
            "required": true,
            "schema": {
              "type": "number",
            },
          },
          {
            "in": "path",
            "name": "driverId",
            "required": true,
            "schema": {
              "type": "number",
            },
          },
        ],
        "requestBody": {
          "content": {
            "application/json": {
              "schema": {
                "$ref": "#/components/schemas/UpdateDriverRequest",
              },
            },
          },
          "required": true,
        },
        "responses": {
          "204": {
            "description": "",
          },
        },
        "tags": [
          "Driver",
        ],
      },
    },
    "/drivers/{driverId}/charges": {
      "get": {
        "operationId": "DriverChargesController_findByGroupIdAndDriverId",
        "parameters": [
          {
            "in": "query",
            "name": "groupId",
            "required": true,
            "schema": {
              "type": "number",
            },
          },
          {
            "in": "query",
            "name": "groupUid",
            "required": true,
            "schema": {
              "type": "string",
            },
          },
          {
            "in": "query",
            "name": "tariffTier",
            "required": true,
            "schema": {
              "type": "string",
            },
          },
          {
            "in": "path",
            "name": "driverId",
            "required": true,
            "schema": {
              "type": "number",
            },
          },
        ],
        "responses": {
          "200": {
            "description": "",
          },
        },
        "tags": [
          "DriverCharges",
        ],
      },
    },
    "/drivers/{driverId}/charges/csv": {
      "get": {
        "operationId": "DriverChargesController_generateCsv",
        "parameters": [
          {
            "in": "query",
            "name": "groupId",
            "required": true,
            "schema": {
              "type": "number",
            },
          },
          {
            "in": "query",
            "name": "groupUid",
            "required": true,
            "schema": {
              "type": "string",
            },
          },
          {
            "in": "query",
            "name": "tariffTier",
            "required": true,
            "schema": {
              "type": "string",
            },
          },
          {
            "in": "path",
            "name": "driverId",
            "required": true,
            "schema": {
              "type": "number",
            },
          },
          {
            "in": "query",
            "name": "date",
            "required": true,
            "schema": {
              "type": "string",
            },
          },
          {
            "in": "query",
            "name": "all",
            "required": true,
            "schema": {
              "type": "string",
            },
          },
        ],
        "responses": {
          "200": {
            "description": "",
          },
        },
        "tags": [
          "DriverCharges",
        ],
      },
    },
    "/drivers/{driverId}/invitation": {
      "post": {
        "operationId": "DriverController_inviteByGroupIdAndDriverId",
        "parameters": [
          {
            "in": "query",
            "name": "groupId",
            "required": true,
            "schema": {
              "type": "number",
            },
          },
          {
            "in": "query",
            "name": "adminId",
            "required": true,
            "schema": {
              "type": "number",
            },
          },
          {
            "in": "path",
            "name": "driverId",
            "required": true,
            "schema": {
              "type": "number",
            },
          },
        ],
        "requestBody": {
          "content": {
            "application/json": {
              "schema": {
                "$ref": "#/components/schemas/InviteDriverRequest",
              },
            },
          },
          "required": true,
        },
        "responses": {
          "204": {
            "description": "",
          },
        },
        "tags": [
          "Driver",
        ],
      },
    },
    "/expenses/drivers/{driverId}": {
      "get": {
        "operationId": "ExpenseController_findAllByGroupUidAndDriverId",
        "parameters": [
          {
            "in": "query",
            "name": "groupUid",
            "required": true,
            "schema": {
              "type": "string",
            },
          },
          {
            "in": "path",
            "name": "driverId",
            "required": true,
            "schema": {
              "type": "number",
            },
          },
        ],
        "responses": {
          "200": {
            "description": "",
          },
        },
        "tags": [
          "Expense",
        ],
      },
    },
    "/expenses/new": {
      "get": {
        "operationId": "ExpenseController_findNewByGroup",
        "parameters": [
          {
            "in": "query",
            "name": "groupUid",
            "required": true,
            "schema": {
              "type": "string",
            },
          },
          {
            "in": "query",
            "name": "year",
            "required": true,
            "schema": {
              "type": "number",
            },
          },
          {
            "in": "query",
            "name": "month",
            "required": true,
            "schema": {
              "type": "number",
            },
          },
        ],
        "responses": {
          "200": {
            "description": "",
          },
        },
        "tags": [
          "Expense",
        ],
      },
    },
    "/expenses/new/grouped": {
      "get": {
        "operationId": "ExpenseController_findNewByGroupGroupedByDriver",
        "parameters": [
          {
            "in": "query",
            "name": "groupUid",
            "required": true,
            "schema": {
              "type": "string",
            },
          },
          {
            "in": "query",
            "name": "year",
            "required": true,
            "schema": {
              "type": "number",
            },
          },
          {
            "in": "query",
            "name": "month",
            "required": true,
            "schema": {
              "type": "number",
            },
          },
        ],
        "responses": {
          "200": {
            "description": "",
          },
        },
        "tags": [
          "Expense",
        ],
      },
    },
    "/expenses/process": {
      "post": {
        "operationId": "ExpenseController_markExpensesAsProcessed",
        "parameters": [
          {
            "in": "query",
            "name": "adminId",
            "required": true,
            "schema": {
              "type": "number",
            },
          },
          {
            "in": "query",
            "name": "groupUid",
            "required": true,
            "schema": {
              "type": "string",
            },
          },
        ],
        "requestBody": {
          "content": {
            "application/json": {
              "schema": {
                "$ref": "#/components/schemas/ProcessExpensesRequest",
              },
            },
          },
          "required": true,
        },
        "responses": {
          "201": {
            "description": "",
          },
        },
        "tags": [
          "Expense",
        ],
      },
    },
    "/expenses/processed": {
      "get": {
        "operationId": "ExpenseController_findProcessedByGroup",
        "parameters": [
          {
            "in": "query",
            "name": "groupUid",
            "required": true,
            "schema": {
              "type": "string",
            },
          },
          {
            "in": "query",
            "name": "year",
            "required": true,
            "schema": {
              "type": "number",
            },
          },
          {
            "in": "query",
            "name": "month",
            "required": true,
            "schema": {
              "type": "number",
            },
          },
        ],
        "responses": {
          "200": {
            "description": "",
          },
        },
        "tags": [
          "Expense",
        ],
      },
    },
    "/expenses/stats/monthly": {
      "get": {
        "operationId": "ExpenseController_getMonthlyUsageForOrganisation",
        "parameters": [
          {
            "in": "query",
            "name": "groupUid",
            "required": true,
            "schema": {
              "type": "string",
            },
          },
        ],
        "responses": {
          "200": {
            "description": "",
          },
        },
        "tags": [
          "Expense",
        ],
      },
    },
    "/health": {
      "get": {
        "operationId": "HealthController_check",
        "parameters": [],
        "responses": {
          "200": {
            "content": {
              "application/json": {
                "schema": {
                  "properties": {
                    "details": {
                      "additionalProperties": {
                        "additionalProperties": true,
                        "properties": {
                          "status": {
                            "type": "string",
                          },
                        },
                        "required": [
                          "status",
                        ],
                        "type": "object",
                      },
                      "example": {
                        "database": {
                          "status": "up",
                        },
                      },
                      "type": "object",
                    },
                    "error": {
                      "additionalProperties": {
                        "additionalProperties": true,
                        "properties": {
                          "status": {
                            "type": "string",
                          },
                        },
                        "required": [
                          "status",
                        ],
                        "type": "object",
                      },
                      "example": {},
                      "nullable": true,
                      "type": "object",
                    },
                    "info": {
                      "additionalProperties": {
                        "additionalProperties": true,
                        "properties": {
                          "status": {
                            "type": "string",
                          },
                        },
                        "required": [
                          "status",
                        ],
                        "type": "object",
                      },
                      "example": {
                        "database": {
                          "status": "up",
                        },
                      },
                      "nullable": true,
                      "type": "object",
                    },
                    "status": {
                      "example": "ok",
                      "type": "string",
                    },
                  },
                  "type": "object",
                },
              },
            },
            "description": "The Health Check is successful",
          },
          "503": {
            "content": {
              "application/json": {
                "schema": {
                  "properties": {
                    "details": {
                      "additionalProperties": {
                        "additionalProperties": true,
                        "properties": {
                          "status": {
                            "type": "string",
                          },
                        },
                        "required": [
                          "status",
                        ],
                        "type": "object",
                      },
                      "example": {
                        "database": {
                          "status": "up",
                        },
                        "redis": {
                          "message": "Could not connect",
                          "status": "down",
                        },
                      },
                      "type": "object",
                    },
                    "error": {
                      "additionalProperties": {
                        "additionalProperties": true,
                        "properties": {
                          "status": {
                            "type": "string",
                          },
                        },
                        "required": [
                          "status",
                        ],
                        "type": "object",
                      },
                      "example": {
                        "redis": {
                          "message": "Could not connect",
                          "status": "down",
                        },
                      },
                      "nullable": true,
                      "type": "object",
                    },
                    "info": {
                      "additionalProperties": {
                        "additionalProperties": true,
                        "properties": {
                          "status": {
                            "type": "string",
                          },
                        },
                        "required": [
                          "status",
                        ],
                        "type": "object",
                      },
                      "example": {
                        "database": {
                          "status": "up",
                        },
                      },
                      "nullable": true,
                      "type": "object",
                    },
                    "status": {
                      "example": "error",
                      "type": "string",
                    },
                  },
                  "type": "object",
                },
              },
            },
            "description": "The Health Check is not successful",
          },
        },
        "tags": [
          "Health",
        ],
      },
    },
    "/insights": {
      "get": {
        "operationId": "InsightsController_findByGroupUid",
        "parameters": [
          {
            "in": "query",
            "name": "groupUid",
            "required": true,
            "schema": {
              "type": "string",
            },
          },
          {
            "in": "query",
            "name": "year",
            "required": true,
            "schema": {
              "type": "number",
            },
          },
        ],
        "responses": {
          "200": {
            "description": "",
          },
        },
        "tags": [
          "Insights",
        ],
      },
    },
    "/insights/chargers/{chargerId}": {
      "get": {
        "operationId": "InsightsController_findByGroupUidAndChargerId",
        "parameters": [
          {
            "in": "query",
            "name": "groupUid",
            "required": true,
            "schema": {
              "type": "string",
            },
          },
          {
            "in": "query",
            "name": "year",
            "required": true,
            "schema": {
              "type": "number",
            },
          },
        ],
        "responses": {
          "200": {
            "description": "",
          },
        },
        "tags": [
          "Insights",
        ],
      },
    },
    "/insights/chargers/{chargerId}/csv": {
      "get": {
        "operationId": "InsightsController_generateCsvByGroupUidAndChargerId",
        "parameters": [
          {
            "in": "query",
            "name": "groupId",
            "required": true,
            "schema": {
              "type": "number",
            },
          },
          {
            "in": "query",
            "name": "groupUid",
            "required": true,
            "schema": {
              "type": "string",
            },
          },
          {
            "in": "query",
            "name": "year",
            "required": true,
            "schema": {
              "type": "number",
            },
          },
        ],
        "responses": {
          "200": {
            "description": "",
          },
        },
        "tags": [
          "Insights",
        ],
      },
    },
    "/insights/csv": {
      "get": {
        "operationId": "InsightsController_generateCsvByGroupUid",
        "parameters": [
          {
            "in": "query",
            "name": "groupId",
            "required": true,
            "schema": {
              "type": "number",
            },
          },
          {
            "in": "query",
            "name": "groupUid",
            "required": true,
            "schema": {
              "type": "string",
            },
          },
          {
            "in": "query",
            "name": "year",
            "required": true,
            "schema": {
              "type": "number",
            },
          },
        ],
        "responses": {
          "200": {
            "description": "",
          },
        },
        "tags": [
          "Insights",
        ],
      },
    },
    "/insights/sites/{siteId}": {
      "get": {
        "operationId": "InsightsController_findByGroupUidAndSiteId",
        "parameters": [
          {
            "in": "query",
            "name": "groupUid",
            "required": true,
            "schema": {
              "type": "string",
            },
          },
          {
            "in": "path",
            "name": "siteId",
            "required": true,
            "schema": {
              "type": "number",
            },
          },
          {
            "in": "query",
            "name": "year",
            "required": true,
            "schema": {
              "type": "number",
            },
          },
        ],
        "responses": {
          "200": {
            "description": "",
          },
        },
        "tags": [
          "Insights",
        ],
      },
    },
    "/insights/sites/{siteId}/csv": {
      "get": {
        "operationId": "InsightsController_generateCsvByGroupUidAndSiteId",
        "parameters": [
          {
            "in": "query",
            "name": "groupUid",
            "required": true,
            "schema": {
              "type": "string",
            },
          },
          {
            "in": "path",
            "name": "siteId",
            "required": true,
            "schema": {
              "type": "number",
            },
          },
          {
            "in": "query",
            "name": "year",
            "required": true,
            "schema": {
              "type": "number",
            },
          },
        ],
        "responses": {
          "200": {
            "description": "",
          },
        },
        "tags": [
          "Insights",
        ],
      },
    },
    "/pods": {
      "get": {
        "operationId": "PodController_findByGroupId",
        "parameters": [
          {
            "in": "query",
            "name": "groupId",
            "required": true,
            "schema": {
              "type": "number",
            },
          },
        ],
        "responses": {
          "200": {
            "description": "",
          },
        },
        "tags": [
          "Pod",
        ],
      },
    },
    "/pods/{podId}": {
      "get": {
        "operationId": "PodController_findByGroupIdAndPodId",
        "parameters": [
          {
            "in": "query",
            "name": "groupId",
            "required": true,
            "schema": {
              "type": "number",
            },
          },
          {
            "in": "path",
            "name": "podId",
            "required": true,
            "schema": {
              "type": "string",
            },
          },
        ],
        "responses": {
          "200": {
            "description": "",
          },
        },
        "tags": [
          "Pod",
        ],
      },
    },
    "/pods/{podId}/charges": {
      "get": {
        "operationId": "PodController_generateCsv",
        "parameters": [
          {
            "in": "query",
            "name": "groupId",
            "required": true,
            "schema": {
              "type": "number",
            },
          },
          {
            "in": "query",
            "name": "groupUid",
            "required": true,
            "schema": {
              "type": "string",
            },
          },
          {
            "in": "path",
            "name": "podId",
            "required": true,
            "schema": {
              "type": "number",
            },
          },
          {
            "in": "query",
            "name": "date",
            "required": true,
            "schema": {
              "type": "string",
            },
          },
        ],
        "responses": {
          "200": {
            "description": "",
          },
        },
        "tags": [
          "Pod",
        ],
      },
      "post": {
        "operationId": "PodChargesController_confirmChargeByGroupIdAndPodId",
        "parameters": [
          {
            "in": "query",
            "name": "groupId",
            "required": true,
            "schema": {
              "type": "number",
            },
          },
          {
            "in": "query",
            "name": "adminId",
            "required": true,
            "schema": {
              "type": "number",
            },
          },
          {
            "in": "path",
            "name": "podId",
            "required": true,
            "schema": {
              "type": "number",
            },
          },
        ],
        "requestBody": {
          "content": {
            "application/json": {
              "schema": {
                "$ref": "#/components/schemas/ConfirmChargeRequest",
              },
            },
          },
          "required": true,
        },
        "responses": {
          "204": {
            "description": "",
          },
        },
        "tags": [
          "PodCharges",
        ],
      },
    },
    "/pods/{podId}/events/security": {
      "get": {
        "operationId": "PodController_findSecurityEventsByGroupIdAndPodId",
        "parameters": [
          {
            "in": "path",
            "name": "podId",
            "required": true,
            "schema": {
              "type": "string",
            },
          },
          {
            "in": "query",
            "name": "groupId",
            "required": true,
            "schema": {
              "type": "number",
            },
          },
        ],
        "responses": {
          "200": {
            "description": "",
          },
        },
        "tags": [
          "Pod",
        ],
      },
    },
    "/pods/{podId}/tariff": {
      "delete": {
        "operationId": "PodTariffController_removeByGroupId",
        "parameters": [
          {
            "in": "query",
            "name": "groupId",
            "required": true,
            "schema": {
              "type": "number",
            },
          },
          {
            "in": "path",
            "name": "podId",
            "required": true,
            "schema": {
              "type": "number",
            },
          },
        ],
        "responses": {
          "204": {
            "description": "",
          },
        },
        "tags": [
          "PodTariff",
        ],
      },
      "put": {
        "operationId": "PodTariffController_updateByGroupId",
        "parameters": [
          {
            "in": "query",
            "name": "groupId",
            "required": true,
            "schema": {
              "type": "number",
            },
          },
          {
            "in": "path",
            "name": "podId",
            "required": true,
            "schema": {
              "type": "number",
            },
          },
        ],
        "requestBody": {
          "content": {
            "application/json": {
              "schema": {
                "$ref": "#/components/schemas/UpdateTariffRequest",
              },
            },
          },
          "required": true,
        },
        "responses": {
          "204": {
            "description": "",
          },
        },
        "tags": [
          "PodTariff",
        ],
      },
    },
    "/sites": {
      "get": {
        "operationId": "SiteController_findByGroupUid",
        "parameters": [
          {
            "in": "query",
            "name": "groupUid",
            "required": true,
            "schema": {
              "type": "string",
            },
          },
        ],
        "responses": {
          "200": {
            "description": "",
          },
        },
        "tags": [
          "Site",
        ],
      },
    },
    "/sites/stats/csv": {
      "get": {
        "operationId": "StatsController_generateCsv",
        "parameters": [
          {
            "in": "query",
            "name": "groupId",
            "required": true,
            "schema": {
              "type": "number",
            },
          },
          {
            "in": "query",
            "name": "date",
            "required": true,
            "schema": {
              "type": "string",
            },
          },
        ],
        "responses": {
          "200": {
            "description": "",
          },
        },
        "tags": [
          "Stats",
        ],
      },
    },
    "/sites/{siteId}": {
      "get": {
        "operationId": "SiteController_findByGroupUidAndSiteId",
        "parameters": [
          {
            "in": "query",
            "name": "groupUid",
            "required": true,
            "schema": {
              "type": "string",
            },
          },
          {
            "in": "path",
            "name": "siteId",
            "required": true,
            "schema": {
              "type": "number",
            },
          },
        ],
        "responses": {
          "200": {
            "description": "",
          },
        },
        "tags": [
          "Site",
        ],
      },
    },
    "/sites/{siteId}/additional-information": {
      "post": {
        "operationId": "AdditionalInfoController_upsertByGroupUid",
        "parameters": [
          {
            "in": "query",
            "name": "groupUid",
            "required": true,
            "schema": {
              "type": "string",
            },
          },
          {
            "in": "path",
            "name": "siteId",
            "required": true,
            "schema": {
              "type": "number",
            },
          },
        ],
        "requestBody": {
          "content": {
            "application/json": {
              "schema": {
                "$ref": "#/components/schemas/UpdateAdditionalInfoRequest",
              },
            },
          },
          "required": true,
        },
        "responses": {
          "201": {
            "description": "",
          },
        },
        "tags": [
          "AdditionalInfo",
        ],
      },
    },
    "/sites/{siteId}/charges": {
      "get": {
        "operationId": "SiteController_generateCsv",
        "parameters": [
          {
            "in": "query",
            "name": "groupUid",
            "required": true,
            "schema": {
              "type": "string",
            },
          },
          {
            "in": "path",
            "name": "siteId",
            "required": true,
            "schema": {
              "type": "number",
            },
          },
          {
            "in": "query",
            "name": "date",
            "required": true,
            "schema": {
              "type": "string",
            },
          },
        ],
        "responses": {
          "200": {
            "description": "",
          },
        },
        "tags": [
          "Site",
        ],
      },
    },
    "/sites/{siteId}/contact-details": {
      "put": {
        "operationId": "ContactDetailsController_updateContactDetailsByGroupUidAndSiteId",
        "parameters": [
          {
            "in": "query",
            "name": "groupUid",
            "required": true,
            "schema": {
              "type": "string",
            },
          },
          {
            "in": "path",
            "name": "siteId",
            "required": true,
            "schema": {
              "type": "number",
            },
          },
        ],
        "requestBody": {
          "content": {
            "application/json": {
              "schema": {
                "$ref": "#/components/schemas/UpdateContactDetailsRequest",
              },
            },
          },
          "required": true,
        },
        "responses": {
          "204": {
            "description": "",
          },
        },
        "tags": [
          "ContactDetails",
        ],
      },
    },
    "/sites/{siteId}/energy-cost": {
      "put": {
        "operationId": "EnergyCostController_updateContactDetailsByGroupUidAndSiteId",
        "parameters": [
          {
            "in": "query",
            "name": "groupUid",
            "required": true,
            "schema": {
              "type": "string",
            },
          },
          {
            "in": "path",
            "name": "siteId",
            "required": true,
            "schema": {
              "type": "number",
            },
          },
        ],
        "requestBody": {
          "content": {
            "application/json": {
              "schema": {
                "$ref": "#/components/schemas/UpdateEnergyCostRequest",
              },
            },
          },
          "required": true,
        },
        "responses": {
          "204": {
            "description": "",
          },
        },
        "tags": [
          "EnergyCost",
        ],
      },
    },
    "/sites/{siteId}/opening-times": {
      "post": {
        "operationId": "OpeningTimesController_upsertByGroupUid",
        "parameters": [
          {
            "in": "query",
            "name": "groupUid",
            "required": true,
            "schema": {
              "type": "string",
            },
          },
          {
            "in": "path",
            "name": "siteId",
            "required": true,
            "schema": {
              "type": "number",
            },
          },
        ],
        "requestBody": {
          "content": {
            "application/json": {
              "schema": {
                "$ref": "#/components/schemas/UpsertOpeningTimesRequest",
              },
            },
          },
          "required": true,
        },
        "responses": {
          "201": {
            "description": "",
          },
        },
        "tags": [
          "OpeningTimes",
        ],
      },
    },
    "/sites/{siteId}/statement": {
      "get": {
        "operationId": "StatementController_findByGroupUidAndSiteId",
        "parameters": [
          {
            "in": "query",
            "name": "groupUid",
            "required": true,
            "schema": {
              "type": "string",
            },
          },
          {
            "in": "path",
            "name": "siteId",
            "required": true,
            "schema": {
              "type": "number",
            },
          },
          {
            "in": "query",
            "name": "period",
            "required": true,
            "schema": {
              "type": "number",
            },
          },
        ],
        "responses": {
          "200": {
            "description": "",
          },
        },
        "tags": [
          "Statement",
        ],
      },
      "post": {
        "operationId": "StatementController_createByGroupUidAndSiteId",
        "parameters": [
          {
            "in": "query",
            "name": "groupUid",
            "required": true,
            "schema": {
              "type": "string",
            },
          },
          {
            "in": "path",
            "name": "siteId",
            "required": true,
            "schema": {
              "type": "number",
            },
          },
          {
            "in": "query",
            "name": "period",
            "required": true,
            "schema": {
              "type": "number",
            },
          },
          {
            "in": "query",
            "name": "dateOverride",
            "required": true,
            "schema": {
              "type": "string",
            },
          },
        ],
        "requestBody": {
          "content": {
            "application/json": {
              "schema": {
                "$ref": "#/components/schemas/CreateSiteStatementRequest",
              },
            },
          },
          "required": true,
        },
        "responses": {
          "201": {
            "description": "",
          },
        },
        "tags": [
          "Statement",
        ],
      },
    },
    "/tariffs": {
      "get": {
        "operationId": "TariffController_findAll",
        "parameters": [
          {
            "in": "query",
            "name": "groupId",
            "required": true,
            "schema": {
              "type": "number",
            },
          },
        ],
        "responses": {
          "200": {
            "description": "",
          },
        },
        "tags": [
          "Tariff",
        ],
      },
      "post": {
        "operationId": "TariffController_createByGroupId",
        "parameters": [
          {
            "in": "query",
            "name": "groupId",
            "required": true,
            "schema": {
              "type": "number",
            },
          },
        ],
        "requestBody": {
          "content": {
            "application/json": {
              "schema": {
                "$ref": "#/components/schemas/CreateTariffRequest",
              },
            },
          },
          "required": true,
        },
        "responses": {
          "201": {
            "description": "",
          },
        },
        "tags": [
          "Tariff",
        ],
      },
    },
    "/tariffs/{tariffId}": {
      "delete": {
        "operationId": "TariffController_deleteByGroupId",
        "parameters": [
          {
            "in": "query",
            "name": "groupId",
            "required": true,
            "schema": {
              "type": "number",
            },
          },
          {
            "in": "path",
            "name": "tariffId",
            "required": true,
            "schema": {
              "type": "number",
            },
          },
        ],
        "responses": {
          "204": {
            "description": "",
          },
        },
        "tags": [
          "Tariff",
        ],
      },
      "get": {
        "operationId": "TariffController_findByGroupIdAndTariffId",
        "parameters": [
          {
            "in": "query",
            "name": "groupId",
            "required": true,
            "schema": {
              "type": "number",
            },
          },
          {
            "in": "path",
            "name": "tariffId",
            "required": true,
            "schema": {
              "type": "number",
            },
          },
        ],
        "responses": {
          "200": {
            "description": "",
          },
        },
        "tags": [
          "Tariff",
        ],
      },
      "put": {
        "operationId": "TariffController_updateByGroupId",
        "parameters": [
          {
            "in": "query",
            "name": "groupId",
            "required": true,
            "schema": {
              "type": "number",
            },
          },
          {
            "in": "path",
            "name": "tariffId",
            "required": true,
            "schema": {
              "type": "number",
            },
          },
        ],
        "requestBody": {
          "content": {
            "application/json": {
              "schema": {
                "$ref": "#/components/schemas/CreateTariffRequest",
              },
            },
          },
          "required": true,
        },
        "responses": {
          "204": {
            "description": "",
          },
        },
        "tags": [
          "Tariff",
        ],
      },
    },
    "/tariffs/{tariffId}/pods": {
      "put": {
        "operationId": "TariffPodController_updateByGroupIdAndTariffId",
        "parameters": [
          {
            "in": "query",
            "name": "groupId",
            "required": true,
            "schema": {
              "type": "number",
            },
          },
          {
            "in": "path",
            "name": "tariffId",
            "required": true,
            "schema": {
              "type": "number",
            },
          },
        ],
        "requestBody": {
          "content": {
            "application/json": {
              "schema": {
                "$ref": "#/components/schemas/AssignTariffPodsRequest",
              },
            },
          },
          "required": true,
        },
        "responses": {
          "204": {
            "description": "",
          },
        },
        "tags": [
          "TariffPod",
        ],
      },
    },
    "/tariffs/{tariffId}/schedules": {
      "delete": {
        "operationId": "ScheduleController_deleteByGroupId",
        "parameters": [
          {
            "in": "query",
            "name": "groupId",
            "required": true,
            "schema": {
              "type": "number",
            },
          },
          {
            "in": "path",
            "name": "tariffId",
            "required": true,
            "schema": {
              "type": "number",
            },
          },
        ],
        "requestBody": {
          "content": {
            "application/json": {
              "schema": {
                "$ref": "#/components/schemas/DeleteTariffScheduleRequest",
              },
            },
          },
          "required": true,
        },
        "responses": {
          "204": {
            "description": "",
          },
        },
        "tags": [
          "Schedule",
        ],
      },
      "post": {
        "operationId": "ScheduleController_createScheduleByGroupIdAndTariffId",
        "parameters": [
          {
            "in": "query",
            "name": "groupId",
            "required": true,
            "schema": {
              "type": "number",
            },
          },
          {
            "in": "path",
            "name": "tariffId",
            "required": true,
            "schema": {
              "type": "number",
            },
          },
        ],
        "requestBody": {
          "content": {
            "application/json": {
              "schema": {
                "$ref": "#/components/schemas/CreateTariffScheduleRequest",
              },
            },
          },
          "required": true,
        },
        "responses": {
          "201": {
            "description": "",
          },
        },
        "tags": [
          "Schedule",
        ],
      },
    },
    "/tariffs/{tariffId}/schedules/daynight": {
      "post": {
        "operationId": "ScheduleController_createDayNightScheduleByGroupIdAndTariffId",
        "parameters": [
          {
            "in": "query",
            "name": "groupId",
            "required": true,
            "schema": {
              "type": "number",
            },
          },
          {
            "in": "path",
            "name": "tariffId",
            "required": true,
            "schema": {
              "type": "number",
            },
          },
        ],
        "requestBody": {
          "content": {
            "application/json": {
              "schema": {
                "$ref": "#/components/schemas/CreateDayNightTariffScheduleRequest",
              },
            },
          },
          "required": true,
        },
        "responses": {
          "201": {
            "description": "",
          },
        },
        "tags": [
          "Schedule",
        ],
      },
    },
    "/tariffs/{tariffId}/schedules/workweek": {
      "post": {
        "operationId": "ScheduleController_createWorkWeekScheduleByGroupIdAndTariffId",
        "parameters": [
          {
            "in": "query",
            "name": "groupId",
            "required": true,
            "schema": {
              "type": "number",
            },
          },
          {
            "in": "path",
            "name": "tariffId",
            "required": true,
            "schema": {
              "type": "number",
            },
          },
        ],
        "requestBody": {
          "content": {
            "application/json": {
              "schema": {
                "$ref": "#/components/schemas/CreateWorkWeekTariffScheduleRequest",
              },
            },
          },
          "required": true,
        },
        "responses": {
          "201": {
            "description": "",
          },
        },
        "tags": [
          "Schedule",
        ],
      },
    },
    "/tariffs/{tariffId}/schedules/{scheduleId}": {
      "put": {
        "operationId": "ScheduleController_updateScheduleByGroupIdAndTariffId",
        "parameters": [
          {
            "in": "query",
            "name": "groupId",
            "required": true,
            "schema": {
              "type": "number",
            },
          },
          {
            "in": "path",
            "name": "tariffId",
            "required": true,
            "schema": {
              "type": "number",
            },
          },
          {
            "in": "path",
            "name": "scheduleId",
            "required": true,
            "schema": {
              "type": "number",
            },
          },
        ],
        "requestBody": {
          "content": {
            "application/json": {
              "schema": {
                "$ref": "#/components/schemas/CreateTariffScheduleRequest",
              },
            },
          },
          "required": true,
        },
        "responses": {
          "204": {
            "description": "",
          },
        },
        "tags": [
          "Schedule",
        ],
      },
    },
    "/user": {
      "get": {
        "operationId": "UserController_findByUserId",
        "parameters": [
          {
            "in": "query",
            "name": "adminUid",
            "required": true,
            "schema": {
              "type": "string",
            },
          },
        ],
        "responses": {
          "200": {
            "description": "",
          },
        },
        "tags": [
          "User",
        ],
      },
      "put": {
        "operationId": "UserController_updateByUserUid",
        "parameters": [
          {
            "in": "query",
            "name": "adminUid",
            "required": true,
            "schema": {
              "type": "string",
            },
          },
        ],
        "requestBody": {
          "content": {
            "application/json": {
              "schema": {
                "$ref": "#/components/schemas/UpdateUserRequest",
              },
            },
          },
          "required": true,
        },
        "responses": {
          "204": {
            "description": "",
          },
        },
        "tags": [
          "User",
        ],
      },
    },
    "/user/group": {
      "get": {
        "operationId": "GroupController_findByGroupId",
        "parameters": [
          {
            "in": "query",
            "name": "groupId",
            "required": true,
            "schema": {
              "type": "number",
            },
          },
        ],
        "responses": {
          "200": {
            "description": "",
          },
        },
        "tags": [
          "Group",
        ],
      },
    },
    "/user/group/charges/csv": {
      "get": {
        "operationId": "GroupController_generateCsv",
        "parameters": [
          {
            "in": "query",
            "name": "groupId",
            "required": true,
            "schema": {
              "type": "number",
            },
          },
          {
            "in": "query",
            "name": "date",
            "required": true,
            "schema": {
              "type": "string",
            },
          },
        ],
        "responses": {
          "200": {
            "description": "",
          },
        },
        "tags": [
          "Group",
        ],
      },
    },
    "/user/groups": {
      "get": {
        "operationId": "GroupController_findAllByGroupId",
        "parameters": [
          {
            "in": "query",
            "name": "groupId",
            "required": true,
            "schema": {
              "type": "number",
            },
          },
        ],
        "responses": {
          "200": {
            "description": "",
          },
        },
        "tags": [
          "Group",
        ],
      },
    },
    "/user/terms-and-conditions/acceptance": {
      "put": {
        "operationId": "UserController_acceptTermsAndConditions",
        "parameters": [
          {
            "in": "query",
            "name": "adminUid",
            "required": true,
            "schema": {
              "type": "string",
            },
          },
        ],
        "responses": {
          "204": {
            "description": "",
          },
        },
        "tags": [
          "User",
        ],
      },
    },
    "/version": {
      "get": {
        "operationId": "VersionController_getVersion",
        "parameters": [],
        "responses": {
          "200": {
            "description": "application version",
          },
        },
        "summary": "get application version",
        "tags": [
          "Version",
        ],
      },
    },
  },
  "servers": [],
  "tags": [],
}
`;

exports[`destination site admin api billing module billing controller should find billing information 1`] = `
{
  "customerDetails": {
    "accountReference": "*********",
    "address": {
      "county": "Test County",
      "line1": "Flat 1",
      "line2": "1 Test Street",
      "postcode": "TE1 1ST",
      "town": "Test Town",
    },
    "email": "<EMAIL>",
    "name": "Test Group Inc Invoice office",
    "poNumber": "UK10010001",
  },
  "statements": [
    {
      "feeInvoiceNumber": "ADF-1001",
      "feeInvoiceStatus": "open",
      "invoiceId": "5ab6cbf4-9902-4942-a052-b6a62bcdbaac",
      "month": "2023-01-13",
      "revenuePayoutStatus": "PENDING",
      "siteName": "Site 1",
      "statementId": "9bd247e0-92b9-11ee-b9d1-0242ac120002",
    },
    {
      "feeInvoiceNumber": "ADF-1002",
      "feeInvoiceStatus": "overdue",
      "invoiceId": "9be0da46-1462-42b5-85b1-d43cf64f339e",
      "month": "2023-02-13",
      "revenuePayoutStatus": "TRANSFERRED",
      "siteName": "Site 1",
      "statementId": "369b1302-92b9-11ee-b9d1-0242ac120002",
    },
    {
      "feeInvoiceNumber": "ADF-1003",
      "feeInvoiceStatus": "paid",
      "invoiceId": "ff407cc1-6898-4559-a220-ae66acb3f391",
      "month": "2023-02-13",
      "revenuePayoutStatus": "PAID_OUT",
      "siteName": "Banner Street",
      "statementId": "f7c7552c-92b9-11ee-b9d1-0242ac120002",
    },
    {
      "feeInvoiceNumber": "ADF-1004",
      "feeInvoiceStatus": "paid",
      "invoiceId": "ff407cc1-6898-4559-a220-ae66acb3f111",
      "month": "2023-02-13",
      "revenuePayoutStatus": "WARNING",
      "siteName": "Banner Street",
      "statementId": "f7c7552c-92b9-11ee-b9d1-0242ac121234",
    },
  ],
  "subscription": {
    "chargers": [
      {
        "ppid": "PG-70500",
        "socket": "A",
      },
    ],
    "invoices": [
      {
        "amount": 50,
        "created": "2022-01-14",
        "customerEmail": "<EMAIL>",
        "due": "2022-02-11",
        "hostedInvoiceUrl": "https://stripe.com/invoice?id=in_NggdGfEfND3Bfs",
        "invoiceNumber": "ADF-1001",
        "invoicePdfUrl": "https://stripe.com/invoice.pdf",
        "status": "paid",
      },
    ],
    "status": "active",
  },
}
`;

exports[`destination site admin api billing module billing invoices controller should find billing invoice pdf 1`] = `"Hello!"`;

exports[`destination site admin api billing module billing statements controller should find billing statement pdf 1`] = `"Hello!"`;

exports[`destination site admin api domain module domain controller should add a new domain 1`] = `
{
  "activatedOn": StringMatching /\\^\\(\\\\d\\{4\\}\\)-\\(\\\\d\\{2\\}\\)-\\(\\\\d\\{2\\}\\)T\\(\\\\d\\{2\\}\\):\\(\\\\d\\{2\\}\\):\\(\\\\d\\{2\\}\\(\\?:\\\\\\.\\\\d\\*\\)\\?\\)\\(\\(-\\(\\\\d\\{2\\}\\):\\(\\\\d\\{2\\}\\)\\|Z\\)\\?\\)\\$/,
  "domainName": "testing.net",
  "groupId": 30,
  "id": Any<Number>,
}
`;

exports[`destination site admin api domain module domain controller should delete an existing domain 1`] = `
{
  "data": [
    {
      "activatedOn": StringMatching /\\^\\(\\\\d\\{4\\}\\)-\\(\\\\d\\{2\\}\\)-\\(\\\\d\\{2\\}\\)T\\(\\\\d\\{2\\}\\):\\(\\\\d\\{2\\}\\):\\(\\\\d\\{2\\}\\(\\?:\\\\\\.\\\\d\\*\\)\\?\\)\\(\\(-\\(\\\\d\\{2\\}\\):\\(\\\\d\\{2\\}\\)\\|Z\\)\\?\\)\\$/,
      "domainName": "testing.net",
      "groupId": 30,
      "id": Any<Number>,
    },
  ],
}
`;

exports[`destination site admin api domain module domain controller should edit an existing domain 1`] = `
{
  "data": [
    {
      "activatedOn": StringMatching /\\^\\(\\\\d\\{4\\}\\)-\\(\\\\d\\{2\\}\\)-\\(\\\\d\\{2\\}\\)T\\(\\\\d\\{2\\}\\):\\(\\\\d\\{2\\}\\):\\(\\\\d\\{2\\}\\(\\?:\\\\\\.\\\\d\\*\\)\\?\\)\\(\\(-\\(\\\\d\\{2\\}\\):\\(\\\\d\\{2\\}\\)\\|Z\\)\\?\\)\\$/,
      "domainName": "testing2.com",
      "groupId": 30,
      "id": Any<Number>,
    },
    {
      "activatedOn": StringMatching /\\^\\(\\\\d\\{4\\}\\)-\\(\\\\d\\{2\\}\\)-\\(\\\\d\\{2\\}\\)T\\(\\\\d\\{2\\}\\):\\(\\\\d\\{2\\}\\):\\(\\\\d\\{2\\}\\(\\?:\\\\\\.\\\\d\\*\\)\\?\\)\\(\\(-\\(\\\\d\\{2\\}\\):\\(\\\\d\\{2\\}\\)\\|Z\\)\\?\\)\\$/,
      "domainName": "testing.net",
      "groupId": 30,
      "id": Any<Number>,
    },
  ],
}
`;

exports[`destination site admin api domain module domain controller should find a list of domains 1`] = `
[
  {
    "activatedOn": "2016-07-05T09:40:11.000Z",
    "domainName": "lexautolease.co.uk",
    "groupId": 30,
    "id": 23,
  },
]
`;

exports[`destination site admin api driver module bulk upload should error if the csv contains duplicates (%s) 1`] = `
[
  {
    "canExpense": false,
    "email": "<EMAIL>",
    "emailBounced": false,
    "firstName": "Bart",
    "fullName": "Bart Simpson",
    "group": {
      "id": 11671,
      "name": "Lex Autolease",
      "uid": "c7668cd2-**************-fcdbf6461e0c",
    },
    "id": Any<Number>,
    "lastName": "Simpson",
    "registeredDate": StringMatching /\\^\\\\d\\{4\\}-\\\\d\\{2\\}-\\\\d\\{2\\}T\\\\d\\{2\\}:\\\\d\\{2\\}:\\\\d\\{2\\}\\\\\\.\\\\d\\{3\\}Z\\$/,
    "status": "Pending",
    "tariffTier": "Member",
  },
  {
    "canExpense": false,
    "email": "<EMAIL>",
    "emailBounced": false,
    "firstName": "Homer",
    "fullName": "Homer Simpson",
    "group": {
      "id": 11670,
      "name": "Lex Autolease",
      "uid": "c7668cd2-**************-fcdbf6461e0c",
    },
    "id": Any<Number>,
    "lastName": "Simpson",
    "registeredDate": StringMatching /\\^\\\\d\\{4\\}-\\\\d\\{2\\}-\\\\d\\{2\\}T\\\\d\\{2\\}:\\\\d\\{2\\}:\\\\d\\{2\\}\\\\\\.\\\\d\\{3\\}Z\\$/,
    "status": "Pending",
    "tariffTier": "Member",
  },
  {
    "canExpense": true,
    "email": "<EMAIL>",
    "emailBounced": false,
    "firstName": "Jane",
    "fullName": "Jane Bloggs",
    "group": {
      "id": 100129,
      "name": "Lex Autolease",
      "uid": "c7668cd2-**************-fcdbf6461e0c",
    },
    "id": Any<Number>,
    "lastName": "Bloggs",
    "registeredDate": StringMatching /\\^\\\\d\\{4\\}-\\\\d\\{2\\}-\\\\d\\{2\\}T\\\\d\\{2\\}:\\\\d\\{2\\}:\\\\d\\{2\\}\\\\\\.\\\\d\\{3\\}Z\\$/,
    "status": "Pending",
    "tariffTier": "Driver",
  },
  {
    "canExpense": true,
    "email": "<EMAIL>",
    "emailBounced": false,
    "firstName": "Jane",
    "fullName": "Jane Bloggs",
    "group": {
      "id": 100128,
      "name": "Lex Autolease",
      "uid": "c7668cd2-**************-fcdbf6461e0c",
    },
    "id": Any<Number>,
    "lastName": "Bloggs",
    "registeredDate": StringMatching /\\^\\\\d\\{4\\}-\\\\d\\{2\\}-\\\\d\\{2\\}T\\\\d\\{2\\}:\\\\d\\{2\\}:\\\\d\\{2\\}\\\\\\.\\\\d\\{3\\}Z\\$/,
    "status": "Pending",
    "tariffTier": "Driver",
  },
  {
    "canExpense": true,
    "email": "<EMAIL>",
    "emailBounced": false,
    "firstName": "Robert",
    "fullName": "Robert Carlyle",
    "group": {
      "id": 100130,
      "name": "Lex Autolease",
      "uid": "c7668cd2-**************-fcdbf6461e0c",
    },
    "id": Any<Number>,
    "lastName": "Carlyle",
    "registeredDate": StringMatching /\\^\\\\d\\{4\\}-\\\\d\\{2\\}-\\\\d\\{2\\}T\\\\d\\{2\\}:\\\\d\\{2\\}:\\\\d\\{2\\}\\\\\\.\\\\d\\{3\\}Z\\$/,
    "status": "Pending",
    "tariffTier": "Driver",
  },
]
`;

exports[`destination site admin api driver module bulk upload should error if the csv contains errors (%s) 1`] = `
[
  {
    "canExpense": false,
    "email": "<EMAIL>",
    "emailBounced": false,
    "firstName": "Bart",
    "fullName": "Bart Simpson",
    "group": {
      "id": 11671,
      "name": "Lex Autolease",
      "uid": "c7668cd2-**************-fcdbf6461e0c",
    },
    "id": Any<Number>,
    "lastName": "Simpson",
    "registeredDate": StringMatching /\\^\\\\d\\{4\\}-\\\\d\\{2\\}-\\\\d\\{2\\}T\\\\d\\{2\\}:\\\\d\\{2\\}:\\\\d\\{2\\}\\\\\\.\\\\d\\{3\\}Z\\$/,
    "status": "Pending",
    "tariffTier": "Member",
  },
  {
    "canExpense": false,
    "email": "<EMAIL>",
    "emailBounced": false,
    "firstName": "Homer",
    "fullName": "Homer Simpson",
    "group": {
      "id": 11670,
      "name": "Lex Autolease",
      "uid": "c7668cd2-**************-fcdbf6461e0c",
    },
    "id": Any<Number>,
    "lastName": "Simpson",
    "registeredDate": StringMatching /\\^\\\\d\\{4\\}-\\\\d\\{2\\}-\\\\d\\{2\\}T\\\\d\\{2\\}:\\\\d\\{2\\}:\\\\d\\{2\\}\\\\\\.\\\\d\\{3\\}Z\\$/,
    "status": "Pending",
    "tariffTier": "Member",
  },
  {
    "canExpense": true,
    "email": "<EMAIL>",
    "emailBounced": false,
    "firstName": "Jane",
    "fullName": "Jane Bloggs",
    "group": {
      "id": 100129,
      "name": "Lex Autolease",
      "uid": "c7668cd2-**************-fcdbf6461e0c",
    },
    "id": Any<Number>,
    "lastName": "Bloggs",
    "registeredDate": StringMatching /\\^\\\\d\\{4\\}-\\\\d\\{2\\}-\\\\d\\{2\\}T\\\\d\\{2\\}:\\\\d\\{2\\}:\\\\d\\{2\\}\\\\\\.\\\\d\\{3\\}Z\\$/,
    "status": "Pending",
    "tariffTier": "Driver",
  },
  {
    "canExpense": true,
    "email": "<EMAIL>",
    "emailBounced": false,
    "firstName": "Jane",
    "fullName": "Jane Bloggs",
    "group": {
      "id": 100128,
      "name": "Lex Autolease",
      "uid": "c7668cd2-**************-fcdbf6461e0c",
    },
    "id": Any<Number>,
    "lastName": "Bloggs",
    "registeredDate": StringMatching /\\^\\\\d\\{4\\}-\\\\d\\{2\\}-\\\\d\\{2\\}T\\\\d\\{2\\}:\\\\d\\{2\\}:\\\\d\\{2\\}\\\\\\.\\\\d\\{3\\}Z\\$/,
    "status": "Pending",
    "tariffTier": "Driver",
  },
  {
    "canExpense": true,
    "email": "<EMAIL>",
    "emailBounced": false,
    "firstName": "Robert",
    "fullName": "Robert Carlyle",
    "group": {
      "id": 100130,
      "name": "Lex Autolease",
      "uid": "c7668cd2-**************-fcdbf6461e0c",
    },
    "id": Any<Number>,
    "lastName": "Carlyle",
    "registeredDate": StringMatching /\\^\\\\d\\{4\\}-\\\\d\\{2\\}-\\\\d\\{2\\}T\\\\d\\{2\\}:\\\\d\\{2\\}:\\\\d\\{2\\}\\\\\\.\\\\d\\{3\\}Z\\$/,
    "status": "Pending",
    "tariffTier": "Driver",
  },
]
`;

exports[`destination site admin api driver module bulk upload should upload a csv of drivers (even-more-drivers.csv) 1`] = `
[
  {
    "canExpense": false,
    "email": "<EMAIL>",
    "emailBounced": false,
    "firstName": "Bart",
    "fullName": "Bart Simpson",
    "group": {
      "id": 11671,
      "name": "Lex Autolease",
      "uid": "c7668cd2-**************-fcdbf6461e0c",
    },
    "id": Any<Number>,
    "lastName": "Simpson",
    "registeredDate": StringMatching /\\^\\\\d\\{4\\}-\\\\d\\{2\\}-\\\\d\\{2\\}T\\\\d\\{2\\}:\\\\d\\{2\\}:\\\\d\\{2\\}\\\\\\.\\\\d\\{3\\}Z\\$/,
    "status": "Pending",
    "tariffTier": "Member",
  },
  {
    "canExpense": false,
    "email": "<EMAIL>",
    "emailBounced": false,
    "firstName": "Homer",
    "fullName": "Homer Simpson",
    "group": {
      "id": 11670,
      "name": "Lex Autolease",
      "uid": "c7668cd2-**************-fcdbf6461e0c",
    },
    "id": Any<Number>,
    "lastName": "Simpson",
    "registeredDate": StringMatching /\\^\\\\d\\{4\\}-\\\\d\\{2\\}-\\\\d\\{2\\}T\\\\d\\{2\\}:\\\\d\\{2\\}:\\\\d\\{2\\}\\\\\\.\\\\d\\{3\\}Z\\$/,
    "status": "Pending",
    "tariffTier": "Member",
  },
  {
    "canExpense": true,
    "email": "<EMAIL>",
    "emailBounced": false,
    "firstName": "Jane",
    "fullName": "Jane Bloggs",
    "group": {
      "id": 100129,
      "name": "Lex Autolease",
      "uid": "c7668cd2-**************-fcdbf6461e0c",
    },
    "id": Any<Number>,
    "lastName": "Bloggs",
    "registeredDate": StringMatching /\\^\\\\d\\{4\\}-\\\\d\\{2\\}-\\\\d\\{2\\}T\\\\d\\{2\\}:\\\\d\\{2\\}:\\\\d\\{2\\}\\\\\\.\\\\d\\{3\\}Z\\$/,
    "status": "Pending",
    "tariffTier": "Driver",
  },
  {
    "canExpense": true,
    "email": "<EMAIL>",
    "emailBounced": false,
    "firstName": "Jane",
    "fullName": "Jane Bloggs",
    "group": {
      "id": 100128,
      "name": "Lex Autolease",
      "uid": "c7668cd2-**************-fcdbf6461e0c",
    },
    "id": Any<Number>,
    "lastName": "Bloggs",
    "registeredDate": StringMatching /\\^\\\\d\\{4\\}-\\\\d\\{2\\}-\\\\d\\{2\\}T\\\\d\\{2\\}:\\\\d\\{2\\}:\\\\d\\{2\\}\\\\\\.\\\\d\\{3\\}Z\\$/,
    "status": "Pending",
    "tariffTier": "Driver",
  },
  {
    "canExpense": true,
    "email": "<EMAIL>",
    "emailBounced": false,
    "firstName": "Robert",
    "fullName": "Robert Carlyle",
    "group": {
      "id": 100130,
      "name": "Lex Autolease",
      "uid": "c7668cd2-**************-fcdbf6461e0c",
    },
    "id": Any<Number>,
    "lastName": "Carlyle",
    "registeredDate": StringMatching /\\^\\\\d\\{4\\}-\\\\d\\{2\\}-\\\\d\\{2\\}T\\\\d\\{2\\}:\\\\d\\{2\\}:\\\\d\\{2\\}\\\\\\.\\\\d\\{3\\}Z\\$/,
    "status": "Pending",
    "tariffTier": "Driver",
  },
]
`;

exports[`destination site admin api driver module bulk upload should upload a csv of drivers (more-drivers.csv) 1`] = `
[
  {
    "canExpense": false,
    "email": "<EMAIL>",
    "emailBounced": false,
    "firstName": "Homer",
    "fullName": "Homer Simpson",
    "group": {
      "id": 11670,
      "name": "Lex Autolease",
      "uid": "c7668cd2-**************-fcdbf6461e0c",
    },
    "id": Any<Number>,
    "lastName": "Simpson",
    "registeredDate": StringMatching /\\^\\\\d\\{4\\}-\\\\d\\{2\\}-\\\\d\\{2\\}T\\\\d\\{2\\}:\\\\d\\{2\\}:\\\\d\\{2\\}\\\\\\.\\\\d\\{3\\}Z\\$/,
    "status": "Pending",
    "tariffTier": "Member",
  },
  {
    "canExpense": true,
    "email": "<EMAIL>",
    "emailBounced": false,
    "firstName": "Jane",
    "fullName": "Jane Bloggs",
    "group": {
      "id": 100129,
      "name": "Lex Autolease",
      "uid": "c7668cd2-**************-fcdbf6461e0c",
    },
    "id": Any<Number>,
    "lastName": "Bloggs",
    "registeredDate": StringMatching /\\^\\\\d\\{4\\}-\\\\d\\{2\\}-\\\\d\\{2\\}T\\\\d\\{2\\}:\\\\d\\{2\\}:\\\\d\\{2\\}\\\\\\.\\\\d\\{3\\}Z\\$/,
    "status": "Pending",
    "tariffTier": "Driver",
  },
  {
    "canExpense": true,
    "email": "<EMAIL>",
    "emailBounced": false,
    "firstName": "Jane",
    "fullName": "Jane Bloggs",
    "group": {
      "id": 100128,
      "name": "Lex Autolease",
      "uid": "c7668cd2-**************-fcdbf6461e0c",
    },
    "id": Any<Number>,
    "lastName": "Bloggs",
    "registeredDate": StringMatching /\\^\\\\d\\{4\\}-\\\\d\\{2\\}-\\\\d\\{2\\}T\\\\d\\{2\\}:\\\\d\\{2\\}:\\\\d\\{2\\}\\\\\\.\\\\d\\{3\\}Z\\$/,
    "status": "Pending",
    "tariffTier": "Driver",
  },
]
`;

exports[`destination site admin api driver module driver charge controller with projections feature flag false should get a driver with a list of charges 1`] = `
{
  "canExpense": true,
  "chargingStats": {
    "charges": [
      {
        "chargingDuration": "10800",
        "co2Savings": "4325.620",
        "confirmed": true,
        "confirmedBy": "driver",
        "endedAt": "2022-09-05T17:58:33Z",
        "energyCost": "324550",
        "energyUsage": "98.76",
        "pluggedIn": "10800",
        "podName": "Nick-Gary",
        "revenueGenerated": "457600",
        "startedAt": "2022-09-05T14:58:33Z",
      },
      {
        "chargingDuration": "10800",
        "co2Savings": "4325.620",
        "confirmed": true,
        "confirmedBy": "driver",
        "endedAt": "2022-09-05T17:58:33Z",
        "energyCost": "324550",
        "energyUsage": "98.76",
        "pluggedIn": "10800",
        "podName": "Nick-Gary",
        "revenueGenerated": "457600",
        "startedAt": "2022-09-05T14:58:33Z",
      },
      {
        "chargingDuration": "10800",
        "co2Savings": "4325.620",
        "confirmed": true,
        "confirmedBy": "driver",
        "endedAt": "2022-09-05T17:58:33Z",
        "energyCost": "324550",
        "energyUsage": "98.76",
        "pluggedIn": "10800",
        "podName": "Nick-Gary",
        "revenueGenerated": "457600",
        "startedAt": "2022-09-05T14:58:33Z",
      },
    ],
    "co2Avoided": "12976.860",
    "energyCost": "973650",
    "energyDelivered": "296.28",
    "revenue": "1372800",
  },
  "email": "<EMAIL>",
  "emailBounced": false,
  "firstName": "Layla",
  "fullName": "Layla Moloney",
  "group": {
    "id": 201,
    "name": "Savills UK",
    "uid": "780d9f10-47e7-472a-8367-88a852458efe",
  },
  "id": 201,
  "lastName": "Moloney",
  "registeredDate": "2016-07-05T09:52:46.000Z",
  "status": "Registered",
  "tariffTier": "Driver",
}
`;

exports[`destination site admin api driver module driver charge controller with projections feature flag false should get a soft deleted driver with a list of charges 1`] = `
{
  "canExpense": false,
  "chargingStats": {
    "charges": [
      {
        "chargingDuration": "10800",
        "co2Savings": "4325.620",
        "confirmed": true,
        "confirmedBy": "driver",
        "endedAt": "2022-09-05T17:58:33Z",
        "energyCost": "324550",
        "energyUsage": "98.76",
        "pluggedIn": "10800",
        "podName": "Nick-Gary",
        "revenueGenerated": "457600",
        "startedAt": "2022-09-05T14:58:33Z",
      },
      {
        "chargingDuration": "10800",
        "co2Savings": "4325.620",
        "confirmed": true,
        "confirmedBy": "driver",
        "endedAt": "2022-09-05T17:58:33Z",
        "energyCost": "324550",
        "energyUsage": "98.76",
        "pluggedIn": "10800",
        "podName": "Nick-Gary",
        "revenueGenerated": "457600",
        "startedAt": "2022-09-05T14:58:33Z",
      },
      {
        "chargingDuration": "10800",
        "co2Savings": "4325.620",
        "confirmed": true,
        "confirmedBy": "driver",
        "endedAt": "2022-09-05T17:58:33Z",
        "energyCost": "324550",
        "energyUsage": "98.76",
        "pluggedIn": "10800",
        "podName": "Nick-Gary",
        "revenueGenerated": "457600",
        "startedAt": "2022-09-05T14:58:33Z",
      },
    ],
    "co2Avoided": "12976.860",
    "energyCost": "973650",
    "energyDelivered": "296.28",
    "revenue": "1372800",
  },
  "email": "daisy.oâ€™<EMAIL>",
  "emailBounced": false,
  "firstName": "Daisy",
  "fullName": "Daisy Oâ€™Kelly",
  "group": {
    "id": 203,
    "name": "Savills UK",
    "uid": "780d9f10-47e7-472a-8367-88a852458efe",
  },
  "id": 203,
  "lastName": "Oâ€™Kelly",
  "registeredDate": "2016-07-05T09:58:37.000Z",
  "status": "Deleted",
  "tariffTier": "Driver",
}
`;

exports[`destination site admin api driver module driver charge controller with projections feature flag true should get a driver with a list of charges 1`] = `
{
  "canExpense": true,
  "chargingStats": {
    "charges": [
      {
        "chargingDuration": "10800",
        "co2Savings": "4325.620",
        "confirmed": true,
        "confirmedBy": "driver",
        "endedAt": "2022-09-05T17:58:33Z",
        "energyCost": "324550",
        "energyUsage": "98.76",
        "pluggedIn": "10800",
        "podName": "Nick-Gary",
        "revenueGenerated": "457600",
        "startedAt": "2022-09-05T14:58:33Z",
      },
      {
        "chargingDuration": "10800",
        "co2Savings": "4325.620",
        "confirmed": true,
        "confirmedBy": "driver",
        "endedAt": "2022-09-05T17:58:33Z",
        "energyCost": "324550",
        "energyUsage": "98.76",
        "pluggedIn": "10800",
        "podName": "Nick-Gary",
        "revenueGenerated": "457600",
        "startedAt": "2022-09-05T14:58:33Z",
      },
      {
        "chargingDuration": "10800",
        "co2Savings": "4325.620",
        "confirmed": true,
        "confirmedBy": "driver",
        "endedAt": "2022-09-05T17:58:33Z",
        "energyCost": "324550",
        "energyUsage": "98.76",
        "pluggedIn": "10800",
        "podName": "Nick-Gary",
        "revenueGenerated": "457600",
        "startedAt": "2022-09-05T14:58:33Z",
      },
      {
        "chargingDuration": "10800",
        "co2Savings": "4325.620",
        "confirmed": true,
        "confirmedBy": "driver",
        "endedAt": "2022-09-05T17:58:33Z",
        "energyCost": "324550",
        "energyUsage": "98.76",
        "pluggedIn": "10800",
        "podName": "Nick-Gary",
        "revenueGenerated": "457600",
        "startedAt": "2022-09-05T14:58:33Z",
      },
      {
        "chargingDuration": "10800",
        "co2Savings": "4325.620",
        "confirmed": true,
        "confirmedBy": "driver",
        "endedAt": "2022-09-05T17:58:33Z",
        "energyCost": "324550",
        "energyUsage": "98.76",
        "pluggedIn": "10800",
        "podName": "Nick-Gary",
        "revenueGenerated": "457600",
        "startedAt": "2022-09-05T14:58:33Z",
      },
      {
        "chargingDuration": "10800",
        "co2Savings": "4325.620",
        "confirmed": true,
        "confirmedBy": "driver",
        "endedAt": "2022-09-05T17:58:33Z",
        "energyCost": "324550",
        "energyUsage": "98.76",
        "pluggedIn": "10800",
        "podName": "Nick-Gary",
        "revenueGenerated": "457600",
        "startedAt": "2022-09-05T14:58:33Z",
      },
    ],
    "co2Avoided": "25953.720",
    "energyCost": "1947300",
    "energyDelivered": "592.56",
    "revenue": "2745600",
  },
  "email": "<EMAIL>",
  "emailBounced": false,
  "firstName": "Layla",
  "fullName": "Layla Moloney",
  "group": {
    "id": 201,
    "name": "Savills UK",
    "uid": "780d9f10-47e7-472a-8367-88a852458efe",
  },
  "id": 201,
  "lastName": "Moloney",
  "registeredDate": "2016-07-05T09:52:46.000Z",
  "status": "Registered",
  "tariffTier": "Driver",
}
`;

exports[`destination site admin api driver module driver charge controller with projections feature flag true should get a soft deleted driver with a list of charges 1`] = `
{
  "canExpense": false,
  "chargingStats": {
    "charges": [
      {
        "chargingDuration": "10800",
        "co2Savings": "4325.620",
        "confirmed": true,
        "confirmedBy": "driver",
        "endedAt": "2022-09-05T17:58:33Z",
        "energyCost": "324550",
        "energyUsage": "98.76",
        "pluggedIn": "10800",
        "podName": "Nick-Gary",
        "revenueGenerated": "457600",
        "startedAt": "2022-09-05T14:58:33Z",
      },
      {
        "chargingDuration": "10800",
        "co2Savings": "4325.620",
        "confirmed": true,
        "confirmedBy": "driver",
        "endedAt": "2022-09-05T17:58:33Z",
        "energyCost": "324550",
        "energyUsage": "98.76",
        "pluggedIn": "10800",
        "podName": "Nick-Gary",
        "revenueGenerated": "457600",
        "startedAt": "2022-09-05T14:58:33Z",
      },
      {
        "chargingDuration": "10800",
        "co2Savings": "4325.620",
        "confirmed": true,
        "confirmedBy": "driver",
        "endedAt": "2022-09-05T17:58:33Z",
        "energyCost": "324550",
        "energyUsage": "98.76",
        "pluggedIn": "10800",
        "podName": "Nick-Gary",
        "revenueGenerated": "457600",
        "startedAt": "2022-09-05T14:58:33Z",
      },
    ],
    "co2Avoided": "12976.860",
    "energyCost": "973650",
    "energyDelivered": "296.28",
    "revenue": "1372800",
  },
  "email": "daisy.oâ€™<EMAIL>",
  "emailBounced": false,
  "firstName": "Daisy",
  "fullName": "Daisy Oâ€™Kelly",
  "group": {
    "id": 203,
    "name": "Savills UK",
    "uid": "780d9f10-47e7-472a-8367-88a852458efe",
  },
  "id": 203,
  "lastName": "Oâ€™Kelly",
  "registeredDate": "2016-07-05T09:58:37.000Z",
  "status": "Deleted",
  "tariffTier": "Driver",
}
`;

exports[`destination site admin api driver module driver controller should add a new driver 1`] = `
{
  "canExpense": true,
  "email": "<EMAIL>",
  "emailBounced": false,
  "firstName": "Jane",
  "fullName": "Jane Bloggs",
  "id": Any<Number>,
  "lastName": "Bloggs",
  "registeredDate": StringMatching /\\^\\\\d\\{4\\}-\\\\d\\{2\\}-\\\\d\\{2\\}T\\\\d\\{2\\}:\\\\d\\{2\\}:\\\\d\\{2\\}\\\\\\.\\\\d\\{3\\}Z\\$/,
  "status": "Pending",
  "tariffTier": "Driver",
}
`;

exports[`destination site admin api driver module driver controller should delete an existing driver 1`] = `
[
  {
    "canExpense": true,
    "email": "<EMAIL>",
    "emailBounced": false,
    "firstName": "Jane",
    "fullName": "Jane Bloggs",
    "group": {
      "id": 100128,
      "name": "Lex Autolease",
      "uid": "c7668cd2-**************-fcdbf6461e0c",
    },
    "id": Any<Number>,
    "lastName": "Bloggs",
    "registeredDate": StringMatching /\\^\\\\d\\{4\\}-\\\\d\\{2\\}-\\\\d\\{2\\}T\\\\d\\{2\\}:\\\\d\\{2\\}:\\\\d\\{2\\}\\\\\\.\\\\d\\{3\\}Z\\$/,
    "status": "Pending",
    "tariffTier": "Driver",
  },
]
`;

exports[`destination site admin api driver module driver controller should find a list of drivers and hide soft deleted drivers if includeDeleted is false 1`] = `
[
  {
    "canExpense": true,
    "email": "<EMAIL>",
    "emailBounced": false,
    "firstName": "John ",
    "fullName": "John  McDonald",
    "group": {
      "id": 204,
      "name": "Lex Autolease",
      "uid": "c7668cd2-**************-fcdbf6461e0c",
    },
    "id": Any<Number>,
    "lastName": "McDonald",
    "registeredDate": StringMatching /\\^\\\\d\\{4\\}-\\\\d\\{2\\}-\\\\d\\{2\\}T\\\\d\\{2\\}:\\\\d\\{2\\}:\\\\d\\{2\\}\\\\\\.\\\\d\\{3\\}Z\\$/,
    "status": "Pending",
    "tariffTier": "Driver",
  },
]
`;

exports[`destination site admin api driver module driver controller should find a list of drivers and show soft deleted drivers if includeDeleted is true 1`] = `
[
  {
    "canExpense": true,
    "email": "<EMAIL>",
    "emailBounced": false,
    "firstName": "John ",
    "fullName": "John  McDonald",
    "group": {
      "id": 204,
      "name": "Lex Autolease",
      "uid": "c7668cd2-**************-fcdbf6461e0c",
    },
    "id": Any<Number>,
    "lastName": "McDonald",
    "registeredDate": StringMatching /\\^\\\\d\\{4\\}-\\\\d\\{2\\}-\\\\d\\{2\\}T\\\\d\\{2\\}:\\\\d\\{2\\}:\\\\d\\{2\\}\\\\\\.\\\\d\\{3\\}Z\\$/,
    "status": "Pending",
    "tariffTier": "Driver",
  },
  {
    "canExpense": false,
    "email": "<EMAIL>",
    "emailBounced": false,
    "firstName": "Peter ",
    "fullName": "Peter  Davies",
    "group": {
      "id": 205,
      "name": "Lex Autolease",
      "uid": "c7668cd2-**************-fcdbf6461e0c",
    },
    "id": Any<Number>,
    "lastName": "Davies",
    "registeredDate": StringMatching /\\^\\\\d\\{4\\}-\\\\d\\{2\\}-\\\\d\\{2\\}T\\\\d\\{2\\}:\\\\d\\{2\\}:\\\\d\\{2\\}\\\\\\.\\\\d\\{3\\}Z\\$/,
    "status": "Deleted",
    "tariffTier": "Driver",
  },
]
`;

exports[`destination site admin api driver module driver controller should update an existing driver 1`] = `
[
  {
    "canExpense": true,
    "email": "<EMAIL>",
    "emailBounced": false,
    "firstName": "Jane",
    "fullName": "Jane Bloggs",
    "group": {
      "id": 100128,
      "name": "Lex Autolease",
      "uid": "c7668cd2-**************-fcdbf6461e0c",
    },
    "id": Any<Number>,
    "lastName": "Bloggs",
    "registeredDate": StringMatching /\\^\\\\d\\{4\\}-\\\\d\\{2\\}-\\\\d\\{2\\}T\\\\d\\{2\\}:\\\\d\\{2\\}:\\\\d\\{2\\}\\\\\\.\\\\d\\{3\\}Z\\$/,
    "status": "Pending",
    "tariffTier": "Driver",
  },
  {
    "canExpense": true,
    "email": "<EMAIL>",
    "emailBounced": false,
    "firstName": "Joe",
    "fullName": "Joe Bloggs",
    "group": {
      "id": 204,
      "name": "Lex Autolease",
      "uid": "c7668cd2-**************-fcdbf6461e0c",
    },
    "id": Any<Number>,
    "lastName": "Bloggs",
    "registeredDate": StringMatching /\\^\\\\d\\{4\\}-\\\\d\\{2\\}-\\\\d\\{2\\}T\\\\d\\{2\\}:\\\\d\\{2\\}:\\\\d\\{2\\}\\\\\\.\\\\d\\{3\\}Z\\$/,
    "status": "Pending",
    "tariffTier": "Driver",
  },
]
`;

exports[`destination site admin api driver module should update an existing driver tariff tier 1`] = `
[
  {
    "canExpense": true,
    "email": "daisy.oâ€™<EMAIL>",
    "emailBounced": false,
    "firstName": "Daisy",
    "fullName": "Daisy Oâ€™Kelly",
    "group": {
      "id": 203,
      "name": "Savills UK",
      "uid": "780d9f10-47e7-472a-8367-88a852458efe",
    },
    "id": Any<Number>,
    "lastName": "Oâ€™Kelly",
    "registeredDate": StringMatching /\\^\\\\d\\{4\\}-\\\\d\\{2\\}-\\\\d\\{2\\}T\\\\d\\{2\\}:\\\\d\\{2\\}:\\\\d\\{2\\}\\\\\\.\\\\d\\{3\\}Z\\$/,
    "status": "Registered",
    "tariffTier": "Driver",
  },
  {
    "canExpense": true,
    "email": "<EMAIL>",
    "emailBounced": false,
    "firstName": "Jane",
    "fullName": "Jane Bloggs",
    "group": {
      "id": 11669,
      "name": "Savills UK",
      "uid": "780d9f10-47e7-472a-8367-88a852458efe",
    },
    "id": Any<Number>,
    "lastName": "Bloggs",
    "registeredDate": StringMatching /\\^\\\\d\\{4\\}-\\\\d\\{2\\}-\\\\d\\{2\\}T\\\\d\\{2\\}:\\\\d\\{2\\}:\\\\d\\{2\\}\\\\\\.\\\\d\\{3\\}Z\\$/,
    "status": "Pending",
    "tariffTier": "Member",
  },
  {
    "canExpense": true,
    "email": "<EMAIL>",
    "emailBounced": false,
    "firstName": "Layla",
    "fullName": "Layla Moloney",
    "group": {
      "id": 201,
      "name": "Savills UK",
      "uid": "780d9f10-47e7-472a-8367-88a852458efe",
    },
    "id": Any<Number>,
    "lastName": "Moloney",
    "registeredDate": StringMatching /\\^\\\\d\\{4\\}-\\\\d\\{2\\}-\\\\d\\{2\\}T\\\\d\\{2\\}:\\\\d\\{2\\}:\\\\d\\{2\\}\\\\\\.\\\\d\\{3\\}Z\\$/,
    "status": "Registered",
    "tariffTier": "Driver",
  },
]
`;

exports[`destination site admin api health controller should perform a health check 1`] = `
{
  "details": {
    "podadmin": {
      "status": "up",
    },
  },
  "error": {},
  "info": {
    "podadmin": {
      "status": "up",
    },
  },
  "status": "ok",
}
`;

exports[`destination site admin api insights module insights controller with projections feature flag false should return insight statistics for the current group 1`] = `
[
  {
    "co2Savings": 100,
    "cost": 200,
    "intervalStartDate": "2021-01-01",
    "revenueGenerated": 300,
    "totalUsage": 400,
  },
  {
    "co2Savings": 70,
    "cost": 50,
    "intervalStartDate": "2021-02-01",
    "revenueGenerated": 60,
    "totalUsage": 30,
  },
  {
    "co2Savings": 50,
    "cost": 23,
    "intervalStartDate": "2021-03-01",
    "revenueGenerated": 55,
    "totalUsage": 43,
  },
  {
    "co2Savings": 13,
    "cost": 66,
    "intervalStartDate": "2021-04-01",
    "revenueGenerated": 12,
    "totalUsage": 87,
  },
  {
    "co2Savings": 300,
    "cost": 200,
    "intervalStartDate": "2021-05-01",
    "revenueGenerated": 100,
    "totalUsage": 500,
  },
  {
    "co2Savings": 70,
    "cost": 50,
    "intervalStartDate": "2021-06-01",
    "revenueGenerated": 60,
    "totalUsage": 30,
  },
  {
    "co2Savings": 50,
    "cost": 23,
    "intervalStartDate": "2021-07-01",
    "revenueGenerated": 55,
    "totalUsage": 43,
  },
  {
    "co2Savings": 13,
    "cost": 66,
    "intervalStartDate": "2021-08-01",
    "revenueGenerated": 12,
    "totalUsage": 87,
  },
  {
    "co2Savings": 100,
    "cost": 200,
    "intervalStartDate": "2021-09-01",
    "revenueGenerated": 300,
    "totalUsage": 400,
  },
  {
    "co2Savings": 70,
    "cost": 50,
    "intervalStartDate": "2021-10-01",
    "revenueGenerated": 60,
    "totalUsage": 30,
  },
  {
    "co2Savings": 50,
    "cost": 23,
    "intervalStartDate": "2021-11-01",
    "revenueGenerated": 55,
    "totalUsage": 43,
  },
  {
    "co2Savings": 13,
    "cost": 66,
    "intervalStartDate": "2021-12-01",
    "revenueGenerated": 12,
    "totalUsage": 87,
  },
  {
    "co2Savings": 65,
    "cost": 12,
    "intervalStartDate": "2022-01-01",
    "revenueGenerated": 87,
    "totalUsage": 34,
  },
]
`;

exports[`destination site admin api insights module insights controller with projections feature flag false should return insight statistics for the current group and a given charger 1`] = `
[
  {
    "co2Savings": 100,
    "cost": 200,
    "intervalStartDate": "2021-01-01",
    "revenueGenerated": 300,
    "totalUsage": 400,
  },
  {
    "co2Savings": 70,
    "cost": 50,
    "intervalStartDate": "2021-02-01",
    "revenueGenerated": 60,
    "totalUsage": 30,
  },
  {
    "co2Savings": 50,
    "cost": 23,
    "intervalStartDate": "2021-03-01",
    "revenueGenerated": 55,
    "totalUsage": 43,
  },
  {
    "co2Savings": 13,
    "cost": 66,
    "intervalStartDate": "2021-04-01",
    "revenueGenerated": 12,
    "totalUsage": 87,
  },
  {
    "co2Savings": 300,
    "cost": 200,
    "intervalStartDate": "2021-05-01",
    "revenueGenerated": 100,
    "totalUsage": 500,
  },
  {
    "co2Savings": 70,
    "cost": 50,
    "intervalStartDate": "2021-06-01",
    "revenueGenerated": 60,
    "totalUsage": 30,
  },
  {
    "co2Savings": 50,
    "cost": 23,
    "intervalStartDate": "2021-07-01",
    "revenueGenerated": 55,
    "totalUsage": 43,
  },
  {
    "co2Savings": 13,
    "cost": 66,
    "intervalStartDate": "2021-08-01",
    "revenueGenerated": 12,
    "totalUsage": 87,
  },
  {
    "co2Savings": 100,
    "cost": 200,
    "intervalStartDate": "2021-09-01",
    "revenueGenerated": 300,
    "totalUsage": 400,
  },
  {
    "co2Savings": 70,
    "cost": 50,
    "intervalStartDate": "2021-10-01",
    "revenueGenerated": 60,
    "totalUsage": 30,
  },
  {
    "co2Savings": 50,
    "cost": 23,
    "intervalStartDate": "2021-11-01",
    "revenueGenerated": 55,
    "totalUsage": 43,
  },
  {
    "co2Savings": 13,
    "cost": 66,
    "intervalStartDate": "2021-12-01",
    "revenueGenerated": 12,
    "totalUsage": 87,
  },
  {
    "co2Savings": 65,
    "cost": 12,
    "intervalStartDate": "2022-01-01",
    "revenueGenerated": 87,
    "totalUsage": 34,
  },
]
`;

exports[`destination site admin api insights module insights controller with projections feature flag false should return insight statistics for the current group and a given site 1`] = `
[
  {
    "co2Savings": 100,
    "cost": 200,
    "intervalStartDate": "2021-01-01",
    "revenueGenerated": 300,
    "totalUsage": 400,
  },
  {
    "co2Savings": 70,
    "cost": 50,
    "intervalStartDate": "2021-02-01",
    "revenueGenerated": 60,
    "totalUsage": 30,
  },
  {
    "co2Savings": 50,
    "cost": 23,
    "intervalStartDate": "2021-03-01",
    "revenueGenerated": 55,
    "totalUsage": 43,
  },
  {
    "co2Savings": 13,
    "cost": 66,
    "intervalStartDate": "2021-04-01",
    "revenueGenerated": 12,
    "totalUsage": 87,
  },
  {
    "co2Savings": 300,
    "cost": 200,
    "intervalStartDate": "2021-05-01",
    "revenueGenerated": 100,
    "totalUsage": 500,
  },
  {
    "co2Savings": 70,
    "cost": 50,
    "intervalStartDate": "2021-06-01",
    "revenueGenerated": 60,
    "totalUsage": 30,
  },
  {
    "co2Savings": 50,
    "cost": 23,
    "intervalStartDate": "2021-07-01",
    "revenueGenerated": 55,
    "totalUsage": 43,
  },
  {
    "co2Savings": 13,
    "cost": 66,
    "intervalStartDate": "2021-08-01",
    "revenueGenerated": 12,
    "totalUsage": 87,
  },
  {
    "co2Savings": 100,
    "cost": 200,
    "intervalStartDate": "2021-09-01",
    "revenueGenerated": 300,
    "totalUsage": 400,
  },
  {
    "co2Savings": 70,
    "cost": 50,
    "intervalStartDate": "2021-10-01",
    "revenueGenerated": 60,
    "totalUsage": 30,
  },
  {
    "co2Savings": 50,
    "cost": 23,
    "intervalStartDate": "2021-11-01",
    "revenueGenerated": 55,
    "totalUsage": 43,
  },
  {
    "co2Savings": 13,
    "cost": 66,
    "intervalStartDate": "2021-12-01",
    "revenueGenerated": 12,
    "totalUsage": 87,
  },
  {
    "co2Savings": 65,
    "cost": 12,
    "intervalStartDate": "2022-01-01",
    "revenueGenerated": 87,
    "totalUsage": 34,
  },
]
`;

exports[`destination site admin api insights module insights controller with projections feature flag true should return insight statistics for the current group 1`] = `
[
  {
    "co2Savings": 100,
    "cost": 200,
    "intervalStartDate": "2021-01-01",
    "revenueGenerated": 300,
    "totalUsage": 400,
  },
  {
    "co2Savings": 70,
    "cost": 50,
    "intervalStartDate": "2021-02-01",
    "revenueGenerated": 60,
    "totalUsage": 30,
  },
  {
    "co2Savings": 50,
    "cost": 23,
    "intervalStartDate": "2021-03-01",
    "revenueGenerated": 55,
    "totalUsage": 43,
  },
  {
    "co2Savings": 13,
    "cost": 66,
    "intervalStartDate": "2021-04-01",
    "revenueGenerated": 12,
    "totalUsage": 87,
  },
  {
    "co2Savings": 300,
    "cost": 200,
    "intervalStartDate": "2021-05-01",
    "revenueGenerated": 100,
    "totalUsage": 500,
  },
  {
    "co2Savings": 70,
    "cost": 50,
    "intervalStartDate": "2021-06-01",
    "revenueGenerated": 60,
    "totalUsage": 30,
  },
  {
    "co2Savings": 50,
    "cost": 23,
    "intervalStartDate": "2021-07-01",
    "revenueGenerated": 55,
    "totalUsage": 43,
  },
  {
    "co2Savings": 13,
    "cost": 66,
    "intervalStartDate": "2021-08-01",
    "revenueGenerated": 12,
    "totalUsage": 87,
  },
  {
    "co2Savings": 100,
    "cost": 200,
    "intervalStartDate": "2021-09-01",
    "revenueGenerated": 300,
    "totalUsage": 400,
  },
  {
    "co2Savings": 70,
    "cost": 50,
    "intervalStartDate": "2021-10-01",
    "revenueGenerated": 60,
    "totalUsage": 30,
  },
  {
    "co2Savings": 50,
    "cost": 23,
    "intervalStartDate": "2021-11-01",
    "revenueGenerated": 55,
    "totalUsage": 43,
  },
  {
    "co2Savings": 13,
    "cost": 66,
    "intervalStartDate": "2021-12-01",
    "revenueGenerated": 12,
    "totalUsage": 87,
  },
  {
    "co2Savings": 65,
    "cost": 12,
    "intervalStartDate": "2022-01-01",
    "revenueGenerated": 87,
    "totalUsage": 34,
  },
]
`;

exports[`destination site admin api insights module insights controller with projections feature flag true should return insight statistics for the current group and a given charger 1`] = `
[
  {
    "co2Savings": 100,
    "cost": 200,
    "intervalStartDate": "2021-01-01",
    "revenueGenerated": 300,
    "totalUsage": 400,
  },
  {
    "co2Savings": 70,
    "cost": 50,
    "intervalStartDate": "2021-02-01",
    "revenueGenerated": 60,
    "totalUsage": 30,
  },
  {
    "co2Savings": 50,
    "cost": 23,
    "intervalStartDate": "2021-03-01",
    "revenueGenerated": 55,
    "totalUsage": 43,
  },
  {
    "co2Savings": 13,
    "cost": 66,
    "intervalStartDate": "2021-04-01",
    "revenueGenerated": 12,
    "totalUsage": 87,
  },
  {
    "co2Savings": 300,
    "cost": 200,
    "intervalStartDate": "2021-05-01",
    "revenueGenerated": 100,
    "totalUsage": 500,
  },
  {
    "co2Savings": 70,
    "cost": 50,
    "intervalStartDate": "2021-06-01",
    "revenueGenerated": 60,
    "totalUsage": 30,
  },
  {
    "co2Savings": 50,
    "cost": 23,
    "intervalStartDate": "2021-07-01",
    "revenueGenerated": 55,
    "totalUsage": 43,
  },
  {
    "co2Savings": 13,
    "cost": 66,
    "intervalStartDate": "2021-08-01",
    "revenueGenerated": 12,
    "totalUsage": 87,
  },
  {
    "co2Savings": 100,
    "cost": 200,
    "intervalStartDate": "2021-09-01",
    "revenueGenerated": 300,
    "totalUsage": 400,
  },
  {
    "co2Savings": 70,
    "cost": 50,
    "intervalStartDate": "2021-10-01",
    "revenueGenerated": 60,
    "totalUsage": 30,
  },
  {
    "co2Savings": 50,
    "cost": 23,
    "intervalStartDate": "2021-11-01",
    "revenueGenerated": 55,
    "totalUsage": 43,
  },
  {
    "co2Savings": 13,
    "cost": 66,
    "intervalStartDate": "2021-12-01",
    "revenueGenerated": 12,
    "totalUsage": 87,
  },
  {
    "co2Savings": 65,
    "cost": 12,
    "intervalStartDate": "2022-01-01",
    "revenueGenerated": 87,
    "totalUsage": 34,
  },
]
`;

exports[`destination site admin api insights module insights controller with projections feature flag true should return insight statistics for the current group and a given site 1`] = `
[
  {
    "co2Savings": 100,
    "cost": 200,
    "intervalStartDate": "2021-01-01",
    "revenueGenerated": 300,
    "totalUsage": 400,
  },
  {
    "co2Savings": 70,
    "cost": 50,
    "intervalStartDate": "2021-02-01",
    "revenueGenerated": 60,
    "totalUsage": 30,
  },
  {
    "co2Savings": 50,
    "cost": 23,
    "intervalStartDate": "2021-03-01",
    "revenueGenerated": 55,
    "totalUsage": 43,
  },
  {
    "co2Savings": 13,
    "cost": 66,
    "intervalStartDate": "2021-04-01",
    "revenueGenerated": 12,
    "totalUsage": 87,
  },
  {
    "co2Savings": 300,
    "cost": 200,
    "intervalStartDate": "2021-05-01",
    "revenueGenerated": 100,
    "totalUsage": 500,
  },
  {
    "co2Savings": 70,
    "cost": 50,
    "intervalStartDate": "2021-06-01",
    "revenueGenerated": 60,
    "totalUsage": 30,
  },
  {
    "co2Savings": 50,
    "cost": 23,
    "intervalStartDate": "2021-07-01",
    "revenueGenerated": 55,
    "totalUsage": 43,
  },
  {
    "co2Savings": 13,
    "cost": 66,
    "intervalStartDate": "2021-08-01",
    "revenueGenerated": 12,
    "totalUsage": 87,
  },
  {
    "co2Savings": 100,
    "cost": 200,
    "intervalStartDate": "2021-09-01",
    "revenueGenerated": 300,
    "totalUsage": 400,
  },
  {
    "co2Savings": 70,
    "cost": 50,
    "intervalStartDate": "2021-10-01",
    "revenueGenerated": 60,
    "totalUsage": 30,
  },
  {
    "co2Savings": 50,
    "cost": 23,
    "intervalStartDate": "2021-11-01",
    "revenueGenerated": 55,
    "totalUsage": 43,
  },
  {
    "co2Savings": 13,
    "cost": 66,
    "intervalStartDate": "2021-12-01",
    "revenueGenerated": 12,
    "totalUsage": 87,
  },
  {
    "co2Savings": 65,
    "cost": 12,
    "intervalStartDate": "2022-01-01",
    "revenueGenerated": 87,
    "totalUsage": 34,
  },
]
`;

exports[`destination site admin api pod module pod controller should assign a tariff to a pod 1`] = `
{
  "chargeSummary": {
    "chargingDuration": 22245,
    "claimedEnergyUsage": 243.89,
    "co2Savings": 4325.62,
    "energyCost": 324550,
    "energyDelivered": 567.89,
    "energyUsage": 567.89,
    "numberOfCharges": 2048,
    "revenueGenerated": 457600,
    "revenueGeneratingClaimedUsage": 43.5,
  },
  "confirmChargeEnabled": true,
  "coordinates": {
    "latitude": 55.822099,
    "longitude": -4.340059,
  },
  "description": "This is an open charge unit installed at Tesco Extra Silverburn site at 751 Barrhead Road, Glasgow",
  "id": 91647,
  "isEvZone": false,
  "isPublic": true,
  "lastContact": "2022-07-14T08:20:00.000Z",
  "model": "MT7-S-6mA-2-RO",
  "name": "Kyle-Dion",
  "ppid": "PG-70500",
  "recentCharges": [
    {
      "chargingDuration": "-",
      "co2Savings": "2.912",
      "confirmed": false,
      "door": "A",
      "endedAt": StringMatching /\\^\\(\\\\d\\{4\\}\\)-\\(\\\\d\\{2\\}\\)-\\(\\\\d\\{2\\}\\) \\(\\\\d\\{2\\}\\):\\(\\\\d\\{2\\}\\):\\(\\\\d\\{2\\}\\)\\$/,
      "energyCost": "£10.00",
      "energyUsage": "5.20",
      "locationId": 91647,
      "pluggedIn": StringMatching /\\^\\(\\\\d\\{4\\}\\)-\\(\\\\d\\{2\\}\\)-\\(\\\\d\\{2\\}\\) \\(\\\\d\\{2\\}\\):\\(\\\\d\\{2\\}\\):\\(\\\\d\\{2\\}\\)\\$/,
      "revenueGenerated": "£0.00",
      "startedAt": StringMatching /\\^\\(\\\\d\\{4\\}\\)-\\(\\\\d\\{2\\}\\)-\\(\\\\d\\{2\\}\\) \\(\\\\d\\{2\\}\\):\\(\\\\d\\{2\\}\\):\\(\\\\d\\{2\\}\\)\\$/,
      "totalDuration": "8",
      "userEmail": "-",
      "userName": "-",
      "vehicle": "Audi A3 e-tron",
    },
    {
      "chargingDuration": "-",
      "co2Savings": "3.080",
      "confirmed": false,
      "door": "A",
      "endedAt": StringMatching /\\^\\(\\\\d\\{4\\}\\)-\\(\\\\d\\{2\\}\\)-\\(\\\\d\\{2\\}\\) \\(\\\\d\\{2\\}\\):\\(\\\\d\\{2\\}\\):\\(\\\\d\\{2\\}\\)\\$/,
      "energyCost": "£20.00",
      "energyUsage": "5.50",
      "locationId": 91647,
      "pluggedIn": StringMatching /\\^\\(\\\\d\\{4\\}\\)-\\(\\\\d\\{2\\}\\)-\\(\\\\d\\{2\\}\\) \\(\\\\d\\{2\\}\\):\\(\\\\d\\{2\\}\\):\\(\\\\d\\{2\\}\\)\\$/,
      "revenueGenerated": "£0.00",
      "startedAt": StringMatching /\\^\\(\\\\d\\{4\\}\\)-\\(\\\\d\\{2\\}\\)-\\(\\\\d\\{2\\}\\) \\(\\\\d\\{2\\}\\):\\(\\\\d\\{2\\}\\):\\(\\\\d\\{2\\}\\)\\$/,
      "totalDuration": "-",
      "userEmail": "-",
      "userName": "-",
      "vehicle": "Audi A3 e-tron",
    },
  ],
  "schedules": [],
  "schemes": [],
  "site": {
    "address": {
      "country": "GB",
      "line1": "Barrhead Road",
      "line2": "",
      "name": "Tesco Extra - Silverburn",
      "postcode": "G53 6AG",
      "prettyPrint": "Barrhead Road, Glasgow, G53 6AG, GB",
      "town": "Glasgow",
    },
    "contactDetails": {
      "email": "",
      "name": "Raymond Hutchinson",
      "telephone": "07834611729",
    },
    "description": "",
    "energyCost": 15,
    "group": {
      "id": 2,
      "name": "Tesco Stores Ltd",
      "uid": "c9c5d39c-23ba-4a21-ac45-75968fdc055f",
    },
    "id": 75710,
    "parking": {
      "openingTimes": {
        "notes": [
          "For any parking restrictions that may apply, please check for signage at the store.
Max stay period - 2 hours",
        ],
      },
    },
  },
  "sockets": [
    {
      "door": "A",
      "firmwareVersion": "Unknown",
      "isUpdateAvailable": false,
      "lastContact": "2022-07-14T08:20:00.000Z",
      "serialNumber": "213611186",
      "status": "Available",
    },
    {
      "door": "B",
      "firmwareVersion": "Unknown",
      "isUpdateAvailable": false,
      "lastContact": "2022-07-14T07:17:02.000Z",
      "serialNumber": "213611185",
      "status": "Available",
    },
  ],
  "status": "Available",
  "supportsConfirmCharge": true,
  "supportsContactless": false,
  "supportsEnergyTariff": true,
  "supportsOcpp": false,
  "supportsPerKwh": true,
  "supportsRfid": true,
  "supportsTariffs": true,
  "tariff": {
    "id": 249,
    "name": "Tesco Rapid kWh",
  },
}
`;

exports[`destination site admin api pod module pod controller should find a list of pods 1`] = `
[
  {
    "confirmChargeEnabled": true,
    "coordinates": {
      "latitude": 51.124148,
      "longitude": -0.16234,
    },
    "description": "This is a rapid charger installed at the Tesco Extra superstore in Hazelwick Avenue, Crawley, West Sussex",
    "id": 5189,
    "isEvZone": false,
    "isPublic": false,
    "lastContact": "2022-05-14T20:52:34.000Z",
    "model": "Terra 53 CGT",
    "name": "Bill-Tara",
    "ppid": "t53_it1_3717_004",
    "schemes": [],
    "site": {
      "address": {
        "country": "GB",
        "line1": "Barrhead Road",
        "line2": "",
        "name": "Tesco Extra - Silverburn",
        "postcode": "G53 6AG",
        "prettyPrint": "Barrhead Road, Glasgow, G53 6AG, GB",
        "town": "Glasgow",
      },
      "contactDetails": {
        "email": "",
        "name": "Raymond Hutchinson",
        "telephone": "07834611729",
      },
      "description": "",
      "energyCost": 15,
      "group": {
        "id": 2,
        "name": "Tesco Stores Ltd",
        "uid": "c9c5d39c-23ba-4a21-ac45-75968fdc055f",
      },
      "id": 75710,
      "parking": {},
    },
    "sockets": [
      {
        "door": "A",
        "firmwareVersion": "Unknown",
        "isUpdateAvailable": false,
        "lastContact": "2022-05-14T20:52:34.000Z",
        "serialNumber": "T53-IT1-3717-004-Door1",
        "status": "Available",
      },
      {
        "door": "B",
        "firmwareVersion": "Unknown",
        "isUpdateAvailable": false,
        "lastContact": "2022-05-14T20:52:34.000Z",
        "serialNumber": "T53-IT1-3717-004-Door2",
        "status": "Charging",
      },
      {
        "door": "C",
        "firmwareVersion": "Unknown",
        "isUpdateAvailable": false,
        "lastContact": "2022-05-14T20:52:34.000Z",
        "serialNumber": "T53-IT1-3717-004-Door3",
        "status": "Unavailable",
      },
    ],
    "status": "Unavailable",
    "supportsConfirmCharge": true,
    "supportsContactless": false,
    "supportsEnergyTariff": true,
    "supportsOcpp": true,
    "supportsPerKwh": true,
    "supportsTariffs": false,
    "tariff": {
      "id": 249,
      "name": "Tesco Rapid kWh",
    },
  },
  {
    "confirmChargeEnabled": true,
    "coordinates": {
      "latitude": 55.822099,
      "longitude": -4.340059,
    },
    "description": "This is an open charge unit installed at Tesco Extra Silverburn site at 751 Barrhead Road, Glasgow",
    "id": 91647,
    "isEvZone": false,
    "isPublic": true,
    "lastContact": "2022-07-14T08:20:00.000Z",
    "model": "MT7-S-6mA-2-RO",
    "name": "Kyle-Dion",
    "ppid": "PG-70500",
    "schemes": [],
    "site": {
      "address": {
        "country": "GB",
        "line1": "Barrhead Road",
        "line2": "",
        "name": "Tesco Extra - Silverburn",
        "postcode": "G53 6AG",
        "prettyPrint": "Barrhead Road, Glasgow, G53 6AG, GB",
        "town": "Glasgow",
      },
      "contactDetails": {
        "email": "",
        "name": "Raymond Hutchinson",
        "telephone": "07834611729",
      },
      "description": "",
      "energyCost": 15,
      "group": {
        "id": 2,
        "name": "Tesco Stores Ltd",
        "uid": "c9c5d39c-23ba-4a21-ac45-75968fdc055f",
      },
      "id": 75710,
      "parking": {},
    },
    "sockets": [
      {
        "door": "A",
        "firmwareVersion": "Unknown",
        "isUpdateAvailable": false,
        "lastContact": "2022-07-14T08:20:00.000Z",
        "serialNumber": "213611186",
        "status": "Available",
      },
      {
        "door": "B",
        "firmwareVersion": "Unknown",
        "isUpdateAvailable": false,
        "lastContact": "2022-07-14T07:17:02.000Z",
        "serialNumber": "213611185",
        "status": "Available",
      },
    ],
    "status": "Available",
    "supportsConfirmCharge": true,
    "supportsContactless": false,
    "supportsEnergyTariff": true,
    "supportsOcpp": false,
    "supportsPerKwh": true,
    "supportsTariffs": true,
  },
  {
    "confirmChargeEnabled": true,
    "coordinates": {
      "latitude": 55.822099,
      "longitude": -4.340059,
    },
    "description": "This is an open charge unit installed at Tesco Extra Silverburn site at 751 Barrhead Road, Glasgow",
    "id": 91648,
    "isEvZone": false,
    "isPublic": true,
    "lastContact": "2022-07-14T07:19:55.000Z",
    "model": "T7-S-03-WH-TES-6mA",
    "name": "Zach-Jade",
    "ppid": "PG-86983",
    "schemes": [],
    "site": {
      "address": {
        "country": "GB",
        "line1": "Barrhead Road",
        "line2": "",
        "name": "Tesco Extra - Silverburn",
        "postcode": "G53 6AG",
        "prettyPrint": "Barrhead Road, Glasgow, G53 6AG, GB",
        "town": "Glasgow",
      },
      "contactDetails": {
        "email": "",
        "name": "Raymond Hutchinson",
        "telephone": "07834611729",
      },
      "description": "",
      "energyCost": 15,
      "group": {
        "id": 2,
        "name": "Tesco Stores Ltd",
        "uid": "c9c5d39c-23ba-4a21-ac45-75968fdc055f",
      },
      "id": 75710,
      "parking": {},
    },
    "sockets": [
      {
        "door": "A",
        "firmwareVersion": "Unknown",
        "isUpdateAvailable": false,
        "lastContact": "2024-09-14T10:24:42.000Z",
        "serialNumber": "210910491",
        "status": "Available",
      },
      {
        "door": "B",
        "firmwareVersion": "Unknown",
        "isUpdateAvailable": false,
        "lastContact": "2024-09-14T10:18:54.000Z",
        "serialNumber": "210910492",
        "status": "Available",
      },
    ],
    "status": "Available",
    "supportsConfirmCharge": true,
    "supportsContactless": false,
    "supportsEnergyTariff": true,
    "supportsOcpp": false,
    "supportsPerKwh": true,
    "supportsTariffs": true,
  },
]
`;

exports[`destination site admin api pod module pod controller should find a list of pods for a scheme 1`] = `
[
  {
    "confirmChargeEnabled": true,
    "coordinates": {
      "latitude": 53.264091,
      "longitude": -2.885418,
    },
    "description": "This is an open charge unit located at The Coliseum Shopping retail park in Ellesmere Port",
    "id": 3754,
    "isEvZone": false,
    "isPublic": true,
    "lastContact": "2019-07-04T14:51:39.000Z",
    "model": "T7-S-2",
    "name": "Tony-Kate",
    "ppid": "PG-80367",
    "schemes": [
      {
        "id": 188,
        "name": "Savills UK",
        "uid": "780d9f10-47e7-472a-8367-88a852458efe",
      },
    ],
    "site": {
      "address": {
        "country": "GB",
        "line1": "30 Coliseum Way",
        "line2": "",
        "name": "Coliseum Shopping Park",
        "postcode": "CH65 9HD",
        "prettyPrint": "30 Coliseum Way, Ellesmere Port, CH65 9HD, GB",
        "town": "Ellesmere Port",
      },
      "contactDetails": {
        "email": "<EMAIL>",
        "name": "Ella O'Reilly",
        "telephone": "06718 264434",
      },
      "description": "",
      "energyCost": null,
      "group": {
        "id": 336,
        "name": "Coliseum Shopping Park",
        "uid": "b2de66af-834c-4dd6-b119-99d94ffc2abe",
      },
      "id": 963,
      "parking": {},
    },
    "sockets": [
      {
        "door": "A",
        "firmwareVersion": "Unknown",
        "isUpdateAvailable": false,
        "lastContact": "2019-07-04T14:47:02.000Z",
        "serialNumber": "181410663",
        "status": "Available",
      },
      {
        "door": "B",
        "firmwareVersion": "Unknown",
        "isUpdateAvailable": false,
        "lastContact": "2019-07-04T14:51:39.000Z",
        "serialNumber": "181310113",
        "status": "Available",
      },
    ],
    "status": "Available",
    "supportsConfirmCharge": true,
    "supportsContactless": false,
    "supportsEnergyTariff": false,
    "supportsOcpp": false,
    "supportsPerKwh": false,
    "supportsTariffs": true,
  },
  {
    "confirmChargeEnabled": true,
    "coordinates": {
      "latitude": 51.91904621,
      "longitude": -2.10648304,
    },
    "description": "This is an open charge unit installed at the Gallagher Shopping site in Tewkesbury Road, Cheltenham, Gloucestershire",
    "id": 4090,
    "isEvZone": false,
    "isPublic": true,
    "lastContact": "2022-07-14T07:19:55.000Z",
    "model": "T3-S-2",
    "name": "Hank-Neil",
    "ppid": "PG-80435",
    "schemes": [
      {
        "id": 188,
        "name": "Savills UK",
        "uid": "780d9f10-47e7-472a-8367-88a852458efe",
      },
    ],
    "site": {
      "address": {
        "country": "GB",
        "line1": "Tewkesbury Road",
        "line2": "Cheltenham",
        "name": "Gallagher Shopping",
        "postcode": "GL51 9RR",
        "prettyPrint": "Tewkesbury Road, Cheltenham, Gloucestershire, GL51 9RR, GB",
        "town": "Gloucestershire",
      },
      "contactDetails": {
        "email": "<EMAIL>",
        "name": "Martin Maher",
        "telephone": "01688 71894",
      },
      "description": "",
      "energyCost": null,
      "group": {
        "id": 451,
        "name": "Gallagher Shopping",
        "uid": "087e9b6b-1d60-47a0-89e9-87ff0d54f719",
      },
      "id": 1106,
      "parking": {},
    },
    "sockets": [
      {
        "door": "A",
        "firmwareVersion": "Unknown",
        "isUpdateAvailable": false,
        "lastContact": "2022-03-16T11:48:13.000Z",
        "serialNumber": "183110186",
        "status": "Charging",
      },
      {
        "door": "B",
        "firmwareVersion": "Unknown",
        "isUpdateAvailable": false,
        "lastContact": "2022-07-14T07:19:55.000Z",
        "serialNumber": "183110181",
        "status": "Charging",
      },
    ],
    "status": "Available",
    "supportsConfirmCharge": true,
    "supportsContactless": false,
    "supportsEnergyTariff": true,
    "supportsOcpp": false,
    "supportsPerKwh": true,
    "supportsTariffs": true,
  },
]
`;

exports[`destination site admin api pod module pod controller should find an individual pods security event log 1`] = `
{
  "data": [
    {
      "door": "A",
      "eventTimestamp": "2021-06-13T22:49:20.000Z",
      "ppid": "PG-001",
      "receivedTimestamp": "2021-06-13T22:49:20.000Z",
      "sequenceNumber": 1,
      "serialNumber": "abc-123",
      "techInfo": "",
      "type": {
        "code": "TamperDetectionActivated",
        "description": "This is a test event",
        "links": [
          {
            "description": "test link description",
            "url": "www.test-url.com",
          },
        ],
        "name": "Test event",
      },
    },
  ],
}
`;

exports[`destination site admin api pod module pod controller should remove a tariff from a pod 1`] = `
{
  "chargeSummary": {
    "chargingDuration": 22245,
    "claimedEnergyUsage": 243.89,
    "co2Savings": 4325.62,
    "energyCost": 324550,
    "energyDelivered": 567.89,
    "energyUsage": 567.89,
    "numberOfCharges": 2048,
    "revenueGenerated": 457600,
    "revenueGeneratingClaimedUsage": 43.5,
  },
  "confirmChargeEnabled": true,
  "coordinates": {
    "latitude": 55.822099,
    "longitude": -4.340059,
  },
  "description": "This is an open charge unit installed at Tesco Extra Silverburn site at 751 Barrhead Road, Glasgow",
  "id": 91647,
  "isEvZone": false,
  "isPublic": true,
  "lastContact": "2022-07-14T08:20:00.000Z",
  "model": "MT7-S-6mA-2-RO",
  "name": "Kyle-Dion",
  "ppid": "PG-70500",
  "recentCharges": [
    {
      "chargingDuration": "-",
      "co2Savings": "2.912",
      "confirmed": false,
      "door": "A",
      "endedAt": StringMatching /\\^\\(\\\\d\\{4\\}\\)-\\(\\\\d\\{2\\}\\)-\\(\\\\d\\{2\\}\\) \\(\\\\d\\{2\\}\\):\\(\\\\d\\{2\\}\\):\\(\\\\d\\{2\\}\\)\\$/,
      "energyCost": "£10.00",
      "energyUsage": "5.20",
      "locationId": 91647,
      "pluggedIn": StringMatching /\\^\\(\\\\d\\{4\\}\\)-\\(\\\\d\\{2\\}\\)-\\(\\\\d\\{2\\}\\) \\(\\\\d\\{2\\}\\):\\(\\\\d\\{2\\}\\):\\(\\\\d\\{2\\}\\)\\$/,
      "revenueGenerated": "£0.00",
      "startedAt": StringMatching /\\^\\(\\\\d\\{4\\}\\)-\\(\\\\d\\{2\\}\\)-\\(\\\\d\\{2\\}\\) \\(\\\\d\\{2\\}\\):\\(\\\\d\\{2\\}\\):\\(\\\\d\\{2\\}\\)\\$/,
      "totalDuration": "8",
      "userEmail": "-",
      "userName": "-",
      "vehicle": "Audi A3 e-tron",
    },
    {
      "chargingDuration": "-",
      "co2Savings": "3.080",
      "confirmed": false,
      "door": "A",
      "endedAt": StringMatching /\\^\\(\\\\d\\{4\\}\\)-\\(\\\\d\\{2\\}\\)-\\(\\\\d\\{2\\}\\) \\(\\\\d\\{2\\}\\):\\(\\\\d\\{2\\}\\):\\(\\\\d\\{2\\}\\)\\$/,
      "energyCost": "£20.00",
      "energyUsage": "5.50",
      "locationId": 91647,
      "pluggedIn": StringMatching /\\^\\(\\\\d\\{4\\}\\)-\\(\\\\d\\{2\\}\\)-\\(\\\\d\\{2\\}\\) \\(\\\\d\\{2\\}\\):\\(\\\\d\\{2\\}\\):\\(\\\\d\\{2\\}\\)\\$/,
      "revenueGenerated": "£0.00",
      "startedAt": StringMatching /\\^\\(\\\\d\\{4\\}\\)-\\(\\\\d\\{2\\}\\)-\\(\\\\d\\{2\\}\\) \\(\\\\d\\{2\\}\\):\\(\\\\d\\{2\\}\\):\\(\\\\d\\{2\\}\\)\\$/,
      "totalDuration": "-",
      "userEmail": "-",
      "userName": "-",
      "vehicle": "Audi A3 e-tron",
    },
  ],
  "schedules": [],
  "schemes": [],
  "site": {
    "address": {
      "country": "GB",
      "line1": "Barrhead Road",
      "line2": "",
      "name": "Tesco Extra - Silverburn",
      "postcode": "G53 6AG",
      "prettyPrint": "Barrhead Road, Glasgow, G53 6AG, GB",
      "town": "Glasgow",
    },
    "contactDetails": {
      "email": "",
      "name": "Raymond Hutchinson",
      "telephone": "07834611729",
    },
    "description": "",
    "energyCost": 15,
    "group": {
      "id": 2,
      "name": "Tesco Stores Ltd",
      "uid": "c9c5d39c-23ba-4a21-ac45-75968fdc055f",
    },
    "id": 75710,
    "parking": {
      "openingTimes": {
        "notes": [
          "For any parking restrictions that may apply, please check for signage at the store.
Max stay period - 2 hours",
        ],
      },
    },
  },
  "sockets": [
    {
      "door": "A",
      "firmwareVersion": "Unknown",
      "isUpdateAvailable": false,
      "lastContact": "2022-07-14T08:20:00.000Z",
      "serialNumber": "213611186",
      "status": "Available",
    },
    {
      "door": "B",
      "firmwareVersion": "Unknown",
      "isUpdateAvailable": false,
      "lastContact": "2022-07-14T07:17:02.000Z",
      "serialNumber": "213611185",
      "status": "Available",
    },
  ],
  "status": "Available",
  "supportsConfirmCharge": true,
  "supportsContactless": false,
  "supportsEnergyTariff": true,
  "supportsOcpp": false,
  "supportsPerKwh": true,
  "supportsRfid": true,
  "supportsTariffs": true,
}
`;

exports[`destination site admin api pod module pod controller with projections feature flag false should find an individual pod by id 1`] = `
{
  "chargeSummary": {
    "chargingDuration": 22245,
    "claimedEnergyUsage": 243.89,
    "co2Savings": 4325.62,
    "energyCost": 324550,
    "energyDelivered": 567.89,
    "energyUsage": 567.89,
    "numberOfCharges": 2048,
    "revenueGenerated": 457600,
    "revenueGeneratingClaimedUsage": 43.5,
  },
  "confirmChargeEnabled": true,
  "coordinates": {
    "latitude": 55.822099,
    "longitude": -4.340059,
  },
  "description": "This is an open charge unit installed at Tesco Extra Silverburn site at 751 Barrhead Road, Glasgow",
  "id": 91647,
  "isEvZone": false,
  "isPublic": true,
  "lastContact": "2022-07-14T08:20:00.000Z",
  "model": "MT7-S-6mA-2-RO",
  "name": "Kyle-Dion",
  "ppid": "PG-70500",
  "recentCharges": [
    {
      "chargingDuration": "-",
      "co2Savings": "2.912",
      "confirmed": false,
      "door": "A",
      "endedAt": StringMatching /\\^\\(\\\\d\\{4\\}\\)-\\(\\\\d\\{2\\}\\)-\\(\\\\d\\{2\\}\\) \\(\\\\d\\{2\\}\\):\\(\\\\d\\{2\\}\\):\\(\\\\d\\{2\\}\\)\\$/,
      "energyCost": "£10.00",
      "energyUsage": "5.20",
      "locationId": 91647,
      "pluggedIn": StringMatching /\\^\\(\\\\d\\{4\\}\\)-\\(\\\\d\\{2\\}\\)-\\(\\\\d\\{2\\}\\) \\(\\\\d\\{2\\}\\):\\(\\\\d\\{2\\}\\):\\(\\\\d\\{2\\}\\)\\$/,
      "revenueGenerated": "£0.00",
      "startedAt": StringMatching /\\^\\(\\\\d\\{4\\}\\)-\\(\\\\d\\{2\\}\\)-\\(\\\\d\\{2\\}\\) \\(\\\\d\\{2\\}\\):\\(\\\\d\\{2\\}\\):\\(\\\\d\\{2\\}\\)\\$/,
      "totalDuration": "8",
      "userEmail": "-",
      "userName": "-",
      "vehicle": "Audi A3 e-tron",
    },
    {
      "chargingDuration": "-",
      "co2Savings": "3.080",
      "confirmed": false,
      "door": "A",
      "endedAt": StringMatching /\\^\\(\\\\d\\{4\\}\\)-\\(\\\\d\\{2\\}\\)-\\(\\\\d\\{2\\}\\) \\(\\\\d\\{2\\}\\):\\(\\\\d\\{2\\}\\):\\(\\\\d\\{2\\}\\)\\$/,
      "energyCost": "£20.00",
      "energyUsage": "5.50",
      "locationId": 91647,
      "pluggedIn": StringMatching /\\^\\(\\\\d\\{4\\}\\)-\\(\\\\d\\{2\\}\\)-\\(\\\\d\\{2\\}\\) \\(\\\\d\\{2\\}\\):\\(\\\\d\\{2\\}\\):\\(\\\\d\\{2\\}\\)\\$/,
      "revenueGenerated": "£0.00",
      "startedAt": StringMatching /\\^\\(\\\\d\\{4\\}\\)-\\(\\\\d\\{2\\}\\)-\\(\\\\d\\{2\\}\\) \\(\\\\d\\{2\\}\\):\\(\\\\d\\{2\\}\\):\\(\\\\d\\{2\\}\\)\\$/,
      "totalDuration": "-",
      "userEmail": "-",
      "userName": "-",
      "vehicle": "Audi A3 e-tron",
    },
  ],
  "schedules": [],
  "schemes": [],
  "site": {
    "address": {
      "country": "GB",
      "line1": "Barrhead Road",
      "line2": "",
      "name": "Tesco Extra - Silverburn",
      "postcode": "G53 6AG",
      "prettyPrint": "Barrhead Road, Glasgow, G53 6AG, GB",
      "town": "Glasgow",
    },
    "contactDetails": {
      "email": "",
      "name": "Raymond Hutchinson",
      "telephone": "07834611729",
    },
    "description": "",
    "energyCost": 15,
    "group": {
      "id": 2,
      "name": "Tesco Stores Ltd",
      "uid": "c9c5d39c-23ba-4a21-ac45-75968fdc055f",
    },
    "id": 75710,
    "parking": {
      "openingTimes": {
        "notes": [
          "For any parking restrictions that may apply, please check for signage at the store.
Max stay period - 2 hours",
        ],
      },
    },
  },
  "sockets": [
    {
      "door": "A",
      "firmwareVersion": "Unknown",
      "isUpdateAvailable": false,
      "lastContact": "2022-07-14T08:20:00.000Z",
      "serialNumber": "213611186",
      "status": "Available",
    },
    {
      "door": "B",
      "firmwareVersion": "Unknown",
      "isUpdateAvailable": false,
      "lastContact": "2022-07-14T07:17:02.000Z",
      "serialNumber": "213611185",
      "status": "Available",
    },
  ],
  "status": "Available",
  "supportsConfirmCharge": true,
  "supportsContactless": false,
  "supportsEnergyTariff": true,
  "supportsOcpp": false,
  "supportsPerKwh": true,
  "supportsRfid": true,
  "supportsTariffs": true,
}
`;

exports[`destination site admin api pod module pod controller with projections feature flag false should find an individual pod by id for a scheme 1`] = `
{
  "chargeSummary": {
    "chargingDuration": 22245,
    "claimedEnergyUsage": 243.89,
    "co2Savings": 4325.62,
    "energyCost": 324550,
    "energyDelivered": 567.89,
    "energyUsage": 567.89,
    "numberOfCharges": 2048,
    "revenueGenerated": 457600,
    "revenueGeneratingClaimedUsage": 43.5,
  },
  "confirmChargeEnabled": true,
  "coordinates": {
    "latitude": 51.91904621,
    "longitude": -2.10648304,
  },
  "description": "This is an open charge unit installed at the Gallagher Shopping site in Tewkesbury Road, Cheltenham, Gloucestershire",
  "id": 4090,
  "isEvZone": false,
  "isPublic": true,
  "lastContact": "2022-07-14T07:19:55.000Z",
  "model": "T3-S-2",
  "name": "Hank-Neil",
  "ppid": "PG-80435",
  "recentCharges": [
    {
      "chargingDuration": "-",
      "co2Savings": "1.624",
      "confirmed": false,
      "door": "A",
      "energyCost": "£0.00",
      "energyUsage": "2.90",
      "locationId": 4090,
      "pluggedIn": StringMatching /\\^\\(\\\\d\\{4\\}\\)-\\(\\\\d\\{2\\}\\)-\\(\\\\d\\{2\\}\\) \\(\\\\d\\{2\\}\\):\\(\\\\d\\{2\\}\\):\\(\\\\d\\{2\\}\\)\\$/,
      "revenueGenerated": "£0.00",
      "startedAt": StringMatching /\\^\\(\\\\d\\{4\\}\\)-\\(\\\\d\\{2\\}\\)-\\(\\\\d\\{2\\}\\) \\(\\\\d\\{2\\}\\):\\(\\\\d\\{2\\}\\):\\(\\\\d\\{2\\}\\)\\$/,
      "totalDuration": "-",
      "userEmail": "-",
      "userName": "-",
      "vehicle": "Audi A3 e-tron",
    },
    {
      "chargingDuration": "3610",
      "co2Savings": "0.952",
      "confirmed": true,
      "confirmedBy": "driver",
      "door": "A",
      "endedAt": StringMatching /\\^\\(\\\\d\\{4\\}\\)-\\(\\\\d\\{2\\}\\)-\\(\\\\d\\{2\\}\\) \\(\\\\d\\{2\\}\\):\\(\\\\d\\{2\\}\\):\\(\\\\d\\{2\\}\\)\\$/,
      "energyCost": "£0.24",
      "energyUsage": "1.70",
      "locationId": 4090,
      "pluggedIn": StringMatching /\\^\\(\\\\d\\{4\\}\\)-\\(\\\\d\\{2\\}\\)-\\(\\\\d\\{2\\}\\) \\(\\\\d\\{2\\}\\):\\(\\\\d\\{2\\}\\):\\(\\\\d\\{2\\}\\)\\$/,
      "revenueGenerated": "£0.00",
      "startedAt": StringMatching /\\^\\(\\\\d\\{4\\}\\)-\\(\\\\d\\{2\\}\\)-\\(\\\\d\\{2\\}\\) \\(\\\\d\\{2\\}\\):\\(\\\\d\\{2\\}\\):\\(\\\\d\\{2\\}\\)\\$/,
      "totalDuration": "3625",
      "userEmail": "-",
      "userName": "-",
      "vehicle": "-",
    },
    {
      "chargingDuration": "3610",
      "co2Savings": "0.952",
      "confirmed": true,
      "confirmedBy": "driver",
      "door": "A",
      "endedAt": StringMatching /\\^\\(\\\\d\\{4\\}\\)-\\(\\\\d\\{2\\}\\)-\\(\\\\d\\{2\\}\\) \\(\\\\d\\{2\\}\\):\\(\\\\d\\{2\\}\\):\\(\\\\d\\{2\\}\\)\\$/,
      "energyCost": "£0.24",
      "energyUsage": "1.70",
      "locationId": 4090,
      "pluggedIn": StringMatching /\\^\\(\\\\d\\{4\\}\\)-\\(\\\\d\\{2\\}\\)-\\(\\\\d\\{2\\}\\) \\(\\\\d\\{2\\}\\):\\(\\\\d\\{2\\}\\):\\(\\\\d\\{2\\}\\)\\$/,
      "revenueGenerated": "£0.00",
      "startedAt": StringMatching /\\^\\(\\\\d\\{4\\}\\)-\\(\\\\d\\{2\\}\\)-\\(\\\\d\\{2\\}\\) \\(\\\\d\\{2\\}\\):\\(\\\\d\\{2\\}\\):\\(\\\\d\\{2\\}\\)\\$/,
      "totalDuration": "20",
      "userEmail": "-",
      "userName": "-",
      "vehicle": "Audi A3 e-tron",
    },
    {
      "chargingDuration": "-",
      "co2Savings": "2.912",
      "confirmed": false,
      "door": "A",
      "endedAt": StringMatching /\\^\\(\\\\d\\{4\\}\\)-\\(\\\\d\\{2\\}\\)-\\(\\\\d\\{2\\}\\) \\(\\\\d\\{2\\}\\):\\(\\\\d\\{2\\}\\):\\(\\\\d\\{2\\}\\)\\$/,
      "energyCost": "£10.00",
      "energyUsage": "5.20",
      "locationId": 4090,
      "pluggedIn": StringMatching /\\^\\(\\\\d\\{4\\}\\)-\\(\\\\d\\{2\\}\\)-\\(\\\\d\\{2\\}\\) \\(\\\\d\\{2\\}\\):\\(\\\\d\\{2\\}\\):\\(\\\\d\\{2\\}\\)\\$/,
      "revenueGenerated": "£0.00",
      "startedAt": StringMatching /\\^\\(\\\\d\\{4\\}\\)-\\(\\\\d\\{2\\}\\)-\\(\\\\d\\{2\\}\\) \\(\\\\d\\{2\\}\\):\\(\\\\d\\{2\\}\\):\\(\\\\d\\{2\\}\\)\\$/,
      "totalDuration": "8",
      "userEmail": "-",
      "userName": "-",
      "vehicle": "Audi A3 e-tron",
    },
    {
      "chargingDuration": "200",
      "co2Savings": "1.624",
      "confirmed": false,
      "door": "A",
      "endedAt": StringMatching /\\^\\(\\\\d\\{4\\}\\)-\\(\\\\d\\{2\\}\\)-\\(\\\\d\\{2\\}\\) \\(\\\\d\\{2\\}\\):\\(\\\\d\\{2\\}\\):\\(\\\\d\\{2\\}\\)\\$/,
      "energyCost": "£5.00",
      "energyUsage": "2.90",
      "locationId": 4090,
      "pluggedIn": StringMatching /\\^\\(\\\\d\\{4\\}\\)-\\(\\\\d\\{2\\}\\)-\\(\\\\d\\{2\\}\\) \\(\\\\d\\{2\\}\\):\\(\\\\d\\{2\\}\\):\\(\\\\d\\{2\\}\\)\\$/,
      "revenueGenerated": "£0.00",
      "startedAt": StringMatching /\\^\\(\\\\d\\{4\\}\\)-\\(\\\\d\\{2\\}\\)-\\(\\\\d\\{2\\}\\) \\(\\\\d\\{2\\}\\):\\(\\\\d\\{2\\}\\):\\(\\\\d\\{2\\}\\)\\$/,
      "totalDuration": "4",
      "userEmail": "-",
      "userName": "-",
      "vehicle": "Audi A3 e-tron",
    },
    {
      "chargingDuration": "3610",
      "co2Savings": "0.952",
      "confirmed": true,
      "confirmedBy": "driver",
      "door": "A",
      "endedAt": StringMatching /\\^\\(\\\\d\\{4\\}\\)-\\(\\\\d\\{2\\}\\)-\\(\\\\d\\{2\\}\\) \\(\\\\d\\{2\\}\\):\\(\\\\d\\{2\\}\\):\\(\\\\d\\{2\\}\\)\\$/,
      "energyCost": "£0.24",
      "energyUsage": "1.70",
      "locationId": 4090,
      "pluggedIn": StringMatching /\\^\\(\\\\d\\{4\\}\\)-\\(\\\\d\\{2\\}\\)-\\(\\\\d\\{2\\}\\) \\(\\\\d\\{2\\}\\):\\(\\\\d\\{2\\}\\):\\(\\\\d\\{2\\}\\)\\$/,
      "revenueGenerated": "£0.00",
      "startedAt": StringMatching /\\^\\(\\\\d\\{4\\}\\)-\\(\\\\d\\{2\\}\\)-\\(\\\\d\\{2\\}\\) \\(\\\\d\\{2\\}\\):\\(\\\\d\\{2\\}\\):\\(\\\\d\\{2\\}\\)\\$/,
      "totalDuration": "2",
      "userEmail": "-",
      "userName": "-",
      "vehicle": "Audi A3 e-tron",
    },
  ],
  "schedules": [],
  "schemes": [
    {
      "id": 188,
      "name": "Savills UK",
      "uid": "780d9f10-47e7-472a-8367-88a852458efe",
    },
  ],
  "site": {
    "address": {
      "country": "GB",
      "line1": "Tewkesbury Road",
      "line2": "Cheltenham",
      "name": "Gallagher Shopping",
      "postcode": "GL51 9RR",
      "prettyPrint": "Tewkesbury Road, Cheltenham, Gloucestershire, GL51 9RR, GB",
      "town": "Gloucestershire",
    },
    "contactDetails": {
      "email": "<EMAIL>",
      "name": "Martin Maher",
      "telephone": "01688 71894",
    },
    "description": "",
    "energyCost": null,
    "group": {
      "id": 451,
      "name": "Gallagher Shopping",
      "uid": "087e9b6b-1d60-47a0-89e9-87ff0d54f719",
    },
    "id": 1106,
    "parking": {
      "openingTimes": {
        "fri": {
          "allDay": false,
          "from": "08:00:00",
          "to": "19:00:00",
        },
        "mon": {
          "allDay": false,
          "from": "08:00:00",
          "to": "19:00:00",
        },
        "notes": [],
        "sat": {
          "allDay": false,
          "from": "08:00:00",
          "to": "18:00:00",
        },
        "sun": {
          "allDay": false,
          "from": "10:00:00",
          "to": "16:00:00",
        },
        "thu": {
          "allDay": false,
          "from": "08:00:00",
          "to": "19:00:00",
        },
        "tue": {
          "allDay": false,
          "from": "08:00:00",
          "to": "19:00:00",
        },
        "wed": {
          "allDay": false,
          "from": "08:00:00",
          "to": "19:00:00",
        },
      },
    },
  },
  "sockets": [
    {
      "door": "A",
      "firmwareVersion": "Unknown",
      "isUpdateAvailable": false,
      "lastContact": "2022-03-16T11:48:13.000Z",
      "serialNumber": "183110186",
      "status": "Charging",
    },
    {
      "door": "B",
      "firmwareVersion": "Unknown",
      "isUpdateAvailable": false,
      "lastContact": "2022-07-14T07:19:55.000Z",
      "serialNumber": "183110181",
      "status": "Charging",
    },
  ],
  "status": "Available",
  "supportsConfirmCharge": true,
  "supportsContactless": false,
  "supportsEnergyTariff": true,
  "supportsOcpp": false,
  "supportsPerKwh": true,
  "supportsRfid": true,
  "supportsTariffs": true,
}
`;

exports[`destination site admin api pod module pod controller with projections feature flag false should find an individual pod by ppid 1`] = `
{
  "chargeSummary": {
    "chargingDuration": 22245,
    "claimedEnergyUsage": 243.89,
    "co2Savings": 4325.62,
    "energyCost": 324550,
    "energyDelivered": 567.89,
    "energyUsage": 567.89,
    "numberOfCharges": 2048,
    "revenueGenerated": 457600,
    "revenueGeneratingClaimedUsage": 43.5,
  },
  "confirmChargeEnabled": true,
  "coordinates": {
    "latitude": 55.822099,
    "longitude": -4.340059,
  },
  "description": "This is an open charge unit installed at Tesco Extra Silverburn site at 751 Barrhead Road, Glasgow",
  "id": 91647,
  "isEvZone": false,
  "isPublic": true,
  "lastContact": "2022-07-14T08:20:00.000Z",
  "model": "MT7-S-6mA-2-RO",
  "name": "Kyle-Dion",
  "ppid": "PG-70500",
  "recentCharges": [
    {
      "chargingDuration": "-",
      "co2Savings": "2.912",
      "confirmed": false,
      "door": "A",
      "endedAt": StringMatching /\\^\\(\\\\d\\{4\\}\\)-\\(\\\\d\\{2\\}\\)-\\(\\\\d\\{2\\}\\) \\(\\\\d\\{2\\}\\):\\(\\\\d\\{2\\}\\):\\(\\\\d\\{2\\}\\)\\$/,
      "energyCost": "£10.00",
      "energyUsage": "5.20",
      "locationId": 91647,
      "pluggedIn": StringMatching /\\^\\(\\\\d\\{4\\}\\)-\\(\\\\d\\{2\\}\\)-\\(\\\\d\\{2\\}\\) \\(\\\\d\\{2\\}\\):\\(\\\\d\\{2\\}\\):\\(\\\\d\\{2\\}\\)\\$/,
      "revenueGenerated": "£0.00",
      "startedAt": StringMatching /\\^\\(\\\\d\\{4\\}\\)-\\(\\\\d\\{2\\}\\)-\\(\\\\d\\{2\\}\\) \\(\\\\d\\{2\\}\\):\\(\\\\d\\{2\\}\\):\\(\\\\d\\{2\\}\\)\\$/,
      "totalDuration": "8",
      "userEmail": "-",
      "userName": "-",
      "vehicle": "Audi A3 e-tron",
    },
    {
      "chargingDuration": "-",
      "co2Savings": "3.080",
      "confirmed": false,
      "door": "A",
      "endedAt": StringMatching /\\^\\(\\\\d\\{4\\}\\)-\\(\\\\d\\{2\\}\\)-\\(\\\\d\\{2\\}\\) \\(\\\\d\\{2\\}\\):\\(\\\\d\\{2\\}\\):\\(\\\\d\\{2\\}\\)\\$/,
      "energyCost": "£20.00",
      "energyUsage": "5.50",
      "locationId": 91647,
      "pluggedIn": StringMatching /\\^\\(\\\\d\\{4\\}\\)-\\(\\\\d\\{2\\}\\)-\\(\\\\d\\{2\\}\\) \\(\\\\d\\{2\\}\\):\\(\\\\d\\{2\\}\\):\\(\\\\d\\{2\\}\\)\\$/,
      "revenueGenerated": "£0.00",
      "startedAt": StringMatching /\\^\\(\\\\d\\{4\\}\\)-\\(\\\\d\\{2\\}\\)-\\(\\\\d\\{2\\}\\) \\(\\\\d\\{2\\}\\):\\(\\\\d\\{2\\}\\):\\(\\\\d\\{2\\}\\)\\$/,
      "totalDuration": "-",
      "userEmail": "-",
      "userName": "-",
      "vehicle": "Audi A3 e-tron",
    },
  ],
  "schedules": [],
  "schemes": [],
  "site": {
    "address": {
      "country": "GB",
      "line1": "Barrhead Road",
      "line2": "",
      "name": "Tesco Extra - Silverburn",
      "postcode": "G53 6AG",
      "prettyPrint": "Barrhead Road, Glasgow, G53 6AG, GB",
      "town": "Glasgow",
    },
    "contactDetails": {
      "email": "",
      "name": "Raymond Hutchinson",
      "telephone": "07834611729",
    },
    "description": "",
    "energyCost": 15,
    "group": {
      "id": 2,
      "name": "Tesco Stores Ltd",
      "uid": "c9c5d39c-23ba-4a21-ac45-75968fdc055f",
    },
    "id": 75710,
    "parking": {
      "openingTimes": {
        "notes": [
          "For any parking restrictions that may apply, please check for signage at the store.
Max stay period - 2 hours",
        ],
      },
    },
  },
  "sockets": [
    {
      "door": "A",
      "firmwareVersion": "Unknown",
      "isUpdateAvailable": false,
      "lastContact": "2022-07-14T08:20:00.000Z",
      "serialNumber": "213611186",
      "status": "Available",
    },
    {
      "door": "B",
      "firmwareVersion": "Unknown",
      "isUpdateAvailable": false,
      "lastContact": "2022-07-14T07:17:02.000Z",
      "serialNumber": "213611185",
      "status": "Available",
    },
  ],
  "status": "Available",
  "supportsConfirmCharge": true,
  "supportsContactless": false,
  "supportsEnergyTariff": true,
  "supportsOcpp": false,
  "supportsPerKwh": true,
  "supportsRfid": true,
  "supportsTariffs": true,
}
`;

exports[`destination site admin api pod module pod controller with projections feature flag false should find an individual pod by ppid for a scheme 1`] = `
{
  "chargeSummary": {
    "chargingDuration": 22245,
    "claimedEnergyUsage": 243.89,
    "co2Savings": 4325.62,
    "energyCost": 324550,
    "energyDelivered": 567.89,
    "energyUsage": 567.89,
    "numberOfCharges": 2048,
    "revenueGenerated": 457600,
    "revenueGeneratingClaimedUsage": 43.5,
  },
  "confirmChargeEnabled": true,
  "coordinates": {
    "latitude": 51.91904621,
    "longitude": -2.10648304,
  },
  "description": "This is an open charge unit installed at the Gallagher Shopping site in Tewkesbury Road, Cheltenham, Gloucestershire",
  "id": 4090,
  "isEvZone": false,
  "isPublic": true,
  "lastContact": "2022-07-14T07:19:55.000Z",
  "model": "T3-S-2",
  "name": "Hank-Neil",
  "ppid": "PG-80435",
  "recentCharges": [
    {
      "chargingDuration": "-",
      "co2Savings": "1.624",
      "confirmed": false,
      "door": "A",
      "energyCost": "£0.00",
      "energyUsage": "2.90",
      "locationId": 4090,
      "pluggedIn": StringMatching /\\^\\(\\\\d\\{4\\}\\)-\\(\\\\d\\{2\\}\\)-\\(\\\\d\\{2\\}\\) \\(\\\\d\\{2\\}\\):\\(\\\\d\\{2\\}\\):\\(\\\\d\\{2\\}\\)\\$/,
      "revenueGenerated": "£0.00",
      "startedAt": StringMatching /\\^\\(\\\\d\\{4\\}\\)-\\(\\\\d\\{2\\}\\)-\\(\\\\d\\{2\\}\\) \\(\\\\d\\{2\\}\\):\\(\\\\d\\{2\\}\\):\\(\\\\d\\{2\\}\\)\\$/,
      "totalDuration": "-",
      "userEmail": "-",
      "userName": "-",
      "vehicle": "Audi A3 e-tron",
    },
    {
      "chargingDuration": "3610",
      "co2Savings": "0.952",
      "confirmed": true,
      "confirmedBy": "driver",
      "door": "A",
      "endedAt": StringMatching /\\^\\(\\\\d\\{4\\}\\)-\\(\\\\d\\{2\\}\\)-\\(\\\\d\\{2\\}\\) \\(\\\\d\\{2\\}\\):\\(\\\\d\\{2\\}\\):\\(\\\\d\\{2\\}\\)\\$/,
      "energyCost": "£0.24",
      "energyUsage": "1.70",
      "locationId": 4090,
      "pluggedIn": StringMatching /\\^\\(\\\\d\\{4\\}\\)-\\(\\\\d\\{2\\}\\)-\\(\\\\d\\{2\\}\\) \\(\\\\d\\{2\\}\\):\\(\\\\d\\{2\\}\\):\\(\\\\d\\{2\\}\\)\\$/,
      "revenueGenerated": "£0.00",
      "startedAt": StringMatching /\\^\\(\\\\d\\{4\\}\\)-\\(\\\\d\\{2\\}\\)-\\(\\\\d\\{2\\}\\) \\(\\\\d\\{2\\}\\):\\(\\\\d\\{2\\}\\):\\(\\\\d\\{2\\}\\)\\$/,
      "totalDuration": "3625",
      "userEmail": "-",
      "userName": "-",
      "vehicle": "-",
    },
    {
      "chargingDuration": "3610",
      "co2Savings": "0.952",
      "confirmed": true,
      "confirmedBy": "driver",
      "door": "A",
      "endedAt": StringMatching /\\^\\(\\\\d\\{4\\}\\)-\\(\\\\d\\{2\\}\\)-\\(\\\\d\\{2\\}\\) \\(\\\\d\\{2\\}\\):\\(\\\\d\\{2\\}\\):\\(\\\\d\\{2\\}\\)\\$/,
      "energyCost": "£0.24",
      "energyUsage": "1.70",
      "locationId": 4090,
      "pluggedIn": StringMatching /\\^\\(\\\\d\\{4\\}\\)-\\(\\\\d\\{2\\}\\)-\\(\\\\d\\{2\\}\\) \\(\\\\d\\{2\\}\\):\\(\\\\d\\{2\\}\\):\\(\\\\d\\{2\\}\\)\\$/,
      "revenueGenerated": "£0.00",
      "startedAt": StringMatching /\\^\\(\\\\d\\{4\\}\\)-\\(\\\\d\\{2\\}\\)-\\(\\\\d\\{2\\}\\) \\(\\\\d\\{2\\}\\):\\(\\\\d\\{2\\}\\):\\(\\\\d\\{2\\}\\)\\$/,
      "totalDuration": "20",
      "userEmail": "-",
      "userName": "-",
      "vehicle": "Audi A3 e-tron",
    },
    {
      "chargingDuration": "-",
      "co2Savings": "2.912",
      "confirmed": false,
      "door": "A",
      "endedAt": StringMatching /\\^\\(\\\\d\\{4\\}\\)-\\(\\\\d\\{2\\}\\)-\\(\\\\d\\{2\\}\\) \\(\\\\d\\{2\\}\\):\\(\\\\d\\{2\\}\\):\\(\\\\d\\{2\\}\\)\\$/,
      "energyCost": "£10.00",
      "energyUsage": "5.20",
      "locationId": 4090,
      "pluggedIn": StringMatching /\\^\\(\\\\d\\{4\\}\\)-\\(\\\\d\\{2\\}\\)-\\(\\\\d\\{2\\}\\) \\(\\\\d\\{2\\}\\):\\(\\\\d\\{2\\}\\):\\(\\\\d\\{2\\}\\)\\$/,
      "revenueGenerated": "£0.00",
      "startedAt": StringMatching /\\^\\(\\\\d\\{4\\}\\)-\\(\\\\d\\{2\\}\\)-\\(\\\\d\\{2\\}\\) \\(\\\\d\\{2\\}\\):\\(\\\\d\\{2\\}\\):\\(\\\\d\\{2\\}\\)\\$/,
      "totalDuration": "8",
      "userEmail": "-",
      "userName": "-",
      "vehicle": "Audi A3 e-tron",
    },
    {
      "chargingDuration": "200",
      "co2Savings": "1.624",
      "confirmed": false,
      "door": "A",
      "endedAt": StringMatching /\\^\\(\\\\d\\{4\\}\\)-\\(\\\\d\\{2\\}\\)-\\(\\\\d\\{2\\}\\) \\(\\\\d\\{2\\}\\):\\(\\\\d\\{2\\}\\):\\(\\\\d\\{2\\}\\)\\$/,
      "energyCost": "£5.00",
      "energyUsage": "2.90",
      "locationId": 4090,
      "pluggedIn": StringMatching /\\^\\(\\\\d\\{4\\}\\)-\\(\\\\d\\{2\\}\\)-\\(\\\\d\\{2\\}\\) \\(\\\\d\\{2\\}\\):\\(\\\\d\\{2\\}\\):\\(\\\\d\\{2\\}\\)\\$/,
      "revenueGenerated": "£0.00",
      "startedAt": StringMatching /\\^\\(\\\\d\\{4\\}\\)-\\(\\\\d\\{2\\}\\)-\\(\\\\d\\{2\\}\\) \\(\\\\d\\{2\\}\\):\\(\\\\d\\{2\\}\\):\\(\\\\d\\{2\\}\\)\\$/,
      "totalDuration": "4",
      "userEmail": "-",
      "userName": "-",
      "vehicle": "Audi A3 e-tron",
    },
    {
      "chargingDuration": "3610",
      "co2Savings": "0.952",
      "confirmed": true,
      "confirmedBy": "driver",
      "door": "A",
      "endedAt": StringMatching /\\^\\(\\\\d\\{4\\}\\)-\\(\\\\d\\{2\\}\\)-\\(\\\\d\\{2\\}\\) \\(\\\\d\\{2\\}\\):\\(\\\\d\\{2\\}\\):\\(\\\\d\\{2\\}\\)\\$/,
      "energyCost": "£0.24",
      "energyUsage": "1.70",
      "locationId": 4090,
      "pluggedIn": StringMatching /\\^\\(\\\\d\\{4\\}\\)-\\(\\\\d\\{2\\}\\)-\\(\\\\d\\{2\\}\\) \\(\\\\d\\{2\\}\\):\\(\\\\d\\{2\\}\\):\\(\\\\d\\{2\\}\\)\\$/,
      "revenueGenerated": "£0.00",
      "startedAt": StringMatching /\\^\\(\\\\d\\{4\\}\\)-\\(\\\\d\\{2\\}\\)-\\(\\\\d\\{2\\}\\) \\(\\\\d\\{2\\}\\):\\(\\\\d\\{2\\}\\):\\(\\\\d\\{2\\}\\)\\$/,
      "totalDuration": "2",
      "userEmail": "-",
      "userName": "-",
      "vehicle": "Audi A3 e-tron",
    },
  ],
  "schedules": [],
  "schemes": [
    {
      "id": 188,
      "name": "Savills UK",
      "uid": "780d9f10-47e7-472a-8367-88a852458efe",
    },
  ],
  "site": {
    "address": {
      "country": "GB",
      "line1": "Tewkesbury Road",
      "line2": "Cheltenham",
      "name": "Gallagher Shopping",
      "postcode": "GL51 9RR",
      "prettyPrint": "Tewkesbury Road, Cheltenham, Gloucestershire, GL51 9RR, GB",
      "town": "Gloucestershire",
    },
    "contactDetails": {
      "email": "<EMAIL>",
      "name": "Martin Maher",
      "telephone": "01688 71894",
    },
    "description": "",
    "energyCost": null,
    "group": {
      "id": 451,
      "name": "Gallagher Shopping",
      "uid": "087e9b6b-1d60-47a0-89e9-87ff0d54f719",
    },
    "id": 1106,
    "parking": {
      "openingTimes": {
        "fri": {
          "allDay": false,
          "from": "08:00:00",
          "to": "19:00:00",
        },
        "mon": {
          "allDay": false,
          "from": "08:00:00",
          "to": "19:00:00",
        },
        "notes": [],
        "sat": {
          "allDay": false,
          "from": "08:00:00",
          "to": "18:00:00",
        },
        "sun": {
          "allDay": false,
          "from": "10:00:00",
          "to": "16:00:00",
        },
        "thu": {
          "allDay": false,
          "from": "08:00:00",
          "to": "19:00:00",
        },
        "tue": {
          "allDay": false,
          "from": "08:00:00",
          "to": "19:00:00",
        },
        "wed": {
          "allDay": false,
          "from": "08:00:00",
          "to": "19:00:00",
        },
      },
    },
  },
  "sockets": [
    {
      "door": "A",
      "firmwareVersion": "Unknown",
      "isUpdateAvailable": false,
      "lastContact": "2022-03-16T11:48:13.000Z",
      "serialNumber": "183110186",
      "status": "Charging",
    },
    {
      "door": "B",
      "firmwareVersion": "Unknown",
      "isUpdateAvailable": false,
      "lastContact": "2022-07-14T07:19:55.000Z",
      "serialNumber": "183110181",
      "status": "Charging",
    },
  ],
  "status": "Available",
  "supportsConfirmCharge": true,
  "supportsContactless": false,
  "supportsEnergyTariff": true,
  "supportsOcpp": false,
  "supportsPerKwh": true,
  "supportsRfid": true,
  "supportsTariffs": true,
}
`;

exports[`destination site admin api pod module pod controller with projections feature flag true should find an individual pod by id 1`] = `
{
  "chargeSummary": {
    "chargingDuration": 22245,
    "claimedEnergyUsage": 243.89,
    "co2Savings": 4325.62,
    "energyCost": 324550,
    "energyDelivered": 567.89,
    "energyUsage": 567.89,
    "numberOfCharges": 2048,
    "revenueGenerated": 457600,
    "revenueGeneratingClaimedUsage": 43.5,
  },
  "confirmChargeEnabled": true,
  "coordinates": {
    "latitude": 55.822099,
    "longitude": -4.340059,
  },
  "description": "This is an open charge unit installed at Tesco Extra Silverburn site at 751 Barrhead Road, Glasgow",
  "id": 91647,
  "isEvZone": false,
  "isPublic": true,
  "lastContact": "2022-07-14T08:20:00.000Z",
  "model": "MT7-S-6mA-2-RO",
  "name": "Kyle-Dion",
  "ppid": "PG-70500",
  "recentCharges": [
    {
      "chargingDuration": "3300",
      "co2Savings": "4325.620",
      "confirmed": true,
      "door": "A",
      "endedAt": StringMatching /\\^\\(\\\\d\\{4\\}\\)-\\(\\\\d\\{2\\}\\)-\\(\\\\d\\{2\\}\\) \\(\\\\d\\{2\\}\\):\\(\\\\d\\{2\\}\\):\\(\\\\d\\{2\\}\\)\\$/,
      "energyCost": "£0.32",
      "energyUsage": "567.89",
      "pluggedIn": StringMatching /\\^\\(\\\\d\\{4\\}\\)-\\(\\\\d\\{2\\}\\)-\\(\\\\d\\{2\\}\\) \\(\\\\d\\{2\\}\\):\\(\\\\d\\{2\\}\\):\\(\\\\d\\{2\\}\\)\\$/,
      "podName": "Bill-Tara",
      "ppid": "t53_it1_3717_004",
      "revenueGenerated": "£4,576.00",
      "siteName": "Big site",
      "startedAt": StringMatching /\\^\\(\\\\d\\{4\\}\\)-\\(\\\\d\\{2\\}\\)-\\(\\\\d\\{2\\}\\) \\(\\\\d\\{2\\}\\):\\(\\\\d\\{2\\}\\):\\(\\\\d\\{2\\}\\)\\$/,
      "totalDuration": "3300",
      "userEmail": "-",
      "userName": "-",
      "vehicle": "-",
    },
    {
      "chargingDuration": "3300",
      "co2Savings": "4325.620",
      "confirmed": true,
      "door": "A",
      "endedAt": StringMatching /\\^\\(\\\\d\\{4\\}\\)-\\(\\\\d\\{2\\}\\)-\\(\\\\d\\{2\\}\\) \\(\\\\d\\{2\\}\\):\\(\\\\d\\{2\\}\\):\\(\\\\d\\{2\\}\\)\\$/,
      "energyCost": "£0.32",
      "energyUsage": "567.89",
      "pluggedIn": StringMatching /\\^\\(\\\\d\\{4\\}\\)-\\(\\\\d\\{2\\}\\)-\\(\\\\d\\{2\\}\\) \\(\\\\d\\{2\\}\\):\\(\\\\d\\{2\\}\\):\\(\\\\d\\{2\\}\\)\\$/,
      "podName": "Hank-Neil",
      "ppid": "PG-80435",
      "revenueGenerated": "£4,576.00",
      "siteName": "Big site",
      "startedAt": StringMatching /\\^\\(\\\\d\\{4\\}\\)-\\(\\\\d\\{2\\}\\)-\\(\\\\d\\{2\\}\\) \\(\\\\d\\{2\\}\\):\\(\\\\d\\{2\\}\\):\\(\\\\d\\{2\\}\\)\\$/,
      "totalDuration": "3300",
      "userEmail": "-",
      "userName": "-",
      "vehicle": "-",
    },
    {
      "chargingDuration": "3300",
      "co2Savings": "4325.620",
      "confirmed": true,
      "door": "A",
      "endedAt": StringMatching /\\^\\(\\\\d\\{4\\}\\)-\\(\\\\d\\{2\\}\\)-\\(\\\\d\\{2\\}\\) \\(\\\\d\\{2\\}\\):\\(\\\\d\\{2\\}\\):\\(\\\\d\\{2\\}\\)\\$/,
      "energyCost": "£0.32",
      "energyUsage": "567.89",
      "pluggedIn": StringMatching /\\^\\(\\\\d\\{4\\}\\)-\\(\\\\d\\{2\\}\\)-\\(\\\\d\\{2\\}\\) \\(\\\\d\\{2\\}\\):\\(\\\\d\\{2\\}\\):\\(\\\\d\\{2\\}\\)\\$/,
      "podName": "Kyle-Dion",
      "ppid": "PG-70500",
      "revenueGenerated": "£4,576.00",
      "siteName": "Little site",
      "startedAt": StringMatching /\\^\\(\\\\d\\{4\\}\\)-\\(\\\\d\\{2\\}\\)-\\(\\\\d\\{2\\}\\) \\(\\\\d\\{2\\}\\):\\(\\\\d\\{2\\}\\):\\(\\\\d\\{2\\}\\)\\$/,
      "totalDuration": "3300",
      "userEmail": "-",
      "userName": "-",
      "vehicle": "-",
    },
    {
      "chargingDuration": "3300",
      "co2Savings": "4325.620",
      "confirmed": true,
      "door": "A",
      "endedAt": StringMatching /\\^\\(\\\\d\\{4\\}\\)-\\(\\\\d\\{2\\}\\)-\\(\\\\d\\{2\\}\\) \\(\\\\d\\{2\\}\\):\\(\\\\d\\{2\\}\\):\\(\\\\d\\{2\\}\\)\\$/,
      "energyCost": "£0.32",
      "energyUsage": "567.89",
      "pluggedIn": StringMatching /\\^\\(\\\\d\\{4\\}\\)-\\(\\\\d\\{2\\}\\)-\\(\\\\d\\{2\\}\\) \\(\\\\d\\{2\\}\\):\\(\\\\d\\{2\\}\\):\\(\\\\d\\{2\\}\\)\\$/,
      "podName": "Zach-Jade",
      "ppid": "PG-86983",
      "revenueGenerated": "£4,576.00",
      "siteName": "Little site",
      "startedAt": StringMatching /\\^\\(\\\\d\\{4\\}\\)-\\(\\\\d\\{2\\}\\)-\\(\\\\d\\{2\\}\\) \\(\\\\d\\{2\\}\\):\\(\\\\d\\{2\\}\\):\\(\\\\d\\{2\\}\\)\\$/,
      "totalDuration": "3300",
      "userEmail": "-",
      "userName": "-",
      "vehicle": "-",
    },
  ],
  "schedules": [],
  "schemes": [],
  "site": {
    "address": {
      "country": "GB",
      "line1": "Barrhead Road",
      "line2": "",
      "name": "Tesco Extra - Silverburn",
      "postcode": "G53 6AG",
      "prettyPrint": "Barrhead Road, Glasgow, G53 6AG, GB",
      "town": "Glasgow",
    },
    "contactDetails": {
      "email": "",
      "name": "Raymond Hutchinson",
      "telephone": "07834611729",
    },
    "description": "",
    "energyCost": 15,
    "group": {
      "id": 2,
      "name": "Tesco Stores Ltd",
      "uid": "c9c5d39c-23ba-4a21-ac45-75968fdc055f",
    },
    "id": 75710,
    "parking": {
      "openingTimes": {
        "notes": [
          "For any parking restrictions that may apply, please check for signage at the store.
Max stay period - 2 hours",
        ],
      },
    },
  },
  "sockets": [
    {
      "door": "A",
      "firmwareVersion": "Unknown",
      "isUpdateAvailable": false,
      "lastContact": "2022-07-14T08:20:00.000Z",
      "serialNumber": "213611186",
      "status": "Available",
    },
    {
      "door": "B",
      "firmwareVersion": "Unknown",
      "isUpdateAvailable": false,
      "lastContact": "2022-07-14T07:17:02.000Z",
      "serialNumber": "213611185",
      "status": "Available",
    },
  ],
  "status": "Available",
  "supportsConfirmCharge": true,
  "supportsContactless": false,
  "supportsEnergyTariff": true,
  "supportsOcpp": false,
  "supportsPerKwh": true,
  "supportsRfid": true,
  "supportsTariffs": true,
}
`;

exports[`destination site admin api pod module pod controller with projections feature flag true should find an individual pod by id for a scheme 1`] = `
{
  "chargeSummary": {
    "chargingDuration": 22245,
    "claimedEnergyUsage": 243.89,
    "co2Savings": 4325.62,
    "energyCost": 324550,
    "energyDelivered": 567.89,
    "energyUsage": 567.89,
    "numberOfCharges": 2048,
    "revenueGenerated": 457600,
    "revenueGeneratingClaimedUsage": 43.5,
  },
  "confirmChargeEnabled": true,
  "coordinates": {
    "latitude": 51.91904621,
    "longitude": -2.10648304,
  },
  "description": "This is an open charge unit installed at the Gallagher Shopping site in Tewkesbury Road, Cheltenham, Gloucestershire",
  "id": 4090,
  "isEvZone": false,
  "isPublic": true,
  "lastContact": "2022-07-14T07:19:55.000Z",
  "model": "T3-S-2",
  "name": "Hank-Neil",
  "ppid": "PG-80435",
  "recentCharges": [
    {
      "chargingDuration": "3300",
      "co2Savings": "4325.620",
      "confirmed": true,
      "door": "A",
      "endedAt": "2022-09-27 19:00:00",
      "energyCost": "£0.32",
      "energyUsage": "567.89",
      "pluggedIn": StringMatching /\\^\\(\\\\d\\{4\\}\\)-\\(\\\\d\\{2\\}\\)-\\(\\\\d\\{2\\}\\) \\(\\\\d\\{2\\}\\):\\(\\\\d\\{2\\}\\):\\(\\\\d\\{2\\}\\)\\$/,
      "podName": "Bill-Tara",
      "ppid": "t53_it1_3717_004",
      "revenueGenerated": "£4,576.00",
      "siteName": "Big site",
      "startedAt": StringMatching /\\^\\(\\\\d\\{4\\}\\)-\\(\\\\d\\{2\\}\\)-\\(\\\\d\\{2\\}\\) \\(\\\\d\\{2\\}\\):\\(\\\\d\\{2\\}\\):\\(\\\\d\\{2\\}\\)\\$/,
      "totalDuration": "3300",
      "userEmail": "daisy.oâ€™<EMAIL>",
      "userName": "Daisy Oâ€™Kelly",
      "vehicle": "-",
    },
    {
      "chargingDuration": "3300",
      "co2Savings": "4325.620",
      "confirmed": true,
      "door": "A",
      "endedAt": StringMatching /\\^\\(\\\\d\\{4\\}\\)-\\(\\\\d\\{2\\}\\)-\\(\\\\d\\{2\\}\\) \\(\\\\d\\{2\\}\\):\\(\\\\d\\{2\\}\\):\\(\\\\d\\{2\\}\\)\\$/,
      "energyCost": "£0.32",
      "energyUsage": "567.89",
      "pluggedIn": StringMatching /\\^\\(\\\\d\\{4\\}\\)-\\(\\\\d\\{2\\}\\)-\\(\\\\d\\{2\\}\\) \\(\\\\d\\{2\\}\\):\\(\\\\d\\{2\\}\\):\\(\\\\d\\{2\\}\\)\\$/,
      "podName": "Hank-Neil",
      "ppid": "PG-80435",
      "revenueGenerated": "£4,576.00",
      "siteName": "Big site",
      "startedAt": StringMatching /\\^\\(\\\\d\\{4\\}\\)-\\(\\\\d\\{2\\}\\)-\\(\\\\d\\{2\\}\\) \\(\\\\d\\{2\\}\\):\\(\\\\d\\{2\\}\\):\\(\\\\d\\{2\\}\\)\\$/,
      "totalDuration": "3300",
      "userEmail": "<EMAIL>",
      "userName": "Layla Moloney",
      "vehicle": "-",
    },
    {
      "chargingDuration": "3300",
      "co2Savings": "4325.620",
      "confirmed": true,
      "door": "A",
      "endedAt": StringMatching /\\^\\(\\\\d\\{4\\}\\)-\\(\\\\d\\{2\\}\\)-\\(\\\\d\\{2\\}\\) \\(\\\\d\\{2\\}\\):\\(\\\\d\\{2\\}\\):\\(\\\\d\\{2\\}\\)\\$/,
      "energyCost": "£0.32",
      "energyUsage": "567.89",
      "pluggedIn": StringMatching /\\^\\(\\\\d\\{4\\}\\)-\\(\\\\d\\{2\\}\\)-\\(\\\\d\\{2\\}\\) \\(\\\\d\\{2\\}\\):\\(\\\\d\\{2\\}\\):\\(\\\\d\\{2\\}\\)\\$/,
      "podName": "Kyle-Dion",
      "ppid": "PG-70500",
      "revenueGenerated": "£4,576.00",
      "siteName": "Little site",
      "startedAt": StringMatching /\\^\\(\\\\d\\{4\\}\\)-\\(\\\\d\\{2\\}\\)-\\(\\\\d\\{2\\}\\) \\(\\\\d\\{2\\}\\):\\(\\\\d\\{2\\}\\):\\(\\\\d\\{2\\}\\)\\$/,
      "totalDuration": "3300",
      "userEmail": "daisy.oâ€™<EMAIL>",
      "userName": "Daisy Oâ€™Kelly",
      "vehicle": "-",
    },
    {
      "chargingDuration": "3300",
      "co2Savings": "4325.620",
      "confirmed": true,
      "door": "A",
      "endedAt": StringMatching /\\^\\(\\\\d\\{4\\}\\)-\\(\\\\d\\{2\\}\\)-\\(\\\\d\\{2\\}\\) \\(\\\\d\\{2\\}\\):\\(\\\\d\\{2\\}\\):\\(\\\\d\\{2\\}\\)\\$/,
      "energyCost": "£0.32",
      "energyUsage": "567.89",
      "pluggedIn": StringMatching /\\^\\(\\\\d\\{4\\}\\)-\\(\\\\d\\{2\\}\\)-\\(\\\\d\\{2\\}\\) \\(\\\\d\\{2\\}\\):\\(\\\\d\\{2\\}\\):\\(\\\\d\\{2\\}\\)\\$/,
      "podName": "Zach-Jade",
      "ppid": "PG-86983",
      "revenueGenerated": "£4,576.00",
      "siteName": "Little site",
      "startedAt": StringMatching /\\^\\(\\\\d\\{4\\}\\)-\\(\\\\d\\{2\\}\\)-\\(\\\\d\\{2\\}\\) \\(\\\\d\\{2\\}\\):\\(\\\\d\\{2\\}\\):\\(\\\\d\\{2\\}\\)\\$/,
      "totalDuration": "3300",
      "userEmail": "daisy.oâ€™<EMAIL>",
      "userName": "Daisy Oâ€™Kelly",
      "vehicle": "-",
    },
  ],
  "schedules": [],
  "schemes": [
    {
      "id": 188,
      "name": "Savills UK",
      "uid": "780d9f10-47e7-472a-8367-88a852458efe",
    },
  ],
  "site": {
    "address": {
      "country": "GB",
      "line1": "Tewkesbury Road",
      "line2": "Cheltenham",
      "name": "Gallagher Shopping",
      "postcode": "GL51 9RR",
      "prettyPrint": "Tewkesbury Road, Cheltenham, Gloucestershire, GL51 9RR, GB",
      "town": "Gloucestershire",
    },
    "contactDetails": {
      "email": "<EMAIL>",
      "name": "Martin Maher",
      "telephone": "01688 71894",
    },
    "description": "",
    "energyCost": null,
    "group": {
      "id": 451,
      "name": "Gallagher Shopping",
      "uid": "087e9b6b-1d60-47a0-89e9-87ff0d54f719",
    },
    "id": 1106,
    "parking": {
      "openingTimes": {
        "fri": {
          "allDay": false,
          "from": "08:00:00",
          "to": "19:00:00",
        },
        "mon": {
          "allDay": false,
          "from": "08:00:00",
          "to": "19:00:00",
        },
        "notes": [],
        "sat": {
          "allDay": false,
          "from": "08:00:00",
          "to": "18:00:00",
        },
        "sun": {
          "allDay": false,
          "from": "10:00:00",
          "to": "16:00:00",
        },
        "thu": {
          "allDay": false,
          "from": "08:00:00",
          "to": "19:00:00",
        },
        "tue": {
          "allDay": false,
          "from": "08:00:00",
          "to": "19:00:00",
        },
        "wed": {
          "allDay": false,
          "from": "08:00:00",
          "to": "19:00:00",
        },
      },
    },
  },
  "sockets": [
    {
      "door": "A",
      "firmwareVersion": "Unknown",
      "isUpdateAvailable": false,
      "lastContact": "2022-03-16T11:48:13.000Z",
      "serialNumber": "183110186",
      "status": "Charging",
    },
    {
      "door": "B",
      "firmwareVersion": "Unknown",
      "isUpdateAvailable": false,
      "lastContact": "2022-07-14T07:19:55.000Z",
      "serialNumber": "183110181",
      "status": "Charging",
    },
  ],
  "status": "Available",
  "supportsConfirmCharge": true,
  "supportsContactless": false,
  "supportsEnergyTariff": true,
  "supportsOcpp": false,
  "supportsPerKwh": true,
  "supportsRfid": true,
  "supportsTariffs": true,
}
`;

exports[`destination site admin api pod module pod controller with projections feature flag true should find an individual pod by ppid 1`] = `
{
  "chargeSummary": {
    "chargingDuration": 22245,
    "claimedEnergyUsage": 243.89,
    "co2Savings": 4325.62,
    "energyCost": 324550,
    "energyDelivered": 567.89,
    "energyUsage": 567.89,
    "numberOfCharges": 2048,
    "revenueGenerated": 457600,
    "revenueGeneratingClaimedUsage": 43.5,
  },
  "confirmChargeEnabled": true,
  "coordinates": {
    "latitude": 55.822099,
    "longitude": -4.340059,
  },
  "description": "This is an open charge unit installed at Tesco Extra Silverburn site at 751 Barrhead Road, Glasgow",
  "id": 91647,
  "isEvZone": false,
  "isPublic": true,
  "lastContact": "2022-07-14T08:20:00.000Z",
  "model": "MT7-S-6mA-2-RO",
  "name": "Kyle-Dion",
  "ppid": "PG-70500",
  "recentCharges": [
    {
      "chargingDuration": "3300",
      "co2Savings": "4325.620",
      "confirmed": true,
      "door": "A",
      "endedAt": StringMatching /\\^\\(\\\\d\\{4\\}\\)-\\(\\\\d\\{2\\}\\)-\\(\\\\d\\{2\\}\\) \\(\\\\d\\{2\\}\\):\\(\\\\d\\{2\\}\\):\\(\\\\d\\{2\\}\\)\\$/,
      "energyCost": "£0.32",
      "energyUsage": "567.89",
      "pluggedIn": StringMatching /\\^\\(\\\\d\\{4\\}\\)-\\(\\\\d\\{2\\}\\)-\\(\\\\d\\{2\\}\\) \\(\\\\d\\{2\\}\\):\\(\\\\d\\{2\\}\\):\\(\\\\d\\{2\\}\\)\\$/,
      "podName": "Bill-Tara",
      "ppid": "t53_it1_3717_004",
      "revenueGenerated": "£4,576.00",
      "siteName": "Big site",
      "startedAt": StringMatching /\\^\\(\\\\d\\{4\\}\\)-\\(\\\\d\\{2\\}\\)-\\(\\\\d\\{2\\}\\) \\(\\\\d\\{2\\}\\):\\(\\\\d\\{2\\}\\):\\(\\\\d\\{2\\}\\)\\$/,
      "totalDuration": "3300",
      "userEmail": "-",
      "userName": "-",
      "vehicle": "-",
    },
    {
      "chargingDuration": "3300",
      "co2Savings": "4325.620",
      "confirmed": true,
      "door": "A",
      "endedAt": StringMatching /\\^\\(\\\\d\\{4\\}\\)-\\(\\\\d\\{2\\}\\)-\\(\\\\d\\{2\\}\\) \\(\\\\d\\{2\\}\\):\\(\\\\d\\{2\\}\\):\\(\\\\d\\{2\\}\\)\\$/,
      "energyCost": "£0.32",
      "energyUsage": "567.89",
      "pluggedIn": StringMatching /\\^\\(\\\\d\\{4\\}\\)-\\(\\\\d\\{2\\}\\)-\\(\\\\d\\{2\\}\\) \\(\\\\d\\{2\\}\\):\\(\\\\d\\{2\\}\\):\\(\\\\d\\{2\\}\\)\\$/,
      "podName": "Hank-Neil",
      "ppid": "PG-80435",
      "revenueGenerated": "£4,576.00",
      "siteName": "Big site",
      "startedAt": StringMatching /\\^\\(\\\\d\\{4\\}\\)-\\(\\\\d\\{2\\}\\)-\\(\\\\d\\{2\\}\\) \\(\\\\d\\{2\\}\\):\\(\\\\d\\{2\\}\\):\\(\\\\d\\{2\\}\\)\\$/,
      "totalDuration": "3300",
      "userEmail": "-",
      "userName": "-",
      "vehicle": "-",
    },
    {
      "chargingDuration": "3300",
      "co2Savings": "4325.620",
      "confirmed": true,
      "door": "A",
      "endedAt": StringMatching /\\^\\(\\\\d\\{4\\}\\)-\\(\\\\d\\{2\\}\\)-\\(\\\\d\\{2\\}\\) \\(\\\\d\\{2\\}\\):\\(\\\\d\\{2\\}\\):\\(\\\\d\\{2\\}\\)\\$/,
      "energyCost": "£0.32",
      "energyUsage": "567.89",
      "pluggedIn": StringMatching /\\^\\(\\\\d\\{4\\}\\)-\\(\\\\d\\{2\\}\\)-\\(\\\\d\\{2\\}\\) \\(\\\\d\\{2\\}\\):\\(\\\\d\\{2\\}\\):\\(\\\\d\\{2\\}\\)\\$/,
      "podName": "Kyle-Dion",
      "ppid": "PG-70500",
      "revenueGenerated": "£4,576.00",
      "siteName": "Little site",
      "startedAt": StringMatching /\\^\\(\\\\d\\{4\\}\\)-\\(\\\\d\\{2\\}\\)-\\(\\\\d\\{2\\}\\) \\(\\\\d\\{2\\}\\):\\(\\\\d\\{2\\}\\):\\(\\\\d\\{2\\}\\)\\$/,
      "totalDuration": "3300",
      "userEmail": "-",
      "userName": "-",
      "vehicle": "-",
    },
    {
      "chargingDuration": "3300",
      "co2Savings": "4325.620",
      "confirmed": true,
      "door": "A",
      "endedAt": StringMatching /\\^\\(\\\\d\\{4\\}\\)-\\(\\\\d\\{2\\}\\)-\\(\\\\d\\{2\\}\\) \\(\\\\d\\{2\\}\\):\\(\\\\d\\{2\\}\\):\\(\\\\d\\{2\\}\\)\\$/,
      "energyCost": "£0.32",
      "energyUsage": "567.89",
      "pluggedIn": StringMatching /\\^\\(\\\\d\\{4\\}\\)-\\(\\\\d\\{2\\}\\)-\\(\\\\d\\{2\\}\\) \\(\\\\d\\{2\\}\\):\\(\\\\d\\{2\\}\\):\\(\\\\d\\{2\\}\\)\\$/,
      "podName": "Zach-Jade",
      "ppid": "PG-86983",
      "revenueGenerated": "£4,576.00",
      "siteName": "Little site",
      "startedAt": StringMatching /\\^\\(\\\\d\\{4\\}\\)-\\(\\\\d\\{2\\}\\)-\\(\\\\d\\{2\\}\\) \\(\\\\d\\{2\\}\\):\\(\\\\d\\{2\\}\\):\\(\\\\d\\{2\\}\\)\\$/,
      "totalDuration": "3300",
      "userEmail": "-",
      "userName": "-",
      "vehicle": "-",
    },
  ],
  "schedules": [],
  "schemes": [],
  "site": {
    "address": {
      "country": "GB",
      "line1": "Barrhead Road",
      "line2": "",
      "name": "Tesco Extra - Silverburn",
      "postcode": "G53 6AG",
      "prettyPrint": "Barrhead Road, Glasgow, G53 6AG, GB",
      "town": "Glasgow",
    },
    "contactDetails": {
      "email": "",
      "name": "Raymond Hutchinson",
      "telephone": "07834611729",
    },
    "description": "",
    "energyCost": 15,
    "group": {
      "id": 2,
      "name": "Tesco Stores Ltd",
      "uid": "c9c5d39c-23ba-4a21-ac45-75968fdc055f",
    },
    "id": 75710,
    "parking": {
      "openingTimes": {
        "notes": [
          "For any parking restrictions that may apply, please check for signage at the store.
Max stay period - 2 hours",
        ],
      },
    },
  },
  "sockets": [
    {
      "door": "A",
      "firmwareVersion": "Unknown",
      "isUpdateAvailable": false,
      "lastContact": "2022-07-14T08:20:00.000Z",
      "serialNumber": "213611186",
      "status": "Available",
    },
    {
      "door": "B",
      "firmwareVersion": "Unknown",
      "isUpdateAvailable": false,
      "lastContact": "2022-07-14T07:17:02.000Z",
      "serialNumber": "213611185",
      "status": "Available",
    },
  ],
  "status": "Available",
  "supportsConfirmCharge": true,
  "supportsContactless": false,
  "supportsEnergyTariff": true,
  "supportsOcpp": false,
  "supportsPerKwh": true,
  "supportsRfid": true,
  "supportsTariffs": true,
}
`;

exports[`destination site admin api pod module pod controller with projections feature flag true should find an individual pod by ppid for a scheme 1`] = `
{
  "chargeSummary": {
    "chargingDuration": 22245,
    "claimedEnergyUsage": 243.89,
    "co2Savings": 4325.62,
    "energyCost": 324550,
    "energyDelivered": 567.89,
    "energyUsage": 567.89,
    "numberOfCharges": 2048,
    "revenueGenerated": 457600,
    "revenueGeneratingClaimedUsage": 43.5,
  },
  "confirmChargeEnabled": true,
  "coordinates": {
    "latitude": 51.91904621,
    "longitude": -2.10648304,
  },
  "description": "This is an open charge unit installed at the Gallagher Shopping site in Tewkesbury Road, Cheltenham, Gloucestershire",
  "id": 4090,
  "isEvZone": false,
  "isPublic": true,
  "lastContact": "2022-07-14T07:19:55.000Z",
  "model": "T3-S-2",
  "name": "Hank-Neil",
  "ppid": "PG-80435",
  "recentCharges": [
    {
      "chargingDuration": "3300",
      "co2Savings": "4325.620",
      "confirmed": true,
      "door": "A",
      "endedAt": "2022-09-27 19:00:00",
      "energyCost": "£0.32",
      "energyUsage": "567.89",
      "pluggedIn": StringMatching /\\^\\(\\\\d\\{4\\}\\)-\\(\\\\d\\{2\\}\\)-\\(\\\\d\\{2\\}\\) \\(\\\\d\\{2\\}\\):\\(\\\\d\\{2\\}\\):\\(\\\\d\\{2\\}\\)\\$/,
      "podName": "Bill-Tara",
      "ppid": "t53_it1_3717_004",
      "revenueGenerated": "£4,576.00",
      "siteName": "Big site",
      "startedAt": StringMatching /\\^\\(\\\\d\\{4\\}\\)-\\(\\\\d\\{2\\}\\)-\\(\\\\d\\{2\\}\\) \\(\\\\d\\{2\\}\\):\\(\\\\d\\{2\\}\\):\\(\\\\d\\{2\\}\\)\\$/,
      "totalDuration": "3300",
      "userEmail": "daisy.oâ€™<EMAIL>",
      "userName": "Daisy Oâ€™Kelly",
      "vehicle": "-",
    },
    {
      "chargingDuration": "3300",
      "co2Savings": "4325.620",
      "confirmed": true,
      "door": "A",
      "endedAt": StringMatching /\\^\\(\\\\d\\{4\\}\\)-\\(\\\\d\\{2\\}\\)-\\(\\\\d\\{2\\}\\) \\(\\\\d\\{2\\}\\):\\(\\\\d\\{2\\}\\):\\(\\\\d\\{2\\}\\)\\$/,
      "energyCost": "£0.32",
      "energyUsage": "567.89",
      "pluggedIn": StringMatching /\\^\\(\\\\d\\{4\\}\\)-\\(\\\\d\\{2\\}\\)-\\(\\\\d\\{2\\}\\) \\(\\\\d\\{2\\}\\):\\(\\\\d\\{2\\}\\):\\(\\\\d\\{2\\}\\)\\$/,
      "podName": "Hank-Neil",
      "ppid": "PG-80435",
      "revenueGenerated": "£4,576.00",
      "siteName": "Big site",
      "startedAt": StringMatching /\\^\\(\\\\d\\{4\\}\\)-\\(\\\\d\\{2\\}\\)-\\(\\\\d\\{2\\}\\) \\(\\\\d\\{2\\}\\):\\(\\\\d\\{2\\}\\):\\(\\\\d\\{2\\}\\)\\$/,
      "totalDuration": "3300",
      "userEmail": "<EMAIL>",
      "userName": "Layla Moloney",
      "vehicle": "-",
    },
    {
      "chargingDuration": "3300",
      "co2Savings": "4325.620",
      "confirmed": true,
      "door": "A",
      "endedAt": StringMatching /\\^\\(\\\\d\\{4\\}\\)-\\(\\\\d\\{2\\}\\)-\\(\\\\d\\{2\\}\\) \\(\\\\d\\{2\\}\\):\\(\\\\d\\{2\\}\\):\\(\\\\d\\{2\\}\\)\\$/,
      "energyCost": "£0.32",
      "energyUsage": "567.89",
      "pluggedIn": StringMatching /\\^\\(\\\\d\\{4\\}\\)-\\(\\\\d\\{2\\}\\)-\\(\\\\d\\{2\\}\\) \\(\\\\d\\{2\\}\\):\\(\\\\d\\{2\\}\\):\\(\\\\d\\{2\\}\\)\\$/,
      "podName": "Kyle-Dion",
      "ppid": "PG-70500",
      "revenueGenerated": "£4,576.00",
      "siteName": "Little site",
      "startedAt": StringMatching /\\^\\(\\\\d\\{4\\}\\)-\\(\\\\d\\{2\\}\\)-\\(\\\\d\\{2\\}\\) \\(\\\\d\\{2\\}\\):\\(\\\\d\\{2\\}\\):\\(\\\\d\\{2\\}\\)\\$/,
      "totalDuration": "3300",
      "userEmail": "daisy.oâ€™<EMAIL>",
      "userName": "Daisy Oâ€™Kelly",
      "vehicle": "-",
    },
    {
      "chargingDuration": "3300",
      "co2Savings": "4325.620",
      "confirmed": true,
      "door": "A",
      "endedAt": StringMatching /\\^\\(\\\\d\\{4\\}\\)-\\(\\\\d\\{2\\}\\)-\\(\\\\d\\{2\\}\\) \\(\\\\d\\{2\\}\\):\\(\\\\d\\{2\\}\\):\\(\\\\d\\{2\\}\\)\\$/,
      "energyCost": "£0.32",
      "energyUsage": "567.89",
      "pluggedIn": StringMatching /\\^\\(\\\\d\\{4\\}\\)-\\(\\\\d\\{2\\}\\)-\\(\\\\d\\{2\\}\\) \\(\\\\d\\{2\\}\\):\\(\\\\d\\{2\\}\\):\\(\\\\d\\{2\\}\\)\\$/,
      "podName": "Zach-Jade",
      "ppid": "PG-86983",
      "revenueGenerated": "£4,576.00",
      "siteName": "Little site",
      "startedAt": StringMatching /\\^\\(\\\\d\\{4\\}\\)-\\(\\\\d\\{2\\}\\)-\\(\\\\d\\{2\\}\\) \\(\\\\d\\{2\\}\\):\\(\\\\d\\{2\\}\\):\\(\\\\d\\{2\\}\\)\\$/,
      "totalDuration": "3300",
      "userEmail": "daisy.oâ€™<EMAIL>",
      "userName": "Daisy Oâ€™Kelly",
      "vehicle": "-",
    },
  ],
  "schedules": [],
  "schemes": [
    {
      "id": 188,
      "name": "Savills UK",
      "uid": "780d9f10-47e7-472a-8367-88a852458efe",
    },
  ],
  "site": {
    "address": {
      "country": "GB",
      "line1": "Tewkesbury Road",
      "line2": "Cheltenham",
      "name": "Gallagher Shopping",
      "postcode": "GL51 9RR",
      "prettyPrint": "Tewkesbury Road, Cheltenham, Gloucestershire, GL51 9RR, GB",
      "town": "Gloucestershire",
    },
    "contactDetails": {
      "email": "<EMAIL>",
      "name": "Martin Maher",
      "telephone": "01688 71894",
    },
    "description": "",
    "energyCost": null,
    "group": {
      "id": 451,
      "name": "Gallagher Shopping",
      "uid": "087e9b6b-1d60-47a0-89e9-87ff0d54f719",
    },
    "id": 1106,
    "parking": {
      "openingTimes": {
        "fri": {
          "allDay": false,
          "from": "08:00:00",
          "to": "19:00:00",
        },
        "mon": {
          "allDay": false,
          "from": "08:00:00",
          "to": "19:00:00",
        },
        "notes": [],
        "sat": {
          "allDay": false,
          "from": "08:00:00",
          "to": "18:00:00",
        },
        "sun": {
          "allDay": false,
          "from": "10:00:00",
          "to": "16:00:00",
        },
        "thu": {
          "allDay": false,
          "from": "08:00:00",
          "to": "19:00:00",
        },
        "tue": {
          "allDay": false,
          "from": "08:00:00",
          "to": "19:00:00",
        },
        "wed": {
          "allDay": false,
          "from": "08:00:00",
          "to": "19:00:00",
        },
      },
    },
  },
  "sockets": [
    {
      "door": "A",
      "firmwareVersion": "Unknown",
      "isUpdateAvailable": false,
      "lastContact": "2022-03-16T11:48:13.000Z",
      "serialNumber": "183110186",
      "status": "Charging",
    },
    {
      "door": "B",
      "firmwareVersion": "Unknown",
      "isUpdateAvailable": false,
      "lastContact": "2022-07-14T07:19:55.000Z",
      "serialNumber": "183110181",
      "status": "Charging",
    },
  ],
  "status": "Available",
  "supportsConfirmCharge": true,
  "supportsContactless": false,
  "supportsEnergyTariff": true,
  "supportsOcpp": false,
  "supportsPerKwh": true,
  "supportsRfid": true,
  "supportsTariffs": true,
}
`;

exports[`destination site admin api site module site additional information controller should update by group uid and site id 1`] = `
{
  "address": {
    "country": "GB",
    "line1": "Barrhead Road",
    "line2": "",
    "name": "Tesco Extra - Silverburn",
    "postcode": "G53 6AG",
    "prettyPrint": "Barrhead Road, Glasgow, G53 6AG, GB",
    "town": "Glasgow",
  },
  "chargeSummary": {
    "chargingDuration": 22245,
    "claimedEnergyUsage": 243.89,
    "co2Savings": 4325.62,
    "energyCost": 324550,
    "energyDelivered": 567.89,
    "energyUsage": 567.89,
    "numberOfCharges": 2048,
    "revenueGenerated": 457600,
    "revenueGeneratingClaimedUsage": 43.5,
  },
  "contactDetails": {
    "email": "<EMAIL>",
    "name": "Jane Blogs",
    "telephone": "+44*********",
  },
  "description": "",
  "energyCost": 13,
  "group": {
    "id": 2,
    "name": "Tesco Stores Ltd",
    "uid": "c9c5d39c-23ba-4a21-ac45-75968fdc055f",
  },
  "id": 75710,
  "parking": {
    "openingTimes": {
      "fri": {
        "allDay": false,
        "from": "10:30:00",
        "to": "12:30:00",
      },
      "notes": [
        "Parking limited during the day",
      ],
      "sat": {
        "allDay": false,
        "from": "10:30:00",
        "to": "21:00:00",
      },
      "sun": {
        "allDay": false,
        "from": "10:30:00",
        "to": "21:00:00",
      },
      "thu": {
        "allDay": false,
        "from": "10:30:00",
        "to": "12:30:00",
      },
    },
  },
  "pods": [
    {
      "confirmChargeEnabled": true,
      "coordinates": {
        "latitude": 51.124148,
        "longitude": -0.16234,
      },
      "description": "This is a rapid charger installed at the Tesco Extra superstore in Hazelwick Avenue, Crawley, West Sussex",
      "id": 5189,
      "isEvZone": false,
      "isPublic": false,
      "lastContact": "2022-05-14T20:52:34.000Z",
      "model": "Terra 53 CGT",
      "mostRecentCharge": {
        "chargingDuration": "-",
        "co2Savings": "4.200",
        "confirmed": false,
        "door": "A",
        "endedAt": StringMatching /\\^\\(\\\\d\\{4\\}\\)-\\(\\\\d\\{2\\}\\)-\\(\\\\d\\{2\\}\\) \\(\\\\d\\{2\\}\\):\\(\\\\d\\{2\\}\\):\\(\\\\d\\{2\\}\\)\\$/,
        "energyCost": "£6.00",
        "energyUsage": "7.50",
        "locationId": 5189,
        "pluggedIn": StringMatching /\\^\\(\\\\d\\{4\\}\\)-\\(\\\\d\\{2\\}\\)-\\(\\\\d\\{2\\}\\) \\(\\\\d\\{2\\}\\):\\(\\\\d\\{2\\}\\):\\(\\\\d\\{2\\}\\)\\$/,
        "revenueGenerated": "£0.00",
        "startedAt": StringMatching /\\^\\(\\\\d\\{4\\}\\)-\\(\\\\d\\{2\\}\\)-\\(\\\\d\\{2\\}\\) \\(\\\\d\\{2\\}\\):\\(\\\\d\\{2\\}\\):\\(\\\\d\\{2\\}\\)\\$/,
        "totalDuration": "10",
        "userEmail": "-",
        "userName": "-",
        "vehicle": "Audi A3 e-tron",
      },
      "name": "Bill-Tara",
      "ppid": "t53_it1_3717_004",
      "schemes": [],
      "sockets": [
        {
          "door": "A",
          "firmwareVersion": "Unknown",
          "isUpdateAvailable": false,
          "status": "Available",
        },
        {
          "door": "B",
          "firmwareVersion": "Unknown",
          "isUpdateAvailable": false,
          "status": "Charging",
        },
        {
          "door": "C",
          "firmwareVersion": "Unknown",
          "isUpdateAvailable": false,
          "status": "Unavailable",
        },
      ],
      "status": "Unavailable",
      "supportsConfirmCharge": true,
      "supportsContactless": false,
      "supportsEnergyTariff": true,
      "supportsOcpp": true,
      "supportsPerKwh": true,
      "supportsTariffs": false,
    },
    {
      "confirmChargeEnabled": true,
      "coordinates": {
        "latitude": 55.822099,
        "longitude": -4.340059,
      },
      "description": "This is an open charge unit installed at Tesco Extra Silverburn site at 751 Barrhead Road, Glasgow",
      "id": 91647,
      "isEvZone": false,
      "isPublic": true,
      "lastContact": "2022-07-14T08:20:00.000Z",
      "model": "MT7-S-6mA-2-RO",
      "mostRecentCharge": {
        "chargingDuration": "-",
        "co2Savings": "2.912",
        "confirmed": false,
        "door": "A",
        "endedAt": StringMatching /\\^\\(\\\\d\\{4\\}\\)-\\(\\\\d\\{2\\}\\)-\\(\\\\d\\{2\\}\\) \\(\\\\d\\{2\\}\\):\\(\\\\d\\{2\\}\\):\\(\\\\d\\{2\\}\\)\\$/,
        "energyCost": "£10.00",
        "energyUsage": "5.20",
        "locationId": 91647,
        "pluggedIn": StringMatching /\\^\\(\\\\d\\{4\\}\\)-\\(\\\\d\\{2\\}\\)-\\(\\\\d\\{2\\}\\) \\(\\\\d\\{2\\}\\):\\(\\\\d\\{2\\}\\):\\(\\\\d\\{2\\}\\)\\$/,
        "revenueGenerated": "£0.00",
        "startedAt": StringMatching /\\^\\(\\\\d\\{4\\}\\)-\\(\\\\d\\{2\\}\\)-\\(\\\\d\\{2\\}\\) \\(\\\\d\\{2\\}\\):\\(\\\\d\\{2\\}\\):\\(\\\\d\\{2\\}\\)\\$/,
        "totalDuration": "8",
        "userEmail": "-",
        "userName": "-",
        "vehicle": "Audi A3 e-tron",
      },
      "name": "Kyle-Dion",
      "ppid": "PG-70500",
      "schemes": [],
      "sockets": [
        {
          "door": "A",
          "firmwareVersion": "Unknown",
          "isUpdateAvailable": false,
          "status": "Available",
        },
        {
          "door": "B",
          "firmwareVersion": "Unknown",
          "isUpdateAvailable": false,
          "status": "Available",
        },
      ],
      "status": "Available",
      "supportsConfirmCharge": true,
      "supportsContactless": false,
      "supportsEnergyTariff": true,
      "supportsOcpp": false,
      "supportsPerKwh": true,
      "supportsTariffs": true,
    },
    {
      "confirmChargeEnabled": true,
      "coordinates": {
        "latitude": 55.822099,
        "longitude": -4.340059,
      },
      "description": "This is an open charge unit installed at Tesco Extra Silverburn site at 751 Barrhead Road, Glasgow",
      "id": 91648,
      "isEvZone": false,
      "isPublic": true,
      "lastContact": "2022-07-14T07:19:55.000Z",
      "model": "T7-S-03-WH-TES-6mA",
      "mostRecentCharge": {
        "chargingDuration": "-",
        "co2Savings": "4.200",
        "confirmed": false,
        "door": "A",
        "endedAt": StringMatching /\\^\\(\\\\d\\{4\\}\\)-\\(\\\\d\\{2\\}\\)-\\(\\\\d\\{2\\}\\) \\(\\\\d\\{2\\}\\):\\(\\\\d\\{2\\}\\):\\(\\\\d\\{2\\}\\)\\$/,
        "energyCost": "£6.00",
        "energyUsage": "7.50",
        "locationId": 91648,
        "pluggedIn": StringMatching /\\^\\(\\\\d\\{4\\}\\)-\\(\\\\d\\{2\\}\\)-\\(\\\\d\\{2\\}\\) \\(\\\\d\\{2\\}\\):\\(\\\\d\\{2\\}\\):\\(\\\\d\\{2\\}\\)\\$/,
        "revenueGenerated": "£0.00",
        "startedAt": StringMatching /\\^\\(\\\\d\\{4\\}\\)-\\(\\\\d\\{2\\}\\)-\\(\\\\d\\{2\\}\\) \\(\\\\d\\{2\\}\\):\\(\\\\d\\{2\\}\\):\\(\\\\d\\{2\\}\\)\\$/,
        "totalDuration": "10",
        "userEmail": "-",
        "userName": "-",
        "vehicle": "Audi A3 e-tron",
      },
      "name": "Zach-Jade",
      "ppid": "PG-86983",
      "schemes": [],
      "sockets": [
        {
          "door": "A",
          "firmwareVersion": "Unknown",
          "isUpdateAvailable": false,
          "status": "Available",
        },
        {
          "door": "B",
          "firmwareVersion": "Unknown",
          "isUpdateAvailable": false,
          "status": "Available",
        },
      ],
      "status": "Available",
      "supportsConfirmCharge": true,
      "supportsContactless": false,
      "supportsEnergyTariff": true,
      "supportsOcpp": false,
      "supportsPerKwh": true,
      "supportsTariffs": true,
    },
  ],
}
`;

exports[`destination site admin api site module site contact details controller should update by group uid and site id 1`] = `
{
  "address": {
    "country": "GB",
    "line1": "Barrhead Road",
    "line2": "",
    "name": "Tesco Extra - Silverburn",
    "postcode": "G53 6AG",
    "prettyPrint": "Barrhead Road, Glasgow, G53 6AG, GB",
    "town": "Glasgow",
  },
  "chargeSummary": {
    "chargingDuration": 22245,
    "claimedEnergyUsage": 243.89,
    "co2Savings": 4325.62,
    "energyCost": 324550,
    "energyDelivered": 567.89,
    "energyUsage": 567.89,
    "numberOfCharges": 2048,
    "revenueGenerated": 457600,
    "revenueGeneratingClaimedUsage": 43.5,
  },
  "contactDetails": {
    "email": "<EMAIL>",
    "name": "Jane Blogs",
    "telephone": "+44*********",
  },
  "description": "",
  "energyCost": 13,
  "group": {
    "id": 2,
    "name": "Tesco Stores Ltd",
    "uid": "c9c5d39c-23ba-4a21-ac45-75968fdc055f",
  },
  "id": 75710,
  "parking": {
    "openingTimes": {
      "fri": {
        "allDay": false,
        "from": "10:30:00",
        "to": "12:30:00",
      },
      "notes": [
        "For any parking restrictions that may apply, please check for signage at the store.
Max stay period - 2 hours",
      ],
      "sat": {
        "allDay": false,
        "from": "10:30:00",
        "to": "21:00:00",
      },
      "sun": {
        "allDay": false,
        "from": "10:30:00",
        "to": "21:00:00",
      },
      "thu": {
        "allDay": false,
        "from": "10:30:00",
        "to": "12:30:00",
      },
    },
  },
  "pods": [
    {
      "confirmChargeEnabled": true,
      "coordinates": {
        "latitude": 51.124148,
        "longitude": -0.16234,
      },
      "description": "This is a rapid charger installed at the Tesco Extra superstore in Hazelwick Avenue, Crawley, West Sussex",
      "id": 5189,
      "isEvZone": false,
      "isPublic": false,
      "lastContact": "2022-05-14T20:52:34.000Z",
      "model": "Terra 53 CGT",
      "mostRecentCharge": {
        "chargingDuration": "-",
        "co2Savings": "4.200",
        "confirmed": false,
        "door": "A",
        "endedAt": StringMatching /\\^\\(\\\\d\\{4\\}\\)-\\(\\\\d\\{2\\}\\)-\\(\\\\d\\{2\\}\\) \\(\\\\d\\{2\\}\\):\\(\\\\d\\{2\\}\\):\\(\\\\d\\{2\\}\\)\\$/,
        "energyCost": "£6.00",
        "energyUsage": "7.50",
        "locationId": 5189,
        "pluggedIn": StringMatching /\\^\\(\\\\d\\{4\\}\\)-\\(\\\\d\\{2\\}\\)-\\(\\\\d\\{2\\}\\) \\(\\\\d\\{2\\}\\):\\(\\\\d\\{2\\}\\):\\(\\\\d\\{2\\}\\)\\$/,
        "revenueGenerated": "£0.00",
        "startedAt": StringMatching /\\^\\(\\\\d\\{4\\}\\)-\\(\\\\d\\{2\\}\\)-\\(\\\\d\\{2\\}\\) \\(\\\\d\\{2\\}\\):\\(\\\\d\\{2\\}\\):\\(\\\\d\\{2\\}\\)\\$/,
        "totalDuration": "10",
        "userEmail": "-",
        "userName": "-",
        "vehicle": "Audi A3 e-tron",
      },
      "name": "Bill-Tara",
      "ppid": "t53_it1_3717_004",
      "schemes": [],
      "sockets": [
        {
          "door": "A",
          "firmwareVersion": "Unknown",
          "isUpdateAvailable": false,
          "status": "Available",
        },
        {
          "door": "B",
          "firmwareVersion": "Unknown",
          "isUpdateAvailable": false,
          "status": "Charging",
        },
        {
          "door": "C",
          "firmwareVersion": "Unknown",
          "isUpdateAvailable": false,
          "status": "Unavailable",
        },
      ],
      "status": "Unavailable",
      "supportsConfirmCharge": true,
      "supportsContactless": false,
      "supportsEnergyTariff": true,
      "supportsOcpp": true,
      "supportsPerKwh": true,
      "supportsTariffs": false,
    },
    {
      "confirmChargeEnabled": true,
      "coordinates": {
        "latitude": 55.822099,
        "longitude": -4.340059,
      },
      "description": "This is an open charge unit installed at Tesco Extra Silverburn site at 751 Barrhead Road, Glasgow",
      "id": 91647,
      "isEvZone": false,
      "isPublic": true,
      "lastContact": "2022-07-14T08:20:00.000Z",
      "model": "MT7-S-6mA-2-RO",
      "mostRecentCharge": {
        "chargingDuration": "-",
        "co2Savings": "2.912",
        "confirmed": false,
        "door": "A",
        "endedAt": StringMatching /\\^\\(\\\\d\\{4\\}\\)-\\(\\\\d\\{2\\}\\)-\\(\\\\d\\{2\\}\\) \\(\\\\d\\{2\\}\\):\\(\\\\d\\{2\\}\\):\\(\\\\d\\{2\\}\\)\\$/,
        "energyCost": "£10.00",
        "energyUsage": "5.20",
        "locationId": 91647,
        "pluggedIn": StringMatching /\\^\\(\\\\d\\{4\\}\\)-\\(\\\\d\\{2\\}\\)-\\(\\\\d\\{2\\}\\) \\(\\\\d\\{2\\}\\):\\(\\\\d\\{2\\}\\):\\(\\\\d\\{2\\}\\)\\$/,
        "revenueGenerated": "£0.00",
        "startedAt": StringMatching /\\^\\(\\\\d\\{4\\}\\)-\\(\\\\d\\{2\\}\\)-\\(\\\\d\\{2\\}\\) \\(\\\\d\\{2\\}\\):\\(\\\\d\\{2\\}\\):\\(\\\\d\\{2\\}\\)\\$/,
        "totalDuration": "8",
        "userEmail": "-",
        "userName": "-",
        "vehicle": "Audi A3 e-tron",
      },
      "name": "Kyle-Dion",
      "ppid": "PG-70500",
      "schemes": [],
      "sockets": [
        {
          "door": "A",
          "firmwareVersion": "Unknown",
          "isUpdateAvailable": false,
          "status": "Available",
        },
        {
          "door": "B",
          "firmwareVersion": "Unknown",
          "isUpdateAvailable": false,
          "status": "Available",
        },
      ],
      "status": "Available",
      "supportsConfirmCharge": true,
      "supportsContactless": false,
      "supportsEnergyTariff": true,
      "supportsOcpp": false,
      "supportsPerKwh": true,
      "supportsTariffs": true,
    },
    {
      "confirmChargeEnabled": true,
      "coordinates": {
        "latitude": 55.822099,
        "longitude": -4.340059,
      },
      "description": "This is an open charge unit installed at Tesco Extra Silverburn site at 751 Barrhead Road, Glasgow",
      "id": 91648,
      "isEvZone": false,
      "isPublic": true,
      "lastContact": "2022-07-14T07:19:55.000Z",
      "model": "T7-S-03-WH-TES-6mA",
      "mostRecentCharge": {
        "chargingDuration": "-",
        "co2Savings": "4.200",
        "confirmed": false,
        "door": "A",
        "endedAt": StringMatching /\\^\\(\\\\d\\{4\\}\\)-\\(\\\\d\\{2\\}\\)-\\(\\\\d\\{2\\}\\) \\(\\\\d\\{2\\}\\):\\(\\\\d\\{2\\}\\):\\(\\\\d\\{2\\}\\)\\$/,
        "energyCost": "£6.00",
        "energyUsage": "7.50",
        "locationId": 91648,
        "pluggedIn": StringMatching /\\^\\(\\\\d\\{4\\}\\)-\\(\\\\d\\{2\\}\\)-\\(\\\\d\\{2\\}\\) \\(\\\\d\\{2\\}\\):\\(\\\\d\\{2\\}\\):\\(\\\\d\\{2\\}\\)\\$/,
        "revenueGenerated": "£0.00",
        "startedAt": StringMatching /\\^\\(\\\\d\\{4\\}\\)-\\(\\\\d\\{2\\}\\)-\\(\\\\d\\{2\\}\\) \\(\\\\d\\{2\\}\\):\\(\\\\d\\{2\\}\\):\\(\\\\d\\{2\\}\\)\\$/,
        "totalDuration": "10",
        "userEmail": "-",
        "userName": "-",
        "vehicle": "Audi A3 e-tron",
      },
      "name": "Zach-Jade",
      "ppid": "PG-86983",
      "schemes": [],
      "sockets": [
        {
          "door": "A",
          "firmwareVersion": "Unknown",
          "isUpdateAvailable": false,
          "status": "Available",
        },
        {
          "door": "B",
          "firmwareVersion": "Unknown",
          "isUpdateAvailable": false,
          "status": "Available",
        },
      ],
      "status": "Available",
      "supportsConfirmCharge": true,
      "supportsContactless": false,
      "supportsEnergyTariff": true,
      "supportsOcpp": false,
      "supportsPerKwh": true,
      "supportsTariffs": true,
    },
  ],
}
`;

exports[`destination site admin api site module site contact details controller should update by group uid and site id for a scheme 1`] = `
{
  "address": {
    "country": "GB",
    "line1": "Tewkesbury Road",
    "line2": "Cheltenham",
    "name": "Gallagher Shopping",
    "postcode": "GL51 9RR",
    "prettyPrint": "Tewkesbury Road, Cheltenham, Gloucestershire, GL51 9RR, GB",
    "town": "Gloucestershire",
  },
  "chargeSummary": {
    "chargingDuration": 22245,
    "claimedEnergyUsage": 243.89,
    "co2Savings": 4325.62,
    "energyCost": 324550,
    "energyDelivered": 567.89,
    "energyUsage": 567.89,
    "numberOfCharges": 2048,
    "revenueGenerated": 457600,
    "revenueGeneratingClaimedUsage": 43.5,
  },
  "contactDetails": {
    "email": "<EMAIL>",
    "name": "Jane Blogs",
    "telephone": "+44*********",
  },
  "description": "",
  "energyCost": 15,
  "group": {
    "id": 451,
    "name": "Gallagher Shopping",
    "uid": "087e9b6b-1d60-47a0-89e9-87ff0d54f719",
  },
  "id": 1106,
  "parking": {
    "openingTimes": {
      "fri": {
        "allDay": false,
        "from": "08:00:00",
        "to": "19:00:00",
      },
      "mon": {
        "allDay": false,
        "from": "08:00:00",
        "to": "19:00:00",
      },
      "notes": [],
      "sat": {
        "allDay": false,
        "from": "08:00:00",
        "to": "18:00:00",
      },
      "sun": {
        "allDay": false,
        "from": "10:00:00",
        "to": "16:00:00",
      },
      "thu": {
        "allDay": false,
        "from": "08:00:00",
        "to": "19:00:00",
      },
      "tue": {
        "allDay": false,
        "from": "08:00:00",
        "to": "19:00:00",
      },
      "wed": {
        "allDay": false,
        "from": "08:00:00",
        "to": "19:00:00",
      },
    },
  },
  "pods": [
    {
      "confirmChargeEnabled": true,
      "coordinates": {
        "latitude": 51.91904621,
        "longitude": -2.10648304,
      },
      "description": "This is an open charge unit installed at the Gallagher Shopping site in Tewkesbury Road, Cheltenham, Gloucestershire",
      "id": 4090,
      "isEvZone": false,
      "isPublic": true,
      "lastContact": "2022-07-14T07:19:55.000Z",
      "model": "T3-S-2",
      "mostRecentCharge": {
        "chargingDuration": "3610",
        "co2Savings": "0.952",
        "confirmed": true,
        "confirmedBy": "driver",
        "door": "A",
        "endedAt": StringMatching /\\^\\(\\\\d\\{4\\}\\)-\\(\\\\d\\{2\\}\\)-\\(\\\\d\\{2\\}\\) \\(\\\\d\\{2\\}\\):\\(\\\\d\\{2\\}\\):\\(\\\\d\\{2\\}\\)\\$/,
        "energyCost": "£0.24",
        "energyUsage": "1.70",
        "locationId": 4090,
        "pluggedIn": StringMatching /\\^\\(\\\\d\\{4\\}\\)-\\(\\\\d\\{2\\}\\)-\\(\\\\d\\{2\\}\\) \\(\\\\d\\{2\\}\\):\\(\\\\d\\{2\\}\\):\\(\\\\d\\{2\\}\\)\\$/,
        "revenueGenerated": "£0.00",
        "startedAt": StringMatching /\\^\\(\\\\d\\{4\\}\\)-\\(\\\\d\\{2\\}\\)-\\(\\\\d\\{2\\}\\) \\(\\\\d\\{2\\}\\):\\(\\\\d\\{2\\}\\):\\(\\\\d\\{2\\}\\)\\$/,
        "totalDuration": "3625",
        "userEmail": "-",
        "userName": "-",
        "vehicle": "-",
      },
      "name": "Hank-Neil",
      "ppid": "PG-80435",
      "schemes": [
        {
          "id": 188,
          "name": "Savills UK",
          "uid": "780d9f10-47e7-472a-8367-88a852458efe",
        },
      ],
      "sockets": [
        {
          "door": "A",
          "firmwareVersion": "Unknown",
          "isUpdateAvailable": false,
          "status": "Charging",
        },
        {
          "door": "B",
          "firmwareVersion": "Unknown",
          "isUpdateAvailable": false,
          "status": "Charging",
        },
      ],
      "status": "Available",
      "supportsConfirmCharge": true,
      "supportsContactless": false,
      "supportsEnergyTariff": true,
      "supportsOcpp": false,
      "supportsPerKwh": true,
      "supportsTariffs": true,
    },
  ],
}
`;

exports[`destination site admin api site module site controller should find a list of sites 1`] = `
[
  {
    "address": {
      "country": "GB",
      "line1": "Barrhead Road",
      "line2": "",
      "name": "Tesco Extra - Silverburn",
      "postcode": "G53 6AG",
      "prettyPrint": "Barrhead Road, Glasgow, G53 6AG, GB",
      "town": "Glasgow",
    },
    "contactDetails": {
      "email": "",
      "name": "Raymond Hutchinson",
      "telephone": "07834611729",
    },
    "description": "",
    "energyCost": 15,
    "group": {
      "id": 2,
      "name": "Tesco Stores Ltd",
      "uid": "c9c5d39c-23ba-4a21-ac45-75968fdc055f",
    },
    "id": 75710,
    "parking": {},
    "pods": [
      {
        "confirmChargeEnabled": true,
        "coordinates": {
          "latitude": 51.124148,
          "longitude": -0.16234,
        },
        "description": "This is a rapid charger installed at the Tesco Extra superstore in Hazelwick Avenue, Crawley, West Sussex",
        "id": 5189,
        "isEvZone": false,
        "isPublic": false,
        "lastContact": "2022-05-14T20:52:34.000Z",
        "name": "Bill-Tara",
        "ppid": "t53_it1_3717_004",
        "schemes": [],
        "status": "Unavailable",
        "supportsConfirmCharge": false,
        "supportsContactless": false,
        "supportsEnergyTariff": false,
        "supportsOcpp": false,
        "supportsPerKwh": true,
        "supportsTariffs": false,
      },
      {
        "confirmChargeEnabled": true,
        "coordinates": {
          "latitude": 55.822099,
          "longitude": -4.340059,
        },
        "description": "This is an open charge unit installed at Tesco Extra Silverburn site at 751 Barrhead Road, Glasgow",
        "id": 91647,
        "isEvZone": false,
        "isPublic": true,
        "lastContact": "2022-07-14T08:20:00.000Z",
        "name": "Kyle-Dion",
        "ppid": "PG-70500",
        "schemes": [],
        "status": "Unavailable",
        "supportsConfirmCharge": false,
        "supportsContactless": false,
        "supportsEnergyTariff": false,
        "supportsOcpp": false,
        "supportsPerKwh": true,
        "supportsTariffs": false,
      },
      {
        "confirmChargeEnabled": true,
        "coordinates": {
          "latitude": 55.822099,
          "longitude": -4.340059,
        },
        "description": "This is an open charge unit installed at Tesco Extra Silverburn site at 751 Barrhead Road, Glasgow",
        "id": 91648,
        "isEvZone": false,
        "isPublic": true,
        "lastContact": "2022-07-14T07:19:55.000Z",
        "name": "Zach-Jade",
        "ppid": "PG-86983",
        "schemes": [],
        "status": "Unavailable",
        "supportsConfirmCharge": false,
        "supportsContactless": false,
        "supportsEnergyTariff": false,
        "supportsOcpp": false,
        "supportsPerKwh": true,
        "supportsTariffs": false,
      },
    ],
  },
]
`;

exports[`destination site admin api site module site controller should find a list of sites for a scheme 1`] = `
[
  {
    "address": {
      "country": "GB",
      "line1": "30 Coliseum Way",
      "line2": "",
      "name": "Coliseum Shopping Park",
      "postcode": "CH65 9HD",
      "prettyPrint": "30 Coliseum Way, Ellesmere Port, CH65 9HD, GB",
      "town": "Ellesmere Port",
    },
    "contactDetails": {
      "email": "<EMAIL>",
      "name": "Ella O'Reilly",
      "telephone": "06718 264434",
    },
    "description": "",
    "energyCost": null,
    "group": {
      "id": 336,
      "name": "Coliseum Shopping Park",
      "uid": "b2de66af-834c-4dd6-b119-99d94ffc2abe",
    },
    "id": 963,
    "parking": {},
    "pods": [
      {
        "confirmChargeEnabled": true,
        "coordinates": {
          "latitude": 53.264091,
          "longitude": -2.885418,
        },
        "description": "This is an open charge unit located at The Coliseum Shopping retail park in Ellesmere Port",
        "id": 3754,
        "isEvZone": false,
        "isPublic": true,
        "lastContact": "2019-07-04T14:51:39.000Z",
        "name": "Tony-Kate",
        "ppid": "PG-80367",
        "schemes": [
          {
            "id": 188,
            "name": "Savills UK",
            "uid": "780d9f10-47e7-472a-8367-88a852458efe",
          },
        ],
        "status": "Unavailable",
        "supportsConfirmCharge": false,
        "supportsContactless": false,
        "supportsEnergyTariff": false,
        "supportsOcpp": false,
        "supportsPerKwh": false,
        "supportsTariffs": false,
      },
    ],
  },
  {
    "address": {
      "country": "GB",
      "line1": "Tewkesbury Road",
      "line2": "Cheltenham",
      "name": "Gallagher Shopping",
      "postcode": "GL51 9RR",
      "prettyPrint": "Tewkesbury Road, Cheltenham, Gloucestershire, GL51 9RR, GB",
      "town": "Gloucestershire",
    },
    "contactDetails": {
      "email": "<EMAIL>",
      "name": "Martin Maher",
      "telephone": "01688 71894",
    },
    "description": "",
    "energyCost": null,
    "group": {
      "id": 451,
      "name": "Gallagher Shopping",
      "uid": "087e9b6b-1d60-47a0-89e9-87ff0d54f719",
    },
    "id": 1106,
    "parking": {},
    "pods": [
      {
        "confirmChargeEnabled": true,
        "coordinates": {
          "latitude": 51.91904621,
          "longitude": -2.10648304,
        },
        "description": "This is an open charge unit installed at the Gallagher Shopping site in Tewkesbury Road, Cheltenham, Gloucestershire",
        "id": 4090,
        "isEvZone": false,
        "isPublic": true,
        "lastContact": "2022-07-14T07:19:55.000Z",
        "name": "Hank-Neil",
        "ppid": "PG-80435",
        "schemes": [
          {
            "id": 188,
            "name": "Savills UK",
            "uid": "780d9f10-47e7-472a-8367-88a852458efe",
          },
        ],
        "status": "Unavailable",
        "supportsConfirmCharge": false,
        "supportsContactless": false,
        "supportsEnergyTariff": false,
        "supportsOcpp": false,
        "supportsPerKwh": true,
        "supportsTariffs": false,
      },
    ],
  },
]
`;

exports[`destination site admin api site module site controller with projections feature flag false should find an individual site 1`] = `
{
  "address": {
    "country": "GB",
    "line1": "Barrhead Road",
    "line2": "",
    "name": "Tesco Extra - Silverburn",
    "postcode": "G53 6AG",
    "prettyPrint": "Barrhead Road, Glasgow, G53 6AG, GB",
    "town": "Glasgow",
  },
  "chargeSummary": {
    "chargingDuration": 22245,
    "claimedEnergyUsage": 243.89,
    "co2Savings": 4325.62,
    "energyCost": 324550,
    "energyDelivered": 567.89,
    "energyUsage": 567.89,
    "numberOfCharges": 2048,
    "revenueGenerated": 457600,
    "revenueGeneratingClaimedUsage": 43.5,
  },
  "contactDetails": {
    "email": "",
    "name": "Raymond Hutchinson",
    "telephone": "07834611729",
  },
  "description": "",
  "energyCost": 13,
  "group": {
    "id": 2,
    "name": "Tesco Stores Ltd",
    "uid": "c9c5d39c-23ba-4a21-ac45-75968fdc055f",
  },
  "id": 75710,
  "parking": {
    "openingTimes": {
      "notes": [
        "For any parking restrictions that may apply, please check for signage at the store.
Max stay period - 2 hours",
      ],
    },
  },
  "pods": [
    {
      "confirmChargeEnabled": true,
      "coordinates": {
        "latitude": 51.124148,
        "longitude": -0.16234,
      },
      "description": "This is a rapid charger installed at the Tesco Extra superstore in Hazelwick Avenue, Crawley, West Sussex",
      "id": 5189,
      "isEvZone": false,
      "isPublic": false,
      "lastContact": "2022-05-14T20:52:34.000Z",
      "model": "Terra 53 CGT",
      "mostRecentCharge": {
        "chargingDuration": "-",
        "co2Savings": "4.200",
        "confirmed": false,
        "door": "A",
        "endedAt": StringMatching /\\^\\(\\\\d\\{4\\}\\)-\\(\\\\d\\{2\\}\\)-\\(\\\\d\\{2\\}\\) \\(\\\\d\\{2\\}\\):\\(\\\\d\\{2\\}\\):\\(\\\\d\\{2\\}\\)\\$/,
        "energyCost": "£6.00",
        "energyUsage": "7.50",
        "locationId": 5189,
        "pluggedIn": StringMatching /\\^\\(\\\\d\\{4\\}\\)-\\(\\\\d\\{2\\}\\)-\\(\\\\d\\{2\\}\\) \\(\\\\d\\{2\\}\\):\\(\\\\d\\{2\\}\\):\\(\\\\d\\{2\\}\\)\\$/,
        "revenueGenerated": "£0.00",
        "startedAt": StringMatching /\\^\\(\\\\d\\{4\\}\\)-\\(\\\\d\\{2\\}\\)-\\(\\\\d\\{2\\}\\) \\(\\\\d\\{2\\}\\):\\(\\\\d\\{2\\}\\):\\(\\\\d\\{2\\}\\)\\$/,
        "totalDuration": "10",
        "userEmail": "-",
        "userName": "-",
        "vehicle": "Audi A3 e-tron",
      },
      "name": "Bill-Tara",
      "ppid": "t53_it1_3717_004",
      "schemes": [],
      "sockets": [
        {
          "door": "A",
          "firmwareVersion": "Unknown",
          "isUpdateAvailable": false,
          "status": "Available",
        },
        {
          "door": "B",
          "firmwareVersion": "Unknown",
          "isUpdateAvailable": false,
          "status": "Charging",
        },
        {
          "door": "C",
          "firmwareVersion": "Unknown",
          "isUpdateAvailable": false,
          "status": "Unavailable",
        },
      ],
      "status": "Unavailable",
      "supportsConfirmCharge": true,
      "supportsContactless": false,
      "supportsEnergyTariff": true,
      "supportsOcpp": true,
      "supportsPerKwh": true,
      "supportsTariffs": false,
    },
    {
      "confirmChargeEnabled": true,
      "coordinates": {
        "latitude": 55.822099,
        "longitude": -4.340059,
      },
      "description": "This is an open charge unit installed at Tesco Extra Silverburn site at 751 Barrhead Road, Glasgow",
      "id": 91647,
      "isEvZone": false,
      "isPublic": true,
      "lastContact": "2022-07-14T08:20:00.000Z",
      "model": "MT7-S-6mA-2-RO",
      "mostRecentCharge": {
        "chargingDuration": "-",
        "co2Savings": "2.912",
        "confirmed": false,
        "door": "A",
        "endedAt": StringMatching /\\^\\(\\\\d\\{4\\}\\)-\\(\\\\d\\{2\\}\\)-\\(\\\\d\\{2\\}\\) \\(\\\\d\\{2\\}\\):\\(\\\\d\\{2\\}\\):\\(\\\\d\\{2\\}\\)\\$/,
        "energyCost": "£10.00",
        "energyUsage": "5.20",
        "locationId": 91647,
        "pluggedIn": StringMatching /\\^\\(\\\\d\\{4\\}\\)-\\(\\\\d\\{2\\}\\)-\\(\\\\d\\{2\\}\\) \\(\\\\d\\{2\\}\\):\\(\\\\d\\{2\\}\\):\\(\\\\d\\{2\\}\\)\\$/,
        "revenueGenerated": "£0.00",
        "startedAt": StringMatching /\\^\\(\\\\d\\{4\\}\\)-\\(\\\\d\\{2\\}\\)-\\(\\\\d\\{2\\}\\) \\(\\\\d\\{2\\}\\):\\(\\\\d\\{2\\}\\):\\(\\\\d\\{2\\}\\)\\$/,
        "totalDuration": "8",
        "userEmail": "-",
        "userName": "-",
        "vehicle": "Audi A3 e-tron",
      },
      "name": "Kyle-Dion",
      "ppid": "PG-70500",
      "schemes": [],
      "sockets": [
        {
          "door": "A",
          "firmwareVersion": "Unknown",
          "isUpdateAvailable": false,
          "status": "Available",
        },
        {
          "door": "B",
          "firmwareVersion": "Unknown",
          "isUpdateAvailable": false,
          "status": "Available",
        },
      ],
      "status": "Available",
      "supportsConfirmCharge": true,
      "supportsContactless": false,
      "supportsEnergyTariff": true,
      "supportsOcpp": false,
      "supportsPerKwh": true,
      "supportsTariffs": true,
    },
    {
      "confirmChargeEnabled": true,
      "coordinates": {
        "latitude": 55.822099,
        "longitude": -4.340059,
      },
      "description": "This is an open charge unit installed at Tesco Extra Silverburn site at 751 Barrhead Road, Glasgow",
      "id": 91648,
      "isEvZone": false,
      "isPublic": true,
      "lastContact": "2022-07-14T07:19:55.000Z",
      "model": "T7-S-03-WH-TES-6mA",
      "mostRecentCharge": {
        "chargingDuration": "-",
        "co2Savings": "4.200",
        "confirmed": false,
        "door": "A",
        "endedAt": StringMatching /\\^\\(\\\\d\\{4\\}\\)-\\(\\\\d\\{2\\}\\)-\\(\\\\d\\{2\\}\\) \\(\\\\d\\{2\\}\\):\\(\\\\d\\{2\\}\\):\\(\\\\d\\{2\\}\\)\\$/,
        "energyCost": "£6.00",
        "energyUsage": "7.50",
        "locationId": 91648,
        "pluggedIn": StringMatching /\\^\\(\\\\d\\{4\\}\\)-\\(\\\\d\\{2\\}\\)-\\(\\\\d\\{2\\}\\) \\(\\\\d\\{2\\}\\):\\(\\\\d\\{2\\}\\):\\(\\\\d\\{2\\}\\)\\$/,
        "revenueGenerated": "£0.00",
        "startedAt": StringMatching /\\^\\(\\\\d\\{4\\}\\)-\\(\\\\d\\{2\\}\\)-\\(\\\\d\\{2\\}\\) \\(\\\\d\\{2\\}\\):\\(\\\\d\\{2\\}\\):\\(\\\\d\\{2\\}\\)\\$/,
        "totalDuration": "10",
        "userEmail": "-",
        "userName": "-",
        "vehicle": "Audi A3 e-tron",
      },
      "name": "Zach-Jade",
      "ppid": "PG-86983",
      "schemes": [],
      "sockets": [
        {
          "door": "A",
          "firmwareVersion": "Unknown",
          "isUpdateAvailable": false,
          "status": "Available",
        },
        {
          "door": "B",
          "firmwareVersion": "Unknown",
          "isUpdateAvailable": false,
          "status": "Available",
        },
      ],
      "status": "Available",
      "supportsConfirmCharge": true,
      "supportsContactless": false,
      "supportsEnergyTariff": true,
      "supportsOcpp": false,
      "supportsPerKwh": true,
      "supportsTariffs": true,
    },
  ],
}
`;

exports[`destination site admin api site module site controller with projections feature flag false should find an individual site for a scheme 1`] = `
{
  "address": {
    "country": "GB",
    "line1": "Tewkesbury Road",
    "line2": "Cheltenham",
    "name": "Gallagher Shopping",
    "postcode": "GL51 9RR",
    "prettyPrint": "Tewkesbury Road, Cheltenham, Gloucestershire, GL51 9RR, GB",
    "town": "Gloucestershire",
  },
  "chargeSummary": {
    "chargingDuration": 22245,
    "claimedEnergyUsage": 243.89,
    "co2Savings": 4325.62,
    "energyCost": 324550,
    "energyDelivered": 567.89,
    "energyUsage": 567.89,
    "numberOfCharges": 2048,
    "revenueGenerated": 457600,
    "revenueGeneratingClaimedUsage": 43.5,
  },
  "contactDetails": {
    "email": "<EMAIL>",
    "name": "Martin Maher",
    "telephone": "01688 71894",
  },
  "description": "",
  "energyCost": 15,
  "group": {
    "id": 451,
    "name": "Gallagher Shopping",
    "uid": "087e9b6b-1d60-47a0-89e9-87ff0d54f719",
  },
  "id": 1106,
  "parking": {
    "openingTimes": {
      "fri": {
        "allDay": false,
        "from": "08:00:00",
        "to": "19:00:00",
      },
      "mon": {
        "allDay": false,
        "from": "08:00:00",
        "to": "19:00:00",
      },
      "notes": [],
      "sat": {
        "allDay": false,
        "from": "08:00:00",
        "to": "18:00:00",
      },
      "sun": {
        "allDay": false,
        "from": "10:00:00",
        "to": "16:00:00",
      },
      "thu": {
        "allDay": false,
        "from": "08:00:00",
        "to": "19:00:00",
      },
      "tue": {
        "allDay": false,
        "from": "08:00:00",
        "to": "19:00:00",
      },
      "wed": {
        "allDay": false,
        "from": "08:00:00",
        "to": "19:00:00",
      },
    },
  },
  "pods": [
    {
      "confirmChargeEnabled": true,
      "coordinates": {
        "latitude": 51.91904621,
        "longitude": -2.10648304,
      },
      "description": "This is an open charge unit installed at the Gallagher Shopping site in Tewkesbury Road, Cheltenham, Gloucestershire",
      "id": 4090,
      "isEvZone": false,
      "isPublic": true,
      "lastContact": "2022-07-14T07:19:55.000Z",
      "model": "T3-S-2",
      "mostRecentCharge": {
        "chargingDuration": "3610",
        "co2Savings": "0.952",
        "confirmed": true,
        "confirmedBy": "driver",
        "door": "A",
        "endedAt": StringMatching /\\^\\(\\\\d\\{4\\}\\)-\\(\\\\d\\{2\\}\\)-\\(\\\\d\\{2\\}\\) \\(\\\\d\\{2\\}\\):\\(\\\\d\\{2\\}\\):\\(\\\\d\\{2\\}\\)\\$/,
        "energyCost": "£0.24",
        "energyUsage": "1.70",
        "locationId": 4090,
        "pluggedIn": StringMatching /\\^\\(\\\\d\\{4\\}\\)-\\(\\\\d\\{2\\}\\)-\\(\\\\d\\{2\\}\\) \\(\\\\d\\{2\\}\\):\\(\\\\d\\{2\\}\\):\\(\\\\d\\{2\\}\\)\\$/,
        "revenueGenerated": "£0.00",
        "startedAt": StringMatching /\\^\\(\\\\d\\{4\\}\\)-\\(\\\\d\\{2\\}\\)-\\(\\\\d\\{2\\}\\) \\(\\\\d\\{2\\}\\):\\(\\\\d\\{2\\}\\):\\(\\\\d\\{2\\}\\)\\$/,
        "totalDuration": "3625",
        "userEmail": "-",
        "userName": "-",
        "vehicle": "-",
      },
      "name": "Hank-Neil",
      "ppid": "PG-80435",
      "schemes": [
        {
          "id": 188,
          "name": "Savills UK",
          "uid": "780d9f10-47e7-472a-8367-88a852458efe",
        },
      ],
      "sockets": [
        {
          "door": "A",
          "firmwareVersion": "Unknown",
          "isUpdateAvailable": false,
          "status": "Charging",
        },
        {
          "door": "B",
          "firmwareVersion": "Unknown",
          "isUpdateAvailable": false,
          "status": "Charging",
        },
      ],
      "status": "Available",
      "supportsConfirmCharge": true,
      "supportsContactless": false,
      "supportsEnergyTariff": true,
      "supportsOcpp": false,
      "supportsPerKwh": true,
      "supportsTariffs": true,
    },
  ],
}
`;

exports[`destination site admin api site module site controller with projections feature flag true should find an individual site 1`] = `
{
  "address": {
    "country": "GB",
    "line1": "Barrhead Road",
    "line2": "",
    "name": "Tesco Extra - Silverburn",
    "postcode": "G53 6AG",
    "prettyPrint": "Barrhead Road, Glasgow, G53 6AG, GB",
    "town": "Glasgow",
  },
  "chargeSummary": {
    "chargingDuration": 22245,
    "claimedEnergyUsage": 243.89,
    "co2Savings": 4325.62,
    "energyCost": 324550,
    "energyDelivered": 567.89,
    "energyUsage": 567.89,
    "numberOfCharges": 2048,
    "revenueGenerated": 457600,
    "revenueGeneratingClaimedUsage": 43.5,
  },
  "contactDetails": {
    "email": "",
    "name": "Raymond Hutchinson",
    "telephone": "07834611729",
  },
  "description": "",
  "energyCost": 13,
  "group": {
    "id": 2,
    "name": "Tesco Stores Ltd",
    "uid": "c9c5d39c-23ba-4a21-ac45-75968fdc055f",
  },
  "id": 75710,
  "parking": {
    "openingTimes": {
      "notes": [
        "For any parking restrictions that may apply, please check for signage at the store.
Max stay period - 2 hours",
      ],
    },
  },
  "pods": [
    {
      "confirmChargeEnabled": true,
      "coordinates": {
        "latitude": 51.124148,
        "longitude": -0.16234,
      },
      "description": "This is a rapid charger installed at the Tesco Extra superstore in Hazelwick Avenue, Crawley, West Sussex",
      "id": 5189,
      "isEvZone": false,
      "isPublic": false,
      "lastContact": "2022-05-14T20:52:34.000Z",
      "model": "Terra 53 CGT",
      "mostRecentCharge": {
        "chargingDuration": "3300",
        "co2Savings": "4325.620",
        "confirmed": true,
        "door": "A",
        "endedAt": StringMatching /\\^\\(\\\\d\\{4\\}\\)-\\(\\\\d\\{2\\}\\)-\\(\\\\d\\{2\\}\\) \\(\\\\d\\{2\\}\\):\\(\\\\d\\{2\\}\\):\\(\\\\d\\{2\\}\\)\\$/,
        "energyCost": "£0.32",
        "energyUsage": "567.89",
        "pluggedIn": StringMatching /\\^\\(\\\\d\\{4\\}\\)-\\(\\\\d\\{2\\}\\)-\\(\\\\d\\{2\\}\\) \\(\\\\d\\{2\\}\\):\\(\\\\d\\{2\\}\\):\\(\\\\d\\{2\\}\\)\\$/,
        "podName": "Bill-Tara",
        "ppid": "t53_it1_3717_004",
        "revenueGenerated": "£4,576.00",
        "siteName": "Big site",
        "startedAt": StringMatching /\\^\\(\\\\d\\{4\\}\\)-\\(\\\\d\\{2\\}\\)-\\(\\\\d\\{2\\}\\) \\(\\\\d\\{2\\}\\):\\(\\\\d\\{2\\}\\):\\(\\\\d\\{2\\}\\)\\$/,
        "totalDuration": "3300",
        "userEmail": "-",
        "userName": "-",
        "vehicle": "-",
      },
      "name": "Bill-Tara",
      "ppid": "t53_it1_3717_004",
      "schemes": [],
      "sockets": [
        {
          "door": "A",
          "firmwareVersion": "Unknown",
          "isUpdateAvailable": false,
          "status": "Available",
        },
        {
          "door": "B",
          "firmwareVersion": "Unknown",
          "isUpdateAvailable": false,
          "status": "Charging",
        },
        {
          "door": "C",
          "firmwareVersion": "Unknown",
          "isUpdateAvailable": false,
          "status": "Unavailable",
        },
      ],
      "status": "Unavailable",
      "supportsConfirmCharge": true,
      "supportsContactless": false,
      "supportsEnergyTariff": true,
      "supportsOcpp": true,
      "supportsPerKwh": true,
      "supportsTariffs": false,
    },
    {
      "confirmChargeEnabled": true,
      "coordinates": {
        "latitude": 55.822099,
        "longitude": -4.340059,
      },
      "description": "This is an open charge unit installed at Tesco Extra Silverburn site at 751 Barrhead Road, Glasgow",
      "id": 91647,
      "isEvZone": false,
      "isPublic": true,
      "lastContact": "2022-07-14T08:20:00.000Z",
      "model": "MT7-S-6mA-2-RO",
      "mostRecentCharge": {
        "chargingDuration": "3300",
        "co2Savings": "4325.620",
        "confirmed": true,
        "door": "A",
        "endedAt": StringMatching /\\^\\(\\\\d\\{4\\}\\)-\\(\\\\d\\{2\\}\\)-\\(\\\\d\\{2\\}\\) \\(\\\\d\\{2\\}\\):\\(\\\\d\\{2\\}\\):\\(\\\\d\\{2\\}\\)\\$/,
        "energyCost": "£0.32",
        "energyUsage": "567.89",
        "pluggedIn": StringMatching /\\^\\(\\\\d\\{4\\}\\)-\\(\\\\d\\{2\\}\\)-\\(\\\\d\\{2\\}\\) \\(\\\\d\\{2\\}\\):\\(\\\\d\\{2\\}\\):\\(\\\\d\\{2\\}\\)\\$/,
        "podName": "Kyle-Dion",
        "ppid": "PG-70500",
        "revenueGenerated": "£4,576.00",
        "siteName": "Little site",
        "startedAt": StringMatching /\\^\\(\\\\d\\{4\\}\\)-\\(\\\\d\\{2\\}\\)-\\(\\\\d\\{2\\}\\) \\(\\\\d\\{2\\}\\):\\(\\\\d\\{2\\}\\):\\(\\\\d\\{2\\}\\)\\$/,
        "totalDuration": "3300",
        "userEmail": "-",
        "userName": "-",
        "vehicle": "-",
      },
      "name": "Kyle-Dion",
      "ppid": "PG-70500",
      "schemes": [],
      "sockets": [
        {
          "door": "A",
          "firmwareVersion": "Unknown",
          "isUpdateAvailable": false,
          "status": "Available",
        },
        {
          "door": "B",
          "firmwareVersion": "Unknown",
          "isUpdateAvailable": false,
          "status": "Available",
        },
      ],
      "status": "Available",
      "supportsConfirmCharge": true,
      "supportsContactless": false,
      "supportsEnergyTariff": true,
      "supportsOcpp": false,
      "supportsPerKwh": true,
      "supportsTariffs": true,
    },
    {
      "confirmChargeEnabled": true,
      "coordinates": {
        "latitude": 55.822099,
        "longitude": -4.340059,
      },
      "description": "This is an open charge unit installed at Tesco Extra Silverburn site at 751 Barrhead Road, Glasgow",
      "id": 91648,
      "isEvZone": false,
      "isPublic": true,
      "lastContact": "2022-07-14T07:19:55.000Z",
      "model": "T7-S-03-WH-TES-6mA",
      "mostRecentCharge": {
        "chargingDuration": "3300",
        "co2Savings": "4325.620",
        "confirmed": true,
        "door": "A",
        "endedAt": StringMatching /\\^\\(\\\\d\\{4\\}\\)-\\(\\\\d\\{2\\}\\)-\\(\\\\d\\{2\\}\\) \\(\\\\d\\{2\\}\\):\\(\\\\d\\{2\\}\\):\\(\\\\d\\{2\\}\\)\\$/,
        "energyCost": "£0.32",
        "energyUsage": "567.89",
        "pluggedIn": StringMatching /\\^\\(\\\\d\\{4\\}\\)-\\(\\\\d\\{2\\}\\)-\\(\\\\d\\{2\\}\\) \\(\\\\d\\{2\\}\\):\\(\\\\d\\{2\\}\\):\\(\\\\d\\{2\\}\\)\\$/,
        "podName": "Zach-Jade",
        "ppid": "PG-86983",
        "revenueGenerated": "£4,576.00",
        "siteName": "Little site",
        "startedAt": StringMatching /\\^\\(\\\\d\\{4\\}\\)-\\(\\\\d\\{2\\}\\)-\\(\\\\d\\{2\\}\\) \\(\\\\d\\{2\\}\\):\\(\\\\d\\{2\\}\\):\\(\\\\d\\{2\\}\\)\\$/,
        "totalDuration": "3300",
        "userEmail": "-",
        "userName": "-",
        "vehicle": "-",
      },
      "name": "Zach-Jade",
      "ppid": "PG-86983",
      "schemes": [],
      "sockets": [
        {
          "door": "A",
          "firmwareVersion": "Unknown",
          "isUpdateAvailable": false,
          "status": "Available",
        },
        {
          "door": "B",
          "firmwareVersion": "Unknown",
          "isUpdateAvailable": false,
          "status": "Available",
        },
      ],
      "status": "Available",
      "supportsConfirmCharge": true,
      "supportsContactless": false,
      "supportsEnergyTariff": true,
      "supportsOcpp": false,
      "supportsPerKwh": true,
      "supportsTariffs": true,
    },
  ],
}
`;

exports[`destination site admin api site module site controller with projections feature flag true should find an individual site for a scheme 1`] = `
{
  "address": {
    "country": "GB",
    "line1": "Tewkesbury Road",
    "line2": "Cheltenham",
    "name": "Gallagher Shopping",
    "postcode": "GL51 9RR",
    "prettyPrint": "Tewkesbury Road, Cheltenham, Gloucestershire, GL51 9RR, GB",
    "town": "Gloucestershire",
  },
  "chargeSummary": {
    "chargingDuration": 22245,
    "claimedEnergyUsage": 243.89,
    "co2Savings": 4325.62,
    "energyCost": 324550,
    "energyDelivered": 567.89,
    "energyUsage": 567.89,
    "numberOfCharges": 2048,
    "revenueGenerated": 457600,
    "revenueGeneratingClaimedUsage": 43.5,
  },
  "contactDetails": {
    "email": "<EMAIL>",
    "name": "Martin Maher",
    "telephone": "01688 71894",
  },
  "description": "",
  "energyCost": 15,
  "group": {
    "id": 451,
    "name": "Gallagher Shopping",
    "uid": "087e9b6b-1d60-47a0-89e9-87ff0d54f719",
  },
  "id": 1106,
  "parking": {
    "openingTimes": {
      "fri": {
        "allDay": false,
        "from": "08:00:00",
        "to": "19:00:00",
      },
      "mon": {
        "allDay": false,
        "from": "08:00:00",
        "to": "19:00:00",
      },
      "notes": [],
      "sat": {
        "allDay": false,
        "from": "08:00:00",
        "to": "18:00:00",
      },
      "sun": {
        "allDay": false,
        "from": "10:00:00",
        "to": "16:00:00",
      },
      "thu": {
        "allDay": false,
        "from": "08:00:00",
        "to": "19:00:00",
      },
      "tue": {
        "allDay": false,
        "from": "08:00:00",
        "to": "19:00:00",
      },
      "wed": {
        "allDay": false,
        "from": "08:00:00",
        "to": "19:00:00",
      },
    },
  },
  "pods": [
    {
      "confirmChargeEnabled": true,
      "coordinates": {
        "latitude": 51.91904621,
        "longitude": -2.10648304,
      },
      "description": "This is an open charge unit installed at the Gallagher Shopping site in Tewkesbury Road, Cheltenham, Gloucestershire",
      "id": 4090,
      "isEvZone": false,
      "isPublic": true,
      "lastContact": "2022-07-14T07:19:55.000Z",
      "model": "T3-S-2",
      "mostRecentCharge": {
        "chargingDuration": "3610",
        "co2Savings": "0.952",
        "confirmed": true,
        "confirmedBy": "driver",
        "door": "A",
        "endedAt": StringMatching /\\^\\(\\\\d\\{4\\}\\)-\\(\\\\d\\{2\\}\\)-\\(\\\\d\\{2\\}\\) \\(\\\\d\\{2\\}\\):\\(\\\\d\\{2\\}\\):\\(\\\\d\\{2\\}\\)\\$/,
        "energyCost": "£0.24",
        "energyUsage": "1.70",
        "locationId": 4090,
        "pluggedIn": StringMatching /\\^\\(\\\\d\\{4\\}\\)-\\(\\\\d\\{2\\}\\)-\\(\\\\d\\{2\\}\\) \\(\\\\d\\{2\\}\\):\\(\\\\d\\{2\\}\\):\\(\\\\d\\{2\\}\\)\\$/,
        "revenueGenerated": "£0.00",
        "startedAt": StringMatching /\\^\\(\\\\d\\{4\\}\\)-\\(\\\\d\\{2\\}\\)-\\(\\\\d\\{2\\}\\) \\(\\\\d\\{2\\}\\):\\(\\\\d\\{2\\}\\):\\(\\\\d\\{2\\}\\)\\$/,
        "totalDuration": "3625",
        "userEmail": "-",
        "userName": "-",
        "vehicle": "-",
      },
      "name": "Hank-Neil",
      "ppid": "PG-80435",
      "schemes": [
        {
          "id": 188,
          "name": "Savills UK",
          "uid": "780d9f10-47e7-472a-8367-88a852458efe",
        },
      ],
      "sockets": [
        {
          "door": "A",
          "firmwareVersion": "Unknown",
          "isUpdateAvailable": false,
          "status": "Charging",
        },
        {
          "door": "B",
          "firmwareVersion": "Unknown",
          "isUpdateAvailable": false,
          "status": "Charging",
        },
      ],
      "status": "Available",
      "supportsConfirmCharge": true,
      "supportsContactless": false,
      "supportsEnergyTariff": true,
      "supportsOcpp": false,
      "supportsPerKwh": true,
      "supportsTariffs": true,
    },
  ],
}
`;

exports[`destination site admin api site module site energy cost controller should update by group uid and site id 1`] = `
{
  "address": {
    "country": "GB",
    "line1": "Barrhead Road",
    "line2": "",
    "name": "Tesco Extra - Silverburn",
    "postcode": "G53 6AG",
    "prettyPrint": "Barrhead Road, Glasgow, G53 6AG, GB",
    "town": "Glasgow",
  },
  "chargeSummary": {
    "chargingDuration": 22245,
    "claimedEnergyUsage": 243.89,
    "co2Savings": 4325.62,
    "energyCost": 324550,
    "energyDelivered": 567.89,
    "energyUsage": 567.89,
    "numberOfCharges": 2048,
    "revenueGenerated": 457600,
    "revenueGeneratingClaimedUsage": 43.5,
  },
  "contactDetails": {
    "email": "<EMAIL>",
    "name": "Jane Blogs",
    "telephone": "+44*********",
  },
  "description": "",
  "energyCost": 300,
  "group": {
    "id": 2,
    "name": "Tesco Stores Ltd",
    "uid": "c9c5d39c-23ba-4a21-ac45-75968fdc055f",
  },
  "id": 75710,
  "parking": {
    "openingTimes": {
      "fri": {
        "allDay": false,
        "from": "10:30:00",
        "to": "12:30:00",
      },
      "notes": [
        "Parking limited during the day",
      ],
      "sat": {
        "allDay": false,
        "from": "10:30:00",
        "to": "21:00:00",
      },
      "sun": {
        "allDay": false,
        "from": "10:30:00",
        "to": "21:00:00",
      },
      "thu": {
        "allDay": false,
        "from": "10:30:00",
        "to": "12:30:00",
      },
    },
  },
  "pods": [
    {
      "confirmChargeEnabled": true,
      "coordinates": {
        "latitude": 51.124148,
        "longitude": -0.16234,
      },
      "description": "This is a rapid charger installed at the Tesco Extra superstore in Hazelwick Avenue, Crawley, West Sussex",
      "id": 5189,
      "isEvZone": false,
      "isPublic": false,
      "lastContact": "2022-05-14T20:52:34.000Z",
      "model": "Terra 53 CGT",
      "mostRecentCharge": {
        "chargingDuration": "-",
        "co2Savings": "4.200",
        "confirmed": false,
        "door": "A",
        "endedAt": StringMatching /\\^\\(\\\\d\\{4\\}\\)-\\(\\\\d\\{2\\}\\)-\\(\\\\d\\{2\\}\\) \\(\\\\d\\{2\\}\\):\\(\\\\d\\{2\\}\\):\\(\\\\d\\{2\\}\\)\\$/,
        "energyCost": "£6.00",
        "energyUsage": "7.50",
        "locationId": 5189,
        "pluggedIn": StringMatching /\\^\\(\\\\d\\{4\\}\\)-\\(\\\\d\\{2\\}\\)-\\(\\\\d\\{2\\}\\) \\(\\\\d\\{2\\}\\):\\(\\\\d\\{2\\}\\):\\(\\\\d\\{2\\}\\)\\$/,
        "revenueGenerated": "£0.00",
        "startedAt": StringMatching /\\^\\(\\\\d\\{4\\}\\)-\\(\\\\d\\{2\\}\\)-\\(\\\\d\\{2\\}\\) \\(\\\\d\\{2\\}\\):\\(\\\\d\\{2\\}\\):\\(\\\\d\\{2\\}\\)\\$/,
        "totalDuration": "10",
        "userEmail": "-",
        "userName": "-",
        "vehicle": "Audi A3 e-tron",
      },
      "name": "Bill-Tara",
      "ppid": "t53_it1_3717_004",
      "schemes": [],
      "sockets": [
        {
          "door": "A",
          "firmwareVersion": "Unknown",
          "isUpdateAvailable": false,
          "status": "Available",
        },
        {
          "door": "B",
          "firmwareVersion": "Unknown",
          "isUpdateAvailable": false,
          "status": "Charging",
        },
        {
          "door": "C",
          "firmwareVersion": "Unknown",
          "isUpdateAvailable": false,
          "status": "Unavailable",
        },
      ],
      "status": "Unavailable",
      "supportsConfirmCharge": true,
      "supportsContactless": false,
      "supportsEnergyTariff": true,
      "supportsOcpp": true,
      "supportsPerKwh": true,
      "supportsTariffs": false,
    },
    {
      "confirmChargeEnabled": true,
      "coordinates": {
        "latitude": 55.822099,
        "longitude": -4.340059,
      },
      "description": "This is an open charge unit installed at Tesco Extra Silverburn site at 751 Barrhead Road, Glasgow",
      "id": 91647,
      "isEvZone": false,
      "isPublic": true,
      "lastContact": "2022-07-14T08:20:00.000Z",
      "model": "MT7-S-6mA-2-RO",
      "mostRecentCharge": {
        "chargingDuration": "-",
        "co2Savings": "2.912",
        "confirmed": false,
        "door": "A",
        "endedAt": StringMatching /\\^\\(\\\\d\\{4\\}\\)-\\(\\\\d\\{2\\}\\)-\\(\\\\d\\{2\\}\\) \\(\\\\d\\{2\\}\\):\\(\\\\d\\{2\\}\\):\\(\\\\d\\{2\\}\\)\\$/,
        "energyCost": "£10.00",
        "energyUsage": "5.20",
        "locationId": 91647,
        "pluggedIn": StringMatching /\\^\\(\\\\d\\{4\\}\\)-\\(\\\\d\\{2\\}\\)-\\(\\\\d\\{2\\}\\) \\(\\\\d\\{2\\}\\):\\(\\\\d\\{2\\}\\):\\(\\\\d\\{2\\}\\)\\$/,
        "revenueGenerated": "£0.00",
        "startedAt": StringMatching /\\^\\(\\\\d\\{4\\}\\)-\\(\\\\d\\{2\\}\\)-\\(\\\\d\\{2\\}\\) \\(\\\\d\\{2\\}\\):\\(\\\\d\\{2\\}\\):\\(\\\\d\\{2\\}\\)\\$/,
        "totalDuration": "8",
        "userEmail": "-",
        "userName": "-",
        "vehicle": "Audi A3 e-tron",
      },
      "name": "Kyle-Dion",
      "ppid": "PG-70500",
      "schemes": [],
      "sockets": [
        {
          "door": "A",
          "firmwareVersion": "Unknown",
          "isUpdateAvailable": false,
          "status": "Available",
        },
        {
          "door": "B",
          "firmwareVersion": "Unknown",
          "isUpdateAvailable": false,
          "status": "Available",
        },
      ],
      "status": "Available",
      "supportsConfirmCharge": true,
      "supportsContactless": false,
      "supportsEnergyTariff": true,
      "supportsOcpp": false,
      "supportsPerKwh": true,
      "supportsTariffs": true,
    },
    {
      "confirmChargeEnabled": true,
      "coordinates": {
        "latitude": 55.822099,
        "longitude": -4.340059,
      },
      "description": "This is an open charge unit installed at Tesco Extra Silverburn site at 751 Barrhead Road, Glasgow",
      "id": 91648,
      "isEvZone": false,
      "isPublic": true,
      "lastContact": "2022-07-14T07:19:55.000Z",
      "model": "T7-S-03-WH-TES-6mA",
      "mostRecentCharge": {
        "chargingDuration": "-",
        "co2Savings": "4.200",
        "confirmed": false,
        "door": "A",
        "endedAt": StringMatching /\\^\\(\\\\d\\{4\\}\\)-\\(\\\\d\\{2\\}\\)-\\(\\\\d\\{2\\}\\) \\(\\\\d\\{2\\}\\):\\(\\\\d\\{2\\}\\):\\(\\\\d\\{2\\}\\)\\$/,
        "energyCost": "£6.00",
        "energyUsage": "7.50",
        "locationId": 91648,
        "pluggedIn": StringMatching /\\^\\(\\\\d\\{4\\}\\)-\\(\\\\d\\{2\\}\\)-\\(\\\\d\\{2\\}\\) \\(\\\\d\\{2\\}\\):\\(\\\\d\\{2\\}\\):\\(\\\\d\\{2\\}\\)\\$/,
        "revenueGenerated": "£0.00",
        "startedAt": StringMatching /\\^\\(\\\\d\\{4\\}\\)-\\(\\\\d\\{2\\}\\)-\\(\\\\d\\{2\\}\\) \\(\\\\d\\{2\\}\\):\\(\\\\d\\{2\\}\\):\\(\\\\d\\{2\\}\\)\\$/,
        "totalDuration": "10",
        "userEmail": "-",
        "userName": "-",
        "vehicle": "Audi A3 e-tron",
      },
      "name": "Zach-Jade",
      "ppid": "PG-86983",
      "schemes": [],
      "sockets": [
        {
          "door": "A",
          "firmwareVersion": "Unknown",
          "isUpdateAvailable": false,
          "status": "Available",
        },
        {
          "door": "B",
          "firmwareVersion": "Unknown",
          "isUpdateAvailable": false,
          "status": "Available",
        },
      ],
      "status": "Available",
      "supportsConfirmCharge": true,
      "supportsContactless": false,
      "supportsEnergyTariff": true,
      "supportsOcpp": false,
      "supportsPerKwh": true,
      "supportsTariffs": true,
    },
  ],
}
`;

exports[`destination site admin api site module site parking times controller should upsert by group uid and site id 1`] = `
{
  "address": {
    "country": "GB",
    "line1": "Barrhead Road",
    "line2": "",
    "name": "Tesco Extra - Silverburn",
    "postcode": "G53 6AG",
    "prettyPrint": "Barrhead Road, Glasgow, G53 6AG, GB",
    "town": "Glasgow",
  },
  "chargeSummary": {
    "chargingDuration": 22245,
    "claimedEnergyUsage": 243.89,
    "co2Savings": 4325.62,
    "energyCost": 324550,
    "energyDelivered": 567.89,
    "energyUsage": 567.89,
    "numberOfCharges": 2048,
    "revenueGenerated": 457600,
    "revenueGeneratingClaimedUsage": 43.5,
  },
  "contactDetails": {
    "email": "",
    "name": "Raymond Hutchinson",
    "telephone": "07834611729",
  },
  "description": "",
  "energyCost": 13,
  "group": {
    "id": 2,
    "name": "Tesco Stores Ltd",
    "uid": "c9c5d39c-23ba-4a21-ac45-75968fdc055f",
  },
  "id": 75710,
  "parking": {
    "openingTimes": {
      "fri": {
        "allDay": false,
        "from": "10:30:00",
        "to": "12:30:00",
      },
      "mon": {
        "allDay": false,
        "from": "10:30:00",
        "to": "11:00:00",
      },
      "notes": [
        "For any parking restrictions that may apply, please check for signage at the store.
Max stay period - 2 hours",
      ],
      "sat": {
        "allDay": false,
        "from": "10:30:00",
        "to": "21:00:00",
      },
      "sun": {
        "allDay": false,
        "from": "10:30:00",
        "to": "21:00:00",
      },
      "thu": {
        "allDay": false,
        "from": "10:30:00",
        "to": "12:30:00",
      },
      "tue": {
        "allDay": true,
        "from": "00:00:00",
        "to": "23:59:00",
      },
      "wed": {
        "allDay": false,
        "from": "10:30:00",
        "to": "12:30:00",
      },
    },
  },
  "pods": [
    {
      "confirmChargeEnabled": true,
      "coordinates": {
        "latitude": 51.124148,
        "longitude": -0.16234,
      },
      "description": "This is a rapid charger installed at the Tesco Extra superstore in Hazelwick Avenue, Crawley, West Sussex",
      "id": 5189,
      "isEvZone": false,
      "isPublic": false,
      "lastContact": "2022-05-14T20:52:34.000Z",
      "model": "Terra 53 CGT",
      "mostRecentCharge": {
        "chargingDuration": "-",
        "co2Savings": "4.200",
        "confirmed": false,
        "door": "A",
        "endedAt": StringMatching /\\^\\(\\\\d\\{4\\}\\)-\\(\\\\d\\{2\\}\\)-\\(\\\\d\\{2\\}\\) \\(\\\\d\\{2\\}\\):\\(\\\\d\\{2\\}\\):\\(\\\\d\\{2\\}\\)\\$/,
        "energyCost": "£6.00",
        "energyUsage": "7.50",
        "locationId": 5189,
        "pluggedIn": StringMatching /\\^\\(\\\\d\\{4\\}\\)-\\(\\\\d\\{2\\}\\)-\\(\\\\d\\{2\\}\\) \\(\\\\d\\{2\\}\\):\\(\\\\d\\{2\\}\\):\\(\\\\d\\{2\\}\\)\\$/,
        "revenueGenerated": "£0.00",
        "startedAt": StringMatching /\\^\\(\\\\d\\{4\\}\\)-\\(\\\\d\\{2\\}\\)-\\(\\\\d\\{2\\}\\) \\(\\\\d\\{2\\}\\):\\(\\\\d\\{2\\}\\):\\(\\\\d\\{2\\}\\)\\$/,
        "totalDuration": "10",
        "userEmail": "-",
        "userName": "-",
        "vehicle": "Audi A3 e-tron",
      },
      "name": "Bill-Tara",
      "ppid": "t53_it1_3717_004",
      "schemes": [],
      "sockets": [
        {
          "door": "A",
          "firmwareVersion": "Unknown",
          "isUpdateAvailable": false,
          "status": "Available",
        },
        {
          "door": "B",
          "firmwareVersion": "Unknown",
          "isUpdateAvailable": false,
          "status": "Charging",
        },
        {
          "door": "C",
          "firmwareVersion": "Unknown",
          "isUpdateAvailable": false,
          "status": "Unavailable",
        },
      ],
      "status": "Unavailable",
      "supportsConfirmCharge": true,
      "supportsContactless": false,
      "supportsEnergyTariff": true,
      "supportsOcpp": true,
      "supportsPerKwh": true,
      "supportsTariffs": false,
    },
    {
      "confirmChargeEnabled": true,
      "coordinates": {
        "latitude": 55.822099,
        "longitude": -4.340059,
      },
      "description": "This is an open charge unit installed at Tesco Extra Silverburn site at 751 Barrhead Road, Glasgow",
      "id": 91647,
      "isEvZone": false,
      "isPublic": true,
      "lastContact": "2022-07-14T08:20:00.000Z",
      "model": "MT7-S-6mA-2-RO",
      "mostRecentCharge": {
        "chargingDuration": "-",
        "co2Savings": "2.912",
        "confirmed": false,
        "door": "A",
        "endedAt": StringMatching /\\^\\(\\\\d\\{4\\}\\)-\\(\\\\d\\{2\\}\\)-\\(\\\\d\\{2\\}\\) \\(\\\\d\\{2\\}\\):\\(\\\\d\\{2\\}\\):\\(\\\\d\\{2\\}\\)\\$/,
        "energyCost": "£10.00",
        "energyUsage": "5.20",
        "locationId": 91647,
        "pluggedIn": StringMatching /\\^\\(\\\\d\\{4\\}\\)-\\(\\\\d\\{2\\}\\)-\\(\\\\d\\{2\\}\\) \\(\\\\d\\{2\\}\\):\\(\\\\d\\{2\\}\\):\\(\\\\d\\{2\\}\\)\\$/,
        "revenueGenerated": "£0.00",
        "startedAt": StringMatching /\\^\\(\\\\d\\{4\\}\\)-\\(\\\\d\\{2\\}\\)-\\(\\\\d\\{2\\}\\) \\(\\\\d\\{2\\}\\):\\(\\\\d\\{2\\}\\):\\(\\\\d\\{2\\}\\)\\$/,
        "totalDuration": "8",
        "userEmail": "-",
        "userName": "-",
        "vehicle": "Audi A3 e-tron",
      },
      "name": "Kyle-Dion",
      "ppid": "PG-70500",
      "schemes": [],
      "sockets": [
        {
          "door": "A",
          "firmwareVersion": "Unknown",
          "isUpdateAvailable": false,
          "status": "Available",
        },
        {
          "door": "B",
          "firmwareVersion": "Unknown",
          "isUpdateAvailable": false,
          "status": "Available",
        },
      ],
      "status": "Available",
      "supportsConfirmCharge": true,
      "supportsContactless": false,
      "supportsEnergyTariff": true,
      "supportsOcpp": false,
      "supportsPerKwh": true,
      "supportsTariffs": true,
    },
    {
      "confirmChargeEnabled": true,
      "coordinates": {
        "latitude": 55.822099,
        "longitude": -4.340059,
      },
      "description": "This is an open charge unit installed at Tesco Extra Silverburn site at 751 Barrhead Road, Glasgow",
      "id": 91648,
      "isEvZone": false,
      "isPublic": true,
      "lastContact": "2022-07-14T07:19:55.000Z",
      "model": "T7-S-03-WH-TES-6mA",
      "mostRecentCharge": {
        "chargingDuration": "-",
        "co2Savings": "4.200",
        "confirmed": false,
        "door": "A",
        "endedAt": StringMatching /\\^\\(\\\\d\\{4\\}\\)-\\(\\\\d\\{2\\}\\)-\\(\\\\d\\{2\\}\\) \\(\\\\d\\{2\\}\\):\\(\\\\d\\{2\\}\\):\\(\\\\d\\{2\\}\\)\\$/,
        "energyCost": "£6.00",
        "energyUsage": "7.50",
        "locationId": 91648,
        "pluggedIn": StringMatching /\\^\\(\\\\d\\{4\\}\\)-\\(\\\\d\\{2\\}\\)-\\(\\\\d\\{2\\}\\) \\(\\\\d\\{2\\}\\):\\(\\\\d\\{2\\}\\):\\(\\\\d\\{2\\}\\)\\$/,
        "revenueGenerated": "£0.00",
        "startedAt": StringMatching /\\^\\(\\\\d\\{4\\}\\)-\\(\\\\d\\{2\\}\\)-\\(\\\\d\\{2\\}\\) \\(\\\\d\\{2\\}\\):\\(\\\\d\\{2\\}\\):\\(\\\\d\\{2\\}\\)\\$/,
        "totalDuration": "10",
        "userEmail": "-",
        "userName": "-",
        "vehicle": "Audi A3 e-tron",
      },
      "name": "Zach-Jade",
      "ppid": "PG-86983",
      "schemes": [],
      "sockets": [
        {
          "door": "A",
          "firmwareVersion": "Unknown",
          "isUpdateAvailable": false,
          "status": "Available",
        },
        {
          "door": "B",
          "firmwareVersion": "Unknown",
          "isUpdateAvailable": false,
          "status": "Available",
        },
      ],
      "status": "Available",
      "supportsConfirmCharge": true,
      "supportsContactless": false,
      "supportsEnergyTariff": true,
      "supportsOcpp": false,
      "supportsPerKwh": true,
      "supportsTariffs": true,
    },
  ],
}
`;

exports[`destination site admin api site module site parking times controller should upsert by group uid and site id when there are closed days in the request 1`] = `
{
  "address": {
    "country": "GB",
    "line1": "Barrhead Road",
    "line2": "",
    "name": "Tesco Extra - Silverburn",
    "postcode": "G53 6AG",
    "prettyPrint": "Barrhead Road, Glasgow, G53 6AG, GB",
    "town": "Glasgow",
  },
  "chargeSummary": {
    "chargingDuration": 22245,
    "claimedEnergyUsage": 243.89,
    "co2Savings": 4325.62,
    "energyCost": 324550,
    "energyDelivered": 567.89,
    "energyUsage": 567.89,
    "numberOfCharges": 2048,
    "revenueGenerated": 457600,
    "revenueGeneratingClaimedUsage": 43.5,
  },
  "contactDetails": {
    "email": "",
    "name": "Raymond Hutchinson",
    "telephone": "07834611729",
  },
  "description": "",
  "energyCost": 13,
  "group": {
    "id": 2,
    "name": "Tesco Stores Ltd",
    "uid": "c9c5d39c-23ba-4a21-ac45-75968fdc055f",
  },
  "id": 75710,
  "parking": {
    "openingTimes": {
      "fri": {
        "allDay": false,
        "from": "10:30:00",
        "to": "12:30:00",
      },
      "notes": [
        "For any parking restrictions that may apply, please check for signage at the store.
Max stay period - 2 hours",
      ],
      "sat": {
        "allDay": false,
        "from": "10:30:00",
        "to": "21:00:00",
      },
      "sun": {
        "allDay": false,
        "from": "10:30:00",
        "to": "21:00:00",
      },
      "thu": {
        "allDay": false,
        "from": "10:30:00",
        "to": "12:30:00",
      },
    },
  },
  "pods": [
    {
      "confirmChargeEnabled": true,
      "coordinates": {
        "latitude": 51.124148,
        "longitude": -0.16234,
      },
      "description": "This is a rapid charger installed at the Tesco Extra superstore in Hazelwick Avenue, Crawley, West Sussex",
      "id": 5189,
      "isEvZone": false,
      "isPublic": false,
      "lastContact": "2022-05-14T20:52:34.000Z",
      "model": "Terra 53 CGT",
      "mostRecentCharge": {
        "chargingDuration": "-",
        "co2Savings": "4.200",
        "confirmed": false,
        "door": "A",
        "endedAt": StringMatching /\\^\\(\\\\d\\{4\\}\\)-\\(\\\\d\\{2\\}\\)-\\(\\\\d\\{2\\}\\) \\(\\\\d\\{2\\}\\):\\(\\\\d\\{2\\}\\):\\(\\\\d\\{2\\}\\)\\$/,
        "energyCost": "£6.00",
        "energyUsage": "7.50",
        "locationId": 5189,
        "pluggedIn": StringMatching /\\^\\(\\\\d\\{4\\}\\)-\\(\\\\d\\{2\\}\\)-\\(\\\\d\\{2\\}\\) \\(\\\\d\\{2\\}\\):\\(\\\\d\\{2\\}\\):\\(\\\\d\\{2\\}\\)\\$/,
        "revenueGenerated": "£0.00",
        "startedAt": StringMatching /\\^\\(\\\\d\\{4\\}\\)-\\(\\\\d\\{2\\}\\)-\\(\\\\d\\{2\\}\\) \\(\\\\d\\{2\\}\\):\\(\\\\d\\{2\\}\\):\\(\\\\d\\{2\\}\\)\\$/,
        "totalDuration": "10",
        "userEmail": "-",
        "userName": "-",
        "vehicle": "Audi A3 e-tron",
      },
      "name": "Bill-Tara",
      "ppid": "t53_it1_3717_004",
      "schemes": [],
      "sockets": [
        {
          "door": "A",
          "firmwareVersion": "Unknown",
          "isUpdateAvailable": false,
          "status": "Available",
        },
        {
          "door": "B",
          "firmwareVersion": "Unknown",
          "isUpdateAvailable": false,
          "status": "Charging",
        },
        {
          "door": "C",
          "firmwareVersion": "Unknown",
          "isUpdateAvailable": false,
          "status": "Unavailable",
        },
      ],
      "status": "Unavailable",
      "supportsConfirmCharge": true,
      "supportsContactless": false,
      "supportsEnergyTariff": true,
      "supportsOcpp": true,
      "supportsPerKwh": true,
      "supportsTariffs": false,
    },
    {
      "confirmChargeEnabled": true,
      "coordinates": {
        "latitude": 55.822099,
        "longitude": -4.340059,
      },
      "description": "This is an open charge unit installed at Tesco Extra Silverburn site at 751 Barrhead Road, Glasgow",
      "id": 91647,
      "isEvZone": false,
      "isPublic": true,
      "lastContact": "2022-07-14T08:20:00.000Z",
      "model": "MT7-S-6mA-2-RO",
      "mostRecentCharge": {
        "chargingDuration": "-",
        "co2Savings": "2.912",
        "confirmed": false,
        "door": "A",
        "endedAt": StringMatching /\\^\\(\\\\d\\{4\\}\\)-\\(\\\\d\\{2\\}\\)-\\(\\\\d\\{2\\}\\) \\(\\\\d\\{2\\}\\):\\(\\\\d\\{2\\}\\):\\(\\\\d\\{2\\}\\)\\$/,
        "energyCost": "£10.00",
        "energyUsage": "5.20",
        "locationId": 91647,
        "pluggedIn": StringMatching /\\^\\(\\\\d\\{4\\}\\)-\\(\\\\d\\{2\\}\\)-\\(\\\\d\\{2\\}\\) \\(\\\\d\\{2\\}\\):\\(\\\\d\\{2\\}\\):\\(\\\\d\\{2\\}\\)\\$/,
        "revenueGenerated": "£0.00",
        "startedAt": StringMatching /\\^\\(\\\\d\\{4\\}\\)-\\(\\\\d\\{2\\}\\)-\\(\\\\d\\{2\\}\\) \\(\\\\d\\{2\\}\\):\\(\\\\d\\{2\\}\\):\\(\\\\d\\{2\\}\\)\\$/,
        "totalDuration": "8",
        "userEmail": "-",
        "userName": "-",
        "vehicle": "Audi A3 e-tron",
      },
      "name": "Kyle-Dion",
      "ppid": "PG-70500",
      "schemes": [],
      "sockets": [
        {
          "door": "A",
          "firmwareVersion": "Unknown",
          "isUpdateAvailable": false,
          "status": "Available",
        },
        {
          "door": "B",
          "firmwareVersion": "Unknown",
          "isUpdateAvailable": false,
          "status": "Available",
        },
      ],
      "status": "Available",
      "supportsConfirmCharge": true,
      "supportsContactless": false,
      "supportsEnergyTariff": true,
      "supportsOcpp": false,
      "supportsPerKwh": true,
      "supportsTariffs": true,
    },
    {
      "confirmChargeEnabled": true,
      "coordinates": {
        "latitude": 55.822099,
        "longitude": -4.340059,
      },
      "description": "This is an open charge unit installed at Tesco Extra Silverburn site at 751 Barrhead Road, Glasgow",
      "id": 91648,
      "isEvZone": false,
      "isPublic": true,
      "lastContact": "2022-07-14T07:19:55.000Z",
      "model": "T7-S-03-WH-TES-6mA",
      "mostRecentCharge": {
        "chargingDuration": "-",
        "co2Savings": "4.200",
        "confirmed": false,
        "door": "A",
        "endedAt": StringMatching /\\^\\(\\\\d\\{4\\}\\)-\\(\\\\d\\{2\\}\\)-\\(\\\\d\\{2\\}\\) \\(\\\\d\\{2\\}\\):\\(\\\\d\\{2\\}\\):\\(\\\\d\\{2\\}\\)\\$/,
        "energyCost": "£6.00",
        "energyUsage": "7.50",
        "locationId": 91648,
        "pluggedIn": StringMatching /\\^\\(\\\\d\\{4\\}\\)-\\(\\\\d\\{2\\}\\)-\\(\\\\d\\{2\\}\\) \\(\\\\d\\{2\\}\\):\\(\\\\d\\{2\\}\\):\\(\\\\d\\{2\\}\\)\\$/,
        "revenueGenerated": "£0.00",
        "startedAt": StringMatching /\\^\\(\\\\d\\{4\\}\\)-\\(\\\\d\\{2\\}\\)-\\(\\\\d\\{2\\}\\) \\(\\\\d\\{2\\}\\):\\(\\\\d\\{2\\}\\):\\(\\\\d\\{2\\}\\)\\$/,
        "totalDuration": "10",
        "userEmail": "-",
        "userName": "-",
        "vehicle": "Audi A3 e-tron",
      },
      "name": "Zach-Jade",
      "ppid": "PG-86983",
      "schemes": [],
      "sockets": [
        {
          "door": "A",
          "firmwareVersion": "Unknown",
          "isUpdateAvailable": false,
          "status": "Available",
        },
        {
          "door": "B",
          "firmwareVersion": "Unknown",
          "isUpdateAvailable": false,
          "status": "Available",
        },
      ],
      "status": "Available",
      "supportsConfirmCharge": true,
      "supportsContactless": false,
      "supportsEnergyTariff": true,
      "supportsOcpp": false,
      "supportsPerKwh": true,
      "supportsTariffs": true,
    },
  ],
}
`;

exports[`destination site admin api tariff module tariff controller should create a new tariff 1`] = `
{
  "currency": "GBP",
  "id": 4206,
  "name": "test tariff",
}
`;

exports[`destination site admin api tariff module tariff controller should delete an existing tariff that is not assigned to any chargers 1`] = `
[
  {
    "currency": "GBP",
    "id": 4206,
    "name": "Another Test Tariff",
    "pods": [],
  },
  {
    "currency": "GBP",
    "id": 1638,
    "name": "Tesco Esso Alliance PAYG Tariff",
    "pods": [],
  },
  {
    "currency": "GBP",
    "id": 249,
    "name": "Tesco Rapid kWh",
    "pods": [
      {
        "confirmChargeEnabled": true,
        "coordinates": {
          "latitude": 51.124148,
          "longitude": -0.16234,
        },
        "description": "This is a rapid charger installed at the Tesco Extra superstore in Hazelwick Avenue, Crawley, West Sussex",
        "id": 5189,
        "isEvZone": false,
        "isPublic": false,
        "lastContact": "2022-05-14T20:52:34.000Z",
        "model": "Terra 53 CGT",
        "name": "Bill-Tara",
        "ppid": "t53_it1_3717_004",
        "schemes": [],
        "site": {
          "address": {
            "country": "GB",
            "line1": "Barrhead Road",
            "line2": "",
            "name": "Tesco Extra - Silverburn",
            "postcode": "G53 6AG",
            "prettyPrint": "Barrhead Road, Glasgow, G53 6AG, GB",
            "town": "Glasgow",
          },
          "contactDetails": {
            "email": "<EMAIL>",
            "name": "Jane Blogs",
            "telephone": "+44*********",
          },
          "description": "",
          "energyCost": 15,
          "group": {
            "id": 2,
            "name": "Tesco Stores Ltd",
            "uid": "c9c5d39c-23ba-4a21-ac45-75968fdc055f",
          },
          "id": 75710,
          "parking": {},
        },
        "sockets": [
          {
            "door": "A",
            "firmwareVersion": "Unknown",
            "isUpdateAvailable": false,
            "lastContact": "2022-05-14T20:52:34.000Z",
            "serialNumber": "T53-IT1-3717-004-Door1",
            "status": "Available",
          },
          {
            "door": "B",
            "firmwareVersion": "Unknown",
            "isUpdateAvailable": false,
            "lastContact": "2022-05-14T20:52:34.000Z",
            "serialNumber": "T53-IT1-3717-004-Door2",
            "status": "Charging",
          },
          {
            "door": "C",
            "firmwareVersion": "Unknown",
            "isUpdateAvailable": false,
            "lastContact": "2022-05-14T20:52:34.000Z",
            "serialNumber": "T53-IT1-3717-004-Door3",
            "status": "Unavailable",
          },
        ],
        "status": "Unavailable",
        "supportsConfirmCharge": true,
        "supportsContactless": false,
        "supportsEnergyTariff": true,
        "supportsOcpp": true,
        "supportsPerKwh": true,
        "supportsTariffs": false,
        "tariff": {
          "id": 249,
          "name": "Tesco Rapid kWh",
        },
      },
    ],
    "schedule": {
      "drivers": [
        {
          "bands": [
            {
              "after": 0,
              "cost": 50,
              "id": 63600,
              "prettyPrint": "50p",
            },
          ],
          "endDay": 1,
          "endTime": "17:00:00",
          "id": 63600,
          "pricingModel": "energy",
          "startDay": 1,
          "startTime": "09:00:00",
          "tariffTier": "drivers",
        },
      ],
      "members": [
        {
          "bands": [
            {
              "after": 0,
              "cost": 30,
              "id": 63601,
              "prettyPrint": "30p",
            },
          ],
          "endDay": 1,
          "endTime": "14:00:00",
          "id": 63601,
          "pricingModel": "energy",
          "startDay": 1,
          "startTime": "09:00:00",
          "tariffTier": "members",
        },
      ],
      "public": [
        {
          "bands": [
            {
              "after": 0,
              "cost": 80,
              "id": 63602,
              "prettyPrint": "80p",
            },
          ],
          "endDay": 7,
          "endTime": "22:00:00",
          "id": 63602,
          "pricingModel": "energy",
          "startDay": 7,
          "startTime": "16:00:00",
          "tariffTier": "public",
        },
      ],
    },
  },
]
`;

exports[`destination site admin api tariff module tariff controller should find a list of tariffs 1`] = `
[
  {
    "currency": "GBP",
    "id": 1638,
    "name": "Tesco Esso Alliance PAYG Tariff",
    "pods": [],
  },
  {
    "currency": "GBP",
    "id": 249,
    "name": "Tesco Rapid kWh",
    "pods": [
      {
        "confirmChargeEnabled": true,
        "coordinates": {
          "latitude": 51.124148,
          "longitude": -0.16234,
        },
        "description": "This is a rapid charger installed at the Tesco Extra superstore in Hazelwick Avenue, Crawley, West Sussex",
        "id": 5189,
        "isEvZone": false,
        "isPublic": false,
        "lastContact": "2022-05-14T20:52:34.000Z",
        "model": "Terra 53 CGT",
        "name": "Bill-Tara",
        "ppid": "t53_it1_3717_004",
        "schemes": [],
        "site": {
          "address": {
            "country": "GB",
            "line1": "Barrhead Road",
            "line2": "",
            "name": "Tesco Extra - Silverburn",
            "postcode": "G53 6AG",
            "prettyPrint": "Barrhead Road, Glasgow, G53 6AG, GB",
            "town": "Glasgow",
          },
          "contactDetails": {
            "email": "<EMAIL>",
            "name": "Jane Blogs",
            "telephone": "+44*********",
          },
          "description": "",
          "energyCost": 15,
          "group": {
            "id": 2,
            "name": "Tesco Stores Ltd",
            "uid": "c9c5d39c-23ba-4a21-ac45-75968fdc055f",
          },
          "id": 75710,
          "parking": {},
        },
        "sockets": [
          {
            "door": "A",
            "firmwareVersion": "Unknown",
            "isUpdateAvailable": false,
            "lastContact": "2022-05-14T20:52:34.000Z",
            "serialNumber": "T53-IT1-3717-004-Door1",
            "status": "Available",
          },
          {
            "door": "B",
            "firmwareVersion": "Unknown",
            "isUpdateAvailable": false,
            "lastContact": "2022-05-14T20:52:34.000Z",
            "serialNumber": "T53-IT1-3717-004-Door2",
            "status": "Charging",
          },
          {
            "door": "C",
            "firmwareVersion": "Unknown",
            "isUpdateAvailable": false,
            "lastContact": "2022-05-14T20:52:34.000Z",
            "serialNumber": "T53-IT1-3717-004-Door3",
            "status": "Unavailable",
          },
        ],
        "status": "Unavailable",
        "supportsConfirmCharge": true,
        "supportsContactless": false,
        "supportsEnergyTariff": true,
        "supportsOcpp": true,
        "supportsPerKwh": true,
        "supportsTariffs": false,
        "tariff": {
          "id": 249,
          "name": "Tesco Rapid kWh",
        },
      },
    ],
    "schedule": {
      "drivers": [
        {
          "bands": [
            {
              "after": 0,
              "cost": 50,
              "id": 63600,
              "prettyPrint": "50p",
            },
          ],
          "endDay": 1,
          "endTime": "17:00:00",
          "id": 63600,
          "pricingModel": "energy",
          "startDay": 1,
          "startTime": "09:00:00",
          "tariffTier": "drivers",
        },
      ],
      "members": [
        {
          "bands": [
            {
              "after": 0,
              "cost": 30,
              "id": 63601,
              "prettyPrint": "30p",
            },
          ],
          "endDay": 1,
          "endTime": "14:00:00",
          "id": 63601,
          "pricingModel": "energy",
          "startDay": 1,
          "startTime": "09:00:00",
          "tariffTier": "members",
        },
      ],
      "public": [
        {
          "bands": [
            {
              "after": 0,
              "cost": 80,
              "id": 63602,
              "prettyPrint": "80p",
            },
          ],
          "endDay": 7,
          "endTime": "22:00:00",
          "id": 63602,
          "pricingModel": "energy",
          "startDay": 7,
          "startTime": "16:00:00",
          "tariffTier": "public",
        },
      ],
    },
  },
  {
    "currency": "GBP",
    "id": 1637,
    "name": "Test tariff to delete",
    "pods": [],
  },
]
`;

exports[`destination site admin api tariff module tariff controller should find an individual tariff by id 1`] = `
{
  "currency": "GBP",
  "id": 249,
  "name": "Tesco Rapid kWh",
  "pods": [
    {
      "confirmChargeEnabled": true,
      "coordinates": {
        "latitude": 51.124148,
        "longitude": -0.16234,
      },
      "description": "This is a rapid charger installed at the Tesco Extra superstore in Hazelwick Avenue, Crawley, West Sussex",
      "id": 5189,
      "isEvZone": false,
      "isPublic": false,
      "lastContact": "2022-05-14T20:52:34.000Z",
      "model": "Terra 53 CGT",
      "name": "Bill-Tara",
      "ppid": "t53_it1_3717_004",
      "schemes": [],
      "site": {
        "address": {
          "country": "GB",
          "line1": "Barrhead Road",
          "line2": "",
          "name": "Tesco Extra - Silverburn",
          "postcode": "G53 6AG",
          "prettyPrint": "Barrhead Road, Glasgow, G53 6AG, GB",
          "town": "Glasgow",
        },
        "contactDetails": {
          "email": "<EMAIL>",
          "name": "Jane Blogs",
          "telephone": "+44*********",
        },
        "description": "",
        "energyCost": 15,
        "group": {
          "id": 2,
          "name": "Tesco Stores Ltd",
          "uid": "c9c5d39c-23ba-4a21-ac45-75968fdc055f",
        },
        "id": 75710,
        "parking": {},
      },
      "sockets": [
        {
          "door": "A",
          "firmwareVersion": "Unknown",
          "isUpdateAvailable": false,
          "lastContact": "2022-05-14T20:52:34.000Z",
          "serialNumber": "T53-IT1-3717-004-Door1",
          "status": "Available",
        },
        {
          "door": "B",
          "firmwareVersion": "Unknown",
          "isUpdateAvailable": false,
          "lastContact": "2022-05-14T20:52:34.000Z",
          "serialNumber": "T53-IT1-3717-004-Door2",
          "status": "Charging",
        },
        {
          "door": "C",
          "firmwareVersion": "Unknown",
          "isUpdateAvailable": false,
          "lastContact": "2022-05-14T20:52:34.000Z",
          "serialNumber": "T53-IT1-3717-004-Door3",
          "status": "Unavailable",
        },
      ],
      "status": "Unavailable",
      "supportsConfirmCharge": true,
      "supportsContactless": false,
      "supportsEnergyTariff": true,
      "supportsOcpp": true,
      "supportsPerKwh": true,
      "supportsTariffs": false,
      "tariff": {
        "id": 249,
        "name": "Tesco Rapid kWh",
      },
    },
  ],
  "schedule": {
    "drivers": [
      {
        "bands": [
          {
            "after": 0,
            "cost": 50,
            "id": 63600,
            "prettyPrint": "50p",
          },
        ],
        "endDay": 1,
        "endTime": "17:00:00",
        "id": 63600,
        "pricingModel": "energy",
        "startDay": 1,
        "startTime": "09:00:00",
        "tariffTier": "drivers",
      },
    ],
    "members": [
      {
        "bands": [
          {
            "after": 0,
            "cost": 30,
            "id": 63601,
            "prettyPrint": "30p",
          },
        ],
        "endDay": 1,
        "endTime": "14:00:00",
        "id": 63601,
        "pricingModel": "energy",
        "startDay": 1,
        "startTime": "09:00:00",
        "tariffTier": "members",
      },
    ],
    "public": [
      {
        "bands": [
          {
            "after": 0,
            "cost": 80,
            "id": 63602,
            "prettyPrint": "80p",
          },
        ],
        "endDay": 7,
        "endTime": "22:00:00",
        "id": 63602,
        "pricingModel": "energy",
        "startDay": 7,
        "startTime": "16:00:00",
        "tariffTier": "public",
      },
    ],
  },
}
`;

exports[`destination site admin api tariff module tariff controller should update an existing tariff 1`] = `
[
  {
    "currency": "GBP",
    "id": 4206,
    "name": "Another Test Tariff",
    "pods": [],
  },
  {
    "currency": "GBP",
    "id": 1638,
    "name": "Tesco Esso Alliance PAYG Tariff",
    "pods": [],
  },
  {
    "currency": "GBP",
    "id": 249,
    "name": "Tesco Rapid kWh",
    "pods": [
      {
        "confirmChargeEnabled": true,
        "coordinates": {
          "latitude": 51.124148,
          "longitude": -0.16234,
        },
        "description": "This is a rapid charger installed at the Tesco Extra superstore in Hazelwick Avenue, Crawley, West Sussex",
        "id": 5189,
        "isEvZone": false,
        "isPublic": false,
        "lastContact": "2022-05-14T20:52:34.000Z",
        "model": "Terra 53 CGT",
        "name": "Bill-Tara",
        "ppid": "t53_it1_3717_004",
        "schemes": [],
        "site": {
          "address": {
            "country": "GB",
            "line1": "Barrhead Road",
            "line2": "",
            "name": "Tesco Extra - Silverburn",
            "postcode": "G53 6AG",
            "prettyPrint": "Barrhead Road, Glasgow, G53 6AG, GB",
            "town": "Glasgow",
          },
          "contactDetails": {
            "email": "<EMAIL>",
            "name": "Jane Blogs",
            "telephone": "+44*********",
          },
          "description": "",
          "energyCost": 15,
          "group": {
            "id": 2,
            "name": "Tesco Stores Ltd",
            "uid": "c9c5d39c-23ba-4a21-ac45-75968fdc055f",
          },
          "id": 75710,
          "parking": {},
        },
        "sockets": [
          {
            "door": "A",
            "firmwareVersion": "Unknown",
            "isUpdateAvailable": false,
            "lastContact": "2022-05-14T20:52:34.000Z",
            "serialNumber": "T53-IT1-3717-004-Door1",
            "status": "Available",
          },
          {
            "door": "B",
            "firmwareVersion": "Unknown",
            "isUpdateAvailable": false,
            "lastContact": "2022-05-14T20:52:34.000Z",
            "serialNumber": "T53-IT1-3717-004-Door2",
            "status": "Charging",
          },
          {
            "door": "C",
            "firmwareVersion": "Unknown",
            "isUpdateAvailable": false,
            "lastContact": "2022-05-14T20:52:34.000Z",
            "serialNumber": "T53-IT1-3717-004-Door3",
            "status": "Unavailable",
          },
        ],
        "status": "Unavailable",
        "supportsConfirmCharge": true,
        "supportsContactless": false,
        "supportsEnergyTariff": true,
        "supportsOcpp": true,
        "supportsPerKwh": true,
        "supportsTariffs": false,
        "tariff": {
          "id": 249,
          "name": "Tesco Rapid kWh",
        },
      },
    ],
    "schedule": {
      "drivers": [
        {
          "bands": [
            {
              "after": 0,
              "cost": 50,
              "id": 63600,
              "prettyPrint": "50p",
            },
          ],
          "endDay": 1,
          "endTime": "17:00:00",
          "id": 63600,
          "pricingModel": "energy",
          "startDay": 1,
          "startTime": "09:00:00",
          "tariffTier": "drivers",
        },
      ],
      "members": [
        {
          "bands": [
            {
              "after": 0,
              "cost": 30,
              "id": 63601,
              "prettyPrint": "30p",
            },
          ],
          "endDay": 1,
          "endTime": "14:00:00",
          "id": 63601,
          "pricingModel": "energy",
          "startDay": 1,
          "startTime": "09:00:00",
          "tariffTier": "members",
        },
      ],
      "public": [
        {
          "bands": [
            {
              "after": 0,
              "cost": 80,
              "id": 63602,
              "prettyPrint": "80p",
            },
          ],
          "endDay": 7,
          "endTime": "22:00:00",
          "id": 63602,
          "pricingModel": "energy",
          "startDay": 7,
          "startTime": "16:00:00",
          "tariffTier": "public",
        },
      ],
    },
  },
  {
    "currency": "GBP",
    "id": 1637,
    "name": "Test tariff to delete",
    "pods": [],
  },
]
`;

exports[`destination site admin api tariff module tariff pod controller should update a tariff to assign and unassign pods 1`] = `
[
  {
    "currency": "GBP",
    "id": 4206,
    "name": "Another Test Tariff",
    "pods": [],
  },
  {
    "currency": "GBP",
    "id": 1638,
    "name": "Tesco Esso Alliance PAYG Tariff",
    "pods": [
      {
        "confirmChargeEnabled": true,
        "coordinates": {
          "latitude": 55.822099,
          "longitude": -4.340059,
        },
        "description": "This is an open charge unit installed at Tesco Extra Silverburn site at 751 Barrhead Road, Glasgow",
        "id": 91647,
        "isEvZone": false,
        "isPublic": true,
        "lastContact": "2022-07-14T08:20:00.000Z",
        "model": "MT7-S-6mA-2-RO",
        "name": "Kyle-Dion",
        "ppid": "PG-70500",
        "schemes": [],
        "site": {
          "address": {
            "country": "GB",
            "line1": "Barrhead Road",
            "line2": "",
            "name": "Tesco Extra - Silverburn",
            "postcode": "G53 6AG",
            "prettyPrint": "Barrhead Road, Glasgow, G53 6AG, GB",
            "town": "Glasgow",
          },
          "contactDetails": {
            "email": "<EMAIL>",
            "name": "Jane Blogs",
            "telephone": "+44*********",
          },
          "description": "",
          "energyCost": 15,
          "group": {
            "id": 2,
            "name": "Tesco Stores Ltd",
            "uid": "c9c5d39c-23ba-4a21-ac45-75968fdc055f",
          },
          "id": 75710,
          "parking": {},
        },
        "sockets": [
          {
            "door": "A",
            "firmwareVersion": "Unknown",
            "isUpdateAvailable": false,
            "lastContact": "2022-07-14T08:20:00.000Z",
            "serialNumber": "213611186",
            "status": "Available",
          },
          {
            "door": "B",
            "firmwareVersion": "Unknown",
            "isUpdateAvailable": false,
            "lastContact": "2022-07-14T07:17:02.000Z",
            "serialNumber": "213611185",
            "status": "Available",
          },
        ],
        "status": "Available",
        "supportsConfirmCharge": true,
        "supportsContactless": false,
        "supportsEnergyTariff": true,
        "supportsOcpp": false,
        "supportsPerKwh": true,
        "supportsTariffs": true,
        "tariff": {
          "id": 1638,
          "name": "Tesco Esso Alliance PAYG Tariff",
        },
      },
    ],
  },
  {
    "currency": "GBP",
    "id": 249,
    "name": "Tesco Rapid kWh",
    "pods": [
      {
        "confirmChargeEnabled": true,
        "coordinates": {
          "latitude": 51.124148,
          "longitude": -0.16234,
        },
        "description": "This is a rapid charger installed at the Tesco Extra superstore in Hazelwick Avenue, Crawley, West Sussex",
        "id": 5189,
        "isEvZone": false,
        "isPublic": false,
        "lastContact": "2022-05-14T20:52:34.000Z",
        "model": "Terra 53 CGT",
        "name": "Bill-Tara",
        "ppid": "t53_it1_3717_004",
        "schemes": [],
        "site": {
          "address": {
            "country": "GB",
            "line1": "Barrhead Road",
            "line2": "",
            "name": "Tesco Extra - Silverburn",
            "postcode": "G53 6AG",
            "prettyPrint": "Barrhead Road, Glasgow, G53 6AG, GB",
            "town": "Glasgow",
          },
          "contactDetails": {
            "email": "<EMAIL>",
            "name": "Jane Blogs",
            "telephone": "+44*********",
          },
          "description": "",
          "energyCost": 15,
          "group": {
            "id": 2,
            "name": "Tesco Stores Ltd",
            "uid": "c9c5d39c-23ba-4a21-ac45-75968fdc055f",
          },
          "id": 75710,
          "parking": {},
        },
        "sockets": [
          {
            "door": "A",
            "firmwareVersion": "Unknown",
            "isUpdateAvailable": false,
            "lastContact": "2022-05-14T20:52:34.000Z",
            "serialNumber": "T53-IT1-3717-004-Door1",
            "status": "Available",
          },
          {
            "door": "B",
            "firmwareVersion": "Unknown",
            "isUpdateAvailable": false,
            "lastContact": "2022-05-14T20:52:34.000Z",
            "serialNumber": "T53-IT1-3717-004-Door2",
            "status": "Charging",
          },
          {
            "door": "C",
            "firmwareVersion": "Unknown",
            "isUpdateAvailable": false,
            "lastContact": "2022-05-14T20:52:34.000Z",
            "serialNumber": "T53-IT1-3717-004-Door3",
            "status": "Unavailable",
          },
        ],
        "status": "Unavailable",
        "supportsConfirmCharge": true,
        "supportsContactless": false,
        "supportsEnergyTariff": true,
        "supportsOcpp": true,
        "supportsPerKwh": true,
        "supportsTariffs": false,
        "tariff": {
          "id": 249,
          "name": "Tesco Rapid kWh",
        },
      },
    ],
    "schedule": {
      "drivers": [
        {
          "bands": [
            {
              "after": 0,
              "cost": 50,
              "id": 63600,
              "prettyPrint": "50p",
            },
          ],
          "endDay": 1,
          "endTime": "17:00:00",
          "id": 63600,
          "pricingModel": "energy",
          "startDay": 1,
          "startTime": "09:00:00",
          "tariffTier": "drivers",
        },
      ],
      "members": [
        {
          "bands": [
            {
              "after": 0,
              "cost": 30,
              "id": 63601,
              "prettyPrint": "30p",
            },
          ],
          "endDay": 1,
          "endTime": "14:00:00",
          "id": 63601,
          "pricingModel": "energy",
          "startDay": 1,
          "startTime": "09:00:00",
          "tariffTier": "members",
        },
      ],
      "public": [
        {
          "bands": [
            {
              "after": 0,
              "cost": 80,
              "id": 63602,
              "prettyPrint": "80p",
            },
          ],
          "endDay": 7,
          "endTime": "22:00:00",
          "id": 63602,
          "pricingModel": "energy",
          "startDay": 7,
          "startTime": "16:00:00",
          "tariffTier": "public",
        },
      ],
    },
  },
]
`;

exports[`destination site admin api tariff module tariff schedule controller should create a new custom tariff schedule 1`] = `
{
  "currency": "GBP",
  "id": 4206,
  "name": "Another Test Tariff",
  "pods": [],
  "schedule": {
    "drivers": [
      {
        "bands": [
          {
            "after": 0,
            "cost": 179,
            "id": 85575,
            "prettyPrint": "179p",
          },
        ],
        "endDay": 7,
        "endTime": "16:00:00",
        "id": 85575,
        "pricingModel": "energy",
        "startDay": 6,
        "startTime": "09:00:00",
        "tariffTier": "drivers",
      },
    ],
    "members": [],
    "public": [],
  },
}
`;

exports[`destination site admin api tariff module tariff schedule controller should create a new day/night tariff schedule 1`] = `
{
  "currency": "GBP",
  "id": 4208,
  "name": "day/night tariff",
  "pods": [],
  "schedule": {
    "drivers": [
      {
        "bands": [
          {
            "after": 0,
            "cost": 100,
            "id": 85578,
            "prettyPrint": "100p",
          },
        ],
        "endDay": 0,
        "endTime": "17:00:00",
        "id": 85578,
        "pricingModel": "energy",
        "startDay": 0,
        "startTime": "09:00:00",
        "tariffTier": "drivers",
      },
      {
        "bands": [
          {
            "after": 0,
            "cost": 199,
            "id": 85579,
            "prettyPrint": "199p",
          },
        ],
        "endDay": 1,
        "endTime": "09:00:00",
        "id": 85579,
        "pricingModel": "energy",
        "startDay": 0,
        "startTime": "17:00:00",
        "tariffTier": "drivers",
      },
      {
        "bands": [
          {
            "after": 0,
            "cost": 100,
            "id": 85580,
            "prettyPrint": "100p",
          },
        ],
        "endDay": 1,
        "endTime": "17:00:00",
        "id": 85580,
        "pricingModel": "energy",
        "startDay": 1,
        "startTime": "09:00:00",
        "tariffTier": "drivers",
      },
      {
        "bands": [
          {
            "after": 0,
            "cost": 199,
            "id": 85581,
            "prettyPrint": "199p",
          },
        ],
        "endDay": 2,
        "endTime": "09:00:00",
        "id": 85581,
        "pricingModel": "energy",
        "startDay": 1,
        "startTime": "17:00:00",
        "tariffTier": "drivers",
      },
      {
        "bands": [
          {
            "after": 0,
            "cost": 100,
            "id": 85582,
            "prettyPrint": "100p",
          },
        ],
        "endDay": 2,
        "endTime": "17:00:00",
        "id": 85582,
        "pricingModel": "energy",
        "startDay": 2,
        "startTime": "09:00:00",
        "tariffTier": "drivers",
      },
      {
        "bands": [
          {
            "after": 0,
            "cost": 199,
            "id": 85583,
            "prettyPrint": "199p",
          },
        ],
        "endDay": 3,
        "endTime": "09:00:00",
        "id": 85583,
        "pricingModel": "energy",
        "startDay": 2,
        "startTime": "17:00:00",
        "tariffTier": "drivers",
      },
      {
        "bands": [
          {
            "after": 0,
            "cost": 100,
            "id": 85584,
            "prettyPrint": "100p",
          },
        ],
        "endDay": 3,
        "endTime": "17:00:00",
        "id": 85584,
        "pricingModel": "energy",
        "startDay": 3,
        "startTime": "09:00:00",
        "tariffTier": "drivers",
      },
      {
        "bands": [
          {
            "after": 0,
            "cost": 199,
            "id": 85585,
            "prettyPrint": "199p",
          },
        ],
        "endDay": 4,
        "endTime": "09:00:00",
        "id": 85585,
        "pricingModel": "energy",
        "startDay": 3,
        "startTime": "17:00:00",
        "tariffTier": "drivers",
      },
      {
        "bands": [
          {
            "after": 0,
            "cost": 100,
            "id": 85586,
            "prettyPrint": "100p",
          },
        ],
        "endDay": 4,
        "endTime": "17:00:00",
        "id": 85586,
        "pricingModel": "energy",
        "startDay": 4,
        "startTime": "09:00:00",
        "tariffTier": "drivers",
      },
      {
        "bands": [
          {
            "after": 0,
            "cost": 199,
            "id": 85587,
            "prettyPrint": "199p",
          },
        ],
        "endDay": 5,
        "endTime": "09:00:00",
        "id": 85587,
        "pricingModel": "energy",
        "startDay": 4,
        "startTime": "17:00:00",
        "tariffTier": "drivers",
      },
      {
        "bands": [
          {
            "after": 0,
            "cost": 100,
            "id": 85588,
            "prettyPrint": "100p",
          },
        ],
        "endDay": 5,
        "endTime": "17:00:00",
        "id": 85588,
        "pricingModel": "energy",
        "startDay": 5,
        "startTime": "09:00:00",
        "tariffTier": "drivers",
      },
      {
        "bands": [
          {
            "after": 0,
            "cost": 199,
            "id": 85589,
            "prettyPrint": "199p",
          },
        ],
        "endDay": 6,
        "endTime": "09:00:00",
        "id": 85589,
        "pricingModel": "energy",
        "startDay": 5,
        "startTime": "17:00:00",
        "tariffTier": "drivers",
      },
      {
        "bands": [
          {
            "after": 0,
            "cost": 100,
            "id": 85590,
            "prettyPrint": "100p",
          },
        ],
        "endDay": 6,
        "endTime": "17:00:00",
        "id": 85590,
        "pricingModel": "energy",
        "startDay": 6,
        "startTime": "09:00:00",
        "tariffTier": "drivers",
      },
      {
        "bands": [
          {
            "after": 0,
            "cost": 199,
            "id": 85591,
            "prettyPrint": "199p",
          },
        ],
        "endDay": 0,
        "endTime": "09:00:00",
        "id": 85591,
        "pricingModel": "energy",
        "startDay": 6,
        "startTime": "17:00:00",
        "tariffTier": "drivers",
      },
    ],
    "members": [],
    "public": [],
  },
}
`;

exports[`destination site admin api tariff module tariff schedule controller should create a new workweek tariff schedule 1`] = `
{
  "currency": "GBP",
  "id": 4207,
  "name": "New tariff",
  "pods": [],
  "schedule": {
    "drivers": [
      {
        "bands": [
          {
            "after": 0,
            "cost": 100,
            "id": 85576,
            "prettyPrint": "100p",
          },
        ],
        "endDay": 6,
        "endTime": "00:00:00",
        "id": 85576,
        "pricingModel": "energy",
        "startDay": 1,
        "startTime": "00:00:00",
        "tariffTier": "drivers",
      },
      {
        "bands": [
          {
            "after": 0,
            "cost": 199,
            "id": 85577,
            "prettyPrint": "199p",
          },
        ],
        "endDay": 1,
        "endTime": "00:00:00",
        "id": 85577,
        "pricingModel": "energy",
        "startDay": 6,
        "startTime": "00:00:00",
        "tariffTier": "drivers",
      },
    ],
    "members": [],
    "public": [],
  },
}
`;

exports[`destination site admin api tariff module tariff schedule controller should create a secondary price band 1`] = `
{
  "currency": "GBP",
  "id": 4206,
  "name": "Another Test Tariff",
  "pods": [],
  "schedule": {
    "drivers": [
      {
        "bands": [
          {
            "after": 0,
            "cost": 189,
            "id": 85575,
            "prettyPrint": "189p",
          },
        ],
        "endDay": 2,
        "endTime": "15:00:00",
        "id": 85575,
        "pricingModel": "energy",
        "startDay": 1,
        "startTime": "08:00:00",
        "tariffTier": "drivers",
      },
      {
        "bands": [
          {
            "after": 0,
            "cost": 179,
            "id": 85592,
            "prettyPrint": "179p",
          },
        ],
        "endDay": 7,
        "endTime": "16:00:00",
        "id": 85592,
        "pricingModel": "energy",
        "startDay": 6,
        "startTime": "09:00:00",
        "tariffTier": "drivers",
      },
    ],
    "members": [],
    "public": [
      {
        "bands": [
          {
            "after": 3600,
            "cost": 900,
            "id": 85593,
            "prettyPrint": "900p after 1 hour",
          },
          {
            "after": 7200,
            "cost": 900,
            "id": 85594,
            "prettyPrint": "900p after 2 hours",
          },
        ],
        "endDay": 3,
        "endTime": "17:00:00",
        "id": 85593,
        "pricingModel": "duration",
        "startDay": 3,
        "startTime": "10:00:00",
        "tariffTier": "public",
      },
    ],
  },
}
`;

exports[`destination site admin api tariff module tariff schedule controller should create new price bands 1`] = `
{
  "currency": "GBP",
  "id": 4206,
  "name": "Another Test Tariff",
  "pods": [],
  "schedule": {
    "drivers": [
      {
        "bands": [
          {
            "after": 0,
            "cost": 189,
            "id": 85575,
            "prettyPrint": "189p",
          },
        ],
        "endDay": 2,
        "endTime": "15:00:00",
        "id": 85575,
        "pricingModel": "energy",
        "startDay": 1,
        "startTime": "08:00:00",
        "tariffTier": "drivers",
      },
      {
        "bands": [
          {
            "after": 0,
            "cost": 179,
            "id": 85592,
            "prettyPrint": "179p",
          },
        ],
        "endDay": 7,
        "endTime": "16:00:00",
        "id": 85592,
        "pricingModel": "energy",
        "startDay": 6,
        "startTime": "09:00:00",
        "tariffTier": "drivers",
      },
    ],
    "members": [],
    "public": [
      {
        "bands": [
          {
            "after": 3600,
            "cost": 900,
            "id": 85593,
            "prettyPrint": "900p after 1 hour",
          },
        ],
        "endDay": 3,
        "endTime": "17:00:00",
        "id": 85593,
        "pricingModel": "duration",
        "startDay": 3,
        "startTime": "10:00:00",
        "tariffTier": "public",
      },
    ],
  },
}
`;

exports[`destination site admin api tariff module tariff schedule controller should delete an existing tariff schedule 1`] = `
{
  "currency": "GBP",
  "id": 4206,
  "name": "Another Test Tariff",
  "pods": [],
  "schedule": {
    "drivers": [
      {
        "bands": [
          {
            "after": 0,
            "cost": 189,
            "id": 85575,
            "prettyPrint": "189p",
          },
        ],
        "endDay": 2,
        "endTime": "15:00:00",
        "id": 85575,
        "pricingModel": "energy",
        "startDay": 1,
        "startTime": "08:00:00",
        "tariffTier": "drivers",
      },
      {
        "bands": [
          {
            "after": 0,
            "cost": 179,
            "id": 85592,
            "prettyPrint": "179p",
          },
        ],
        "endDay": 7,
        "endTime": "16:00:00",
        "id": 85592,
        "pricingModel": "energy",
        "startDay": 6,
        "startTime": "09:00:00",
        "tariffTier": "drivers",
      },
    ],
    "members": [],
    "public": [
      {
        "bands": [
          {
            "after": 3600,
            "cost": 900,
            "id": 85593,
            "prettyPrint": "900p after 1 hour",
          },
          {
            "after": 7200,
            "cost": 900,
            "id": 85594,
            "prettyPrint": "900p after 2 hours",
          },
        ],
        "endDay": 3,
        "endTime": "17:00:00",
        "id": 85593,
        "pricingModel": "duration",
        "startDay": 3,
        "startTime": "10:00:00",
        "tariffTier": "public",
      },
    ],
  },
}
`;

exports[`destination site admin api tariff module tariff schedule controller should update an existing tariff schedule 1`] = `
{
  "currency": "GBP",
  "id": 4206,
  "name": "Another Test Tariff",
  "pods": [],
  "schedule": {
    "drivers": [
      {
        "bands": [
          {
            "after": 0,
            "cost": 189,
            "id": 85575,
            "prettyPrint": "189p",
          },
        ],
        "endDay": 2,
        "endTime": "15:00:00",
        "id": 85575,
        "pricingModel": "energy",
        "startDay": 1,
        "startTime": "08:00:00",
        "tariffTier": "drivers",
      },
    ],
    "members": [],
    "public": [],
  },
}
`;

exports[`destination site admin api user module user controller should find user by auth id 1`] = `
{
  "activatedOn": "2016-08-18T10:29:53.000Z",
  "authId": "d54561e3-222e-4e8d-be86-6c77891160ee",
  "email": "<EMAIL>",
  "emailVerified": true,
  "firstName": "Bonnie",
  "groupId": 2,
  "groupName": "Tesco Stores Ltd",
  "groupType": "host",
  "groupUid": "c9c5d39c-23ba-4a21-ac45-75968fdc055f",
  "id": 15552,
  "lastName": "Nolan",
  "status": "Activated",
  "termsAndConditions": {
    "currentVersion": "2025-03-31",
  },
}
`;

exports[`destination site admin api user module user controller should update user by auth id 1`] = `
{
  "activatedOn": "2016-08-18T10:29:53.000Z",
  "authId": "d54561e3-222e-4e8d-be86-6c77891160ee",
  "email": "<EMAIL>",
  "emailVerified": true,
  "firstName": "Bonny",
  "groupId": 2,
  "groupName": "Tesco Stores Ltd",
  "groupType": "host",
  "groupUid": "c9c5d39c-23ba-4a21-ac45-75968fdc055f",
  "id": 15552,
  "lastName": "Lass",
  "status": "Activated",
  "termsAndConditions": {
    "currentVersion": "2025-03-31",
  },
}
`;
