import { NextApiRequest, NextApiResponse } from 'next';
import { authOptions } from './auth/[...nextauth]';
import { handleNextApiRequest } from '@experience/commercial/next/api-request-utils';

const BASE_URL = process.env.SITE_ADMIN_API_URL;

const handler = async (req: NextApiRequest, res: NextApiResponse) => {
  const { year } = req.query;
  const url = year
    ? `${BASE_URL}/insights?year=${year}`
    : `${BASE_URL}/insights`;

  return handleNextApiRequest(url, req, res, authOptions);
};

export default handler;
