import { ApiProperty } from '@nestjs/swagger';
import { BankAccountResponseDTO } from '@experience/mobile/payments-api/axios';
import { IsNotEmpty, IsString, Matches, MaxLength } from 'class-validator';

export class BankAccountDTO {
  @ApiProperty({
    description: 'The ID of the account',
  })
  id: string;

  @ApiProperty({
    description: 'The name on the account',
  })
  accountName: string;

  @ApiProperty({
    description: 'The last four digits of the account number',
    example: '6241',
  })
  accountNumberLastFourDigits: string;

  @ApiProperty({
    description: 'If this account is currently active',
    type: Boolean,
    example: true,
  })
  active: boolean;

  static fromPaymentsResponse(
    response: BankAccountResponseDTO
  ): BankAccountDTO {
    return {
      id: response.id,
      accountName: response.accountName,
      accountNumberLastFourDigits: response.accountNumberLastFourDigits,
      active: response.active,
    };
  }
}

export class CreateBankAccountDTO {
  @ApiProperty({
    description: 'The full name on the bank account',
    example: '<PERSON>',
  })
  @IsString()
  @IsNotEmpty()
  @MaxLength(50)
  @Matches(/^[a-zA-Z.\- '`&]*$/)
  accountName: string;

  @ApiProperty({
    description: 'The account number',
    example: '********',
  })
  accountNumber: string;

  @ApiProperty({
    description: 'The sort code',
    example: '108800',
  })
  sortCode: string;
}

enum COPStatus {
  MATCH = 'MATCH',
  MISMATCH = 'MISMATCH',
  PARTIAL_MATCH = 'PARTIAL_MATCH',
  UNAVAILABLE = 'UNAVAILABLE',
}

export class BankAccountCOPFailedDTO {
  @ApiProperty({
    description: 'The status of the confirmation of payee check',
    enum: COPStatus,
  })
  status: COPStatus;

  @ApiProperty({
    description: 'The name provided',
    example: 'Jenny Rosen',
  })
  provided: string;

  @ApiProperty({
    description:
      'The suggested name associated with the bank account if status is PARTIAL_MATCH',
    example: 'Jennifer Rosen',
    nullable: true,
  })
  suggested: string | null;
}
