import * as Sentry from '@sentry/node';
import { AppModule } from './app/app.module';
import { ConfigService } from '@nestjs/config';
import { DocumentBuilder } from '@nestjs/swagger';
import { INestApplication } from '@nestjs/common';
import { NodeSDK } from '@opentelemetry/sdk-node';
import { OTLPTraceExporter } from '@opentelemetry/exporter-trace-otlp-http';
import { Resource } from '@opentelemetry/resources';
import { SentryPropagator, SentrySpanProcessor } from '@sentry/opentelemetry';
import {
  beforeSendSentryError,
  bootstrap,
} from '@experience/shared/nest/utils';
import { getNodeAutoInstrumentations } from '@opentelemetry/auto-instrumentations-node';
import { getSwaggerDocs } from './swagger/swagger';
import axios from 'axios';

axios.defaults.headers.common[
  'User-Agent'
] = `mobile-api/${process.env.APPLICATION_VERSION} (mobile)`;

void bootstrap({
  module: AppModule,
  port: 5102,
  rawBody: true,
  protectSwagger: true,
  preCallback: () => {
    const sdk = new NodeSDK({
      resource: new Resource({
        'service.name': 'mobile-api',
        'service.version': process.env.APPLICATION_VERSION,
      }),

      // Existing config
      traceExporter: new OTLPTraceExporter(),
      instrumentations: [getNodeAutoInstrumentations()],

      // Sentry config
      spanProcessors: [new SentrySpanProcessor()],
      textMapPropagator: new SentryPropagator(),
    });

    sdk.start();
  },
  callback: (app: INestApplication, docBuilder: DocumentBuilder) => {
    const config = app.get(ConfigService);

    Sentry.init({
      enabled: ['dev', 'stage', 'prod'].includes(
        config.get<string>('ENVIRONMENT')
      ),
      environment: config.get<string>('ENVIRONMENT'),
      release: config.get<string>('SENTRY_RELEASE'),
      dsn: config.get<string>('SENTRY_DSN'),
      tracesSampleRate: 0.01, // 1% of traffic sampled
      ignoreTransactions: ['/health'],
      beforeSend: (event, hint) =>
        beforeSendSentryError(event, hint, [
          {
            errorCodes: [409, 422],
            functions: ['AccountClientService.createNewUser'],
          },
          {
            errorCodes: [402, 409, 422, 503],
            functions: ['Api3Service.claimCharge'],
          },
        ]),
    });

    return getSwaggerDocs(docBuilder);
  },
});
