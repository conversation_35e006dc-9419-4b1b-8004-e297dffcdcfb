import { AuthenticationModule } from '../authentication/authentication.module';
import { CACHE_MANAGER, Cache, CacheModule } from '@nestjs/cache-manager';
import {
  CACHE_TTL,
  SmartChargingService,
  VEHICLES_CACHE_KEY,
} from './smart-charging.service';
import { ConfigModule } from '@nestjs/config';
import { DeepMocked, createMock } from '@golevelup/ts-jest';
import {
  DelegatedControlChargingStationResponseDtoStatusEnum,
  DelegatedControlChargingStationsApi,
  DelegatedControlIntentsApi,
  DelegatedControlVehiclesApi,
} from '@experience/shared/axios/smart-charging-service-client';
import { ITokenAuthUser } from '@experience/mobile/nest/authorisation';
import {
  TEST_CAPTURE_VEHICLE_REQUEST,
  TEST_CAPTURE_VEHICLE_REQUEST_NO_MODEL_VARIANT,
  TEST_CAPTURE_VEHICLE_RESPONSE,
  TEST_GET_INTENTS_RESPONSE,
  TEST_GET_VEHICLES_RESPONSE,
  TEST_SET_VEHICLE_INTENT_REQUEST,
  TEST_SET_VEHICLE_INTENT_RESPONSE,
  TEST_UPDATE_VEHICLE_REQUEST,
  TEST_UPDATE_VEHICLE_RESPONSE,
} from './__fixtures__/smart-charging.interface';
import { TEST_USER_ENTITY } from '@experience/shared/sequelize/podadmin';
import { Test } from '@nestjs/testing';
import { UsersModule } from '../users/users.module';
import { UsersService } from '../users/users.service';
import { jest } from '@jest/globals';
import axios, { AxiosError, AxiosHeaders, AxiosResponse } from 'axios';

jest.mock('uuid', () => ({ v4: () => 'e3e92d8a-966e-4e0d-8895-6e4d8d75dc49' }));

describe('Smart Charging Service', () => {
  const PPID = 'PSL-12345';
  const VEHICLE_ID = 'fac4b3f3-9866-499a-acaf-75c214fbbdf9';
  const AUTH_USER: ITokenAuthUser = {
    uuid: 'uid',
    email: TEST_USER_ENTITY.email,
    id: '12345',
  };

  let service: SmartChargingService;
  let delegatedControlChargingStationsApi: DeepMocked<DelegatedControlChargingStationsApi>;
  let delegatedControlVehiclesApi: DeepMocked<DelegatedControlVehiclesApi>;
  let delegatedControlIntentsApi: DeepMocked<DelegatedControlIntentsApi>;
  let usersService: UsersService;
  let cache: Cache;

  beforeEach(async () => {
    const module = await Test.createTestingModule({
      imports: [
        CacheModule.register(),
        ConfigModule,
        AuthenticationModule,
        UsersModule,
        CacheModule.register(),
      ],
      providers: [
        SmartChargingService,
        {
          provide: DelegatedControlChargingStationsApi,
          useValue: createMock<DelegatedControlChargingStationsApi>(),
        },
        {
          provide: DelegatedControlVehiclesApi,
          useValue: createMock<DelegatedControlVehiclesApi>(),
        },
        {
          provide: DelegatedControlIntentsApi,
          useValue: createMock<DelegatedControlIntentsApi>(),
        },
      ],
    }).compile();

    service = module.get<SmartChargingService>(SmartChargingService);
    delegatedControlChargingStationsApi = module.get(
      DelegatedControlChargingStationsApi
    );
    delegatedControlVehiclesApi = module.get(DelegatedControlVehiclesApi);
    delegatedControlIntentsApi = module.get(DelegatedControlIntentsApi);
    usersService = module.get<UsersService>(UsersService);
    cache = module.get<Cache>(CACHE_MANAGER);
  });

  afterEach(() => jest.resetAllMocks());

  describe('getDelegatedControlChargerStatus', () => {
    it('should return the status of a charger', async () => {
      delegatedControlChargingStationsApi.getDelegatedControlChargingStationByPpid.mockResolvedValueOnce(
        {
          status: 200,
          data: {
            ...TEST_GET_INTENTS_RESPONSE,
          },
        } as AxiosResponse
      );

      const res = await service.getDelegatedControlChargerStatus(PPID);

      expect(
        delegatedControlChargingStationsApi.getDelegatedControlChargingStationByPpid
      ).toHaveBeenCalledTimes(1);
      expect(
        delegatedControlChargingStationsApi.getDelegatedControlChargingStationByPpid
      ).toHaveBeenCalledWith(PPID);

      expect(res).toStrictEqual(
        DelegatedControlChargingStationResponseDtoStatusEnum.Active
      );
    });

    it('should return INACTIVE if status is CANDIDATE', async () => {
      delegatedControlChargingStationsApi.getDelegatedControlChargingStationByPpid.mockResolvedValue(
        {
          status: 200,
          data: {
            status: 'CANDIDATE',
          },
        } as AxiosResponse
      );

      const res = await service.getDelegatedControlChargerStatus(PPID);

      expect(res).toEqual('INACTIVE');

      expect(
        delegatedControlChargingStationsApi.getDelegatedControlChargingStationByPpid
      ).toHaveBeenCalledTimes(1);
      expect(
        delegatedControlChargingStationsApi.getDelegatedControlChargingStationByPpid
      ).toHaveBeenCalledWith(PPID);
    });

    it('should return unknown if get delegated control status returns 404', async () => {
      const request = { path: '/foo' };
      const headers = new AxiosHeaders();
      const config = {
        url: 'http://smart-charging-api:3000',
        headers,
      };

      const error = new AxiosError(
        'Not found error',
        'ENOTFOUND',
        config,
        request,
        {
          status: 404,
          data: {},
          statusText: 'Not found',
          config,
          headers,
        }
      );

      delegatedControlChargingStationsApi.getDelegatedControlChargingStationByPpid.mockRejectedValueOnce(
        error
      );

      const res = await service.getDelegatedControlChargerStatus(PPID);

      expect(
        delegatedControlChargingStationsApi.getDelegatedControlChargingStationByPpid
      ).toHaveBeenCalledTimes(1);
      expect(
        delegatedControlChargingStationsApi.getDelegatedControlChargingStationByPpid
      ).toHaveBeenCalledWith(PPID);

      expect(res).toStrictEqual(
        DelegatedControlChargingStationResponseDtoStatusEnum.Unknown
      );
    });
  });

  describe('getChargersAndVehicles', () => {
    it('should return chargers and vehicles', async () => {
      const userChargers = [
        {
          ppid: 'PSL-12345',
          unitId: 1,
          timezone: 'Etc/UTC',
          linkedAt: '2024-01-01T00:00:00.000Z',
        },
        {
          ppid: 'PSL-34567',
          unitId: 1,
          timezone: 'Etc/UTC',
          linkedAt: '2024-01-01T01:00:00.000Z',
        },
      ];

      jest
        .spyOn(usersService, 'getUserChargers')
        .mockResolvedValue(userChargers);

      delegatedControlVehiclesApi.getDelegatedControlChargingStationVehicles.mockResolvedValue(
        {
          data: {
            ...TEST_GET_VEHICLES_RESPONSE,
          },
        } as AxiosResponse
      );

      const chargersAndVehicles = await service.getChargersAndVehicles(
        AUTH_USER
      );

      expect(chargersAndVehicles).toEqual([
        {
          ppid: 'PSL-12345',
          vehicles: TEST_GET_VEHICLES_RESPONSE.data.map((v) => ({
            ...v,
            isPrimary: true,
          })),
        },
        {
          ppid: 'PSL-34567',
          vehicles: TEST_GET_VEHICLES_RESPONSE.data.map((v) => ({
            ...v,
            isPrimary: true,
          })),
        },
      ]);
    });

    it('vehicles should include isPrimary if the api does not return it', async () => {
      const userChargers = [
        {
          ppid: 'PSL-12345',
          unitId: 1,
          timezone: 'Etc/UTC',
          linkedAt: '2024-01-01T00:00:00.000Z',
        },
        {
          ppid: 'PSL-34567',
          unitId: 1,
          timezone: 'Etc/UTC',
          linkedAt: '2024-01-01T01:00:00.000Z',
        },
      ];

      jest
        .spyOn(usersService, 'getUserChargers')
        .mockResolvedValue(userChargers);

      delegatedControlVehiclesApi.getDelegatedControlChargingStationVehicles.mockResolvedValue(
        {
          data: {
            data: TEST_GET_VEHICLES_RESPONSE.data,
          },
        } as AxiosResponse
      );

      const chargersAndVehicles = await service.getChargersAndVehicles(
        AUTH_USER
      );

      expect(chargersAndVehicles).toEqual([
        {
          ppid: 'PSL-12345',
          vehicles: TEST_GET_VEHICLES_RESPONSE.data.map((veh) => ({
            ...veh,
            isPrimary: expect.any(Boolean),
          })),
        },
        {
          ppid: 'PSL-34567',
          vehicles: TEST_GET_VEHICLES_RESPONSE.data.map((veh) => ({
            ...veh,
            isPrimary: expect.any(Boolean),
          })),
        },
      ]);
    });
  });

  describe('captureVehicle', () => {
    it('captures the vehicle information when not enode compatible (with cache)', async () => {
      delegatedControlVehiclesApi.addVehicleToDelegatedControlChargingStation.mockResolvedValueOnce(
        {
          data: {
            ...TEST_CAPTURE_VEHICLE_RESPONSE,
          },
        } as AxiosResponse
      );

      await cache.set(
        VEHICLES_CACHE_KEY,
        {
          brands: [],
          models: [],
          modelVariants: [
            {
              name: 'Long range',
              modelId: 'polestar-2',
              enodeCompatible: true,
            },
          ],
        },
        CACHE_TTL
      );

      const res = await service.captureVehicle(
        PPID,
        TEST_CAPTURE_VEHICLE_REQUEST
      );

      expect(
        delegatedControlVehiclesApi.addVehicleToDelegatedControlChargingStation
      ).toHaveBeenCalledTimes(1);

      expect(
        delegatedControlVehiclesApi.addVehicleToDelegatedControlChargingStation
      ).toHaveBeenCalledWith(PPID, TEST_CAPTURE_VEHICLE_REQUEST);

      expect(res).toStrictEqual({
        ...(
          await delegatedControlVehiclesApi
            .addVehicleToDelegatedControlChargingStation.mock.results[0].value
        ).data,
        isPrimary: true,
      });
    });

    it('captures the vehicle information when enode compatible (with cache)', async () => {
      delegatedControlVehiclesApi.addVehicleToDelegatedControlChargingStation.mockResolvedValueOnce(
        {
          data: {
            ...TEST_CAPTURE_VEHICLE_RESPONSE,
          },
        } as AxiosResponse
      );

      await cache.set(
        VEHICLES_CACHE_KEY,
        {
          brands: [],
          models: [],
          modelVariants: [
            {
              name: 'Long range',
              modelId: 'polestar-2',
              enodeCompatible: true,
            },
          ],
        },
        CACHE_TTL
      );

      const res = await service.captureVehicle(
        PPID,
        TEST_CAPTURE_VEHICLE_REQUEST
      );

      expect(
        delegatedControlVehiclesApi.addVehicleToDelegatedControlChargingStation
      ).toHaveBeenCalledTimes(1);

      expect(
        delegatedControlVehiclesApi.addVehicleToDelegatedControlChargingStation
      ).toHaveBeenCalledWith(PPID, {
        ...TEST_CAPTURE_VEHICLE_REQUEST,
        vehicle: {
          ...TEST_CAPTURE_VEHICLE_REQUEST.vehicle,
          enodeUserId: 'e3e92d8a-966e-4e0d-8895-6e4d8d75dc49',
        },
      });

      expect(res).toStrictEqual({
        ...(
          await delegatedControlVehiclesApi
            .addVehicleToDelegatedControlChargingStation.mock.results[0].value
        ).data,
        isPrimary: true,
      });
    });

    it('captures the vehicle information when not enode compatible (no cache)', async () => {
      delegatedControlVehiclesApi.addVehicleToDelegatedControlChargingStation.mockResolvedValueOnce(
        {
          data: {
            ...TEST_CAPTURE_VEHICLE_RESPONSE,
          },
        } as AxiosResponse
      );

      jest.spyOn(axios, 'get').mockResolvedValueOnce({
        data: {
          brands: [],
          models: [],
          modelVariants: [],
        },
      });

      const res = await service.captureVehicle(
        PPID,
        TEST_CAPTURE_VEHICLE_REQUEST
      );

      expect(
        delegatedControlVehiclesApi.addVehicleToDelegatedControlChargingStation
      ).toHaveBeenCalledTimes(1);

      expect(
        delegatedControlVehiclesApi.addVehicleToDelegatedControlChargingStation
      ).toHaveBeenCalledWith(PPID, TEST_CAPTURE_VEHICLE_REQUEST);

      expect(res).toStrictEqual({
        ...(
          await delegatedControlVehiclesApi
            .addVehicleToDelegatedControlChargingStation.mock.results[0].value
        ).data,
        isPrimary: true,
      });
    });

    it('captures the vehicle information when enode compatible (no cache)', async () => {
      delegatedControlVehiclesApi.addVehicleToDelegatedControlChargingStation.mockResolvedValueOnce(
        {
          data: {
            ...TEST_CAPTURE_VEHICLE_RESPONSE,
          },
        } as AxiosResponse
      );

      jest.spyOn(axios, 'get').mockResolvedValueOnce({
        data: {
          brands: [],
          models: [],
          modelVariants: [
            {
              name: 'Long range',
              modelId: 'polestar-2',
              enodeCompatible: true,
            },
          ],
        },
      });

      const res = await service.captureVehicle(
        PPID,
        TEST_CAPTURE_VEHICLE_REQUEST
      );

      expect(
        delegatedControlVehiclesApi.addVehicleToDelegatedControlChargingStation
      ).toHaveBeenCalledTimes(1);

      expect(
        delegatedControlVehiclesApi.addVehicleToDelegatedControlChargingStation
      ).toHaveBeenCalledWith(PPID, {
        ...TEST_CAPTURE_VEHICLE_REQUEST,
        vehicle: {
          ...TEST_CAPTURE_VEHICLE_REQUEST.vehicle,
          enodeUserId: 'e3e92d8a-966e-4e0d-8895-6e4d8d75dc49',
        },
      });

      expect(res).toStrictEqual({
        ...(
          await delegatedControlVehiclesApi
            .addVehicleToDelegatedControlChargingStation.mock.results[0].value
        ).data,
        isPrimary: true,
      });
    });

    it('captures the vehicle information for enode compatible vehicle with null variant name', async () => {
      delegatedControlVehiclesApi.addVehicleToDelegatedControlChargingStation.mockResolvedValueOnce(
        {
          data: {
            ...TEST_CAPTURE_VEHICLE_RESPONSE,
          },
        } as AxiosResponse
      );

      jest.spyOn(axios, 'get').mockResolvedValueOnce({
        data: {
          brands: [],
          models: [],
          modelVariants: [
            {
              name: null,
              modelId: 'polestar-2',
              enodeCompatible: true,
            },
          ],
        },
      });

      const request = {
        ...TEST_CAPTURE_VEHICLE_REQUEST_NO_MODEL_VARIANT,
        vehicle: {
          ...TEST_CAPTURE_VEHICLE_REQUEST_NO_MODEL_VARIANT.vehicle,
          vehicleInformation: {
            ...TEST_CAPTURE_VEHICLE_REQUEST_NO_MODEL_VARIANT.vehicle
              .vehicleInformation,
            modelVariant: null,
          },
        },
      };

      const res = await service.captureVehicle(PPID, request);

      expect(
        delegatedControlVehiclesApi.addVehicleToDelegatedControlChargingStation
      ).toHaveBeenCalledTimes(1);

      expect(
        delegatedControlVehiclesApi.addVehicleToDelegatedControlChargingStation
      ).toHaveBeenCalledWith(PPID, {
        ...request,
        vehicle: {
          ...request.vehicle,
          enodeUserId: 'e3e92d8a-966e-4e0d-8895-6e4d8d75dc49',
        },
      });

      expect(res).toStrictEqual({
        ...(
          await delegatedControlVehiclesApi
            .addVehicleToDelegatedControlChargingStation.mock.results[0].value
        ).data,
        isPrimary: true,
      });
    });

    it('captures the vehicle information for enode compatible vehicle with undefined variant name', async () => {
      delegatedControlVehiclesApi.addVehicleToDelegatedControlChargingStation.mockResolvedValueOnce(
        {
          data: {
            ...TEST_CAPTURE_VEHICLE_RESPONSE,
          },
        } as AxiosResponse
      );

      jest.spyOn(axios, 'get').mockResolvedValueOnce({
        data: {
          brands: [],
          models: [],
          modelVariants: [
            {
              name: null,
              modelId: 'polestar-2',
              enodeCompatible: true,
            },
          ],
        },
      });

      const res = await service.captureVehicle(
        PPID,
        TEST_CAPTURE_VEHICLE_REQUEST_NO_MODEL_VARIANT
      );

      expect(
        delegatedControlVehiclesApi.addVehicleToDelegatedControlChargingStation
      ).toHaveBeenCalledTimes(1);

      expect(
        delegatedControlVehiclesApi.addVehicleToDelegatedControlChargingStation
      ).toHaveBeenCalledWith(PPID, {
        ...TEST_CAPTURE_VEHICLE_REQUEST_NO_MODEL_VARIANT,
        vehicle: {
          ...TEST_CAPTURE_VEHICLE_REQUEST_NO_MODEL_VARIANT.vehicle,
          enodeUserId: 'e3e92d8a-966e-4e0d-8895-6e4d8d75dc49',
        },
      });

      expect(res).toStrictEqual({
        ...(
          await delegatedControlVehiclesApi
            .addVehicleToDelegatedControlChargingStation.mock.results[0].value
        ).data,
        isPrimary: true,
      });
    });
  });

  describe('updateVehicle', () => {
    it('updated the vehicle via the delegated control api', async () => {
      delegatedControlVehiclesApi.updateVehicleByPpidAndVehicleId.mockResolvedValue(
        {
          data: TEST_UPDATE_VEHICLE_RESPONSE,
        } as AxiosResponse
      );

      const res = await service.updateVehicle(
        PPID,
        VEHICLE_ID,
        TEST_UPDATE_VEHICLE_REQUEST
      );

      expect(
        delegatedControlVehiclesApi.updateVehicleByPpidAndVehicleId
      ).toHaveBeenCalledTimes(1);
      expect(
        delegatedControlVehiclesApi.updateVehicleByPpidAndVehicleId
      ).toHaveBeenCalledWith(PPID, VEHICLE_ID, TEST_UPDATE_VEHICLE_REQUEST);

      expect(res).toEqual(TEST_UPDATE_VEHICLE_RESPONSE);
    });

    it('bubbles up any errors thrown by the delegated control api', async () => {
      delegatedControlVehiclesApi.updateVehicleByPpidAndVehicleId.mockImplementation(
        () => {
          throw new Error();
        }
      );

      await expect(async () =>
        service.updateVehicle(PPID, VEHICLE_ID, TEST_UPDATE_VEHICLE_REQUEST)
      ).rejects.toThrow(Error);
    });
  });

  describe('setVehicleIntent', () => {
    it('sets the vehicle intent data via the delegated control api', async () => {
      delegatedControlIntentsApi.setDelegatedControlIntents.mockResolvedValue({
        data: TEST_SET_VEHICLE_INTENT_RESPONSE,
      } as AxiosResponse);

      const res = await service.setVehicleIntents(
        PPID,
        VEHICLE_ID,
        TEST_SET_VEHICLE_INTENT_REQUEST
      );

      expect(
        delegatedControlIntentsApi.setDelegatedControlIntents
      ).toHaveBeenCalledTimes(1);
      expect(
        delegatedControlIntentsApi.setDelegatedControlIntents
      ).toHaveBeenCalledWith(PPID, VEHICLE_ID, TEST_SET_VEHICLE_INTENT_REQUEST);

      expect(res).toStrictEqual(TEST_SET_VEHICLE_INTENT_RESPONSE);
    });

    it('bubbles up any errors thrown by the delegated control api', async () => {
      delegatedControlIntentsApi.setDelegatedControlIntents.mockImplementation(
        () => {
          throw new Error();
        }
      );

      await expect(async () =>
        service.setVehicleIntents(
          PPID,
          VEHICLE_ID,
          TEST_SET_VEHICLE_INTENT_REQUEST
        )
      ).rejects.toThrow(Error);
    });
  });

  describe('createDelegatedControlStation', () => {
    it('proxies create delegated control station call', async () => {
      delegatedControlChargingStationsApi.createDelegatedControlChargingStation.mockResolvedValueOnce(
        {
          status: 201,
        } as AxiosResponse
      );

      await service.createDelegatedControlStation(PPID);

      expect(
        delegatedControlChargingStationsApi.createDelegatedControlChargingStation
      ).toHaveBeenCalledTimes(1);
      expect(
        delegatedControlChargingStationsApi.createDelegatedControlChargingStation
      ).toHaveBeenCalledWith(PPID, {
        providerName: 'axle',
      });
    });
  });

  describe('getIntents', () => {
    it('retrieves vehicle intents', async () => {
      delegatedControlChargingStationsApi.getDelegatedControlChargingStationByPpid.mockResolvedValueOnce(
        {
          data: {
            ...TEST_GET_INTENTS_RESPONSE,
          },
        } as AxiosResponse
      );

      const res = await service.getIntents(PPID);

      expect(
        delegatedControlChargingStationsApi.getDelegatedControlChargingStationByPpid
      ).toHaveBeenCalledTimes(1);
      expect(
        delegatedControlChargingStationsApi.getDelegatedControlChargingStationByPpid
      ).toHaveBeenCalledWith(PPID);

      expect(res).toStrictEqual({
        ...TEST_GET_INTENTS_RESPONSE,
        vehicleLinks: TEST_GET_INTENTS_RESPONSE.vehicleLinks.map((vl) => ({
          ...vl,
          isPrimary: true,
        })),
      });
    });

    it('returns INACTIVE if status is CANDIDATE', async () => {
      delegatedControlChargingStationsApi.getDelegatedControlChargingStationByPpid.mockResolvedValueOnce(
        {
          data: {
            status: 'CANDIDATE',
            vehicleLinks: [],
          },
        } as AxiosResponse
      );

      const res = await service.getIntents(PPID);

      expect(
        delegatedControlChargingStationsApi.getDelegatedControlChargingStationByPpid
      ).toHaveBeenCalledTimes(1);
      expect(
        delegatedControlChargingStationsApi.getDelegatedControlChargingStationByPpid
      ).toHaveBeenCalledWith(PPID);

      expect(res.status).toEqual('INACTIVE');
    });
  });

  describe('deleteVehicle', () => {
    it('deletes the vehicle via the delegated controls api', async () => {
      delegatedControlVehiclesApi.unlinkVehicleFromDelegatedControlChargingStation.mockResolvedValue(
        {
          data: {},
        } as AxiosResponse
      );

      const res = await service.deleteVehicle(PPID, VEHICLE_ID);

      expect(
        delegatedControlVehiclesApi.unlinkVehicleFromDelegatedControlChargingStation
      ).toHaveBeenCalledTimes(1);
      expect(
        delegatedControlVehiclesApi.unlinkVehicleFromDelegatedControlChargingStation
      ).toHaveBeenCalledWith(PPID, VEHICLE_ID);

      expect(res).toStrictEqual({});
    });

    it('bubbles up any errors thrown by the delegated controls api', async () => {
      delegatedControlVehiclesApi.unlinkVehicleFromDelegatedControlChargingStation.mockImplementation(
        () => {
          throw new Error();
        }
      );

      await expect(service.deleteVehicle(PPID, VEHICLE_ID)).rejects.toThrow(
        Error
      );

      expect(
        delegatedControlVehiclesApi.unlinkVehicleFromDelegatedControlChargingStation
      ).toHaveBeenCalledTimes(1);
      expect(
        delegatedControlVehiclesApi.unlinkVehicleFromDelegatedControlChargingStation
      ).toHaveBeenCalledWith(PPID, VEHICLE_ID);
    });
  });

  describe('removeChargerFromDelegatedControl()', () => {
    it('removes the charger from delegated controls', async () => {
      delegatedControlChargingStationsApi.deleteDelegatedControlChargingStationByPpid.mockResolvedValue(
        {
          status: 204,
        } as AxiosResponse
      );

      await service.removeChargerFromDelegatedControl(PPID);

      expect(
        delegatedControlChargingStationsApi.deleteDelegatedControlChargingStationByPpid
      ).toHaveBeenCalledTimes(1);
      expect(
        delegatedControlChargingStationsApi.deleteDelegatedControlChargingStationByPpid
      ).toHaveBeenCalledWith(PPID);
    });

    it('bubbles up any errors thrown by the delegated controls api', async () => {
      const error = new Error();

      delegatedControlChargingStationsApi.deleteDelegatedControlChargingStationByPpid.mockImplementation(
        () => {
          throw error;
        }
      );

      await expect(
        service.removeChargerFromDelegatedControl(PPID)
      ).rejects.toThrow(error);
    });
  });

  describe('getPreferences()', () => {
    it('returns nullified object if no preferences', async () => {
      delegatedControlChargingStationsApi.getDelegatedControlChargingStationByPpid.mockResolvedValue(
        {
          data: {
            preferences: undefined,
          },
        } as AxiosResponse
      );

      const res = await service.getPreferences(PPID);

      expect(res).toStrictEqual({
        maxPrice: null,
      });

      expect(
        delegatedControlChargingStationsApi.getDelegatedControlChargingStationByPpid
      ).toHaveBeenCalledTimes(1);
      expect(
        delegatedControlChargingStationsApi.getDelegatedControlChargingStationByPpid
      ).toHaveBeenCalledWith(PPID);
    });

    it('returns the preferences if set', async () => {
      delegatedControlChargingStationsApi.getDelegatedControlChargingStationByPpid.mockResolvedValue(
        {
          data: {
            preferences: {
              maxPrice: 10,
            },
          },
        } as AxiosResponse
      );

      const res = await service.getPreferences(PPID);

      expect(res).toStrictEqual({
        maxPrice: 10,
      });

      expect(
        delegatedControlChargingStationsApi.getDelegatedControlChargingStationByPpid
      ).toHaveBeenCalledTimes(1);
      expect(
        delegatedControlChargingStationsApi.getDelegatedControlChargingStationByPpid
      ).toHaveBeenCalledWith(PPID);
    });

    it('re-throws any errors', async () => {
      delegatedControlChargingStationsApi.getDelegatedControlChargingStationByPpid.mockRejectedValue(
        new AxiosError()
      );

      await expect(() => service.getPreferences(PPID)).rejects.toThrow(
        AxiosError
      );

      expect(
        delegatedControlChargingStationsApi.getDelegatedControlChargingStationByPpid
      ).toHaveBeenCalledTimes(1);
      expect(
        delegatedControlChargingStationsApi.getDelegatedControlChargingStationByPpid
      ).toHaveBeenCalledWith(PPID);
    });
  });

  describe('updatePreferences()', () => {
    it('calls delegated controls API to update preferences', async () => {
      delegatedControlChargingStationsApi.updateDelegatedControlChargingStationPreferences.mockResolvedValue(
        {
          data: {},
        } as AxiosResponse
      );

      await service.updatePreferences(PPID, {
        maxPrice: 10,
      });

      expect(
        delegatedControlChargingStationsApi.updateDelegatedControlChargingStationPreferences
      ).toHaveBeenCalledTimes(1);
      expect(
        delegatedControlChargingStationsApi.updateDelegatedControlChargingStationPreferences
      ).toHaveBeenCalledWith(PPID, {
        maxPrice: 10,
      });
    });

    it('re-throws any errors', async () => {
      delegatedControlChargingStationsApi.updateDelegatedControlChargingStationPreferences.mockRejectedValue(
        new AxiosError()
      );

      await expect(() =>
        service.updatePreferences(PPID, {
          maxPrice: 10,
        })
      ).rejects.toThrow(AxiosError);

      expect(
        delegatedControlChargingStationsApi.updateDelegatedControlChargingStationPreferences
      ).toHaveBeenCalledTimes(1);
      expect(
        delegatedControlChargingStationsApi.updateDelegatedControlChargingStationPreferences
      ).toHaveBeenCalledWith(PPID, {
        maxPrice: 10,
      });
    });
  });
});
