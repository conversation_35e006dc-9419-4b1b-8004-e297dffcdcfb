import { AccountClientService } from './account-client.service';
import { Api3Module } from '../api3/api3.module';
import { Api3Service } from '../api3/api3.service';
import {
  AuthApi,
  CreateUserDto,
  SendPasswordResetRequest,
  SendVerifyAndChangeEmailRequest,
  UsersApi,
} from '@experience/driver-account-api/api-client';
import { AuthenticationModule } from '../authentication/authentication.module';
import { ConfigService } from '@nestjs/config';
import { ITokenAuthUser } from '@experience/mobile/nest/authorisation';
import { Test } from '@nestjs/testing';
import { createMock } from '@golevelup/ts-jest';
import { jest } from '@jest/globals';
import axios, { AxiosResponse } from 'axios';

jest.mock('../api3/api3.service');

describe('AccountClientService', () => {
  let accountClientService: AccountClientService;
  let usersApi: UsersApi;
  let authApi: AuthApi;

  beforeAll(async () => {
    const module = await Test.createTestingModule({
      imports: [AuthenticationModule, Api3Module],
      providers: [
        AccountClientService,
        Api3Service,
        {
          provide: UsersApi,
          useValue: new UsersApi(),
        },
        {
          provide: AuthApi,
          useValue: new AuthApi(),
        },
        {
          provide: ConfigService,
          useValue: createMock<ConfigService>({
            getOrThrow: (key: string) =>
              key === 'POD_APP_MAGIC_LINK_URL'
                ? 'https://identity-stage.podenergy.com/pod/email-login'
                : undefined,
            get: (key: string) =>
              key === 'DRIVER_ACCOUNT_API_BASE_URL'
                ? 'https://a-test-url-for-driver-account-api.com'
                : undefined,
          }),
        },
      ],
    }).compile();
    accountClientService = module.get(AccountClientService);
    authApi = module.get(AuthApi);
    usersApi = module.get(UsersApi);
  });

  describe('AccountClientService', () => {
    it('should call driver account api to create new user', async () => {
      jest
        .spyOn(axios, 'post')
        .mockResolvedValueOnce({ data: authUserCreated });
      await accountClientService.createNewUser(mockNewUser, 'es');
      expect(axios.post).toHaveBeenCalledWith(
        'https://a-test-url-for-driver-account-api.com/v1/users',
        mockNewUser,
        {
          headers: {
            'Accept-Language': 'es',
          },
        }
      );
    });

    it('should call driver account send email verification request', async () => {
      jest
        .spyOn(authApi, 'emailVerificationControllerSendEmailVerification')
        .mockResolvedValueOnce({ data: {}, status: 200 } as AxiosResponse);
      const request = {
        name: 'name',
        uuid: 'uuid',
        email: 'email',
      };
      await accountClientService.sendEmailVerificationRequest(request);
      expect(
        authApi.emailVerificationControllerSendEmailVerification
      ).toHaveBeenCalledWith(
        {
          email: request.email,
        },
        undefined,
        { headers: { 'Accept-Language': 'en' } }
      );
    });

    it('should call driver account api to send password reset email', async () => {
      jest
        .spyOn(authApi, 'passwordResetControllerSendPasswordReset')
        .mockResolvedValue({ data: {}, status: 200 } as AxiosResponse);
      await accountClientService.sendPasswordResetRequest(
        sendPasswordResetRequest
      );
      expect(
        authApi.passwordResetControllerSendPasswordReset
      ).toHaveBeenCalledWith(sendPasswordResetRequest, undefined, {
        headers: { 'Accept-Language': 'en' },
      });
    });

    it('should call driver account api change and verify email', async () => {
      jest
        .spyOn(authApi, 'verifyAndChangeEmailControllerUpdateEmail')
        .mockResolvedValue({ data: {}, status: 200 } as AxiosResponse);
      await accountClientService.sendVerifyAndChangeEmailRequest(
        sendVerifyAndChangeEmailRequest,
        { name: 'name', uuid: 'uuid', email: '<EMAIL>' },
        'es'
      );
      expect(
        authApi.verifyAndChangeEmailControllerUpdateEmail
      ).toHaveBeenCalledWith(
        {
          email: '<EMAIL>',
          newEmail: '<EMAIL>',
        },
        undefined,
        { headers: { 'Accept-Language': 'es' } }
      );
    });

    it('should call driver account api to recover factor', async () => {
      jest
        .spyOn(authApi, 'recoverFactorControllerSendRecoverFactor')
        .mockResolvedValue({ data: {}, status: 200 } as AxiosResponse);

      await accountClientService.sendRecoverFactorEmailRequest({
        email: '<EMAIL>',
      });

      expect(
        authApi.recoverFactorControllerSendRecoverFactor
      ).toHaveBeenCalledWith(
        { email: '<EMAIL>' },
        undefined,
        { headers: { 'Accept-Language': 'en' } }
      );
    });

    it('should call driver account api to update an existing user', async () => {
      const uuid = 'uuid';
      jest.spyOn(usersApi, 'userControllerGetUser').mockResolvedValueOnce({
        data: authUserCreated,
        status: 200,
      } as AxiosResponse);
      jest.spyOn(usersApi, 'userControllerUpdateUser').mockResolvedValueOnce({
        data: authUserCreated,
        status: 200,
      } as AxiosResponse);
      await accountClientService.updateUser(uuid, {
        first_name: 'New',
        last_name: 'Name',
      });
      expect(usersApi.userControllerUpdateUser).toHaveBeenCalledWith(uuid, {
        first_name: 'New',
        last_name: 'Name',
        locale: authUserCreated.locale,
      });
    });

    it('should call API3 to report a user as to be deleted', async () => {
      jest
        .spyOn(Api3Service.prototype, 'reportUserToBeDeleted')
        .mockResolvedValue([]);
      jest
        .spyOn(usersApi, 'userControllerSoftDelete')
        .mockRejectedValueOnce({ error: 'boo', status: 400 });
      const user: ITokenAuthUser = {
        name: 'name',
        uuid: 'uuid',
        email: '<EMAIL>',
      };
      await accountClientService.deleteUser(user);
      expect(usersApi.userControllerSoftDelete).toHaveBeenCalledWith(user.uuid);
      expect(Api3Service.prototype.reportUserToBeDeleted).toHaveBeenCalledWith(
        user
      );
    });

    it('should call usersApi to delete a user and not the API3 one', async () => {
      jest
        .spyOn(Api3Service.prototype, 'reportUserToBeDeleted')
        .mockResolvedValue([]);
      jest
        .spyOn(usersApi, 'userControllerSoftDelete')
        .mockResolvedValueOnce({ status: 200 } as AxiosResponse);
      const user: ITokenAuthUser = {
        name: 'name',
        uuid: 'uuid',
        email: '<EMAIL>',
      };
      await accountClientService.deleteUser(user);
      expect(usersApi.userControllerSoftDelete).toHaveBeenCalledWith(user.uuid);
      expect(
        Api3Service.prototype.reportUserToBeDeleted
      ).not.toHaveBeenCalled();
    });
  });

  describe('signInWithMagicLink()', () => {
    it('proxies the request to the driver account api with the correct urls', async () => {
      const sendMagicLink = jest
        .spyOn(authApi, 'signInWithEmailControllerSendMagicLoginLink')
        .mockResolvedValue({
          status: 200,
        } as AxiosResponse);

      await accountClientService.signInWithMagicLink(
        '<EMAIL>',
        'pod'
      );

      expect(sendMagicLink).toHaveBeenCalledTimes(1);
      expect(sendMagicLink).toHaveBeenCalledWith(
        {
          email: '<EMAIL>',
          continue_url: 'https://identity-stage.podenergy.com/pod/email-login',
          origin_service_url:
            'https://identity-stage.podenergy.com/pod/email-login',
        },
        'pod'
      );
    });
  });
});

const authUserCreated = {
  first_name: 'testfirst1',
  last_name: 'testlast2',
  email: '<EMAIL>',
  locale: 'en',
  auth_id: 'aUuid-XXXX',
  created_at: '2023-09-26 07:08:51',
  id: '*********',
};

const mockNewUser: CreateUserDto = {
  email: '<EMAIL>',
  first_name: 'murray',
  last_name: 'lastName',
  locale: 'en',
  password: 'password1234',
  consent: {
    marketing: {
      isConsentGiven: 0,
      type: 'express',
      copy: 'I would like to receive updates about Pod Point products and services by email (and know that I can update my preferences from within any of the emails if I change my mind)',
      origin: 'opencharge-mobile-app',
    },
  },
  preferences: {
    unitOfDistance: 'mi',
  },
};

const sendPasswordResetRequest: SendPasswordResetRequest = {
  email: '<EMAIL>',
  reset_password_continue_url: 'https://aResetPasswordContinueUrl',
};

const sendVerifyAndChangeEmailRequest: SendVerifyAndChangeEmailRequest = {
  email: '<EMAIL>',
  newEmail: '<EMAIL>',
};
