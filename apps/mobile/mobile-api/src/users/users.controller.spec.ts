import { AuthenticationModule } from '../authentication/authentication.module';
import { ConfigModule } from '@nestjs/config';
import { DeepMocked, createMock } from '@golevelup/ts-jest';
import { EnodeService } from '../enode/enode.service';
import { NestApplication } from '@nestjs/core';
import {
  TEST_USER_INFO_RESPONSE,
  TrackLoginRequest,
} from '@experience/mobile/driver-account/domain/user';
import { Test } from '@nestjs/testing';
import { TokenValidator } from '@experience/mobile/nest/authorisation';
import { UsersController } from './users.controller';
import { UsersService } from './users.service';
import {
  defaultMockAuthenticationOptions,
  mockAuthenticatedUser,
} from '../test.utils';
import request from 'supertest';

describe('UsersController', () => {
  let app: NestApplication;
  let enodeService: DeepMocked<EnodeService>;
  let usersService: DeepMocked<UsersService>;

  beforeEach(async () => {
    const module = await Test.createTestingModule({
      imports: [ConfigModule, AuthenticationModule],
      controllers: [UsersController],
      providers: [
        {
          provide: EnodeService,
          useValue: createMock<EnodeService>(),
        },
        {
          provide: UsersService,
          useValue: createMock<UsersService>(),
        },
        TokenValidator,
      ],
    }).compile();

    app = module.createNestApplication();

    enodeService = module.get(EnodeService);
    usersService = module.get(UsersService);

    await app.init();
  });

  afterEach(async () => {
    jest.resetAllMocks();

    await app.close();
  });

  describe('GET /', () => {
    it('should allow authenticated users to get their info', async () => {
      const token = mockAuthenticatedUser();

      usersService.getDriverUser.mockResolvedValue({
        ...TEST_USER_INFO_RESPONSE,
        deletedAtTimestamp: null,
        accountCreationTimestamp: new Date(
          TEST_USER_INFO_RESPONSE.accountCreationTimestamp
        ),
        lastSignInTimestamp: new Date(
          TEST_USER_INFO_RESPONSE.lastSignInTimestamp
        ),
      });

      const response = await request(app.getHttpServer())
        .get('/users')
        .set('Authorization', `Bearer ${token}`);

      expect(usersService.getDriverUser).toHaveBeenCalledTimes(1);
      expect(usersService.getDriverUser).toHaveBeenCalledWith(
        defaultMockAuthenticationOptions.authId
      );

      expect(response.status).toEqual(200);
      expect(response.body).toEqual(TEST_USER_INFO_RESPONSE);
    });
  });

  describe('POST /enode/link', () => {
    it('should allow authenticated users to link to enode', async () => {
      const token = mockAuthenticatedUser();

      enodeService.getLinkSession.mockResolvedValue({
        linkToken: 'WFiMjNjZDRANThlZmIyMzMtYTNjNS00Njk...',
        linkUrl: 'https://example.com',
      });

      const response = await request(app.getHttpServer())
        .post('/users/enode/link')
        .set('Authorization', 'Bearer ' + token);

      expect(enodeService.getLinkSession).toHaveBeenCalledWith(
        defaultMockAuthenticationOptions.authId,
        {}
      );

      expect(response.status).toEqual(200);
      expect(response.body).toEqual(
        await enodeService.getLinkSession.mock.results[0].value
      );
    });

    it('should allow authenticated users to link to enode for a specified vendor', async () => {
      const token = mockAuthenticatedUser();

      enodeService.getLinkSession.mockResolvedValue({
        linkToken: 'WFiMjNjZDRANThlZmIyMzMtYTNjNS00Njk...',
        linkUrl: 'https://example.com',
      });

      const response = await request(app.getHttpServer())
        .post('/users/enode/link')
        .set('Authorization', 'Bearer ' + token)
        .send({
          vendor: 'VOLVO',
        });

      expect(enodeService.getLinkSession).toHaveBeenCalledWith(
        defaultMockAuthenticationOptions.authId,
        { vendor: 'VOLVO' }
      );

      expect(response.status).toEqual(200);
      expect(response.body).toEqual(
        await enodeService.getLinkSession.mock.results[0].value
      );
    });

    it('should not allow unauthenticated users to link to enode', async () => {
      await request(app.getHttpServer()).post('/users/enode/link').expect(401);

      expect(enodeService.getLinkSession).not.toHaveBeenCalled();
    });
  });

  describe('POST /login/alert', () => {
    it('should allow authenticated users to track login requests', async () => {
      const { authId } = defaultMockAuthenticationOptions;
      const trackLoginRequest: TrackLoginRequest = {
        ipAddress: '123424234',
        userAgent: 'Android',
        timestamp: '2020-03-12 00:00:00',
        authId: authId,
      };

      usersService.sendTrackLoginEmail.mockResolvedValue();

      const response = await request(app.getHttpServer())
        .post('/users/login/alert')
        .set('Authorization', 'Bearer ' + process.env.CUSTOM_API_TOKEN)
        .send(trackLoginRequest);

      expect(usersService.sendTrackLoginEmail).toHaveBeenCalledWith({
        ...trackLoginRequest,
        authId,
      });

      expect(response.status).toEqual(200);
    });

    it('should not allow unauthenticated users to login alert', async () => {
      await request(app.getHttpServer()).post('/users/login/alert').expect(401);

      expect(usersService.sendTrackLoginEmail).not.toHaveBeenCalled();
    });
  });
});
