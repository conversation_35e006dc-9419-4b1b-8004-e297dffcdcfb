import {
  BadRequestException,
  INestApplication,
  UnauthorizedException,
} from '@nestjs/common';
import { ConfigModule } from '@nestjs/config';
import { Test, TestingModule } from '@nestjs/testing';
import request from 'supertest';

import {
  AssetConfigurationNotFound,
  CannotUpdateAssetConfiguration,
  ChargerSensorNotInstalled,
  ConnectivityStatusNotFoundError,
  ConnectivityStatusUnknownError,
  RestrictionsBadRequestError,
  RestrictionsNotFoundError,
} from './chargers.exception';
import { AuthenticationModule } from '../authentication/authentication.module';
import { CacheModule } from '@nestjs/cache-manager';
import {
  ChargerNotFoundError,
  ChargerOfflineError,
} from '@experience/mobile/nest/exception';
import { ChargersController } from './chargers.controller';
import { ChargersModelService } from './chargers-model.service';
import { ChargersOverrideService } from './chargers-override.service';
import { ChargersService } from './chargers.service';
import { ChargersTariffsService } from './chargers-tariffs.service';
import {
  ChargingStationTariffSearchDto,
  SetChargerTariffDto,
} from './chargers.types';
import { DelegatedControlChargingStationResponseDtoStatusEnum } from '@experience/shared/axios/smart-charging-service-client';
import { SmartChargingService } from '../smart-charging/smart-charging.service';
import { SolarPreferencesService } from './solar-preferences.service';
import {
  TEST_CHARGER_CHARGE_OVERRIDE_RESPONSE,
  TEST_CHARGER_DNO_REGION_RESPONSE,
  TEST_CHARGER_FLEX_ENROLMENT,
  TEST_CHARGER_GET_CONNECTIVITY_STATUS_RESPONSE_ARCH3,
  TEST_CHARGER_OVERRIDE_REQUEST,
  TEST_CHARGER_PPID,
  TEST_CHARGER_RESTRICTIONS_ALLOWED_RESPONSE,
  TEST_FIRMWARE_STATUS_RESPONSE,
  TEST_FLEX_REQUEST_INCREASE,
  TEST_FLEX_REQUEST_REDUCE,
} from './__fixtures__/';
import { UsersModule } from '../users/users.module';
import { UsersService } from '../users/users.service';
import { createMock } from '@golevelup/ts-jest';
import {
  defaultMockAuthenticationOptions,
  mockAuthenticatedUser,
  mockUsersService,
} from '../test.utils';

describe('ChargersController', () => {
  let app: INestApplication;
  let module: TestingModule;
  let chargersService: ChargersService;
  let chargersModelService: ChargersModelService;
  let chargersOverrideService: ChargersOverrideService;
  let solarPreferencesService: SolarPreferencesService;
  let usersService: UsersService;
  let smartChargingService: SmartChargingService;
  let chargersTariffsService: ChargersTariffsService;
  let token: string;

  const mockAuthentication = (ppid?: string, usersService?: UsersService) => {
    mockAuthenticatedUser();
    usersService && mockUsersService(usersService, ppid);
  };

  beforeAll(async () => {
    module = await Test.createTestingModule({
      imports: [
        ConfigModule,
        AuthenticationModule,
        CacheModule.register(),
        UsersModule,
      ],
      controllers: [ChargersController],
      providers: [
        {
          provide: ChargersService,
          useValue: createMock<ChargersService>(),
        },
        {
          provide: ChargersModelService,
          useValue: createMock<ChargersModelService>(),
        },
        {
          provide: ChargersOverrideService,
          useValue: createMock<ChargersOverrideService>(),
        },
        {
          provide: SolarPreferencesService,
          useValue: createMock<SolarPreferencesService>(),
        },
        {
          provide: SmartChargingService,
          useValue: createMock<ChargersModelService>(),
        },
        {
          provide: ChargersTariffsService,
          useValue: createMock<ChargersTariffsService>(),
        },
      ],
    }).compile();

    chargersService = module.get<ChargersService>(ChargersService);
    chargersModelService =
      module.get<ChargersModelService>(ChargersModelService);
    chargersOverrideService = module.get<ChargersOverrideService>(
      ChargersOverrideService
    );
    usersService = module.get<UsersService>(UsersService);
    solarPreferencesService = module.get<SolarPreferencesService>(
      SolarPreferencesService
    );
    smartChargingService =
      module.get<SmartChargingService>(SmartChargingService);
    chargersTariffsService = module.get<ChargersTariffsService>(
      ChargersTariffsService
    );

    app = module.createNestApplication();
    await app.init();
  });

  beforeEach(() => {
    jest.clearAllMocks();
    token = mockAuthenticatedUser();
  });

  afterAll(async () => {
    await app.close();
  });

  describe('getChargers', () => {
    it('should return an unauthorised error', async () => {
      const res = await request(app.getHttpServer()).get('/chargers');

      expect(res.status).toEqual(401);
    });

    it("returns a user's chargers", async () => {
      const CHARGERS = [
        {
          ppid: 'PSL-1234567',
          unitId: 1,
          timezone: 'Etc/UTC',
          linkedAt: '2024-01-01T00:00:00.000Z',
        },
        {
          ppid: 'PSL-7654321',
          unitId: 1,
          timezone: 'Etc/UTC',
          linkedAt: '2024-01-01T01:00:00.000Z',
        },
      ];

      const getUserChargersMock = jest
        .spyOn(usersService, 'getUserChargers')
        .mockResolvedValue(CHARGERS);

      const getModelInfoMock = jest
        .spyOn(chargersModelService, 'getChargerModelInfo')
        .mockResolvedValue({
          style: 'solo3',
          architecture: '5.0',
          ledColourSet: 'uk',
          colour: 'black',
        });

      const getDelegatedControlStatusMock = jest
        .spyOn(smartChargingService, 'getDelegatedControlChargerStatus')
        .mockResolvedValue(
          DelegatedControlChargingStationResponseDtoStatusEnum.Active
        );

      const getChargerSubscriptionMock = jest
        .spyOn(chargersService, 'getChargerSubscriptionInfo')
        .mockResolvedValue({
          status: 'AVAILABLE',
        });

      const res = await request(app.getHttpServer())
        .get('/chargers')
        .set('Authorization', 'Bearer ' + token);

      expect(res.status).toEqual(200);
      expect(res.body).toMatchSnapshot();

      expect(getUserChargersMock).toHaveBeenCalledTimes(1);
      expect(getUserChargersMock).toHaveBeenCalledWith(
        defaultMockAuthenticationOptions.authId
      );

      expect(getModelInfoMock).toHaveBeenCalledTimes(2);
      expect(getDelegatedControlStatusMock).toHaveBeenCalledTimes(2);
      expect(getChargerSubscriptionMock).toHaveBeenCalledTimes(2);

      CHARGERS.forEach((charger) => {
        expect(getDelegatedControlStatusMock).toHaveBeenCalledWith(
          charger.ppid
        );
        expect(getModelInfoMock).toHaveBeenCalledWith(charger.ppid);
        expect(getChargerSubscriptionMock).toHaveBeenCalledWith(
          charger.ppid,
          defaultMockAuthenticationOptions.authId
        );
      });
    });

    it('returns an empty array if a user has no chargers', async () => {
      const getUsersChargersMock = jest
        .spyOn(usersService, 'getUserChargers')
        .mockResolvedValue([]);

      const res = await request(app.getHttpServer())
        .get('/chargers')
        .set('Authorization', 'Bearer ' + token);

      expect(res.status).toEqual(200);
      expect(res.body).toStrictEqual([]);

      expect(getUsersChargersMock).toHaveBeenCalledTimes(1);
      expect(getUsersChargersMock).toHaveBeenCalledWith(
        defaultMockAuthenticationOptions.authId
      );
    });
  });

  describe('getFirmwareStatus', () => {
    it('should return an unauthorised error', async () => {
      const mockGetFirmwareStatus = jest.spyOn(
        chargersService,
        'getFirmwareStatus'
      );

      const response = await request(app.getHttpServer()).get(
        `/chargers/${TEST_CHARGER_PPID}/firmware`
      );

      expect(mockGetFirmwareStatus).toHaveBeenCalledTimes(0);
      expect(response.status).toEqual(401);
    });

    it('should return a forbidden error', async () => {
      const mockGetFirmwareStatus = jest.spyOn(
        chargersService,
        'getFirmwareStatus'
      );

      const response = await request(app.getHttpServer())
        .get(`/chargers/${TEST_CHARGER_PPID}/firmware`)
        .set('Authorization', 'Bearer ' + token);

      expect(mockGetFirmwareStatus).toHaveBeenCalledTimes(0);
      expect(response.status).toEqual(403);
    });

    it('should return 200 with firmware status', async () => {
      mockAuthentication(TEST_CHARGER_PPID, usersService);

      const mockGetFirmwareStatus = jest
        .spyOn(chargersService, 'getFirmwareStatus')
        .mockResolvedValueOnce(TEST_FIRMWARE_STATUS_RESPONSE);

      const response = await request(app.getHttpServer())
        .get(`/chargers/${TEST_CHARGER_PPID}/firmware`)
        .set('Authorization', 'Bearer ' + token);

      expect(response.status).toEqual(200);
      expect(response.body).toEqual(TEST_FIRMWARE_STATUS_RESPONSE);

      expect(mockGetFirmwareStatus).toHaveBeenCalledTimes(1);
    });
  });

  describe('getDnoRegion', () => {
    it('should return an unauthorised error', async () => {
      const mockDnoRegion = jest.spyOn(chargersService, 'getDnoRegion');

      const response = await request(app.getHttpServer()).get(
        `/chargers/${TEST_CHARGER_PPID}/dnoregion`
      );

      expect(mockDnoRegion).toHaveBeenCalledTimes(0);
      expect(response.status).toEqual(401);
    });

    it('should return a result', async () => {
      const mockDnoRegion = jest
        .spyOn(chargersService, 'getDnoRegion')
        .mockResolvedValueOnce(TEST_CHARGER_DNO_REGION_RESPONSE);

      const response = await request(app.getHttpServer())
        .get(`/chargers/${TEST_CHARGER_PPID}/dnoregion`)
        .set('Authorization', 'Bearer ' + token);

      expect(mockDnoRegion).toHaveBeenCalledTimes(1);
      expect(response.body).toEqual(TEST_CHARGER_DNO_REGION_RESPONSE);
      expect(response.status).toEqual(200);
    });
  });

  describe('getRestrictions', () => {
    it('should return an allowed restriction with valid ppid and user', async () => {
      mockAuthentication();
      const mockRestrictions = jest
        .spyOn(chargersService, 'getRestrictions')
        .mockResolvedValueOnce(TEST_CHARGER_RESTRICTIONS_ALLOWED_RESPONSE);

      const response = await request(app.getHttpServer())
        .get(`/chargers/${TEST_CHARGER_PPID}/restrictions`)
        .set('Authorization', 'Bearer ' + token);

      expect(mockRestrictions).toHaveBeenCalledTimes(1);
      expect(response.status).toEqual(200);
      expect(response.body).toEqual(TEST_CHARGER_RESTRICTIONS_ALLOWED_RESPONSE);
    });

    it('should return an unauthorised error', async () => {
      const response = await request(app.getHttpServer()).get(
        `/chargers/${TEST_CHARGER_PPID}/restrictions`
      );

      expect(response.status).toEqual(401);
    });

    it('should return a bad request error if received via the service', async () => {
      mockAuthentication();
      jest
        .spyOn(chargersService, 'getRestrictions')
        .mockRejectedValue(new RestrictionsBadRequestError());

      const response = await request(app.getHttpServer())
        .get(`/chargers/${TEST_CHARGER_PPID}/restrictions`)
        .set('Authorization', 'Bearer ' + token);

      expect(response.status).toEqual(400);
    });

    it('should return a not found error if received via the service', async () => {
      mockAuthentication();
      jest
        .spyOn(chargersService, 'getRestrictions')
        .mockRejectedValue(new RestrictionsNotFoundError());

      const response = await request(app.getHttpServer())
        .get(`/chargers/${TEST_CHARGER_PPID}/restrictions`)
        .set('Authorization', 'Bearer ' + token);

      expect(response.status).toEqual(404);
    });
  });

  describe('getStatus', () => {
    it('should return a connectivity status for a valid ppid', async () => {
      mockAuthentication(TEST_CHARGER_PPID, usersService);

      const mockChargerConnectivityStatus = jest
        .spyOn(chargersService, 'getConnectivityStatus')
        .mockResolvedValueOnce(
          TEST_CHARGER_GET_CONNECTIVITY_STATUS_RESPONSE_ARCH3
        );

      const response = await request(app.getHttpServer())
        .get(`/chargers/${TEST_CHARGER_PPID}/connectivity-status`)
        .set('Authorization', 'Bearer ' + token);

      expect(mockChargerConnectivityStatus).toHaveBeenCalledTimes(1);
      expect(response.body).toEqual(
        TEST_CHARGER_GET_CONNECTIVITY_STATUS_RESPONSE_ARCH3
      );
      expect(response.status).toEqual(200);
      expect(
        response.body.evses[0].connectivityState.connectivityStatus
      ).toEqual('ONLINE');
    });

    it.each([
      ['POD Point Native Mobile App/3.27.4 (iOS/17.6.1)', true],
      ['asdfghjkl;', false],
      ['Pod Point Home Mobile App/1.0.0 (iOS/17.1)', false],
      [undefined, false],
    ])(
      'should treat the user agent of %s as isLegacyDriverApp=%s',
      async (userAgent: string | undefined, isLegacyDriverApp: boolean) => {
        mockAuthentication(TEST_CHARGER_PPID, usersService);

        const mockChargerConnectivityStatus = jest.spyOn(
          chargersService,
          'getConnectivityStatus'
        );

        const getReq = request(app.getHttpServer())
          .get(`/chargers/${TEST_CHARGER_PPID}/connectivity-status`)
          .set('Authorization', `Bearer ${token}`);

        if (userAgent) getReq.set('User-Agent', userAgent);

        await getReq;

        expect(mockChargerConnectivityStatus).toHaveBeenCalledTimes(1);
        expect(mockChargerConnectivityStatus).toHaveBeenCalledWith(
          TEST_CHARGER_PPID,
          isLegacyDriverApp
        );
      }
    );

    it('should return a 404 if service throws ConnectivityStatusNotFoundError', async () => {
      mockAuthentication(TEST_CHARGER_PPID, usersService);

      const mockChargerConnectivityStatus = jest
        .spyOn(chargersService, 'getConnectivityStatus')
        .mockRejectedValue(new ConnectivityStatusNotFoundError());

      const response = await request(app.getHttpServer())
        .get(`/chargers/${TEST_CHARGER_PPID}/connectivity-status`)
        .set('Authorization', 'Bearer ' + token);

      expect(mockChargerConnectivityStatus).toHaveBeenCalledTimes(1);
      expect(response.status).toEqual(404);
    });

    it('should return a 404 if service throws ConnectivityStatusUnknownError', async () => {
      mockAuthentication(TEST_CHARGER_PPID, usersService);

      const mockChargerConnectivityStatus = jest
        .spyOn(chargersService, 'getConnectivityStatus')
        .mockRejectedValue(new ConnectivityStatusUnknownError());

      const response = await request(app.getHttpServer())
        .get(`/chargers/${TEST_CHARGER_PPID}/connectivity-status`)
        .set('Authorization', 'Bearer ' + token);

      expect(mockChargerConnectivityStatus).toHaveBeenCalledTimes(1);
      expect(response.status).toEqual(404);
    });

    it('should return a 403 if requesting connectivity status that is not theirs', async () => {
      mockAuthentication('not_valid_ppid', usersService);

      const mockChargerConnectivityStatus = jest
        .spyOn(chargersService, 'getConnectivityStatus')
        .mockResolvedValueOnce(
          TEST_CHARGER_GET_CONNECTIVITY_STATUS_RESPONSE_ARCH3
        );

      const response = await request(app.getHttpServer())
        .get(`/chargers/${TEST_CHARGER_PPID}/connectivity-status`)
        .set('Authorization', 'Bearer ' + token);

      expect(mockChargerConnectivityStatus).toHaveBeenCalledTimes(0);
      expect(response.status).toEqual(403);
    });
  });

  describe('getFlexEnrolment', () => {
    it('should return 200 with flex enrolment for a valid ppid', async () => {
      mockAuthentication(TEST_CHARGER_PPID, usersService);

      const mockChargerFlexEnrolment = jest
        .spyOn(chargersService, 'getFlexEnrolment')
        .mockResolvedValueOnce(TEST_CHARGER_FLEX_ENROLMENT);

      const response = await request(app.getHttpServer())
        .get(`/chargers/${TEST_CHARGER_PPID}/flex-enrolment`)
        .set('Authorization', 'Bearer ' + token);

      expect(mockChargerFlexEnrolment).toHaveBeenCalledTimes(1);
      expect(response.body).toEqual(TEST_CHARGER_FLEX_ENROLMENT);
      expect(response.status).toEqual(200);
    });

    it('should return 401 if user is unauthenticated', async () => {
      const response = await request(app.getHttpServer()).get(
        `/chargers/${TEST_CHARGER_PPID}/flex-enrolment`
      );

      expect(response.status).toEqual(401);
    });

    it('should return 403 if user is not linked to requested charger', async () => {
      mockAuthentication('not_valid_ppid', usersService);

      const response = await request(app.getHttpServer())
        .get(`/chargers/${TEST_CHARGER_PPID}/flex-enrolment`)
        .set('Authorization', 'Bearer ' + token);

      expect(response.status).toEqual(403);
    });

    it.each([
      { serviceError: new UnauthorizedException(), expectedStatus: 401 },
      { serviceError: new ChargerNotFoundError(), expectedStatus: 404 },
    ])(
      'should return $expectedStatus on receiving $serviceError.message error from service',
      async ({ serviceError, expectedStatus }) => {
        mockAuthentication(TEST_CHARGER_PPID, usersService);

        jest
          .spyOn(chargersService, 'getFlexEnrolment')
          .mockRejectedValue(serviceError);

        const response = await request(app.getHttpServer())
          .get(`/chargers/${TEST_CHARGER_PPID}/flex-enrolment`)
          .set('Authorization', 'Bearer ' + token);

        expect(response.status).toEqual(expectedStatus);
      }
    );
  });

  describe('deleteFlexEnrolment', () => {
    it('should return 204 on successful deletion', async () => {
      mockAuthentication(TEST_CHARGER_PPID, usersService);

      const mockDeleteFlexEnrolment = jest
        .spyOn(chargersService, 'deleteFlexEnrolment')
        .mockResolvedValueOnce(null);

      const response = await request(app.getHttpServer())
        .delete(`/chargers/${TEST_CHARGER_PPID}/flex-enrolment`)
        .set('Authorization', 'Bearer ' + token);

      expect(mockDeleteFlexEnrolment).toHaveBeenCalledTimes(1);
      expect(response.status).toEqual(204);
    });

    it('should return 401 if user is unauthenticated', async () => {
      const response = await request(app.getHttpServer()).delete(
        `/chargers/${TEST_CHARGER_PPID}/flex-enrolment`
      );

      expect(response.status).toEqual(401);
    });

    it('should return 403 if user is not linked to requested charger', async () => {
      mockAuthentication('not_valid_ppid', usersService);

      const response = await request(app.getHttpServer())
        .delete(`/chargers/${TEST_CHARGER_PPID}/flex-enrolment`)
        .set('Authorization', 'Bearer ' + token);

      expect(response.status).toEqual(403);
    });

    it.each([
      { serviceError: new UnauthorizedException(), expectedStatus: 401 },
      { serviceError: new ChargerNotFoundError(), expectedStatus: 404 },
    ])(
      'should return $expectedStatus on receiving $serviceError.message error from service',
      async ({ serviceError, expectedStatus }) => {
        mockAuthentication(TEST_CHARGER_PPID, usersService);

        jest
          .spyOn(chargersService, 'deleteFlexEnrolment')
          .mockRejectedValue(serviceError);

        const response = await request(app.getHttpServer())
          .delete(`/chargers/${TEST_CHARGER_PPID}/flex-enrolment`)
          .set('Authorization', 'Bearer ' + token);

        expect(response.status).toEqual(expectedStatus);
      }
    );
  });

  describe('getFlexRequests', () => {
    it('should return 200 with list of FlexRequests', async () => {
      mockAuthentication(TEST_CHARGER_PPID, usersService);

      const mockChargerGetFlexRequest = jest
        .spyOn(chargersService, 'getFlexRequests')
        .mockResolvedValueOnce([
          TEST_FLEX_REQUEST_INCREASE,
          TEST_FLEX_REQUEST_REDUCE,
        ]);

      const response = await request(app.getHttpServer())
        .get(`/chargers/${TEST_CHARGER_PPID}/flex-requests`)
        .set('Authorization', 'Bearer ' + token);

      expect(mockChargerGetFlexRequest).toHaveBeenCalledTimes(1);
      expect(response.body).toEqual([
        TEST_FLEX_REQUEST_INCREASE,
        TEST_FLEX_REQUEST_REDUCE,
      ]);
      expect(response.status).toEqual(200);
    });

    it('should return 401 unauthorised error', async () => {
      const response = await request(app.getHttpServer()).get(
        `/chargers/${TEST_CHARGER_PPID}/flex-requests`
      );

      expect(response.status).toEqual(401);
    });

    it('should return 403 if user is not linked to requested charger', async () => {
      mockAuthentication('not_valid_ppid', usersService);

      const response = await request(app.getHttpServer())
        .get(`/chargers/${TEST_CHARGER_PPID}/flex-requests`)
        .set('Authorization', 'Bearer ' + token);

      expect(response.status).toEqual(403);
    });

    it.each([
      { serviceError: new UnauthorizedException(), expectedStatus: 401 },
      { serviceError: new ChargerNotFoundError(), expectedStatus: 404 },
    ])(
      'should return $expectedStatus on receiving $serviceError.message error from service',
      async ({ serviceError, expectedStatus }) => {
        mockAuthentication(TEST_CHARGER_PPID, usersService);

        jest
          .spyOn(chargersService, 'getFlexRequests')
          .mockRejectedValue(serviceError);

        const response = await request(app.getHttpServer())
          .get(`/chargers/${TEST_CHARGER_PPID}/flex-requests`)
          .set('Authorization', 'Bearer ' + token);

        expect(response.status).toEqual(expectedStatus);
      }
    );
  });

  describe('deleteFlexRequest', () => {
    const testFlexRequestId = 'd40ff7bc-cd94-4e4f-9d52-34a80e83e64b';
    const testEndpoint = `/chargers/${TEST_CHARGER_PPID}/flex-requests/${testFlexRequestId}`;

    it('should return 204 on successful deletion', async () => {
      mockAuthentication(TEST_CHARGER_PPID, usersService);

      const mockDeleteFlexRequest = jest
        .spyOn(chargersService, 'deleteFlexRequest')
        .mockResolvedValueOnce(null);

      const response = await request(app.getHttpServer())
        .delete(testEndpoint)
        .set('Authorization', 'Bearer ' + token);

      expect(mockDeleteFlexRequest).toHaveBeenCalledTimes(1);
      expect(mockDeleteFlexRequest).toHaveBeenCalledWith(testFlexRequestId);
      expect(response.status).toEqual(204);
    });

    it('should return 401 on user unauthenticated', async () => {
      const response = await request(app.getHttpServer()).delete(testEndpoint);

      expect(response.status).toEqual(401);
    });

    it('should return 403 if user is not linked to requested charger', async () => {
      mockAuthentication('not_valid_ppid', usersService);

      const response = await request(app.getHttpServer())
        .delete(testEndpoint)
        .set('Authorization', 'Bearer ' + token);

      expect(response.status).toEqual(403);
    });

    it.each([
      { serviceError: new BadRequestException(), expectedStatus: 400 },
      { serviceError: new UnauthorizedException(), expectedStatus: 401 },
      { serviceError: new ChargerNotFoundError(), expectedStatus: 404 },
    ])(
      'should return $expectedStatus on receiving $serviceError.message error from service',
      async ({ serviceError, expectedStatus }) => {
        mockAuthentication(TEST_CHARGER_PPID, usersService);

        jest
          .spyOn(chargersService, 'deleteFlexRequest')
          .mockRejectedValue(serviceError);

        const response = await request(app.getHttpServer())
          .delete(testEndpoint)
          .set('Authorization', 'Bearer ' + token);

        expect(response.status).toEqual(expectedStatus);
      }
    );
  });

  describe('GetSolarPreferences', () => {
    const testEndpoint = `/chargers/${TEST_CHARGER_PPID}/solar/preferences`;

    it('should return solar preferences', async () => {
      mockAuthentication(TEST_CHARGER_PPID, usersService);

      const mockGetSolarPreferencesRequest = jest
        .spyOn(solarPreferencesService, 'getSolarPreferences')
        .mockResolvedValueOnce({
          powerGeneration: 'ON',
          solarMatching: 'ON',
          solarThreshold: 1.4,
        });

      const response = await request(app.getHttpServer())
        .get(testEndpoint)
        .set('Authorization', 'Bearer ' + token);

      expect(mockGetSolarPreferencesRequest).toHaveBeenCalledTimes(1);
      expect(mockGetSolarPreferencesRequest).toHaveBeenCalledWith(
        TEST_CHARGER_PPID,
        defaultMockAuthenticationOptions.authId
      );
      expect(response.status).toEqual(200);
    });

    it('should return 401 on user unauthenticated', async () => {
      const response = await request(app.getHttpServer()).get(testEndpoint);

      expect(response.status).toEqual(401);
    });

    it('should return 403 if user is not linked to requested charger', async () => {
      mockAuthentication('not_valid_ppid', usersService);

      const response = await request(app.getHttpServer())
        .get(testEndpoint)
        .set('Authorization', 'Bearer ' + token);

      expect(response.status).toEqual(403);
    });
  });

  describe('SetSolarPreferences', () => {
    const testEndpoint = `/chargers/${TEST_CHARGER_PPID}/solar/preferences`;
    const payload = {
      powerGeneration: 'ON',
      solarMatching: 'ON',
      solarThreshold: 1.4,
    };

    beforeEach(() => {
      mockAuthentication(TEST_CHARGER_PPID, usersService);
    });

    it('should set solar preferences', async () => {
      const mockSetSolarPreferencesRequest = jest
        .spyOn(solarPreferencesService, 'setSolarPreferences')
        .mockResolvedValueOnce({
          powerGeneration: 'ON',
          solarMatching: 'ON',
          solarThreshold: 1.4,
        });

      const response = await request(app.getHttpServer())
        .post(testEndpoint)
        .send(payload)
        .set('Authorization', 'Bearer ' + token);

      expect(mockSetSolarPreferencesRequest).toHaveBeenCalledTimes(1);
      expect(mockSetSolarPreferencesRequest).toHaveBeenCalledWith(
        TEST_CHARGER_PPID,
        payload,
        defaultMockAuthenticationOptions.authId
      );
      expect(response.status).toEqual(201);
    });

    it('should return 422 if sensor is not installed', async () => {
      const mockSetSolarPreferencesRequestFailed = jest
        .spyOn(solarPreferencesService, 'setSolarPreferences')
        .mockRejectedValue(new ChargerSensorNotInstalled());

      const response = await request(app.getHttpServer())
        .post(testEndpoint)
        .send(payload)
        .set('Authorization', 'Bearer ' + token);

      expect(response.status).toEqual(422);
      expect(mockSetSolarPreferencesRequestFailed).toHaveBeenCalledTimes(1);
    });

    it('should return 502 if it fails at updating the asset configuration', async () => {
      const mockSetSolarPreferencesRequestFailed = jest
        .spyOn(solarPreferencesService, 'setSolarPreferences')
        .mockRejectedValue(new CannotUpdateAssetConfiguration());

      const response = await request(app.getHttpServer())
        .post(testEndpoint)
        .send(payload)
        .set('Authorization', 'Bearer ' + token);

      expect(response.status).toEqual(502);
      expect(mockSetSolarPreferencesRequestFailed).toHaveBeenCalledTimes(1);
    });

    it('should return 410 if it fails at updating the asset configuration', async () => {
      const mockSetSolarPreferencesRequestFailed = jest
        .spyOn(solarPreferencesService, 'setSolarPreferences')
        .mockRejectedValue(new ChargerOfflineError());

      const response = await request(app.getHttpServer())
        .post(testEndpoint)
        .send(payload)
        .set('Authorization', 'Bearer ' + token);

      expect(response.status).toEqual(410);
      expect(mockSetSolarPreferencesRequestFailed).toHaveBeenCalledTimes(1);
    });

    it('should return 404 if the asset configuration is not found', async () => {
      const mockSetSolarPreferencesRequestFailed = jest
        .spyOn(solarPreferencesService, 'setSolarPreferences')
        .mockRejectedValue(new AssetConfigurationNotFound());

      const response = await request(app.getHttpServer())
        .post(testEndpoint)
        .send(payload)
        .set('Authorization', 'Bearer ' + token);

      expect(response.status).toEqual(404);
      expect(mockSetSolarPreferencesRequestFailed).toHaveBeenCalledTimes(1);
    });

    it('should return 404 if asset configuration is not found', async () => {
      const mockSetSolarPreferencesRequestFailed = jest
        .spyOn(solarPreferencesService, 'setSolarPreferences')
        .mockRejectedValue(new AssetConfigurationNotFound());

      const response = await request(app.getHttpServer())
        .post(testEndpoint)
        .send(payload)
        .set('Authorization', 'Bearer ' + token);

      expect(response.status).toEqual(404);
      expect(mockSetSolarPreferencesRequestFailed).toHaveBeenCalledTimes(1);
    });

    it('should return 401 on user unauthenticated', async () => {
      const response = await request(app.getHttpServer())
        .post(testEndpoint)
        .send(payload);

      expect(response.status).toEqual(401);
    });

    it('should return 403 if user is not linked to requested charger', async () => {
      mockAuthentication('not_valid_ppid', usersService);

      const response = await request(app.getHttpServer())
        .post(testEndpoint)
        .send(payload)
        .set('Authorization', 'Bearer ' + token);

      expect(response.status).toEqual(403);
    });
  });

  describe('getChargerModelInfo', () => {
    it('returns a 200 for a successful charger', async () => {
      mockAuthentication(TEST_CHARGER_PPID, usersService);

      jest
        .spyOn(chargersModelService, 'getChargerModelInfo')
        .mockResolvedValue({
          style: 'solo3s',
          colour: 'white',
          ledColourSet: 'uk',
          architecture: '5.1',
        });

      const response = await request(app.getHttpServer())
        .get(`/chargers/${TEST_CHARGER_PPID}/model-info`)
        .set('Authorization', 'Bearer ' + token);

      expect(response.status).toEqual(200);
      expect(response.body).toEqual({
        style: 'solo3s',
        colour: 'white',
        ledColourSet: 'uk',
        architecture: '5.1',
      });
    });

    it('returns a 404 if the charger could not be found', async () => {
      mockAuthentication(TEST_CHARGER_PPID, usersService);

      jest
        .spyOn(chargersModelService, 'getChargerModelInfo')
        .mockImplementation(() => {
          throw new ChargerNotFoundError();
        });

      const response = await request(app.getHttpServer())
        .get(`/chargers/${TEST_CHARGER_PPID}/model-info`)
        .set('Authorization', 'Bearer ' + token);

      expect(response.status).toEqual(404);
    });

    it('returns a 403 if the user does not own the requested charger', async () => {
      mockAuthentication('random-charger', usersService);

      const response = await request(app.getHttpServer())
        .get(`/chargers/${TEST_CHARGER_PPID}/model-info`)
        .set('Authorization', 'Bearer ' + token);

      expect(response.status).toEqual(403);
    });

    it('returns a 401 if there is no authentication provided', async () => {
      const response = await request(app.getHttpServer()).get(
        `/chargers/${TEST_CHARGER_PPID}/model-info`
      );

      expect(response.status).toEqual(401);
    });
  });

  describe('createChargerOverrides', () => {
    it('returns a 200 for a successful charger', async () => {
      mockAuthentication(TEST_CHARGER_PPID, usersService);

      jest
        .spyOn(chargersOverrideService, 'createChargerOverride')
        .mockResolvedValue([TEST_CHARGER_CHARGE_OVERRIDE_RESPONSE]);

      const response = await request(app.getHttpServer())
        .post(`/chargers/${TEST_CHARGER_PPID}/charge-overrides`)
        .set('Authorization', 'Bearer ' + token)
        .send(TEST_CHARGER_OVERRIDE_REQUEST);

      expect(response.status).toEqual(201);
      expect(response.body).toEqual([TEST_CHARGER_CHARGE_OVERRIDE_RESPONSE]);
    });

    it('returns a 404 if the charger could not be found', async () => {
      mockAuthentication(TEST_CHARGER_PPID, usersService);

      jest
        .spyOn(chargersOverrideService, 'createChargerOverride')
        .mockImplementation(() => {
          throw new ChargerNotFoundError();
        });

      const response = await request(app.getHttpServer())
        .post(`/chargers/${TEST_CHARGER_PPID}/charge-overrides`)
        .set('Authorization', 'Bearer ' + token)
        .send(TEST_CHARGER_OVERRIDE_REQUEST);

      expect(response.status).toEqual(404);
    });

    it('returns a 403 if the user does not own the requested charger', async () => {
      mockAuthentication('random-charger', usersService);

      const response = await request(app.getHttpServer())
        .post(`/chargers/${TEST_CHARGER_PPID}/charge-overrides`)
        .set('Authorization', 'Bearer ' + token)
        .send(TEST_CHARGER_OVERRIDE_REQUEST);

      expect(response.status).toEqual(403);
    });

    it('returns a 401 if there is no authentication provided', async () => {
      const response = await request(app.getHttpServer())
        .post(`/chargers/${TEST_CHARGER_PPID}/charge-overrides`)
        .send(TEST_CHARGER_OVERRIDE_REQUEST);

      expect(response.status).toEqual(401);
    });
  });

  describe('getChargerOverrides', () => {
    it('returns a 200 for a successful charger', async () => {
      mockAuthentication(TEST_CHARGER_PPID, usersService);

      jest
        .spyOn(chargersOverrideService, 'getChargeOverride')
        .mockResolvedValue([TEST_CHARGER_CHARGE_OVERRIDE_RESPONSE]);

      const response = await request(app.getHttpServer())
        .get(`/chargers/${TEST_CHARGER_PPID}/charge-overrides`)
        .set('Authorization', 'Bearer ' + token);

      expect(response.status).toEqual(200);
      expect(response.body).toEqual([TEST_CHARGER_CHARGE_OVERRIDE_RESPONSE]);
    });

    it('returns a 404 if the charger could not be found', async () => {
      mockAuthentication(TEST_CHARGER_PPID, usersService);

      jest
        .spyOn(chargersOverrideService, 'getChargeOverride')
        .mockImplementation(() => {
          throw new ChargerNotFoundError();
        });

      const response = await request(app.getHttpServer())
        .get(`/chargers/${TEST_CHARGER_PPID}/charge-overrides`)
        .set('Authorization', 'Bearer ' + token);

      expect(response.status).toEqual(404);
    });

    it('returns a 403 if the user does not own the requested charger', async () => {
      mockAuthentication('random-charger', usersService);

      const response = await request(app.getHttpServer())
        .get(`/chargers/${TEST_CHARGER_PPID}/charge-overrides`)
        .set('Authorization', 'Bearer ' + token);

      expect(response.status).toEqual(403);
    });

    it('returns a 401 if there is no authentication provided', async () => {
      const response = await request(app.getHttpServer()).get(
        `/chargers/${TEST_CHARGER_PPID}/charge-overrides`
      );

      expect(response.status).toEqual(401);
    });
  });

  describe('deleteChargeOverride', () => {
    it('returns a 200 for a successful charger', async () => {
      mockAuthentication(TEST_CHARGER_PPID, usersService);

      jest
        .spyOn(chargersOverrideService, 'deleteChargeOverride')
        .mockResolvedValue(undefined);

      const response = await request(app.getHttpServer())
        .delete(`/chargers/${TEST_CHARGER_PPID}/charge-overrides`)
        .set('Authorization', 'Bearer ' + token);

      expect(response.status).toEqual(200);
    });

    it('returns a 404 if the charger could not be found', async () => {
      mockAuthentication(TEST_CHARGER_PPID, usersService);

      jest
        .spyOn(chargersOverrideService, 'deleteChargeOverride')
        .mockImplementation(() => {
          throw new ChargerNotFoundError();
        });

      const response = await request(app.getHttpServer())
        .delete(`/chargers/${TEST_CHARGER_PPID}/charge-overrides`)
        .set('Authorization', 'Bearer ' + token);

      expect(response.status).toEqual(404);
    });

    it('returns a 403 if the user does not own the requested charger', async () => {
      mockAuthentication('random-charger', usersService);

      const response = await request(app.getHttpServer())
        .delete(`/chargers/${TEST_CHARGER_PPID}/charge-overrides`)
        .set('Authorization', 'Bearer ' + token);

      expect(response.status).toEqual(403);
    });

    it('returns a 401 if there is no authentication provided', async () => {
      const response = await request(app.getHttpServer()).delete(
        `/chargers/${TEST_CHARGER_PPID}/charge-overrides`
      );

      expect(response.status).toEqual(401);
    });
  });

  describe('setChargerTariffs', () => {
    const MOCK_TARIFF: SetChargerTariffDto = {
      supplierId: '84848e36-1f52-43c1-9a6c-7354e43f0250',
      timezone: 'Europe/London',
      effectiveFrom: '2024-11-28',
      tariffInfo: [
        {
          start: '00:00:00',
          end: '01:00:00',
          price: 0.3,
          days: ['SATURDAY', 'SUNDAY'],
        },
      ],
    };

    it('returns a 401 if unauthorised', async () => {
      const setChargerTariffsMock = jest.spyOn(
        chargersTariffsService,
        'setChargerTariffs'
      );

      const res = await request(app.getHttpServer())
        .post(`/chargers/${TEST_CHARGER_PPID}/tariffs`)
        .send(MOCK_TARIFF);

      expect(res.status).toEqual(401);

      expect(setChargerTariffsMock).not.toHaveBeenCalled();
    });

    it('returns a 403 if the user does not own the requested charger', async () => {
      const setChargerTariffsMock = jest.spyOn(
        chargersTariffsService,
        'setChargerTariffs'
      );

      const res = await request(app.getHttpServer())
        .post('/chargers/PSL-000000/tariffs')
        .set('Authorization', `Bearer ${token}`)
        .send(MOCK_TARIFF);

      expect(res.status).toEqual(403);

      expect(setChargerTariffsMock).not.toHaveBeenCalled();
    });

    it('should set the tariff for an authenticated user on a charger they own', async () => {
      mockAuthentication(TEST_CHARGER_PPID, usersService);

      const setChargerTariffsMock = jest
        .spyOn(chargersTariffsService, 'setChargerTariffs')
        .mockResolvedValue({
          ...MOCK_TARIFF,
          id: 'a9698b5c-4c03-4487-9d99-c794be362db9',
          ppid: TEST_CHARGER_PPID,
          cheapestUnitPrice: 0.2,
        });

      const res = await request(app.getHttpServer())
        .post(`/chargers/${TEST_CHARGER_PPID}/tariffs`)
        .set('Authorization', `Bearer ${token}`)
        .send(MOCK_TARIFF);

      expect(res.status).toEqual(200);
      expect(res.body).toMatchSnapshot();

      expect(setChargerTariffsMock).toHaveBeenCalledTimes(1);
      expect(setChargerTariffsMock).toHaveBeenCalledWith(
        TEST_CHARGER_PPID,
        MOCK_TARIFF
      );
    });
  });

  describe('getChargerTariffs', () => {
    const MOCK_TARIFF: ChargingStationTariffSearchDto = {
      data: [
        {
          id: 'aef6f37a-0903-40ba-88d3-619b6fbc062b',
          ppid: TEST_CHARGER_PPID,
          supplierId: '84848e36-1f52-43c1-9a6c-7354e43f0250',
          timezone: 'Europe/London',
          maxChargePrice: 0.2,
          cheapestUnitPrice: 0.2,
          effectiveFrom: '2024-11-28',
          tariffInfo: [
            {
              start: '00:00:00',
              end: '01:00:00',
              price: 0.3,
              days: ['SATURDAY', 'SUNDAY'],
            },
          ],
        },
      ],
      metadata: {
        criteria: {
          ppid: TEST_CHARGER_PPID,
        },
      },
    };

    it('returns a 401 if unauthorised', async () => {
      const getChargerTariffsMock = jest.spyOn(
        chargersTariffsService,
        'getChargerTariffs'
      );

      const res = await request(app.getHttpServer())
        .get(`/chargers/${TEST_CHARGER_PPID}/tariffs`)
        .send(MOCK_TARIFF);

      expect(res.status).toEqual(401);

      expect(getChargerTariffsMock).not.toHaveBeenCalled();
    });

    it('returns a 403 if the user does not own the requested charger when they request it', async () => {
      const getChargerTariffsMock = jest.spyOn(
        chargersTariffsService,
        'getChargerTariffs'
      );

      const res = await request(app.getHttpServer())
        .get('/chargers/PSL-000000/tariffs')
        .set('Authorization', `Bearer ${token}`);

      expect(res.status).toEqual(403);

      expect(getChargerTariffsMock).not.toHaveBeenCalled();
    });

    it('should get the tariff for an authenticated user on a charger they own', async () => {
      mockAuthentication(TEST_CHARGER_PPID, usersService);

      const getChargerTariffsMock = jest
        .spyOn(chargersTariffsService, 'getChargerTariffs')
        .mockResolvedValue(MOCK_TARIFF);

      const res = await request(app.getHttpServer())
        .get(`/chargers/${TEST_CHARGER_PPID}/tariffs`)
        .set('Authorization', `Bearer ${token}`);

      expect(res.status).toEqual(200);
      expect(res.body).toMatchSnapshot();

      expect(getChargerTariffsMock).toHaveBeenCalledTimes(1);
      expect(getChargerTariffsMock).toHaveBeenCalledWith(TEST_CHARGER_PPID);
    });
  });

  describe('updateChargerTariff()', () => {
    const MOCK_TARIFF_ID = '84848e36-1f52-43c1-9a6c-7354e43f0250';
    const MOCK_TARIFF: SetChargerTariffDto = {
      supplierId: '84848e36-1f52-43c1-9a6c-7354e43f0250',
      timezone: 'Europe/London',
      maxChargePrice: 0.2,
      effectiveFrom: '2024-11-28',
      tariffInfo: [
        {
          start: '00:00:00',
          end: '01:00:00',
          price: 0.3,
          days: ['SATURDAY', 'SUNDAY'],
        },
      ],
    };

    it('returns a 401 if unauthorised', async () => {
      const updateTariffMock = jest.spyOn(
        chargersTariffsService,
        'updateTariff'
      );

      const res = await request(app.getHttpServer())
        .put(`/chargers/${TEST_CHARGER_PPID}/tariffs/${MOCK_TARIFF_ID}`)
        .send(MOCK_TARIFF);

      expect(res.status).toEqual(401);

      expect(updateTariffMock).not.toHaveBeenCalled();
    });

    it('returns a 403 if the user does not own the requested charger', async () => {
      mockAuthentication('not_valid_ppid', usersService);

      const updateTariffMock = jest.spyOn(
        chargersTariffsService,
        'updateTariff'
      );

      const res = await request(app.getHttpServer())
        .put(`/chargers/${TEST_CHARGER_PPID}/tariffs/${MOCK_TARIFF_ID}`)
        .set('Authorization', `Bearer ${token}`)
        .send(MOCK_TARIFF);

      expect(res.status).toEqual(403);

      expect(updateTariffMock).not.toHaveBeenCalled();
    });

    it("returns a 200 if successfully updates the charger's tariff", async () => {
      mockAuthentication(TEST_CHARGER_PPID, usersService);

      const updateTariffMock = jest
        .spyOn(chargersTariffsService, 'updateTariff')
        .mockResolvedValue({
          id: MOCK_TARIFF_ID,
          ppid: TEST_CHARGER_PPID,
          cheapestUnitPrice: 0.2,
          ...MOCK_TARIFF,
        });

      const res = await request(app.getHttpServer())
        .put(`/chargers/${TEST_CHARGER_PPID}/tariffs/${MOCK_TARIFF_ID}`)
        .set('Authorization', `Bearer ${token}`)
        .send(MOCK_TARIFF);

      expect(res.status).toEqual(200);

      expect(updateTariffMock).toHaveBeenCalledTimes(1);
      expect(updateTariffMock).toHaveBeenCalledWith(
        TEST_CHARGER_PPID,
        MOCK_TARIFF_ID,
        MOCK_TARIFF
      );
    });
  });

  describe('deleteTariff', () => {
    const MOCK_TARIFF_ID = '84848e36-1f52-43c1-9a6c-7354e43f0250';

    it('returns a 401 if unauthorised', async () => {
      const deleteTariffMock = jest.spyOn(
        chargersTariffsService,
        'deleteTariff'
      );

      const res = await request(app.getHttpServer()).delete(
        `/chargers/${TEST_CHARGER_PPID}/tariffs/${MOCK_TARIFF_ID}`
      );

      expect(res.status).toEqual(401);

      expect(deleteTariffMock).not.toHaveBeenCalled();
    });

    it('returns a 403 if the user does not own the requested charger', async () => {
      const deleteTariffMock = jest.spyOn(
        chargersTariffsService,
        'setChargerTariffs'
      );

      const res = await request(app.getHttpServer())
        .delete('/chargers/PSL-000000/tariffs/123')
        .set('Authorization', `Bearer ${token}`);

      expect(res.status).toEqual(403);

      expect(deleteTariffMock).not.toHaveBeenCalled();
    });

    it('should delete the tariff for an authenticated user', async () => {
      mockAuthentication(TEST_CHARGER_PPID, usersService);

      const deleteTariffMock = jest
        .spyOn(chargersTariffsService, 'deleteTariff')
        .mockResolvedValueOnce();

      const res = await request(app.getHttpServer())
        .delete(`/chargers/${TEST_CHARGER_PPID}/tariffs/${MOCK_TARIFF_ID}`)
        .set('Authorization', `Bearer ${token}`);

      expect(res.status).toEqual(204);
      expect(res.body).toMatchSnapshot();

      expect(deleteTariffMock).toHaveBeenCalledTimes(1);
      expect(deleteTariffMock).toHaveBeenCalledWith(
        TEST_CHARGER_PPID,
        MOCK_TARIFF_ID
      );
    });
  });
});
