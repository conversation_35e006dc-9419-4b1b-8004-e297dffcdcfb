import { FailedToFindUserProfileError, UserError } from './user-profile.errors';
import { HttpInterceptor } from '@experience/shared/nest/utils';
import { HttpStatus } from '@nestjs/common';

export class UserErrorsInterceptor extends HttpInterceptor {
  constructor() {
    super([
      {
        code: UserError.FAILED_TO_FIND_USER_PROFILE,
        name: FailedToFindUserProfileError,
        statusCode: HttpStatus.INTERNAL_SERVER_ERROR,
      },
    ]);
  }
}
