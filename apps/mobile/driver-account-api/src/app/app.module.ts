import { ConfigModule, ConfigService } from '@nestjs/config';
import { LoggerModule } from 'nestjs-pino';
import { Module } from '@nestjs/common';

import { AcceptLanguageResolver, I18nModule, QueryResolver } from 'nestjs-i18n';
import { AuthModule } from '@experience/mobile/nest/auth';
import { Command, CommandRunner } from 'nest-commander';
import { DatabaseModule } from '@experience/mobile/driver-account/database';
import { HealthModule } from '../health/health.module';
import { HelloModule } from '../hello/hello.module';
import { MigrateModule } from '../migrate/migrate.module';
import { NotificationsModule } from '../notifications/notifications.module';
import { OAuthModule } from '../oauth/oauth.module';
import { PINO_LOGGER_OPTIONS } from '@experience/shared/nest/utils';
import { UserModule } from '../user/user.module';
import { VersionModule } from '@experience/shared/nest/version-module';
import { validate } from '../config/env.validate';

import * as Sentry from '@sentry/node';
import { API_VERSION } from '../constants';
import { DocumentBuilder } from '@nestjs/swagger';
import { INestApplication, VersioningType } from '@nestjs/common';
import { NodeSDK } from '@opentelemetry/sdk-node';
import { OTLPTraceExporter } from '@opentelemetry/exporter-trace-otlp-http';
import { Resource } from '@opentelemetry/resources';
import { SentryPropagator, SentrySpanProcessor } from '@sentry/opentelemetry';
import { UserEventsModule } from '../user-events/user-events.module';
import {
  beforeSendSentryError,
  bootstrap,
} from '@experience/shared/nest/utils';
import { getNodeAutoInstrumentations } from '@opentelemetry/auto-instrumentations-node';
import { getSwaggerDocs } from '../swagger/swagger';
import { useContainer } from 'class-validator';

@Module({
  providers: [ConfigService],
  imports: [
    I18nModule.forRoot({
      fallbackLanguage: 'en',
      loaderOptions: {
        path: './assets/driver-account-api/i18n/',
        watch: true,
      },
      resolvers: [
        { use: QueryResolver, options: ['lang'] },
        AcceptLanguageResolver,
      ],
    }),
    ConfigModule.forRoot({ validate: validate }),
    LoggerModule.forRoot(PINO_LOGGER_OPTIONS),
    AuthModule,
    DatabaseModule,
    HealthModule,
    HelloModule,
    MigrateModule,
    OAuthModule,
    UserModule,
    VersionModule,
    NotificationsModule,
    UserEventsModule,
  ],
})
@Command({ name: 'default', options: { hidden: true, isDefault: true } })
export class AppModule extends CommandRunner {
  async run(): Promise<void> {
    await bootstrap({
      module: AppModule,
      port: 5104,
      versioningOptions: {
        defaultVersion: API_VERSION,
        type: VersioningType.URI,
      },
      preCallback: () => {
        const sdk = new NodeSDK({
          resource: new Resource({
            'service.name': 'driver-account-api',
            'service.version': process.env.APPLICATION_VERSION,
          }),

          // Existing config
          traceExporter: new OTLPTraceExporter(),
          instrumentations: [getNodeAutoInstrumentations()],

          // Sentry config
          spanProcessors: [new SentrySpanProcessor()],
          textMapPropagator: new SentryPropagator(),
        });

        sdk.start();
      },
      callback: (app: INestApplication, docBuilder: DocumentBuilder) => {
        useContainer(app.select(AppModule), { fallbackOnErrors: true });

        const config = app.get(ConfigService);

        Sentry.init({
          enabled: ['dev', 'stage', 'prod'].includes(
            config.get<string>('ENVIRONMENT')
          ),
          environment: config.get<string>('ENVIRONMENT'),
          release: config.get<string>('SENTRY_RELEASE'),
          dsn: config.get<string>('SENTRY_DSN'),
          tracesSampleRate: 0.01, // 1% of traffic sampled
          ignoreTransactions: ['/health'],
          beforeSend: (event, hint) =>
            beforeSendSentryError(event, hint, [
              {
                errorCodes: [409, 422],
                functions: ['UserService.createLegacyAuthUser'],
              },
            ]),
        });

        return getSwaggerDocs(docBuilder);
      },
    });
  }
}
