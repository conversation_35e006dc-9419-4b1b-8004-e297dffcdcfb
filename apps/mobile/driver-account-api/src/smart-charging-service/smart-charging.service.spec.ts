import { ChargeSchedulesApi } from '@experience/shared/axios/smart-charging-service-client';
import { Logger } from '@nestjs/common';
import { SmartChargingService } from './smart-charging.service';
import { createMock } from '@golevelup/ts-jest';

const spy = jest.spyOn(Logger.prototype, 'error');
describe('SmartChargingService', () => {
  const client = createMock<ChargeSchedulesApi>();
  const service = new SmartChargingService(client);

  describe('setDefaultSchedules', () => {
    it('should call ChargeSchedulesApi api', async () => {
      await service.setDefaultSchedules('PSL-1234');

      expect(client.setDefaultChargeSchedule).toHaveBeenCalledTimes(1);
      expect(client.setDefaultChargeSchedule).toHaveBeenCalledWith('PSL-1234');
      expect(spy).not.toHaveBeenCalled();
    });

    it('should log the error when it fails to make the api call', async () => {
      const error = new Error('API call failed');
      client.setDefaultChargeSchedule.mockRejectedValueOnce(error);

      await service.setDefaultSchedules('PSL-1234');

      expect(client.setDefaultChargeSchedule).toHaveBeenCalledTimes(1);
      expect(client.setDefaultChargeSchedule).toHaveBeenCalledWith('PSL-1234');
      expect(spy).toHaveBeenCalledWith(
        { ppid: 'PSL-1234', err: error },
        'could not set default charge schedule'
      );
    });
  });
});
