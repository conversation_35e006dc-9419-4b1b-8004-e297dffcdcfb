import {
  AcquiredCustomerService,
  AcquiredPayeeService,
} from '@experience/shared/nest/acquired';
import {
  BankAccountDoesNotExistError,
  FailedToCreateBankAccountOnAcquiredError,
  FailedToCreateCustomerOnAcquiredError,
  FailedToCreateDBBankAccountError,
  FailedToGetBankAccountsError,
  FailedToGetTransactionSummaryError,
} from './bank-account.errors';
import {
  BankAccountResponseDTO,
  BankAccountTransactionFilters,
  BankAccountTransactionSummaryDTO,
  CreateBankAccountRequestDTO,
} from './bank-account.types';
import { EmailService } from '../email/email.service';
import { Inject, Injectable, Logger } from '@nestjs/common';
import {
  PAYMENTS_BANK_ACCOUNTS_REPOSITORY,
  PaymentsBankAccounts,
} from '@experience/mobile/driver-account/database';
import { Repository } from 'typeorm';

import { TransactionsService } from '../transactions/transactions.service';

@Injectable()
export class BankAccountService {
  private logger: Logger = new Logger(BankAccountService.name);

  constructor(
    private readonly acquiredCustomerService: AcquiredCustomerService,
    private readonly acquiredPayeeService: AcquiredPayeeService,
    @Inject(PAYMENTS_BANK_ACCOUNTS_REPOSITORY)
    private readonly bankAccountsRepository: Repository<PaymentsBankAccounts>,
    private readonly emailService: EmailService,
    private readonly transactionService: TransactionsService
  ) {}

  async getBankAccountById(id: string): Promise<BankAccountResponseDTO> {
    this.logger.log({ id }, 'retrieving bank account by id');

    try {
      const bankAccount = await this.bankAccountsRepository.findOne({
        where: {
          id,
        },
        withDeleted: true,
      });

      if (!bankAccount) {
        throw new BankAccountDoesNotExistError();
      }

      return BankAccountResponseDTO.fromEntity(bankAccount);
    } catch (error) {
      this.logger.error({ id, error }, 'unable to retrieve bank account by id');

      throw error;
    }
  }

  async searchBankAccounts(parameters: {
    userId: string;
    withDeleted: boolean;
  }): Promise<BankAccountResponseDTO[]> {
    this.logger.log({ parameters }, 'searching bank accounts');

    try {
      const bankAccounts = await this.bankAccountsRepository.find({
        where: {
          userId: parameters.userId,
        },
        withDeleted: parameters.withDeleted,
      });

      return bankAccounts.map((bankAccount) =>
        BankAccountResponseDTO.fromEntity(bankAccount)
      );
    } catch (error) {
      this.logger.error(
        { parameters, error },
        'failed to search bank accounts'
      );

      throw error;
    }
  }

  async createBankAccount(
    body: CreateBankAccountRequestDTO
  ): Promise<BankAccountResponseDTO> {
    const context = {
      userId: body.userId,
      accountNumber: body.accountNumber.slice(-4),
    };

    this.logger.log({ context }, 'About to create a bank account');

    const acquiredCustomer = await this.acquiredCustomerService
      .createRecipientAccount({
        firstName: body.accountName.slice(0, 20),
      })
      .catch((error) => {
        this.logger.error({ error }, 'failed to create customer on acquired');

        throw new FailedToCreateCustomerOnAcquiredError();
      });

    const acquiredPayee = await this.acquiredPayeeService
      .createPayee(acquiredCustomer.id, {
        name: body.accountName,
        accountNumber: body.accountNumber,
        sortCode: body.sortCode,
      })
      .catch((error) => {
        this.logger.error({ error }, 'failed to create a bank account');

        throw new FailedToCreateBankAccountOnAcquiredError();
      });

    const bankAccount = await this.persistBankAccount(
      body,
      acquiredCustomer.id,
      acquiredPayee.id
    );

    try {
      await this.emailService.sendBankAccountAddedEmail(
        body.userId,
        bankAccount
      );
    } catch (error) {
      this.logger.log({ context }, 'failed to send bank account added email');
    }

    return BankAccountResponseDTO.fromEntity(bankAccount);
  }

  async archiveBankAccount(bankAccountId: string) {
    this.logger.log({ bankAccountId }, 'attempting to archive bank account');

    try {
      const bankAccount = await this.bankAccountsRepository.findOne({
        where: {
          id: bankAccountId,
        },
      });

      if (!bankAccount) {
        this.logger.error({ bankAccount }, 'no bank account found');

        throw new FailedToGetBankAccountsError();
      }

      await this.acquiredCustomerService.disableRecipientAccount(
        bankAccount.acquiredCustomerId
      );

      await this.bankAccountsRepository.softDelete({
        id: bankAccountId,
      });

      this.logger.log({ bankAccountId }, 'successfully archived bank account');
    } catch (error) {
      this.logger.error(
        { bankAccountId, error },
        'failed to archive bank account'
      );

      throw error;
    }
  }

  private async persistBankAccount(
    request: CreateBankAccountRequestDTO,
    acquiredCustomerId: string,
    acquiredPayeeId: string
  ): Promise<PaymentsBankAccounts> {
    const context = {
      userId: request.userId,
      accountNumber: request.accountNumber.slice(-4),
      acquiredCustomerId,
      acquiredPayeeId,
    };

    this.logger.log(
      { context },
      'attempting to persist bank account in database'
    );

    try {
      const bankAccount = this.bankAccountsRepository.create({
        userId: request.userId,
        accountName: request.accountName,
        accountNumberLastFourDigits: request.accountNumber.slice(-4),
        acquiredCustomerId,
        acquiredPayeeId,
      });

      return await this.bankAccountsRepository.save(bankAccount);
    } catch (error) {
      this.logger.error(
        {
          context,
          error,
        },
        'failed to persist bank account for user in database'
      );

      throw new FailedToCreateDBBankAccountError();
    }
  }

  async getBankAccountTransactionSummary(
    bankAccountId: string,
    filters: BankAccountTransactionFilters
  ): Promise<BankAccountTransactionSummaryDTO> {
    this.logger.log(
      { bankAccountId, filters },
      'getting bank account transaction summary'
    );

    try {
      const bankAccount = await this.bankAccountsRepository.findOneBy({
        id: bankAccountId,
      });

      if (!bankAccount) {
        throw new BankAccountDoesNotExistError();
      }

      const transactions = await this.transactionService.getTransactionTotals(
        bankAccountId,
        filters.type
      );

      return {
        totalWithdrawn: transactions,
      };
    } catch (error) {
      if (error instanceof BankAccountDoesNotExistError) {
        throw error;
      }

      this.logger.error(
        { bankAccountId, filters },
        'failed to get transaction summary'
      );

      throw new FailedToGetTransactionSummaryError();
    }
  }
}
