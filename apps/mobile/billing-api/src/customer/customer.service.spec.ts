import { CustomerService } from './customer.service';
import { DeepMocked, createMock } from '@golevelup/ts-jest';
import {
  FailedToGetCustomerPaymentProcessorError,
  FailedToUpdateCustomerError,
  NoCustomerPaymentProcessorError,
} from './customer.errors';
import { MockRepository, createMockRepository } from '../test.utils';
import {
  StripeCustomerService,
  UpdateCustomerRequest,
} from '@experience/shared/nest/stripe';
import { Test } from '@nestjs/testing';
import { Users } from '@experience/shared/sequelize/podadmin';

describe('CustomerService', () => {
  const MOCK_AUTH_ID = '7141029a-d863-4e0d-9ae6-511063b24235';

  let service: CustomerService;
  let billingAccountsRepository: MockRepository;
  let stripeCustomerService: DeepMocked<StripeCustomerService>;

  beforeEach(async () => {
    const module = await Test.createTestingModule({
      providers: [
        CustomerService,
        {
          provide: 'BILLING_ACCOUNTS_REPOSITORY',
          useValue: createMockRepository(),
        },
        {
          provide: StripeCustomerService,
          useValue: createMock<StripeCustomerService>(),
        },
      ],
    }).compile();

    service = module.get(CustomerService);
    billingAccountsRepository = module.get('BILLING_ACCOUNTS_REPOSITORY');
    stripeCustomerService = module.get(StripeCustomerService);
  });

  afterEach(() => jest.resetAllMocks());

  describe('updateCustomer()', () => {
    const PAYMENT_PROCESSOR_ID = '889e5ec6-51fb-49a7-bad7-232f3b33bfd7';
    const MOCK_UPDATE: UpdateCustomerRequest = {
      email: '<EMAIL>',
    };

    it("throws a FailedToGetCustomerPaymentProcessorError if unable to look up the user's payment processor id", async () => {
      billingAccountsRepository.findOne?.mockImplementation(() => {
        throw new Error();
      });

      await expect(() =>
        service.updateCustomer(MOCK_AUTH_ID, MOCK_UPDATE)
      ).rejects.toThrow(FailedToGetCustomerPaymentProcessorError);

      expect(billingAccountsRepository.findOne).toHaveBeenCalledTimes(1);
      expect(billingAccountsRepository.findOne).toHaveBeenCalledWith({
        raw: true,
        include: [
          {
            model: Users,
            as: 'user',
            where: {
              authId: MOCK_AUTH_ID,
            },
          },
        ],
      });

      expect(stripeCustomerService.update).not.toHaveBeenCalled();
    });

    it('throws a NoCustomerPaymentProcessorError if the user does not have a payment processor id', async () => {
      billingAccountsRepository.findOne?.mockReturnValue({
        paymentProcessorId: undefined,
      });

      await expect(() =>
        service.updateCustomer(MOCK_AUTH_ID, MOCK_UPDATE)
      ).rejects.toThrow(NoCustomerPaymentProcessorError);

      expect(billingAccountsRepository.findOne).toHaveBeenCalledTimes(1);
      expect(billingAccountsRepository.findOne).toHaveBeenCalledWith({
        raw: true,
        include: [
          {
            model: Users,
            as: 'user',
            where: {
              authId: MOCK_AUTH_ID,
            },
          },
        ],
      });

      expect(stripeCustomerService.update).not.toHaveBeenCalled();
    });

    it('gets the customer payment processor id from the users api and updates the related customer via stripe', async () => {
      billingAccountsRepository.findOne?.mockReturnValue({
        paymentProcessorId: PAYMENT_PROCESSOR_ID,
      });

      const res = await service.updateCustomer(MOCK_AUTH_ID, MOCK_UPDATE);

      expect(res).toStrictEqual({
        id: MOCK_AUTH_ID,
        paymentProcessorId: PAYMENT_PROCESSOR_ID,
        ...MOCK_UPDATE,
      });

      expect(billingAccountsRepository.findOne).toHaveBeenCalledTimes(1);
      expect(billingAccountsRepository.findOne).toHaveBeenCalledWith({
        raw: true,
        include: [
          {
            model: Users,
            as: 'user',
            where: {
              authId: MOCK_AUTH_ID,
            },
          },
        ],
      });

      expect(stripeCustomerService.update).toHaveBeenCalledTimes(1);
      expect(stripeCustomerService.update).toHaveBeenCalledWith(
        PAYMENT_PROCESSOR_ID,
        MOCK_UPDATE
      );
    });

    it('throws a FailedToUpdateCustomerError if unable to update the user via stripe', async () => {
      billingAccountsRepository.findOne?.mockReturnValue({
        paymentProcessorId: PAYMENT_PROCESSOR_ID,
      });

      stripeCustomerService.update.mockImplementation(() => {
        throw new Error();
      });

      await expect(() =>
        service.updateCustomer(MOCK_AUTH_ID, MOCK_UPDATE)
      ).rejects.toThrow(FailedToUpdateCustomerError);

      expect(billingAccountsRepository.findOne).toHaveBeenCalledTimes(1);
      expect(billingAccountsRepository.findOne).toHaveBeenCalledWith({
        raw: true,
        include: [
          {
            model: Users,
            as: 'user',
            where: {
              authId: MOCK_AUTH_ID,
            },
          },
        ],
      });

      expect(stripeCustomerService.update).toHaveBeenCalledTimes(1);
      expect(stripeCustomerService.update).toHaveBeenCalledWith(
        PAYMENT_PROCESSOR_ID,
        MOCK_UPDATE
      );
    });
  });
});
