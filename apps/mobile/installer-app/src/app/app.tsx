import { JSX, useContext } from 'react';

import { BrowserRouter, Navigate, Route, Routes } from 'react-router-dom';
import { UserContext } from './UserContext/user-context';
import Login from '../../pages/login';
import Register from '../../pages/register';
import ResetPassword from '../../pages/reset-password';
import UserPage from '../../pages/user-page';

interface userRouteProps {
  children: JSX.Element;
}

export const App = () => {
  const userContext = useContext(UserContext);
  const ProtectedRoute = ({ children }: userRouteProps) => {
    if (!userContext.user) {
      return <Navigate to="/login" replace />;
    }

    return children;
  };

  return (
    <div className="App">
      <BrowserRouter>
        <Routes>
          <Route path="/" element={<Login />} />
          <Route path="login" element={<Login />} />
          <Route path="register" element={<Register />} />
          <Route path="reset-password" element={<ResetPassword />} />
          <Route
            path="profile"
            element={
              <ProtectedRoute>
                <UserPage />
              </ProtectedRoute>
            }
          />
          <Route
            path="*"
            element={
              <main style={{ padding: '1rem' }}>
                <p>There&apos;s nothing here!</p>
              </main>
            }
          />
        </Routes>
      </BrowserRouter>
    </div>
  );
};

export default App;
