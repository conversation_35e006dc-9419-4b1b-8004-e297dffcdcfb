apiVersion: backstage.io/v1alpha1
kind: Component
metadata:
  name: installer-api
  title: Installer and installs (home) service api
  description: API for working with installer and home installations.
  annotations:
    github.com/project-slug: Pod-Point/experience
    backstage.io/techdocs-ref: dir:.
  tags:
    - nestjs
spec:
  type: service
  owner: group:tfm-experience-mobile
  lifecycle: production
  providesApis:
    - installer-api
---
apiVersion: backstage.io/v1alpha1
kind: API
metadata:
  name: installer-api
  description: |
    BFF for the Installer App
spec:
  type: openapi
  owner: group:tfm-experience-mobile
  lifecycle: production
  definition:
    $text: ../../../libs/mobile/installer/api/contract/openapi3.yaml
