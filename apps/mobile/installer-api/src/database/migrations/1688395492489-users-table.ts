import { MigrationInterface, QueryRunner } from 'typeorm';

export class UserTable1688395492489 implements MigrationInterface {
  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`CREATE TABLE "installer"."users" (
                                        "id" SERIAL NOT NULL,
                                         "auth_id" character varying NOT NULL,
                                         "email" character varying NOT NULL,
                                         "is_pod_point_staff" boolean NOT NULL,
                                         "marketing_consent" boolean NOT NULL,
                                         "first_name" character varying,
                                         "last_name" character varying,
                                         "phone_number" character varying,
                                         "company_type" character varying,
                                         "company_name" character varying,
                                         "company_number" character varying,
                                         "created_at" TIMESTAMP NOT NULL DEFAULT now(),
                                         "updated_at" TIMESTAMP NOT NULL DEFAULT now(),
                                         CONSTRAINT "unique_users_auth_id" UNIQUE ("auth_id"),
                                         CONSTRAINT "unique_users_auth_email" UNIQUE ("email"),
                                         CONSTRAINT "users_pk" PRIMARY KEY ("id")
                                   )`);

    await queryRunner.query(
      `GRANT SELECT, INSERT, UPDATE ON installer.users TO installer_api;`
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`DROP TABLE "installer"."users"`);
    await queryRunner.query(
      `REVOKE SELECT, INSERT, UPDATE ON installer.users FROM installer_api;`
    );
  }
}
