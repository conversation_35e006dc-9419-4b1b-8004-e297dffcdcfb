import { MigrationInterface, QueryRunner, TableColumn } from 'typeorm';

export class AddInstallerType1718273101370 implements MigrationInterface {
  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.addColumn(
      'installer.users',
      new TableColumn({
        name: 'installer_type',
        type: 'text',
        isNullable: true,
      })
    );
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.dropColumn('installer.users', 'installer_type');
  }
}
