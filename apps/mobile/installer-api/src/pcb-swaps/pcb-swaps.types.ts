import { ApiProperty, PickType } from '@nestjs/swagger';
import {
  Equals,
  IsEnum,
  IsISO8601,
  IsOptional,
  IsString,
} from 'class-validator';

export enum PcbSwapStatus {
  SUCCESS = 'success',
  PARTIAL_SUCCESS = 'partial_success',
}

export interface PcbSwap {
  ppid: string;
  serialNumber: string;
  status: string;
  changedAt: string;
  emailedAt: string | null;
}

export class PcbSwapDto implements PcbSwap {
  @ApiProperty({
    description: 'The PPID of the charger the swap is happening to',
    example: 'PSL-620893',
  })
  ppid: string;

  @ApiProperty({
    description: 'The serial number of the PCB being swapped in',
    example: '2422110742',
  })
  serialNumber: string;

  @ApiProperty({
    description: 'The status of the PCB swap',
    enum: PcbSwapStatus,
  })
  status: string;

  @ApiProperty({
    description: 'The timestamp at which the PCB was changed',
    example: '2024-09-23T16:15:00Z',
  })
  changedAt: string;

  @ApiProperty({
    description:
      "If present, the time at which the charger's owner was notified of the completed swap",
    example: '2024-09-24T16:15:00Z',
  })
  emailedAt: string | null;
}

export class CreatePcbSwapDto extends PickType(PcbSwapDto, [
  'ppid',
  'serialNumber',
  'status',
]) {
  @IsString()
  ppid: string;

  @IsString()
  serialNumber: string;

  @IsEnum(PcbSwapStatus)
  status: PcbSwapStatus;
}

export class UpdatePcbSwapDto extends PickType(PcbSwapDto, ['emailedAt']) {
  @IsISO8601({ strict: true })
  emailedAt: string;
}

export interface PcbSwapFilters {
  emailedAt?: string;
  changedAtBefore?: string;
}

export class PcbSwapFiltersDto implements PcbSwapFilters {
  @IsOptional()
  @Equals('null')
  emailedAt?: string;

  @IsOptional()
  @IsISO8601({ strict: true })
  changedAtBefore?: string;
}
