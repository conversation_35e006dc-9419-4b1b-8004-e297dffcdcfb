import { ActionParamDto } from './dto/action-param.dto';
import { ActionService } from '../../application/services/action.service';
import { ActionType } from '../../domain/entities/action.entity';
import {
  ApiBadRequestResponse,
  ApiBody,
  ApiExtraModels,
  ApiForbiddenResponse,
  ApiInternalServerErrorResponse,
  ApiNotFoundResponse,
  ApiOkResponse,
  ApiOperation,
  ApiParam,
  ApiQuery,
  ApiTags,
  getSchemaPath,
} from '@nestjs/swagger';
import {
  BadRequestException,
  Body,
  Controller,
  Delete,
  ForbiddenException,
  Get,
  Header,
  InternalServerErrorException,
  Logger,
  NotFoundException,
  Param,
  Patch,
  Query,
  StreamableFile,
} from '@nestjs/common';
import { CheckAffordabilityActionDTO } from './dto/actions/check-affordability-action.dto';
import {
  CouldNotFindEntityError,
  CouldNotPersistEntityError,
  InvalidEntityError,
} from '@experience/mobile/clean-architecture';
import { GetSubscriptionsByCriteriaDTO } from './dto/get-subscriptions-by-criteria.dto';
import { HomeSurveyActionDTO } from './dto/actions/home-survey-action.dto';
import { IncompleteDependencyError } from '../../domain/errors/incomplete-dependency.error';
import { InstallChargingStationActionDTO } from './dto/actions/install-charging-station-action.dto';
import { ListSubscriptionsDTO } from './dto/list-subscriptions.dto';
import { LoanManagementSystemError } from '../../domain/errors/loan-management-system.error';
import { MultiDTOValidationPipe } from '@experience/shared/nest/utils';
import { NoApplicationIdError } from '../../application/errors/no-application-id.error';
import { PayUpfrontFeeActionDTO } from './dto/actions/pay-upfront-fee-action.dto';
import { PersistedSubscriptionDTO } from './dto/persisted-subscription.dto';
import { SetupDirectDebitActionDTO } from './dto/actions/setup-direct-debit-action.dto';
import { SignDocumentsActionDTO } from './dto/actions/sign-documents-action.dto';
import { SubscriptionDirectDebitDTO } from './dto/subscription-direct-debit.dto';
import { SubscriptionDocumentsDTO } from './dto/subscription-documents.dto';
import { SubscriptionNotActiveError } from '../../application/errors/subscription-not-active.error';
import { SubscriptionParamDto } from './dto/subscription-param.dto';
import { SubscriptionsService } from '../../application/services/subscriptions.service';
import { UnknownDocumentError } from '../../application/errors/unknown-document.error';
import {
  UpdateAffordabilityActionDTO,
  UpdateCheckAffordabilityDataDTO,
  UpdateDirectDebitActionDTO,
  UpdateHomeSurveyActionDTO,
  UpdateSetupDirectDebitDataDTO,
  UpdateSignDocumentsActionDTO,
} from './dto/update.action.dto';

@ApiTags('Subscriptions')
@Controller('subscriptions')
export class SubscriptionsController {
  private readonly logger = new Logger(SubscriptionsController.name);

  constructor(
    private readonly subscriptionsService: SubscriptionsService,
    private readonly actionService: ActionService
  ) {}

  @ApiOkResponse({
    type: ListSubscriptionsDTO,
    description: 'Returned when subscriptions are successfully retrieved',
  })
  @ApiOperation({
    summary: 'List subscriptions',
    description: 'List subscriptions by user ID or PPID',
  })
  @ApiInternalServerErrorResponse({
    description: 'Returned when an internal error occurs',
  })
  @Get()
  async search(
    @Query() criteria: GetSubscriptionsByCriteriaDTO
  ): Promise<ListSubscriptionsDTO> {
    const { userId, ppid } = criteria;

    if ((!userId && !ppid) || (userId && ppid)) {
      throw new BadRequestException('Either userId or ppid must be provided.');
    }

    try {
      const subscriptionsWithActions =
        await this.subscriptionsService.getByPpidAndOrUserId(criteria);

      return ListSubscriptionsDTO.from(subscriptionsWithActions);
    } catch (error) {
      this.logger.error(
        { error, userId, ppid },
        'Could not retrieve subscriptions'
      );
      throw new InternalServerErrorException(
        'Could not retrieve subscriptions due to an internal error.'
      );
    }
  }

  @ApiOkResponse({
    type: PersistedSubscriptionDTO,
    description: 'Returned when subscription is successfully retrieved',
  })
  @ApiOperation({
    summary: 'Get a subscription',
    description: 'Get a subscription by id',
  })
  @ApiNotFoundResponse({
    description: 'Returned when subscription could not be found',
  })
  @ApiInternalServerErrorResponse({
    description: 'Returned when an internal error occurs',
  })
  @ApiParam({
    description: 'The ID of the subscription',
    name: 'subscriptionId',
    required: true,
    type: String,
    format: 'uuid',
  })
  @Get(':subscriptionId')
  async getBySubscriptionId(
    @Param() { subscriptionId }: SubscriptionParamDto
  ): Promise<PersistedSubscriptionDTO> {
    const maybeSubscriptionWithActions =
      await this.subscriptionsService.readById(subscriptionId);

    if (!maybeSubscriptionWithActions) {
      this.logger.log(
        { subscriptionId },
        'Could not find a subscription with id'
      );

      throw new NotFoundException(
        `No subscription found with id: ${subscriptionId}`
      );
    }

    return PersistedSubscriptionDTO.from(maybeSubscriptionWithActions);
  }

  @ApiExtraModels(
    SetupDirectDebitActionDTO,
    CheckAffordabilityActionDTO,
    InstallChargingStationActionDTO,
    HomeSurveyActionDTO,
    SignDocumentsActionDTO,
    PayUpfrontFeeActionDTO
  )
  @ApiOkResponse({
    description: 'Returned when action is successfully retrieved',
    schema: {
      oneOf: [
        { $ref: getSchemaPath(SetupDirectDebitActionDTO) },
        { $ref: getSchemaPath(CheckAffordabilityActionDTO) },
        { $ref: getSchemaPath(InstallChargingStationActionDTO) },
        { $ref: getSchemaPath(HomeSurveyActionDTO) },
        { $ref: getSchemaPath(SignDocumentsActionDTO) },
        { $ref: getSchemaPath(PayUpfrontFeeActionDTO) },
      ],
    },
  })
  @ApiOperation({
    summary: 'Get an action',
    description: 'Get an action by action ID within a subscription',
  })
  @ApiNotFoundResponse({
    description: 'Returned when action could not be found',
  })
  @ApiInternalServerErrorResponse({
    description: 'Returned when an internal error occurs',
  })
  @ApiParam({
    description: 'The ID of the action',
    name: 'actionId',
    required: true,
    type: String,
    format: 'uuid',
  })
  @ApiParam({
    description: 'The ID of the subscription',
    name: 'subscriptionId',
    required: true,
    type: String,
    format: 'uuid',
  })
  @Get(':subscriptionId/actions/:actionId')
  async getByActionId(@Param() { subscriptionId, actionId }: ActionParamDto) {
    const action = await this.actionService.readById(actionId);

    if (!action || action.subscriptionId !== subscriptionId) {
      this.logger.log(
        { actionId, subscriptionId },
        'Could not find action with given id within subscription'
      );

      throw new NotFoundException(
        `No action found with id: ${actionId} in subscription: ${subscriptionId}`
      );
    }

    return action;
  }

  @ApiOperation({
    summary: 'Get subscription direct debit details',
    description: 'Get subscription direct debit details',
  })
  @ApiOkResponse({
    type: SubscriptionDirectDebitDTO,
    description: 'Direct debit details',
  })
  @ApiNotFoundResponse({
    description: 'Returned when subscription could not be found',
  })
  @ApiInternalServerErrorResponse({
    description: 'Returned when an internal error occurs',
  })
  @ApiParam({
    description: 'The ID of the subscription',
    name: 'subscriptionId',
    required: true,
    type: String,
    format: 'uuid',
  })
  @Get(':subscriptionId/direct-debit')
  async getSubscriptionDirectDebit(
    @Param('subscriptionId') subscriptionId: string
  ): Promise<SubscriptionDirectDebitDTO> {
    try {
      return await this.subscriptionsService.getSubscriptionDirectDebit(
        subscriptionId
      );
    } catch (error) {
      if (error instanceof CouldNotFindEntityError) {
        throw new NotFoundException('unable to find subscription or action');
      }

      if (error instanceof NoApplicationIdError) {
        throw new NotFoundException('application id was not populated');
      }

      if (error instanceof SubscriptionNotActiveError) {
        throw new NotFoundException('subscription was not active');
      }

      throw new InternalServerErrorException(
        'unable to get subscription direct debit'
      );
    }
  }

  @Get(':subscriptionId/documents')
  @ApiOperation({
    summary: 'Get subscription documents',
    description: 'Get documents related to a given subscription',
  })
  @ApiOkResponse({
    type: SubscriptionDocumentsDTO,
  })
  @ApiNotFoundResponse({
    description: 'Returned when subscription could not be found',
  })
  @ApiInternalServerErrorResponse({
    description: 'Returned when an internal error occurs',
  })
  @ApiParam({
    description: 'The ID of the subscription',
    name: 'subscriptionId',
    required: true,
    type: String,
    format: 'uuid',
  })
  async getSubscriptionDocuments(
    @Param('subscriptionId') subscriptionId: string
  ): Promise<SubscriptionDocumentsDTO> {
    try {
      const documents =
        await this.subscriptionsService.getSubscriptionDocuments(
          subscriptionId
        );

      return { documents };
    } catch (error) {
      this.logger.error({ error }, 'failed to get documents');

      if (error instanceof SubscriptionNotActiveError) {
        throw new NotFoundException('subscription is not active');
      }

      if (error instanceof CouldNotFindEntityError) {
        throw new NotFoundException('unable to find subscription or action');
      }

      if (error instanceof NoApplicationIdError) {
        throw new NotFoundException('application id was not populated');
      }

      throw new InternalServerErrorException(
        'unable to get subscription documents'
      );
    }
  }

  @Get(':subscriptionId/documents/:documentId')
  @Header('Content-Type', 'application/pdf')
  @ApiOperation({
    summary: 'Get subscription document',
    description: 'Downloads the PDF file of a given document',
  })
  @ApiNotFoundResponse({
    description: 'Returned when subscription could not be found',
  })
  @ApiInternalServerErrorResponse({
    description: 'Returned when an internal error occurs',
  })
  @ApiParam({
    description: 'The ID of the subscription',
    name: 'subscriptionId',
    required: true,
    type: String,
    format: 'uuid',
  })
  @ApiParam({
    description: 'The ID of the document',
    name: 'documentId',
    required: true,
    type: String,
  })
  async getSubscriptionDocument(
    @Param('subscriptionId') subscriptionId: string,
    @Param('documentId') documentId: string
  ): Promise<StreamableFile> {
    if (!/LMS-\d*/.test(documentId)) {
      throw new NotFoundException();
    }

    const documentIdNumber = +documentId.split('-')[1];

    try {
      const document = await this.subscriptionsService.getSubscriptionDocument(
        subscriptionId,
        documentIdNumber
      );

      return new StreamableFile(document);
    } catch (error) {
      this.logger.error({ error }, 'failed to get document');

      if (error instanceof CouldNotFindEntityError) {
        throw new NotFoundException();
      }

      if (error instanceof NoApplicationIdError) {
        throw new NotFoundException();
      }

      if (error instanceof UnknownDocumentError) {
        throw new NotFoundException();
      }

      throw new InternalServerErrorException();
    }
  }

  @ApiOkResponse({
    description: 'The body of the resolved action',
    schema: {
      oneOf: [
        { $ref: getSchemaPath(SetupDirectDebitActionDTO) },
        { $ref: getSchemaPath(CheckAffordabilityActionDTO) },
        { $ref: getSchemaPath(HomeSurveyActionDTO) },
        { $ref: getSchemaPath(SignDocumentsActionDTO) },
      ],
    },
  })
  @ApiExtraModels(
    UpdateAffordabilityActionDTO,
    UpdateDirectDebitActionDTO,
    UpdateHomeSurveyActionDTO,
    UpdateSignDocumentsActionDTO
  )
  @ApiBody({
    schema: {
      oneOf: [
        { $ref: getSchemaPath(UpdateAffordabilityActionDTO) },
        { $ref: getSchemaPath(UpdateDirectDebitActionDTO) },
        { $ref: getSchemaPath(UpdateHomeSurveyActionDTO) },
        { $ref: getSchemaPath(UpdateSignDocumentsActionDTO) },
      ],
    },
  })
  @ApiOperation({
    summary: 'Resolve an action',
    description: 'Resolve an action contextually based on the type of action',
  })
  @ApiBadRequestResponse({
    description: 'Returned when the input to the endpoint was invalid',
  })
  @ApiNotFoundResponse({
    description: 'Returned when action could not be found',
  })
  @ApiInternalServerErrorResponse({
    description: 'Returned when an internal error occurs',
  })
  @ApiParam({
    description: 'The ID of the action',
    name: 'actionId',
    required: true,
    type: String,
    format: 'uuid',
  })
  @ApiParam({
    description: 'The ID of the subscription',
    name: 'subscriptionId',
    required: true,
    type: String,
    format: 'uuid',
  })
  @Patch(':subscriptionId/actions/:actionId')
  async updateActionById(
    @Param() { subscriptionId, actionId }: ActionParamDto,
    @Body(
      new MultiDTOValidationPipe([
        UpdateAffordabilityActionDTO,
        UpdateDirectDebitActionDTO,
        UpdateHomeSurveyActionDTO,
        UpdateSignDocumentsActionDTO,
      ])
    )
    body:
      | UpdateAffordabilityActionDTO
      | UpdateDirectDebitActionDTO
      | UpdateHomeSurveyActionDTO
      | UpdateSignDocumentsActionDTO
  ) {
    try {
      switch (body.type) {
        case ActionType.CHECK_AFFORDABILITY_V1:
          if (!(body.data instanceof UpdateCheckAffordabilityDataDTO)) {
            throw new BadRequestException(
              'Action type was not applicable to update data'
            );
          }

          if (
            !(
              body.data.billingAddress.flat ||
              body.data.billingAddress.number ||
              body.data.billingAddress.name
            )
          ) {
            throw new BadRequestException(
              'billingAddress requires at least a flat, number or name'
            );
          }

          return CheckAffordabilityActionDTO.from(
            await this.actionService.completeAffordabilityCheckAction(
              actionId,
              body.data
            )
          );

        case ActionType.SETUP_DIRECT_DEBIT_V1:
          if (!(body.data instanceof UpdateSetupDirectDebitDataDTO)) {
            throw new BadRequestException(
              'Action type was not applicable to update data'
            );
          }

          return SetupDirectDebitActionDTO.from(
            await this.actionService.completeSetupDirectDebitAction(
              subscriptionId,
              actionId,
              body.data
            )
          );

        case ActionType.SIGN_DOCUMENTS_V1:
          return SignDocumentsActionDTO.from(
            await this.actionService.syncSignDocumentsAction(
              subscriptionId,
              actionId
            )
          );

        case ActionType.COMPLETE_HOME_SURVEY_V1:
          return HomeSurveyActionDTO.from(
            await this.actionService.completeHomeSurveyAction({ actionId })
          );

        default:
          throw new BadRequestException('Unsupported action type update');
      }
    } catch (error) {
      this.logger.error(
        {
          error,
          body,
        },
        'Could not update action'
      );

      if (error instanceof BadRequestException) {
        throw error;
      }

      if (error instanceof CouldNotFindEntityError) {
        throw new NotFoundException('Unable to find action');
      }

      if (error instanceof LoanManagementSystemError) {
        throw new InternalServerErrorException(error.message);
      }

      if (error instanceof IncompleteDependencyError) {
        throw new BadRequestException(error.message);
      }

      if (error instanceof InvalidEntityError) {
        throw new BadRequestException(
          'Data provided was not applicable to action'
        );
      }

      if (error instanceof CouldNotPersistEntityError) {
        throw new InternalServerErrorException('Could not update action');
      }

      throw new InternalServerErrorException(
        'Could not update action for unknown reason'
      );
    }
  }

  @Delete(':subscriptionId')
  @ApiOperation({
    summary: 'Delete a subscription',
    description:
      "Cancels and deletes a given subscription and it's corresponding actions and plan.",
  })
  @ApiQuery({
    name: 'mode',
    enum: ['debug'],
    description: 'here be dragons',
  })
  @ApiOkResponse({
    description: 'Returned when successfully cancelled and deleted',
  })
  @ApiNotFoundResponse({
    description: 'Returned when no matching subscription could be found',
  })
  @ApiInternalServerErrorResponse({
    description: 'Returned when something goes wrong',
  })
  @ApiForbiddenResponse({
    description: 'here be dragons, you have been warned',
  })
  @ApiParam({
    description: 'The ID of the subscription',
    name: 'subscriptionId',
    required: true,
    type: String,
    format: 'uuid',
  })
  @Delete(':subscriptionId')
  async delete(
    @Param() { subscriptionId }: SubscriptionParamDto,
    @Query('mode') query: string
  ) {
    if (query !== 'debug') {
      throw new ForbiddenException('here be dragons, this is a warning');
    }

    this.logger.warn(
      { subscriptionId, query },
      'Dragons have been acknowledged and ignored'
    );

    try {
      await this.subscriptionsService.deleteSubscription(subscriptionId);
      this.logger.warn({ subscriptionId }, 'Deleted');
    } catch (error) {
      this.logger.error(
        { error, subscriptionId },
        'could not delete subscription'
      );

      if (error instanceof CouldNotFindEntityError) {
        throw new NotFoundException(
          `No subscription found with id: ${subscriptionId}`
        );
      }

      throw new InternalServerErrorException(
        'Could not delete subscription for unknown reason'
      );
    }
  }
}
