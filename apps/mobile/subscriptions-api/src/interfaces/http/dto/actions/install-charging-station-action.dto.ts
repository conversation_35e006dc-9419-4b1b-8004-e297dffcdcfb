import {
  ActionEntity,
  ActionType,
} from '../../../../domain/entities/action.entity';
import { ApiProperty } from '@nestjs/swagger';
import { BaseActionDTO } from './base-action.dto';

class InstallChargingStationDataDTO {
  @ApiProperty({
    name: 'ppid',
    description: 'The PPID of the charging station. Set once installed.',
    type: 'string',
    nullable: true,
  })
  ppid: string | null;
}

export class InstallChargingStationActionDTO extends BaseActionDTO {
  @ApiProperty({
    enum: [ActionType.INSTALL_CHARGING_STATION_V1],
  })
  type: ActionType.INSTALL_CHARGING_STATION_V1;

  @ApiProperty({ type: InstallChargingStationDataDTO })
  data: InstallChargingStationDataDTO;

  static from(entity: ActionEntity): InstallChargingStationActionDTO {
    const data = entity.data as { ppid: string | null };

    return Object.assign(new InstallChargingStationActionDTO(), {
      id: entity.id,
      subscriptionId: entity.subscriptionId,
      owner: entity.owner,
      status: entity.status,
      type: ActionType.INSTALL_CHARGING_STATION_V1,
      dependsOn: entity.dependsOn.map((d) => d.id),
      data: {
        ppid: data.ppid ?? null,
      },
    });
  }
}
