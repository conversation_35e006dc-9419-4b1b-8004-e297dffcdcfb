import {
  EntityInterface,
  EntityParams,
  UnpersistedEntityHelper,
} from '@experience/mobile/clean-architecture';

export enum OrderOrigin {
  SALESFORCE = 'SALESFORCE',
  SALESFORCE_TEST = 'SALESFORCE_TEST',
}

export class OrderEntity implements EntityInterface<string> {
  constructor(params: EntityParams<OrderEntity>) {
    Object.assign(this, params);
  }

  id: string;
  origin: OrderOrigin;
  orderedAt: Date;
  firstName: string;
  lastName: string;
  email: string;
  phoneNumber: string;
  address: {
    line1: string;
    line2: string | null;
    line3: string | null;
    postcode: string;
  };
  mpan: string;
  eCommerceId: string;
}

export class UnpersistedOrderEntity
  implements UnpersistedEntityHelper<OrderEntity>
{
  constructor(params: EntityParams<UnpersistedOrderEntity>) {
    Object.assign(this, params);
  }

  id: string;
  origin: OrderOrigin;
  orderedAt: Date;
  firstName: string;
  lastName: string;
  email: string;
  phoneNumber: string;
  address: {
    line1: string;
    line2: string | null;
    line3: string | null;
    postcode: string;
  };
  mpan: string;
  eCommerceId: string;
}
