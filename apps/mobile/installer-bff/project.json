{"name": "installer-bff", "$schema": "../../../node_modules/nx/schemas/project-schema.json", "sourceRoot": "apps/mobile/installer-bff/src", "projectType": "application", "generate-swagger": {"executor": "@nx/js:node", "options": {"buildTarget": "{projectName}:build:swagger", "watch": false}}, "tags": ["mobile", "package"], "implicitDependencies": ["installer-bff-maizzle"], "namedInputs": {"projectSpecificFiles": ["{workspaceRoot}/.aws/**/installer-bff/*"]}, "targets": {"build-api": {"executor": "@nx/webpack:webpack", "outputs": ["{options.outputPath}"], "defaultConfiguration": "production", "options": {"target": "node", "compiler": "tsc", "outputPath": "dist/apps/mobile/installer-bff", "main": "apps/mobile/installer-bff/src/main.ts", "tsConfig": "apps/mobile/installer-bff/tsconfig.app.json", "assets": ["apps/mobile/installer-bff/src/assets"], "webpackConfig": "apps/mobile/installer-bff/webpack.config.js", "generatePackageJson": true}, "configurations": {"production": {"optimization": true, "extractLicenses": true, "inspect": false}}}, "build": {"executor": "nx:run-commands", "options": {"commands": ["nx run installer-bff:build-api"]}, "configurations": {"swagger": {"main": "{projectRoot}/src/swagger/generate-swagger.ts"}}}, "serve": {"executor": "@nx/js:node", "options": {"buildTarget": "installer-bff:build"}, "configurations": {"production": {"buildTarget": "installer-bff:build:production"}}}, "test": {"executor": "@nx/jest:jest", "outputs": ["{workspaceRoot}/coverage/{projectRoot}"], "options": {"jestConfig": "apps/mobile/installer-bff/jest.config.ts", "passWithNoTests": false}}, "smoke-test": {"executor": "nx:run-commands", "options": {"args": "--baseUrl=http://host.docker.internal:5106", "commands": ["docker run --rm -i -e BASE_URL={args.baseUrl} grafana/k6 run - <apps/mobile/installer-bff/smoke-test.js"], "parallel": false}, "configurations": {"ecs": {"args": "--baseUrl=http://installer-bff.destination.cluster.com:5106"}}}}}