import {
  Account<PERSON>pi,
  <PERSON><PERSON>wapsApi,
  PcbSwapDto,
} from '@experience/installer/api/axios';
import { ClsService } from 'nestjs-cls';
import { ConfigService } from '@nestjs/config';
import { <PERSON>ron } from '@nestjs/schedule';
import { I18nService } from 'nestjs-i18n';
import { Injectable, Logger } from '@nestjs/common';
import { InstallerClsStore } from '../app/app-cls.type';
import {
  SimpleEmailService,
  getTemplateStrings,
} from '@experience/shared/nest/aws/ses-module';
import { UsersApi } from '@experience/driver-account-api/api-client';
import { getLanguageFromCode } from '@experience/shared/nest/utils';
import { injectParametersIntoTemplateString } from '@experience/shared/typescript/utils';
import dayjs from 'dayjs';

@Injectable()
export class PCBSwapsNotificationService {
  private readonly logger = new Logger(PCBSwapsNotificationService.name);

  constructor(
    private readonly internalSwapApi: PC<PERSON>wapsApi,
    private readonly driverAccountApi: UsersApi,
    private readonly installerAccountApi: AccountApi,
    private readonly simpleEmailService: SimpleEmailService,
    private readonly i18n: I18nService,
    private readonly clsService: ClsService<InstallerClsStore>,
    private readonly configService: ConfigService
  ) {}

  private async sendEmail(pcb: PcbSwapDto) {
    const userResponse = await this.driverAccountApi.userControllerGetByFilter(
      pcb.ppid
    );
    const [user] = userResponse.data;

    if (!user) {
      this.logger.error({ pcb }, 'no user found belong to ppid of PCB swap');
      return;
    }

    const lang = getLanguageFromCode(user.locale);
    const { htmlTemplateString, plainTextTemplateString } = getTemplateStrings(
      `./assets/installer-bff/email-templates/${lang}/maintenance-completed`
    );

    const subject = this.i18n.t('emails.maintenance_completed.subject', {
      lang,
    });

    const params = {
      imageUrl: this.configService.get('IDENTITY_BASE_URL_INSTALLER'),
      emailAddress: user.email,
      customerName: user.first_name,
    };

    await this.simpleEmailService.sendEmail({
      bodyHtml: injectParametersIntoTemplateString(htmlTemplateString, {
        ...params,
      }),
      bodyText: injectParametersIntoTemplateString(plainTextTemplateString, {
        ...params,
      }),
      subject,
      to: params.emailAddress,
    });
  }

  @Cron('0 * * * *')
  async sendDomesticEmails() {
    const { data: needSend } =
      await this.internalSwapApi.pcbSwapsControllerGetAllPcbSwaps(
        'null',
        dayjs().subtract(1, 'day').toISOString()
      );

    this.logger.log(
      { needSend },
      'retrieved PCB swaps which need domestic emails sent for'
    );

    await Promise.all(
      needSend.map(async (pcb) => {
        try {
          this.logger.log(
            { pcb },
            'attempting to send domestic email for PCB swap'
          );

          await this.sendEmail(pcb);
        } catch (error) {
          this.logger.error(
            { pcb, error },
            'failed to send domestic email for PCB swap'
          );

          return;
        }

        try {
          const emailedAt = new Date().toISOString();

          this.logger.log(
            { pcb, emailedAt },
            'attempting to update PCB swap emailedAt time'
          );

          await this.internalSwapApi.pcbSwapsControllerUpdatePcbSwap(
            pcb.ppid,
            pcb.serialNumber,
            {
              emailedAt: emailedAt,
            }
          );
        } catch (error) {
          this.logger.error(
            { pcb, error },
            'failed to update PCB swap - this may result in a customer being notified again'
          );
        }
      })
    );
  }

  async sendInstallerEmail(ppid: string) {
    const installer = this.clsService.get('user');

    if (!installer) {
      return this.logger.warn(
        { ppid },
        'no installer in store. unable to send email'
      );
    }

    try {
      const { data: user, status } =
        await this.installerAccountApi.accountControllerGetProfile(
          installer.uid
        );

      if (!user || status === 404) {
        return this.logger.error(
          { uid: installer.uid, ppid },
          'no user found for authenticated installer'
        );
      }

      const lang = getLanguageFromCode(this.clsService.get('language') ?? 'en');
      const { htmlTemplateString, plainTextTemplateString } =
        getTemplateStrings(
          `./assets/installer-bff/email-templates/${lang}/pcb-swap-completed`
        );

      const subject = this.i18n.t('emails.pcb_swap_completed.subject', {
        lang,
      });

      const params = {
        imageUrl: this.configService.get('IDENTITY_BASE_URL_INSTALLER'),
        emailAddress: installer.email,
        installerName: user.firstName,
        pslNumber: ppid,
        swapTimestamp: dayjs().format('DD-MM-YYYY'),
      };

      await this.simpleEmailService.sendEmail({
        bodyHtml: injectParametersIntoTemplateString(htmlTemplateString, {
          ...params,
        }),
        bodyText: injectParametersIntoTemplateString(plainTextTemplateString, {
          ...params,
        }),
        subject,
        to: params.emailAddress,
      });
    } catch (error) {
      this.logger.error(
        { ppid, installerId: installer.uid, error },
        'a problem occurred when notifying the installer of a pcb swap completion'
      );
    }
  }
}
