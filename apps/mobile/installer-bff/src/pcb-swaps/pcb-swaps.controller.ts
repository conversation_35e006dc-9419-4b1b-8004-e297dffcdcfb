import {
  ApiBadRequestResponse,
  ApiBearerAuth,
  ApiBody,
  ApiInternalServerErrorResponse,
  ApiNotFoundResponse,
  ApiOkResponse,
  ApiOperation,
  ApiRequestTimeoutResponse,
  ApiTags,
} from '@nestjs/swagger';
import { AuthorisationGuard } from '../authorisation/authorisation.guard';
import {
  BadRequestException,
  Body,
  Controller,
  InternalServerErrorException,
  Logger,
  NotFoundException,
  Post,
  UseGuards,
  ValidationPipe,
} from '@nestjs/common';
import { NeedsProfileGuard } from '../authorisation/needs-profile.guard';
import {
  PcbSwapAttachDetachVirtualRequest,
  PcbSwapAttachVirtualResponse,
  PcbSwapDetachVirtualResponse,
  PcbSwapRequest,
  PcbSwapSuccessfulResponse,
} from './pcb-swaps.types';
import {
  PcbSwapError,
  PcbVirtualAttachError,
  PcbVirtualAttachNotWhitelistedError,
  PcbVirtualDetachError,
  PcbVirtualDetachNotWhitelistedError,
} from './pcb-swaps.errors';
import { PcbSwapsService } from './pcb-swaps.service';

@ApiTags('PCB Swaps')
@Controller({
  path: 'pcb-swaps',
  version: '1',
})
@UseGuards(AuthorisationGuard, NeedsProfileGuard)
export class PcbSwapsController {
  private readonly logger = new Logger(PcbSwapsController.name);

  constructor(readonly pcbSwapsService: PcbSwapsService) {}

  @Post()
  @ApiBearerAuth()
  @ApiOperation({
    summary: 'Start or retry a PCB Swap',
    description: `
Starts (or retries) an attempt to register the physical swap of a PCB in a charger.

This is a potentially long-running operation, so the request may take a while to complete.
If a timeout occurs (either on the client, or a server response of 408),
the request should be retried to continue waiting for the PCB swap to complete.
    `,
  })
  @ApiBody({ type: PcbSwapRequest })
  @ApiOkResponse({ type: PcbSwapSuccessfulResponse })
  @ApiBadRequestResponse({
    description: `
An error occurred while swapping the PCB. Please try again.
Often this can be resolved by retrying the request after a few minutes.
The message array will contain more detailed information about the error(s) that can be provided to customer support if necessary.
    `,
    schema: {
      example: {
        error: 'Bad Request',
        message: ['Pcb swap not found'],
        statusCode: 400,
      },
    },
  })
  @ApiInternalServerErrorResponse({
    description: `
An unexpected error occurred while swapping the PCB.
This error is not expected to be resolved by retrying the request.
    `,
    schema: {
      example: {
        error: 'Internal Server Error',
        message: 'An unexpected error occurred while swapping the PCB.',
        statusCode: 500,
      },
    },
  })
  @ApiRequestTimeoutResponse({
    description: `
The maximum time allowed to hold the request open while swapping the PCB has been exceeded.
This is unlikely to mean that the PCB swap was unsuccessful.
Retry the request to continue to wait PCB swap.
    `,
    schema: {
      example: {
        message: 'Request Timeout',
        statusCode: 408,
      },
    },
  })
  async startOrRetryPcbSwap(
    @Body(new ValidationPipe({ transform: true }))
    pcbSwapRequest: PcbSwapRequest
  ): Promise<PcbSwapSuccessfulResponse> {
    try {
      const res = await this.pcbSwapsService.swapPcb(pcbSwapRequest);

      return {
        status: res.status,
        message: 'The PCB has been successfully swapped.',
      };
    } catch (error) {
      if (error instanceof PcbSwapError) {
        if (error.retryable) {
          throw new BadRequestException(error.causes);
        }
        throw new InternalServerErrorException(error.message);
      }
      this.logger.error({
        msg: 'An unexpected error occurred while swapping the PCB.',
        error,
        pcbSwapRequest,
      });
      throw new InternalServerErrorException(
        'An unexpected error occurred while swapping the PCB.'
      );
    }
  }

  @Post('virtual-attach')
  @ApiBearerAuth()
  @ApiOperation({
    summary: 'Attach a virtual PCB',
    description:
      'Allows for whitelisted PPID/PSL numbers to attach a virtual PCB',
  })
  @ApiBody({ type: PcbSwapAttachDetachVirtualRequest })
  @ApiOkResponse({ type: PcbSwapAttachVirtualResponse })
  @ApiNotFoundResponse({
    description:
      'A non-whitelisted PPID/PSL number has been used when trying to attach a virtual PCB',
    schema: {
      example: {
        error: 'Not Found',
        message: [
          'The given pslNumber is not whitelisted for performing virtual attaching',
        ],
        statusCode: 404,
      },
    },
  })
  @ApiInternalServerErrorResponse({
    description: `
An unexpected error occurred while attaching the virtual PCB.
This can occur when either the virtual PCB fails to attach, or if any other unexpected error occurs.
    `,
    schema: {
      example: [
        {
          error: 'Internal Server Error',
          message: [
            'An unexpected error occurred while attaching the virtual PCB.',
          ],
          statusCode: 500,
        },
        {
          error: 'Internal Server Error',
          message: ['Failed to attach virtual PCB'],
          statusCode: 500,
        },
      ],
    },
  })
  async attachVirtualPcb(
    @Body(new ValidationPipe({ transform: true }))
    attachRequest: PcbSwapAttachDetachVirtualRequest
  ): Promise<PcbSwapAttachVirtualResponse> {
    try {
      return await this.pcbSwapsService.attachVirtualPcb(attachRequest);
    } catch (error) {
      switch (error.constructor) {
        case PcbVirtualAttachNotWhitelistedError:
          throw new NotFoundException(error.message);
        case PcbVirtualAttachError:
          throw new InternalServerErrorException(error.message);
      }

      this.logger.error({
        msg: 'An unexpected error occurred while attaching the virtual PCB.',
        error,
        attachRequest,
      });

      throw new InternalServerErrorException(
        'An unexpected error occurred while attaching the virtual PCB.'
      );
    }
  }

  @Post('virtual-detach')
  @ApiBearerAuth()
  @ApiOperation({
    summary: 'Detach a virtual PCB',
    description:
      'Allows for whitelisted PPID/PSL numbers to detach a virtual PCB',
  })
  @ApiBody({ type: PcbSwapAttachDetachVirtualRequest })
  @ApiOkResponse({ type: PcbSwapDetachVirtualResponse })
  @ApiNotFoundResponse({
    description:
      'A non-whitelisted PPID/PSL number has been used when trying to detach a virtual PCB',
    schema: {
      example: {
        error: 'Not Found',
        message: [
          'The given pslNumber is not whitelisted for performing virtual detaching',
        ],
        statusCode: 404,
      },
    },
  })
  @ApiInternalServerErrorResponse({
    description: `
An unexpected error occurred while detaching the virtual PCB.
This can occur when either the virtual PCB fails to detach, or if any other unexpected error occurs.
    `,
    schema: {
      example: [
        {
          error: 'Internal Server Error',
          message: [
            'An unexpected error occured while detaching the virtual PCB',
          ],
          statusCode: 500,
        },
        {
          error: 'Internal Server Error',
          message: ['Failed to detach the virtual PCB'],
          statusCode: 500,
        },
      ],
    },
  })
  async detachVirtualPcb(
    @Body(new ValidationPipe({ transform: true }))
    detachRequest: PcbSwapAttachDetachVirtualRequest
  ): Promise<PcbSwapDetachVirtualResponse> {
    try {
      return await this.pcbSwapsService.detachVirtualPcb(detachRequest);
    } catch (error) {
      switch (error.constructor) {
        case PcbVirtualDetachNotWhitelistedError:
          throw new NotFoundException(error.message);
        case PcbVirtualDetachError:
          throw new InternalServerErrorException(error.message);
      }

      this.logger.error({
        msg: 'An unexpected error occurred while detaching the virtual PCB.',
        error,
        detachRequest,
      });

      throw new InternalServerErrorException(
        'An unexpected error occurred while detaching the virtual PCB.'
      );
    }
  }
}
