import { AcceptLanguageResolver, I18nModule, QueryResolver } from 'nestjs-i18n';
import { AccountApi, Configuration } from '@experience/installer/api/axios';
import { AccountModule } from '../account/account.module';
import { AccountService } from '../account/account.service';
import { AppRequestLanguageMiddleware } from './app-request-language.middleware';
import { AppointmentModule } from '../appointment/appointment.module';
import { AuthActionsModule } from '../auth-actions/auth.actions.module';
import { CheckForUpgradeModule } from '@experience/mobile/nest/app-upgrade-module';
import { ClsMiddleware, ClsModule } from 'nestjs-cls';
import { ConfigModule, ConfigService } from '@nestjs/config';
import { HealthModule } from '../health/health.module';
import { HelloModule } from '../hello/hello.module';
import { InstallImagesModule } from '../install-images/install-images.module';
import { InstallsModule } from '../installs/installs.module';
import { LoggerModule } from 'nestjs-pino';
import { MiddlewareConsumer, Module, NestModule } from '@nestjs/common';
import { PINO_LOGGER_OPTIONS } from '@experience/shared/nest/utils';
import { PcbSwapsModule } from '../pcb-swaps/pcb-swaps.module';
import { ScheduleModule } from '@nestjs/schedule';
import { VersionModule } from '@experience/shared/nest/version-module';
import { validate } from '../config/env.validate';

@Module({
  providers: [
    ConfigService,
    AccountService,
    {
      inject: [ConfigService],
      provide: AccountApi,
      useFactory: async (configService: ConfigService) =>
        new AccountApi(
          new Configuration({
            basePath: configService.get('INSTALLER_API_BASE_URL'),
          })
        ),
    },
  ],
  imports: [
    ClsModule.forRoot({
      global: true,
      middleware: {
        mount: false,
      },
    }),
    I18nModule.forRoot({
      fallbackLanguage: 'en',
      loaderOptions: {
        path: './assets/installer-bff/i18n/',
        watch: true,
      },
      resolvers: [
        { use: QueryResolver, options: ['lang'] },
        AcceptLanguageResolver,
      ],
    }),
    ConfigModule.forRoot({ validate: validate }),
    LoggerModule.forRoot(PINO_LOGGER_OPTIONS),
    ScheduleModule.forRoot(),
    AccountModule,
    AppointmentModule,
    AuthActionsModule,
    CheckForUpgradeModule,
    HealthModule,
    HelloModule,
    InstallImagesModule,
    InstallsModule,
    PcbSwapsModule,
    VersionModule,
  ],
})
export class AppModule implements NestModule {
  configure(consumer: MiddlewareConsumer) {
    consumer.apply(ClsMiddleware, AppRequestLanguageMiddleware).forRoutes('*');
  }
}
