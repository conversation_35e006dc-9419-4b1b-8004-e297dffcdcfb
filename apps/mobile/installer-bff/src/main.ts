import * as Sentry from '@sentry/node';
import { API_VERSION } from './constants';
import { AppModule } from './app/app.module';
import { DocumentBuilder } from '@nestjs/swagger';
import { INestApplication, VersioningType } from '@nestjs/common';
import { NodeSDK } from '@opentelemetry/sdk-node';
import { OTLPTraceExporter } from '@opentelemetry/exporter-trace-otlp-http';
import { Resource } from '@opentelemetry/resources';
import { SentryPropagator, SentrySpanProcessor } from '@sentry/opentelemetry';
import { bootstrap } from '@experience/shared/nest/utils';
import { getNodeAutoInstrumentations } from '@opentelemetry/auto-instrumentations-node';
import { getSwaggerDocs } from './swagger/swagger';
import { useContainer } from 'class-validator';
import axios from 'axios';

axios.defaults.headers.common[
  'User-Agent'
] = `installer-bff/${process.env.APPLICATION_VERSION} (mobile)`;

void bootstrap({
  module: AppModule,
  port: 5106,
  protectSwagger: true,
  versioningOptions: { defaultVersion: API_VERSION, type: VersioningType.URI },
  preCallback: () => {
    Sentry.init({
      enabled: ['dev', 'stage', 'prod'].includes(process.env.ENVIRONMENT),
      environment: process.env.ENVIRONMENT,
      release: process.env.SENTRY_RELEASE,
      dsn: process.env.SENTRY_DSN,
      tracesSampleRate: 0.01, // 1% of traffic sampled
      ignoreTransactions: ['/health'],
    });

    const sdk = new NodeSDK({
      resource: new Resource({
        'service.name': 'installer-bff',
        'service.version': process.env.APPLICATION_VERSION,
      }),

      // Existing config
      traceExporter: new OTLPTraceExporter(),
      instrumentations: [getNodeAutoInstrumentations()],

      // Sentry config
      spanProcessors: [new SentrySpanProcessor()],
      textMapPropagator: new SentryPropagator(),
    });

    sdk.start();
  },
  callback: (app: INestApplication, docBuilder: DocumentBuilder) => {
    useContainer(app.select(AppModule), { fallbackOnErrors: true });

    return getSwaggerDocs(docBuilder);
  },
});
