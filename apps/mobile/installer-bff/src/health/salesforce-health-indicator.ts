import {
  HealthCheckError,
  HealthIndicator,
  HealthIndicatorResult,
} from '@nestjs/terminus';
import { Injectable, Logger } from '@nestjs/common';
import { SalesforceClient } from '@experience/shared/salesforce/client';

@Injectable()
export class SalesforceHealthIndicator extends HealthIndicator {
  private readonly logger = new Logger(SalesforceHealthIndicator.name);

  constructor(private readonly salesforceClient: SalesforceClient) {
    super();
  }

  async isHealthy(key: string): Promise<HealthIndicatorResult> {
    let isHealthy = true;

    try {
      await this.salesforceClient.get('/services/data/v63.0/sobjects/');
    } catch (error) {
      this.logger.error('Salesforce health check failed', error);

      isHealthy = false;
    }

    const result = this.getStatus(key, isHealthy);

    if (isHealthy) {
      return result;
    }

    throw new HealthCheckError('Salesforce check failed', result);
  }
}
