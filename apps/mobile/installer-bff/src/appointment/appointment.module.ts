import { AcceptLanguageResolver, I18nModule, QueryResolver } from 'nestjs-i18n';
import { AccountModule } from '../account/account.module';
import { AppointmentController } from './appointment.controller';
import { AppointmentService } from './appointment.service';
import { AuthorisationModule } from '../authorisation/authorisation.module';
import { ConfigService } from '@nestjs/config';
import { FlagsModule } from '@experience/shared/nest/remote-config';
import { Module } from '@nestjs/common';
import { SalesforceClient } from '@experience/shared/salesforce/client';

@Module({
  imports: [
    I18nModule.forRoot({
      fallbackLanguage: 'en',
      loaderOptions: {
        path: './assets/installer-bff/i18n/',
        watch: true,
      },
      resolvers: [
        { use: QueryResolver, options: ['lang'] },
        AcceptLanguageResolver,
      ],
    }),
    AuthorisationModule,
    AccountModule,
    FlagsModule,
  ],
  controllers: [AppointmentController],
  providers: [
    ConfigService,
    AppointmentService,
    {
      inject: [ConfigService],
      provide: SalesforceClient,
      useFactory: async (configService: ConfigService) =>
        new SalesforceClient({
          audience: configService.getOrThrow(
            'SALESFORCE_AUTH_AUDIENCE'
          ) as string,
          clientId: configService.getOrThrow(
            'SALESFORCE_AUTH_CLIENT_ID'
          ) as string,
          instanceUrl: configService.getOrThrow(
            'SALESFORCE_INSTANCE_URL'
          ) as string,
          privateKey: configService
            .getOrThrow('SALESFORCE_AUTH_PRIVATE_KEY')
            .replace(/\\n/g, '\n'),
          username: configService.getOrThrow(
            'SALESFORCE_AUTH_USERNAME'
          ) as string,
          userAgent: 'PodPoint-InstallerBff',
          maxRetry: 5,
        }),
    },
  ],
})
export class AppointmentModule {}
