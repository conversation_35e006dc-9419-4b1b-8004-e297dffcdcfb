import { API_VERSION } from '../constants';
import { DocumentBuilder } from '@nestjs/swagger';

export const getSwaggerDocs = (docBuilder?: DocumentBuilder) => {
  const dBuilder = docBuilder ?? new DocumentBuilder();
  return dBuilder
    .setTitle('Installer BFF')
    .setDescription('Backend for Frontend for the installer app')
    .setVersion(API_VERSION)
    .addBearerAuth()
    .addTag(
      'Check For Upgrade',
      'API to return whether an app version needs to be upgraded or not'
    )
    .addTag('Account', 'API for administration of user profiles')
    .addTag('Installs', 'API for administration of an installation');
};
