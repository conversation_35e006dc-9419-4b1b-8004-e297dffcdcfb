apiVersion: backstage.io/v1alpha1
kind: Component
metadata:
  name: notifications-api
  title: Notifications API
  description: API providing notifications functionality
  annotations:
    github.com/project-slug: Pod-Point/destination
    backstage.io/techdocs-ref: dir:.
  tags:
    - nestjs
spec:
  type: service
  owner: group:tfm-experience-mobile
  lifecycle: production
  providesApis:
    - notifications-api
---
apiVersion: backstage.io/v1alpha1
kind: API
metadata:
  name: notifications-api
  description: |
    Service providing notifications functionality
spec:
  type: openapi
  owner: group:tfm-experience-mobile
  lifecycle: production
  definition:
    $text: ../../../libs/mobile/notifications-api/contract/openapi3.yaml
