import { AccountTransactionDTO } from './account-transaction.dto';
import { AccountTransactionsDto } from '../../../application/dto/account-transactions.dto';
import { AccountTransactionsMetaHttpDto } from './account-transactions-meta.dto';
import { ApiProperty } from '@nestjs/swagger';

export class AccountTransactionsHttpDto {
  static from(dto: AccountTransactionsDto): AccountTransactionsHttpDto {
    return Object.assign(
      new AccountTransactionsHttpDto(),
      dto satisfies AccountTransactionsDto
    );
  }

  @ApiProperty({
    description: 'The transactions associated with the account',
    type: [AccountTransactionDTO],
  })
  transactions: AccountTransactionDTO[];

  @ApiProperty({
    description: 'Metadata associated with the response',
    type: AccountTransactionsMetaHttpDto,
  })
  meta: AccountTransactionsMetaHttpDto;
}
