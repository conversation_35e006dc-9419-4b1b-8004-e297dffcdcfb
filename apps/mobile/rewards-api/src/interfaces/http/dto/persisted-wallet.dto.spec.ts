import { PersistedWalletHttpDto } from './persisted-wallet.dto';
import {
  WalletEntity,
  WalletEntityType,
} from '../../../domain/entities/wallet.entity';
import { describe, expect, it } from '@jest/globals';
import { v4 } from 'uuid';

describe(PersistedWalletHttpDto.name, () => {
  it('returns an instance of PersistedWalletHttpDto', () => {
    const entity = new WalletEntity({
      id: v4(),
      type: WalletEntityType.POD_DRIVE,
      userId: v4(),
      subscriptionId: null,
      createdAt: new Date(),
      updatedAt: new Date(),
      deletedAt: null,
    });

    expect(PersistedWalletHttpDto.from(entity)).toBeInstanceOf(
      PersistedWalletHttpDto
    );
  });

  it('returns correct shape', () => {
    const entity = new WalletEntity({
      id: v4(),
      type: WalletEntityType.POD_DRIVE,
      userId: v4(),
      subscriptionId: null,
      createdAt: new Date(),
      updatedAt: new Date(),
      deletedAt: null,
    });

    expect(PersistedWalletHttpDto.from(entity)).toEqual({
      id: entity.id,
      type: entity.type,
      subscriptionId: entity.subscriptionId,
    });
  });
});
