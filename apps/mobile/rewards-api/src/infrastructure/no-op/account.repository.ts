import {
  AccountEntity,
  UnpersistedAccountEntity,
} from '../../domain/entities/account.entity';
import { AccountRepositoryInterface } from '../../domain/repositories/account.repository.interface';
import { FatalApplicationError } from '../../application/errors/fatal-application.error';
import { v4 } from 'uuid';

export class NoOpAccountRepository implements AccountRepositoryInterface {
  async create(entity: UnpersistedAccountEntity): Promise<AccountEntity> {
    return new AccountEntity({
      id: v4(),
      rewardWalletId: entity.rewardWalletId,
      type: entity.type,
      createdAt: new Date(),
      updatedAt: new Date(),
      deletedAt: null,
    });
  }
  async read(): Promise<AccountEntity | null> {
    return null;
  }

  async getAllowanceAccount(): Promise<AccountEntity> {
    throw new FatalApplicationError('NoOp not allowed');
  }

  async getSystemAccount(): Promise<AccountEntity> {
    throw new FatalApplicationError('NoOp not allowed');
  }

  async getRewardsAccount(): Promise<AccountEntity> {
    throw new FatalApplicationError('NoOp not allowed');
  }
}
