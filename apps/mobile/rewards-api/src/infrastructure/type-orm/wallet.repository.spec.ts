import {
  CleanLogger,
  FatalRepositoryError,
  Transformer,
} from '@experience/mobile/clean-architecture';
import { ClsService } from 'nestjs-cls';
import { EntityManager, UpdateResult } from 'typeorm';
import {
  MOCK_USER_ID,
  MOCK_WALLET_ENTITY,
} from '../../domain/entities/__fixtures__/wallet.entity.fixtures';
import { MOCK_WALLET } from '../__fixtures__/wallet.fixtures';
import { RewardsWallet } from '@experience/mobile/driver-account/database';
import { TypeOrmWalletRepository } from './wallet.repository';
import {
  UnpersistedWalletEntity,
  WalletEntity,
  WalletEntityType,
} from '../../domain/entities/wallet.entity';
import { beforeEach, describe, expect, it, jest } from '@jest/globals';
import { createMock } from '@golevelup/ts-jest';
import { mockEntityManagerCreateOnce } from '@experience/mobile/nest/typeorm-transactions';
import { v4 } from 'uuid';

describe(TypeOrmWalletRepository.name, () => {
  let mockedEntityManager: jest.Mocked<EntityManager>;
  let mockedClsService: jest.Mocked<ClsService>;
  let mockedTransformer: jest.Mocked<Transformer<RewardsWallet, WalletEntity>>;
  let mockedLogger: jest.Mocked<CleanLogger>;
  let repository: TypeOrmWalletRepository;

  beforeEach(() => {
    mockedEntityManager = createMock();
    mockedClsService = createMock();
    mockedLogger = createMock();
    mockedTransformer = createMock();

    repository = new TypeOrmWalletRepository(
      mockedLogger,
      mockedTransformer,
      mockedEntityManager,
      mockedClsService
    );

    jest.resetAllMocks();

    mockedClsService.get.mockReturnValue(mockedEntityManager);
  });

  describe(TypeOrmWalletRepository.prototype.read, () => {
    it('returns null', async () => {
      mockedEntityManager.findOne.mockResolvedValueOnce(null);

      const actual = await repository.read('foo');

      expect(actual).toBeNull();
    });

    it('calls the transformer with the output of the repository', async () => {
      const ormAccount = new RewardsWallet();
      mockedEntityManager.findOne.mockResolvedValueOnce(ormAccount);

      await repository.read('foo');

      expect(mockedTransformer.transform).toHaveBeenCalledWith(ormAccount);
    });

    it('returns the output of the transformer', async () => {
      const ormAccount = new RewardsWallet();
      mockedEntityManager.findOne.mockResolvedValueOnce(ormAccount);

      const expected = new WalletEntity({
        id: v4(),
        type: WalletEntityType.POD_DRIVE,
        subscriptionId: v4(),
        userId: v4(),
        createdAt: new Date(),
        updatedAt: new Date(),
        deletedAt: null,
      });

      mockedTransformer.transform.mockReturnValueOnce(expected);

      const actual = await repository.read('foo');

      expect(actual).toBe(expected);
    });
  });

  describe(TypeOrmWalletRepository.prototype.create, () => {
    it('calls create', async () => {
      const unpersistedWalletEntity = new UnpersistedWalletEntity({
        type: WalletEntityType.POD_DRIVE,
        subscriptionId: v4(),
        userId: v4(),
      });

      mockEntityManagerCreateOnce(mockedEntityManager, {
        id: v4(),
        type: unpersistedWalletEntity.type,
        subscriptionId: unpersistedWalletEntity.subscriptionId,
        userId: unpersistedWalletEntity.userId,
        createdAt: new Date(),
        updatedAt: new Date(),
        deletedAt: null,
      });

      await repository.create(unpersistedWalletEntity);

      expect(mockedEntityManager.create).toHaveBeenCalledTimes(1);
      expect(mockedEntityManager.create).toHaveBeenCalledWith(RewardsWallet, {
        type: unpersistedWalletEntity.type,
        userId: unpersistedWalletEntity.userId,
        subscriptionId: unpersistedWalletEntity.subscriptionId,
      });
    });

    it('calls save', async () => {
      const unpersistedWalletEntity = new UnpersistedWalletEntity({
        type: WalletEntityType.POD_DRIVE,
        subscriptionId: v4(),
        userId: v4(),
      });

      const expected = {
        id: v4(),
        type: unpersistedWalletEntity.type,
        subscriptionId: unpersistedWalletEntity.subscriptionId,
        createdAt: new Date(),
        updatedAt: new Date(),
        deletedAt: null,
      } as RewardsWallet;

      mockEntityManagerCreateOnce(mockedEntityManager, expected);

      await repository.create(unpersistedWalletEntity);

      expect(mockedEntityManager.save).toHaveBeenCalledTimes(1);
      expect(mockedEntityManager.save).toHaveBeenCalledWith(
        RewardsWallet,
        expected
      );
    });

    it('calls the transformer with the output of the repository', async () => {
      const unpersistedWalletEntity = new UnpersistedWalletEntity({
        type: WalletEntityType.POD_DRIVE,
        subscriptionId: v4(),
        userId: v4(),
      });

      const expected = {
        id: v4(),
        type: unpersistedWalletEntity.type,
        subscriptionId: unpersistedWalletEntity.subscriptionId,
        userId: unpersistedWalletEntity.userId,
        createdAt: new Date(),
        updatedAt: new Date(),
        deletedAt: null,
      } as RewardsWallet;

      mockedEntityManager.save.mockResolvedValueOnce(expected);

      await repository.create(unpersistedWalletEntity);

      expect(mockedTransformer.transform).toHaveBeenCalledWith(expected);
    });

    it('returns the output of the transformer', async () => {
      const unpersistedWalletEntity = new UnpersistedWalletEntity({
        type: WalletEntityType.POD_DRIVE,
        subscriptionId: v4(),
        userId: v4(),
      });

      const expected = new WalletEntity({
        id: v4(),
        type: unpersistedWalletEntity.type,
        subscriptionId: unpersistedWalletEntity.subscriptionId,
        userId: unpersistedWalletEntity.userId,
        createdAt: new Date(),
        updatedAt: new Date(),
        deletedAt: null,
      });

      mockedTransformer.transform.mockReturnValueOnce(expected);

      const actual = await repository.create(unpersistedWalletEntity);

      expect(actual).toBe(expected);
    });

    it('throws CouldNotPersistEntityError if save fails', async () => {
      const unpersistedWalletEntity = new UnpersistedWalletEntity({
        type: WalletEntityType.POD_DRIVE,
        subscriptionId: v4(),
        userId: v4(),
      });

      mockedEntityManager.save.mockRejectedValueOnce(new Error('foo'));

      await expect(
        repository.create(unpersistedWalletEntity)
      ).rejects.toThrow();
    });
  });

  describe(TypeOrmWalletRepository.prototype.getByUserId, () => {
    it('returns null if no wallet found for user', async () => {
      mockedEntityManager.findOneBy.mockResolvedValue(null);

      const result = await repository.getByUserId('random');

      expect(result).toBeNull();

      expect(mockedEntityManager.findOneBy).toHaveBeenCalledTimes(1);
      expect(mockedEntityManager.findOneBy).toHaveBeenCalledWith(
        RewardsWallet,
        {
          userId: 'random',
        }
      );
    });

    it("returns the user's wallet", async () => {
      mockedEntityManager.findOneBy.mockResolvedValue(MOCK_WALLET);
      mockedTransformer.transform.mockReturnValue(MOCK_WALLET_ENTITY);

      const result = await repository.getByUserId(MOCK_USER_ID);

      expect(result).toStrictEqual(MOCK_WALLET_ENTITY);

      expect(mockedEntityManager.findOneBy).toHaveBeenCalledTimes(1);
      expect(mockedEntityManager.findOneBy).toHaveBeenCalledWith(
        RewardsWallet,
        {
          userId: MOCK_USER_ID,
        }
      );

      expect(mockedTransformer.transform).toHaveBeenCalledTimes(1);
      expect(mockedTransformer.transform).toHaveBeenCalledWith(
        (await mockedEntityManager.findOneBy.mock.results[0]
          .value) as RewardsWallet
      );
    });

    it('throws a FatalRepositoryError if something goes wrong', async () => {
      mockedEntityManager.findOneBy.mockRejectedValue(new Error());

      await expect(() => repository.getByUserId(MOCK_USER_ID)).rejects.toThrow(
        FatalRepositoryError
      );

      expect(mockedEntityManager.findOneBy).toHaveBeenCalledTimes(1);
      expect(mockedEntityManager.findOneBy).toHaveBeenCalledWith(
        RewardsWallet,
        {
          userId: MOCK_USER_ID,
        }
      );

      expect(mockedTransformer.transform).not.toHaveBeenCalled();
    });
  });

  describe(TypeOrmWalletRepository.prototype.update, () => {
    const NEW_SUBSCRIPTION_ID = '293b1f04-b859-4658-b723-e9d2cb8d735b';

    it('updates the entity data', async () => {
      mockedEntityManager.update.mockResolvedValue(createMock<UpdateResult>());

      const updated = await repository.update(MOCK_WALLET_ENTITY, {
        subscriptionId: NEW_SUBSCRIPTION_ID,
      });

      expect(updated).toBeInstanceOf(WalletEntity);
      expect(updated.subscriptionId).toEqual(NEW_SUBSCRIPTION_ID);

      expect(mockedEntityManager.update).toHaveBeenCalledTimes(1);
      expect(mockedEntityManager.update).toHaveBeenCalledWith(
        RewardsWallet,
        {
          id: MOCK_WALLET.id,
        },
        {
          subscriptionId: NEW_SUBSCRIPTION_ID,
        }
      );
    });

    it('throws a FatalRepositoryError if unable to update', async () => {
      mockedEntityManager.update.mockRejectedValue(new Error());

      await expect(() =>
        repository.update(MOCK_WALLET_ENTITY, {
          subscriptionId: NEW_SUBSCRIPTION_ID,
        })
      ).rejects.toThrow(FatalRepositoryError);

      expect(mockedEntityManager.update).toHaveBeenCalledTimes(1);
      expect(mockedEntityManager.update).toHaveBeenCalledWith(
        RewardsWallet,
        {
          id: MOCK_WALLET.id,
        },
        {
          subscriptionId: NEW_SUBSCRIPTION_ID,
        }
      );
    });
  });

  describe(TypeOrmWalletRepository.prototype.getByType, () => {
    it('return an array of wallets', async () => {
      mockedEntityManager.find.mockResolvedValueOnce([MOCK_WALLET]);
      mockedTransformer.transform.mockReturnValue(MOCK_WALLET_ENTITY);

      expect(await repository.getByType(WalletEntityType.POD_DRIVE)).toEqual([
        MOCK_WALLET_ENTITY,
      ]);
      expect(mockedEntityManager.find).toHaveBeenCalledWith(RewardsWallet, {
        where: { type: WalletEntityType.POD_DRIVE },
        take: 100,
      });
    });
  });
});
