import {
  AccountEntity,
  AccountEntityType,
} from '../../domain/entities/account.entity';
import { BalanceDto } from './balance.dto';
import { TransactionEntitiesToBalanceDtoTransformer } from './transformer/transaction-entities-to-balance-dto.transformer';
import { TransactionEntity } from '../../domain/entities/transaction.entity';

export class AccountDetailsDto {
  static from(
    account: AccountEntity,
    transactions: TransactionEntity[]
  ): AccountDetailsDto {
    const balanceTransformer = new TransactionEntitiesToBalanceDtoTransformer();

    return Object.assign(new AccountDetailsDto(), {
      id: account.id,
      type: account.type,
      balance: balanceTransformer.transform(transactions),
    } satisfies AccountDetailsDto);
  }

  id: string;
  type: AccountEntityType;
  balance: BalanceDto;
}
