import { AccountEntityType } from '../../domain/entities/account.entity';
import { AccountService } from './account.service';
import {
  CleanLogger,
  CouldNotFindEntityError,
} from '@experience/mobile/clean-architecture';
import { DependencyInjectionToken } from '../../modules/constants';
import { FatalApplicationError } from '../errors/fatal-application.error';
import { Inject, forwardRef } from '@nestjs/common';
import { SubscriptionService } from './subscription.service';
import { TransactionProviderInterface } from '@experience/mobile/clean-architecture';
import { UpsertWalletDTO } from '../dto/upsert-wallet.dto';
import { WalletBalanceSummaryDTO } from '../dto/wallet-balance-summary.dto';
import {
  WalletEntity,
  WalletEntityType,
} from '../../domain/entities/wallet.entity';
import { WalletRepositoryInterface } from '../../domain/repositories/wallet.repository.interface';

export class WalletService {
  constructor(
    @Inject(DependencyInjectionToken.WALLET_SERVICE_LOGGER)
    private readonly logger: CleanLogger,
    @Inject(DependencyInjectionToken.WALLETS_REPOSITORY)
    private readonly walletRepository: WalletRepositoryInterface,
    @Inject(DependencyInjectionToken.TRANSACTION_PROVIDER)
    private readonly transactionProvider: TransactionProviderInterface,
    @Inject(forwardRef(() => AccountService))
    private readonly accountService: AccountService,
    @Inject(SubscriptionService)
    private readonly subscriptionService: SubscriptionService
  ) {}

  async read(id: string): Promise<WalletEntity | null> {
    this.logger.debug({ id }, 'getting wallet by id');
    return this.walletRepository.read(id);
  }

  async readByUserId(id: string): Promise<WalletEntity | null> {
    this.logger.debug({ id }, 'getting wallet by user id');
    return this.walletRepository.getByUserId(id);
  }

  async readByUserIdOrFail(id: string): Promise<WalletEntity> {
    const wallet = await this.readByUserId(id);
    if (!wallet) {
      this.logger.error({ id }, 'wallet not found');
      throw new CouldNotFindEntityError({ id }, 'wallet not found');
    }

    return wallet;
  }

  async getWalletBalanceSummary(
    userId: string
  ): Promise<WalletBalanceSummaryDTO> {
    const allowance = await this.accountService.getAccountDetails(
      userId,
      AccountEntityType.ALLOWANCE
    );
    const rewards = await this.accountService.getAccountDetails(
      userId,
      AccountEntityType.REWARDS
    );
    const { plan } = await this.subscriptionService.getSubscriptionByUserId(
      userId
    );

    const balanceGbp =
      Math.ceil(rewards.balance.amount * plan.ratePencePerMile) / 100;

    return {
      allowance: {
        balanceMiles: allowance.balance.amount,
        annualAllowanceMiles: plan.allowanceMiles,
      },
      rewards: {
        balanceMiles: rewards.balance.amount,
        balanceGbp,
      },
    };
  }

  async upsertWallet(payload: UpsertWalletDTO): Promise<WalletEntity> {
    this.logger.log({ userId: payload.userId }, 'attempting to upsert wallet');

    const subscriptionPlan =
      await this.subscriptionService.getSubscriptionPlanById(
        payload.subscriptionId
      );

    return this.transactionProvider.withTransaction(async () => {
      let wallet = await this.walletRepository.getByUserId(payload.userId);

      if (!wallet) {
        this.logger.log(
          { userId: payload.userId },
          'wallet does not exist. creating...'
        );

        wallet = await this.walletRepository.create(payload);

        this.logger.log(
          { userId: payload.userId, walletId: wallet.id },
          'created wallet. adding default accounts...'
        );

        await this.accountService.createDefaultUserAccounts(
          wallet.id,
          subscriptionPlan
        );

        this.logger.log(
          { userId: payload.userId, walletId: wallet.id },
          'added default accounts to wallet'
        );
      } else {
        this.logger.log(
          { userId: payload.userId, walletId: wallet.id },
          'wallet already exists. updating'
        );

        wallet = await this.walletRepository.update(wallet, {
          type: payload.type,
          subscriptionId: payload.subscriptionId,
        });
      }

      return wallet;
    });
  }

  async getSystemWallet() {
    const wallets = await this.walletRepository.getByType(
      WalletEntityType.SYSTEM
    );

    if (wallets.length > 1) {
      this.logger.error(
        { wallets: wallets.map(({ id }) => id) },
        'multiple system wallets found. this should not happen'
      );

      throw new FatalApplicationError('multiple system wallets found');
    }

    if (wallets.length === 0) {
      this.logger.error({}, 'no system wallet found. this should not happen');

      throw new CouldNotFindEntityError(
        { type: WalletEntityType.SYSTEM },
        'no system wallet found'
      );
    }

    return wallets[0];
  }
}
