networks:
  pod-point:
    name: pod-point
services:
  support-tool-api:
    build:
      context: ../../../..
      dockerfile: apps/support/support-tool/api/Dockerfile
    container_name: support-tool-api
    deploy:
      resources:
        limits:
          cpus: '1'
          memory: 2G
    env_file: .env.local
    networks:
      - pod-point
    platform: linux/amd64
    ports:
      - '7102:7102'
