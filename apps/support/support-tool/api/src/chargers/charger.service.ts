import {
  API3ChargeSchedule,
  ChargeOverrideResponse,
  DelegatedControlChargingStationWithDeletableResponseDto,
  ExtendedVehicleLinkResponseDto,
  ChargeOverridesApi as SmartChargingServiceClientChargeOverridesApi,
  ChargeSchedulesAPI3Api as SmartChargingServiceClientChargeSchedulesAPI3Api,
  DelegatedControlChargingStationsApi as SmartChargingServiceClientDelegatedControlChargingStationsApi,
  DelegatedControlVehiclesApi as SmartChargingServiceClientVehiclesApi,
} from '@experience/shared/axios/smart-charging-service-client';
import {
  AssetSummaryApi as AssetsApiClientAssetSummaryApi,
  ChargingStationSummary,
} from '@experience/shared/axios/assets-api-client';
import {
  WifiCredentialsApi as AssetsProvisioningApiWifiCredentialsApi,
  GetWifiCredentials200Response,
} from '@experience/shared/axios/assets-provisioning-api-client';
import { Charger } from '@experience/support/support-tool/shared';
import { ChargerChargesService } from './charges/charger-charges.service';
import { ChargerConfigService } from './config/charger-config.service';
import {
  ChargingStationProgrammesResponse,
  ChargingStationsApi as CompetitionsServiceClientCompetitionApi,
} from '@experience/shared/axios/competitions-service-client';
import {
  Charger as CommercialAttributes,
  ChargerApi as SiteAdminChargerApi,
} from '@experience/shared/axios/internal-site-admin-api-client';
import {
  ConnectivityStatusApi as ConnectivityServiceClientConnectivityStatusApi,
  DataStatusResponse,
} from '@experience/shared/axios/connectivity-service-client';
import { Door } from '@experience/shared/axios/assets-configuration-api-client';
import {
  UsersApi as DriverAccountApiClientUsersApi,
  UserInfoResponseDto,
} from '@experience/driver-account-api/api-client';
import {
  FirmwareStatusType,
  InfoApi as FirmwareUpgradeClientInfoApi,
} from '@experience/shared/axios/firmware-upgrade-client';
import { Injectable, Logger, NotFoundException } from '@nestjs/common';
import {
  InstallResponse,
  InstallsApi as InstallerApiInstallsApi,
} from '@experience/installer/api/axios';
import { OidcRoles } from '@experience/shared/typescript/oidc-utils';
import { OidcUser } from '@experience/shared/nest/utils';
import {
  PersistedTariffRowDto,
  SupplierDto,
  ChargingStationsTariffsApi as TariffsChargingStationTariffsApi,
  SuppliersApi as TariffsSuppliersApi,
} from '@experience/shared/axios/tariffs-api-client';
import {
  SalesforceAsset,
  SalesforceClient,
} from '@experience/shared/salesforce/client';
import { SubscriptionsApi } from '@experience/mobile/subscriptions-api/axios';
import { sort } from 'fast-sort';
import dayjs from 'dayjs';

const DEFAULT_CHARGER_REGION = 'UK';

type ChargerKey = keyof Charger;
type ChargerValue = () => Promise<Charger[ChargerKey] | undefined>;

@Injectable()
export class ChargerService {
  private readonly logger = new Logger(ChargerService.name);

  constructor(
    private assetSummaryApi: AssetsApiClientAssetSummaryApi,
    private chargerChargesService: ChargerChargesService,
    private chargerConfigService: ChargerConfigService,
    private chargeOverridesApi: SmartChargingServiceClientChargeOverridesApi,
    private chargeSchedulesApi3Api: SmartChargingServiceClientChargeSchedulesAPI3Api,
    private competitionApi: CompetitionsServiceClientCompetitionApi,
    private connectivityStatusApi: ConnectivityServiceClientConnectivityStatusApi,
    private delegatedControlApi: SmartChargingServiceClientDelegatedControlChargingStationsApi,
    private firmwareInfoApi: FirmwareUpgradeClientInfoApi,
    private installsApi: InstallerApiInstallsApi,
    private salesforceClient: SalesforceClient,
    private siteAdminApi: SiteAdminChargerApi,
    private subscriptionsApi: SubscriptionsApi,
    private tariffsSuppliersApi: TariffsSuppliersApi,
    private tariffsChargingStationsApi: TariffsChargingStationTariffsApi,
    private usersApi: DriverAccountApiClientUsersApi,
    private vehiclesApi: SmartChargingServiceClientVehiclesApi,
    private wifiCredentialsApi: AssetsProvisioningApiWifiCredentialsApi
  ) {}

  async findByPpid(
    ppid: string,
    socket: Door,
    redact: boolean,
    user: OidcUser
  ): Promise<Charger> {
    const context = { ppid, redact, user };
    this.logger.log(context, 'getting details');

    const summary = await this.getSummary(ppid, user);

    if (!summary) {
      throw new NotFoundException();
    }

    if (!this.canUserViewCharger(user, summary)) {
      this.logger.log({ context }, 'user does not have access to this charger');
      throw new NotFoundException();
    }

    const chargeHistory = await this.chargerChargesService.getRecentCharges(
      summary.ppid,
      user,
      dayjs().subtract(1, 'month').format('YYYY-MM-DD')
    );
    const chargeOverrides = await this.getChargeOverrides(summary.ppid, user);
    const chargeSchedules = await this.getChargeSchedules(summary.ppid, user);
    const chargingStationProgrammes = await this.getChargingStationProgrammes(
      summary.ppid,
      user
    );
    const configuration = await this.chargerConfigService.getConfiguration(
      summary.ppid,
      socket,
      redact,
      user
    );
    const connectivityStatus = await this.getConnectivityStatus(
      summary.ppid,
      user
    );
    const commercialAttributes = await this.getCommercialAttributes(
      summary.ppid,
      user
    );
    const currentFirmware = await this.getCurrentFirmware(summary.ppid, user);
    const delegatedControlStatus = await this.getDelegatedControlStatus(
      summary.ppid,
      user
    );
    const installationDetails = await this.getInstallationDetails(
      summary.ppid,
      user
    );
    const linkedUsers = await this.getLinkedUsers(summary.ppid, user);
    const name = await this.getChargerName(
      summary.ppid,
      user,
      commercialAttributes
    );
    const salesforceAsset = await this.getSalesforceAsset(summary.ppid, user);
    const subscriptions = await this.getSubscriptions(summary.ppid, user);
    const tariffSuppliers = await this.getTariffSuppliers(user);
    const tariffs = await this.getTariffs(summary.ppid, user);
    const vehicles = await this.getVehicles(summary.ppid, user);
    const wifiCredentials = await this.getWifiCredentials(
      summary.ppid,
      redact,
      user
    );

    return {
      chargeHistory,
      chargeOverrides,
      chargeSchedules,
      chargingStationProgrammes,
      commercialAttributes,
      configuration,
      connectivityStatus,
      currentFirmware,
      delegatedControlStatus,
      installationDetails,
      linkedUsers,
      name,
      salesforceAsset,
      subscriptions,
      tariffSuppliers,
      tariffs,
      vehicles,
      wifiCredentials,
      summary,
    };
  }

  async findChargerAttributesByPpid(
    ppid: string,
    user: OidcUser,
    fields: ChargerKey[] = ['summary']
  ): Promise<Charger> {
    const context = { ppid, user, fields };
    this.logger.log(context, 'getting details');

    const fieldSet = new Set(fields);

    const fieldMap: Record<ChargerKey, ChargerValue> = {
      chargeHistory: () =>
        this.chargerChargesService.getRecentCharges(
          ppid,
          user,
          dayjs().subtract(1, 'month').format('YYYY-MM-DD')
        ),
      chargeOverrides: () => this.getChargeOverrides(ppid, user),
      chargeSchedules: () => this.getChargeSchedules(ppid, user),
      chargingStationProgrammes: () =>
        this.getChargingStationProgrammes(ppid, user),
      currentFirmware: () => this.getCurrentFirmware(ppid, user),
      commercialAttributes: () => this.getCommercialAttributes(ppid, user),
      configuration: () =>
        this.chargerConfigService.getConfiguration(ppid, 'A', false, user),
      connectivityStatus: () => this.getConnectivityStatus(ppid, user),
      delegatedControlStatus: () => this.getDelegatedControlStatus(ppid, user),
      installationDetails: () => this.getInstallationDetails(ppid, user),
      linkedUsers: () => this.getLinkedUsers(ppid, user),
      name: () => this.getChargerName(ppid, user),
      salesforceAsset: () => this.getSalesforceAsset(ppid, user),
      subscriptions: () => this.getSubscriptions(ppid, user),
      summary: () => this.getSummary(ppid, user),
      tariffSuppliers: () => this.getTariffSuppliers(user),
      tariffs: () => this.getTariffs(ppid, user),
      vehicles: () => this.getVehicles(ppid, user),
      wifiCredentials: (redact = true) =>
        this.getWifiCredentials(ppid, redact, user),
    };

    const results = await Promise.all(
      Object.keys(fieldMap)
        .filter((key) => fieldSet.has(key as keyof Charger))
        .map(async (key) => {
          const field = key as keyof Charger;
          const value = await fieldMap[field]();
          return [field, value];
        })
    );

    return Object.fromEntries(results);
  }

  async getSummary(
    ppid: string,
    user: OidcUser
  ): Promise<ChargingStationSummary | undefined> {
    const context = { ppid, user };
    this.logger.log(context, 'getting summary');

    const summary = await this.assetSummaryApi
      .getChargingStation(ppid)
      .then((value) => value.data)
      .catch((error) => {
        this.logger.error({ error, ...context }, 'error getting summary');
        return undefined;
      });

    if (
      !summary &&
      ppid !== ppid.toUpperCase() &&
      ['PG', 'PSL'].includes(ppid.toUpperCase().split('-')[0])
    ) {
      return this.getSummary(ppid.toUpperCase(), user);
    }

    if (
      !summary &&
      ppid !== ppid.toLowerCase() &&
      ['veefil-', 't53_', 't54_']
        .map((prefix) => ppid.toLowerCase().startsWith(prefix))
        .includes(true)
    ) {
      return this.getSummary(ppid.toLowerCase(), user);
    }

    return summary;
  }

  private async getChargeOverrides(
    ppid: string,
    user: OidcUser
  ): Promise<ChargeOverrideResponse[] | undefined> {
    const context = { ppid, user };
    this.logger.log(context, 'getting charge overrides');

    return this.chargeOverridesApi
      .searchActiveOverrides(ppid)
      .then((value) => value.data)
      .catch((error) => {
        this.logger.error(
          { error, ...context },
          'error getting charge overrides'
        );
        return undefined;
      });
  }

  private async getChargeSchedules(
    ppid: string,
    user: OidcUser
  ): Promise<API3ChargeSchedule[] | undefined> {
    const context = { ppid, user };
    this.logger.log(context, 'getting charge schedules');

    return this.chargeSchedulesApi3Api
      .getChargingStationChargeSchedules(ppid)
      .then((value) => value.data.data)
      .catch((error) => {
        this.logger.error(
          { error, ...context },
          'error getting charge schedules'
        );
        return undefined;
      });
  }

  private async getChargerName(
    ppid: string,
    user: OidcUser,
    commercialAttributes?: CommercialAttributes
  ): Promise<string> {
    const context = { ppid, user };
    this.logger.log(context, 'getting charger name');
    if (commercialAttributes?.name) {
      return commercialAttributes.name;
    }
    return await this.siteAdminApi
      .chargerControllerFindChargerNameByPpid(ppid)
      .then((value) => value.data)
      .catch((error) => {
        this.logger.error({ error, ...context }, 'error getting charger name');
        return '';
      });
  }

  private async getCommercialAttributes(
    ppid: string,
    user: OidcUser
  ): Promise<CommercialAttributes | undefined> {
    const context = { ppid, user };
    this.logger.log(context, 'getting commercial attributes');

    return this.siteAdminApi
      .chargerControllerFindByIdentifier(ppid)
      .then((value) => value.data)
      .catch((error) => {
        this.logger.error(
          { error, ...context },
          'error getting commercial attributes'
        );
        return undefined;
      });
  }

  private async getConnectivityStatus(
    ppid: string,
    user: OidcUser
  ): Promise<DataStatusResponse | undefined> {
    const context = { ppid, user };
    this.logger.log(context, 'getting connectivity status');

    return this.connectivityStatusApi
      .getChargingStationStatus(ppid)
      .then((value) => value.data)
      .catch((error) => {
        this.logger.error(
          { error, ...context },
          'error getting connectivity status'
        );
        return undefined;
      });
  }

  private async getDelegatedControlStatus(
    ppid: string,
    user: OidcUser
  ): Promise<
    DelegatedControlChargingStationWithDeletableResponseDto | undefined
  > {
    const context = { ppid, user };
    this.logger.log(context, 'getting delegated control details');

    return this.delegatedControlApi
      .getDelegatedControlChargingStationByPpid(ppid)
      .then((value) => value.data)
      .catch((error) => {
        this.logger.error(
          { error, ...context },
          'error getting delegated control details'
        );
        return undefined;
      });
  }

  private async getCurrentFirmware(
    ppid: string,
    user: OidcUser
  ): Promise<FirmwareStatusType[] | undefined> {
    const context = { ppid, user };
    this.logger.log(context, 'getting current firmware');

    return this.firmwareInfoApi
      .getCurrentFirmware(ppid)
      .then((value) => value.data)
      .catch((error) => {
        this.logger.error(
          { error, ...context },
          'error getting current firmware'
        );
        return undefined;
      });
  }

  private async getChargingStationProgrammes(
    ppid: string,
    user: OidcUser
  ): Promise<ChargingStationProgrammesResponse | undefined> {
    const context = { ppid, user };
    this.logger.log(context, 'getting charging station programmes');

    return this.competitionApi
      .getChargingStationProgrammes(ppid, false)
      .then((value) => value.data)
      .catch((error) => {
        this.logger.error(
          { error, ...context },
          'error getting charging station programmes'
        );
        return undefined;
      });
  }

  private async getInstallationDetails(
    ppid: string,
    user: OidcUser
  ): Promise<InstallResponse[] | undefined> {
    const context = { ppid, user };
    this.logger.log(context, 'getting installation details');

    return this.installsApi
      .installsControllerGetInstall(ppid)
      .then((value) => value.data)
      .then((value) =>
        sort(value).by([
          { asc: (value) => value.socket ?? 'A' },
          { desc: (value) => value.completedAt },
        ])
      )
      .catch((error) => {
        this.logger.error(
          { error, ...context },
          'error getting installation details'
        );
        return undefined;
      });
  }

  private async getLinkedUsers(
    ppid: string,
    user: OidcUser
  ): Promise<UserInfoResponseDto[] | undefined> {
    const context = { ppid, user };
    this.logger.log(context, 'getting linked users');

    return this.usersApi
      .userControllerGetByFilter(ppid, undefined, undefined)
      .then((value) => value.data)
      .catch((error) => {
        this.logger.error({ error, ...context }, 'error getting linked users');
        return undefined;
      });
  }

  private async getSalesforceAsset(
    ppid: string,
    user: OidcUser
  ): Promise<SalesforceAsset | undefined> {
    const context = { ppid, user };
    this.logger.log(context, 'getting salesforce asset');

    return await this.salesforceClient.getAsset(ppid).catch((error) => {
      this.logger.error(
        { error, ...context },
        'error getting salesforce asset'
      );
      return undefined;
    });
  }

  private async getSubscriptions(ppid: string, user: OidcUser) {
    const context = { ppid, user };
    this.logger.log(context, 'getting subscriptions');

    return await this.subscriptionsApi
      .subscriptionsControllerSearch(undefined, ppid)
      .then((value) => value.data.subscriptions)
      .catch((error) => {
        this.logger.error({ error, ...context }, 'error getting subscriptions');
        return undefined;
      });
  }

  private async getTariffSuppliers(
    user: OidcUser
  ): Promise<SupplierDto[] | undefined> {
    const context = { user };
    this.logger.log(context, 'getting tariff suppliers');

    return this.tariffsSuppliersApi
      .getSuppliers()
      .then((value) => value.data)
      .catch((error) => {
        this.logger.error(
          { error, ...context },
          'error getting tariff suppliers'
        );
        return undefined;
      });
  }

  private async getTariffs(
    ppid: string,
    user: OidcUser
  ): Promise<PersistedTariffRowDto[] | undefined> {
    const context = { ppid, user };
    this.logger.log(context, 'getting tariffs');

    return this.tariffsChargingStationsApi
      .getTariffsByPpid(
        ppid,
        dayjs().format('YYYY-MM-DD'),
        dayjs().format('YYYY-MM-DD')
      )
      .then((value) => value.data.data)
      .catch((error) => {
        this.logger.error({ error, ...context }, 'error getting tariffs');
        return undefined;
      });
  }

  private async getVehicles(
    ppid: string,
    user: OidcUser
  ): Promise<ExtendedVehicleLinkResponseDto[] | undefined> {
    const context = { ppid, user };
    this.logger.log(context, 'getting vehicles');

    return this.vehiclesApi
      .getDelegatedControlChargingStationVehicles(ppid)
      .then((value) => value.data.data)
      .catch((error) => {
        this.logger.error({ error, ...context }, 'error getting vehicles');
        return undefined;
      });
  }

  private async getWifiCredentials(
    ppid: string,
    redact: boolean,
    user: OidcUser
  ): Promise<GetWifiCredentials200Response | undefined> {
    const context = { ppid, redact, user };
    this.logger.log(context, 'getting wifi credentials');

    return this.wifiCredentialsApi
      .getWifiCredentials(ppid)
      .then((value) => value.data)
      .then((value) => (redact ? this.redactWifiCredentials(value) : value))
      .catch((error) => {
        this.logger.error(
          { error, ...context },
          'error getting wifi credentials'
        );
        return undefined;
      });
  }

  private redactWifiCredentials(
    wifiCredentials: GetWifiCredentials200Response | undefined
  ): GetWifiCredentials200Response | undefined {
    if (!wifiCredentials) {
      return wifiCredentials;
    }
    return {
      ppid: wifiCredentials.ppid,
      ssid: wifiCredentials.ssid,
    };
  }

  private canUserViewCharger(user: OidcUser, summary: ChargingStationSummary) {
    const chargerRegion = this.getChargerRegion(summary.model?.sku);
    const allowedRoles = [
      OidcRoles.CHARGER_VIEW_ALL,
      `Charger.${chargerRegion}.ViewAll`,
    ];

    return user.roles.some((userRole) =>
      allowedRoles.includes(userRole as OidcRoles)
    );
  }

  private getChargerRegion(sku: string | undefined) {
    if (!sku) {
      return DEFAULT_CHARGER_REGION;
    }

    const extractedRegion = /-(ES|FR|UK)-/.exec(sku);
    return extractedRegion ? extractedRegion[1] : DEFAULT_CHARGER_REGION;
  }
}
