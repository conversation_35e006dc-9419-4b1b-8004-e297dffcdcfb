import { AMAZON_OIDC_DATA_HEADER } from '@experience/shared/typescript/oidc-utils';
import { ChargerLogsController } from './charger-logs.controller';
import { ChargerLogsService } from './charger-logs.service';
import { INestApplication } from '@nestjs/common';
import { TEST_GET_LOGS_RESPONSE } from '@experience/shared/axios/diagnostics-service-client/fixtures';
import {
  TEST_OIDC_DATA,
  TEST_OIDC_USER,
} from '@experience/shared/typescript/oidc-utils/specs';
import { Test, TestingModule } from '@nestjs/testing';
import request from 'supertest';

jest.mock('./charger-logs.service');

const { ppid: TEST_PPID, door: TEST_DOOR } = TEST_GET_LOGS_RESPONSE;

describe('ChargerLogsController', () => {
  let app: INestApplication;
  let controller: ChargerLogsController;
  let service: ChargerLogsService;

  beforeAll(async () => {
    const module: TestingModule = await Test.createTestingModule({
      controllers: [ChargerLogsController],
      providers: [ChargerLogsService],
    }).compile();

    controller = module.get<ChargerLogsController>(ChargerLogsController);
    service = module.get<ChargerLogsService>(ChargerLogsService);

    app = module.createNestApplication();
    await app.init();
  });

  it('should be defined', () => {
    expect(controller).toBeDefined();
  });

  it('should get logs', async () => {
    const mockGetLogs = jest
      .spyOn(service, 'getDiagnosticsLogs')
      .mockResolvedValueOnce([TEST_GET_LOGS_RESPONSE]);

    await request(app.getHttpServer())
      .get(`/chargers/${TEST_PPID}/logs/diagnostic`)
      .set(AMAZON_OIDC_DATA_HEADER, TEST_OIDC_DATA)
      .expect(200)
      .expect([TEST_GET_LOGS_RESPONSE]);

    expect(mockGetLogs).toHaveBeenCalledWith(
      TEST_PPID,
      TEST_DOOR,
      TEST_OIDC_USER
    );
  });

  it.each(['A', 'B'])('should get logs for socket %s', async (socket) => {
    const mockGetLogs = jest
      .spyOn(service, 'getDiagnosticsLogs')
      .mockResolvedValueOnce([TEST_GET_LOGS_RESPONSE]);

    await request(app.getHttpServer())
      .get(`/chargers/${TEST_PPID}/logs/diagnostic?socket=${socket}`)
      .set(AMAZON_OIDC_DATA_HEADER, TEST_OIDC_DATA)
      .expect(200)
      .expect([TEST_GET_LOGS_RESPONSE]);

    expect(mockGetLogs).toHaveBeenCalledWith(TEST_PPID, socket, TEST_OIDC_USER);
  });

  it('should download log', async () => {
    const mockDownloadLog = jest
      .spyOn(service, 'downloadDiagnosticsLog')
      .mockResolvedValueOnce();

    await request(app.getHttpServer())
      .post(`/chargers/${TEST_PPID}/logs/diagnostic`)
      .query({
        to: '2021-01-01',
        from: '2021-02-01',
      })
      .set(AMAZON_OIDC_DATA_HEADER, TEST_OIDC_DATA)
      .expect(200);

    expect(mockDownloadLog).toHaveBeenCalledWith(
      TEST_PPID,
      TEST_OIDC_USER,
      '2021-02-01',
      '2021-01-01',
      undefined
    );
  });

  it.each(['A', 'B'])('should download logs for socket %s', async (socket) => {
    const mockDownloadLog = jest
      .spyOn(service, 'downloadDiagnosticsLog')
      .mockResolvedValueOnce();

    await request(app.getHttpServer())
      .post(`/chargers/${TEST_PPID}/logs/diagnostic`)
      .query({
        to: '2021-01-01',
        from: '2021-02-01',
        socket,
      })
      .set(AMAZON_OIDC_DATA_HEADER, TEST_OIDC_DATA)
      .expect(200);

    expect(mockDownloadLog).toHaveBeenCalledWith(
      TEST_PPID,
      TEST_OIDC_USER,
      '2021-02-01',
      '2021-01-01',
      socket
    );
  });
});
