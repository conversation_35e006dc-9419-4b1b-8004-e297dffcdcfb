import { AMAZON_OIDC_DATA_HEADER } from '@experience/shared/typescript/oidc-utils';
import { AxiosResponse } from 'axios';
import {
  Charger,
  NameToPpidInterceptor,
} from '@experience/support/support-tool/shared';
import { ChargerController } from './charger.controller';
import { ChargerService } from './charger.service';
import { INestApplication } from '@nestjs/common';
import { ChargerApi as SiteAdminChargerApi } from '@experience/shared/axios/internal-site-admin-api-client';
import {
  TEST_OIDC_DATA,
  TEST_OIDC_USER,
} from '@experience/shared/typescript/oidc-utils/specs';
import { Test, TestingModule } from '@nestjs/testing';
import { convertAllDatesToISOString } from '@experience/shared/typescript/utils';
import {
  mockCharger,
  mockCommercialAttributes,
  mockSummary,
} from '@experience/support/support-tool/shared/specs';
import request from 'supertest';

jest.mock('./charger.service');

describe('ChargerController', () => {
  let app: INestApplication;
  let controller: ChargerController;
  let service: ChargerService;
  let nameToPpidInterceptor: NameToPpidInterceptor;
  let siteAdminApi: SiteAdminChargerApi;

  beforeAll(async () => {
    const module: TestingModule = await Test.createTestingModule({
      controllers: [ChargerController],
      providers: [
        ChargerService,
        SiteAdminChargerApi,
        { provide: NameToPpidInterceptor, useClass: NameToPpidInterceptor },
      ],
    }).compile();

    controller = module.get<ChargerController>(ChargerController);
    nameToPpidInterceptor = module.get<NameToPpidInterceptor>(
      NameToPpidInterceptor
    );
    service = module.get<ChargerService>(ChargerService);
    siteAdminApi = module.get<SiteAdminChargerApi>(SiteAdminChargerApi);

    app = module.createNestApplication();
    await app.init();
  });

  it('should be defined', () => {
    expect(controller).toBeDefined();
    expect(nameToPpidInterceptor).toBeDefined();
    expect(service).toBeDefined();
    expect(siteAdminApi).toBeDefined();
  });

  it('should find by ppid', async () => {
    const { ppid } = mockSummary;
    const mockFindByPpid = jest
      .spyOn(service, 'findByPpid')
      .mockResolvedValueOnce(mockCharger);

    await request(app.getHttpServer())
      .get(`/chargers/${ppid}`)
      .set(AMAZON_OIDC_DATA_HEADER, TEST_OIDC_DATA)
      .expect(200)
      .expect(convertAllDatesToISOString(mockCharger) as Charger);

    expect(mockFindByPpid).toHaveBeenCalledWith(
      ppid,
      'A',
      false,
      TEST_OIDC_USER
    );
  });

  it('should find by name', async () => {
    const { name, ppid } = mockCommercialAttributes;
    const mockFindByPpid = jest
      .spyOn(service, 'findByPpid')
      .mockResolvedValueOnce(mockCharger);
    const mockFindPpidByName = jest
      .spyOn(siteAdminApi, 'chargerControllerFindPpidByChargerName')
      .mockResolvedValueOnce({
        data: ppid,
        status: 200,
      } as AxiosResponse<string>);

    await request(app.getHttpServer())
      .get(`/chargers/${name}`)
      .set(AMAZON_OIDC_DATA_HEADER, TEST_OIDC_DATA)
      .expect(200)
      .expect(convertAllDatesToISOString(mockCharger) as Charger);

    expect(mockFindPpidByName).toHaveBeenCalledWith(name);
    expect(mockFindByPpid).toHaveBeenCalledWith(
      ppid,
      'A',
      false,
      TEST_OIDC_USER
    );
  });

  it('should find by ppid and socket', async () => {
    const { ppid } = mockSummary;
    const mockFindByPpid = jest
      .spyOn(service, 'findByPpid')
      .mockResolvedValueOnce(mockCharger);

    await request(app.getHttpServer())
      .get(`/chargers/${ppid}?socket=B`)
      .set(AMAZON_OIDC_DATA_HEADER, TEST_OIDC_DATA)
      .expect(200)
      .expect(convertAllDatesToISOString(mockCharger) as Charger);

    expect(mockFindByPpid).toHaveBeenCalledWith(
      ppid,
      'B',
      false,
      TEST_OIDC_USER
    );
  });

  it('should find charger by ppid and redact sensitive information', async () => {
    const { ppid } = mockSummary;
    const mockFindByPpid = jest
      .spyOn(service, 'findByPpid')
      .mockResolvedValueOnce(mockCharger);

    await request(app.getHttpServer())
      .get(`/chargers/${ppid}?redact=true`)
      .set(AMAZON_OIDC_DATA_HEADER, TEST_OIDC_DATA)
      .expect(200)
      .expect(convertAllDatesToISOString(mockCharger) as Charger);

    expect(mockFindByPpid).toHaveBeenCalledWith(
      ppid,
      'A',
      true,
      TEST_OIDC_USER
    );
  });

  it('should find by ppid for anonymous user', async () => {
    const { ppid } = mockSummary;
    const mockFindByPpid = jest
      .spyOn(service, 'findByPpid')
      .mockResolvedValueOnce(mockCharger);

    await request(app.getHttpServer())
      .get(`/chargers/${ppid}`)
      .expect(200)
      .expect(convertAllDatesToISOString(mockCharger) as Charger);

    expect(mockFindByPpid).toHaveBeenCalledWith(ppid, 'A', false, {
      name: 'Anonymous',
      email: '<EMAIL>',
      roles: ['Charger.ViewAll'],
    });
  });

  it('should find charger summary by ppid', async () => {
    const { ppid } = mockSummary;
    const mockFindSummaryByPpid = jest
      .spyOn(service, 'getSummary')
      .mockResolvedValueOnce(mockSummary);

    await request(app.getHttpServer())
      .get(`/chargers/${ppid}/summary`)
      .set(AMAZON_OIDC_DATA_HEADER, TEST_OIDC_DATA)
      .expect(200);

    expect(mockFindSummaryByPpid).toHaveBeenCalledWith(ppid, TEST_OIDC_USER);
  });
});
