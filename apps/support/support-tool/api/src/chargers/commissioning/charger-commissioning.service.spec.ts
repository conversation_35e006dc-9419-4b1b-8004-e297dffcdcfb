import 'whatwg-fetch';
import {
  Charger,
  CommissioningCertificate,
} from '@experience/support/support-tool/shared';
import { ChargerCommissioningService } from './charger-commissioning.service';
import { ChargerService } from '../charger.service';
import { CommissioningCertificateNotAvailableException } from './commissioning.exception';
import { Map, setIn } from 'immutable';
import { OidcRoles } from '@experience/shared/typescript/oidc-utils';
import { OidcUser } from '@experience/shared/nest/utils';
import { TEST_OIDC_USER } from '@experience/shared/typescript/oidc-utils/specs';
import { Test, TestingModule } from '@nestjs/testing';
import {
  mockCharger,
  mockCommercialCharger,
} from '@experience/support/support-tool/shared/specs';
import MockDate from 'mockdate';

jest.mock('../charger.service');

describe('ChargerCommissioningService', () => {
  let service: ChargerCommissioningService;
  let chargerService: ChargerService;

  const mockChargerMap = Map(mockCharger)
    .setIn(['summary', 'ppid'], 'PG-123456')
    .setIn(['commercialAttributes', 'admins'], [{ email: '<EMAIL>' }])
    .setIn(
      ['commercialAttributes', 'site', 'pods'],
      [{ id: 1, ppid: 'PG-123456', installDate: '2023-01-01' }]
    );
  const mockCommercialChargerMap = Map(mockCommercialCharger)
    .setIn(['summary', 'ppid'], 'PG-123456')
    .setIn(['commercialAttributes', 'admins'], [{ email: '<EMAIL>' }])
    .setIn(
      ['commercialAttributes', 'site', 'pods'],
      [
        {
          id: 1,
          ppid: 'PG-123456',
          installDate: '2023-01-01',
        },
      ]
    );

  const expectedResult: CommissioningCertificate = {
    address: 'Test Address, Test Address Line 2, Test Town, SW1A 1AA',
    adminEmailAddress: '<EMAIL>',
    company: 'Electrical Services Ltd.',
    confirmChargeEnabled: false,
    connectivityStatusOnline: false,
    generatedAt: '2024-01-01T00:00:00.000Z',
    group: 'Test group',
    hasTariff: true,
    installedBy: 'Joe Spark',
    isPublic: true,
    ppids: 'PG-123456',
    siteName: 'Test site',
    warrantyEndDate: '01/08/2027',
    warrantyStartDate: '01/08/2024',
  };

  const mockUser: OidcUser = {
    ...TEST_OIDC_USER,
    roles: [OidcRoles.CHARGER_COMMISSIONING],
  };

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [ChargerCommissioningService, ChargerService],
    }).compile();

    service = module.get<ChargerCommissioningService>(
      ChargerCommissioningService
    );
    chargerService = module.get(ChargerService);

    MockDate.set('2024-01-01');
  });

  afterEach(() => {
    MockDate.reset();
  });

  it('should be defined', () => {
    expect(service).toBeDefined();
  });

  describe('getCommissioningCertificate', () => {
    it('should return commissioning certificate for a single charger', async () => {
      const mockFindByPpid = jest
        .spyOn(chargerService, 'findByPpid')
        .mockResolvedValueOnce(mockCommercialChargerMap.toJS() as Charger);

      const result = await service.getCommissioningCertificate(
        'PG-123456',
        mockUser
      );

      expect(result).toEqual(expectedResult);
      expect(mockFindByPpid).toHaveBeenCalledWith(
        'PG-123456',
        'A',
        false,
        mockUser
      );
    });

    it('should return commissioning certificate for multiple chargers', async () => {
      const pods = [
        {
          id: 1,
          ppid: 'PG-123456',
        },
        {
          id: 2,
          ppid: 'PG-678904',
        },
      ];

      const charger1 = mockCommercialChargerMap
        .setIn(['commercialAttributes', 'site', 'pods'], pods)
        .setIn(['summary', 'ppid'], 'PG-123456')
        .toJS() as Charger;

      const charger2 = mockCommercialChargerMap
        .setIn(['commercialAttributes', 'site', 'pods'], pods)
        .setIn(['summary', 'ppid'], 'PG-678904')
        .toJS() as Charger;

      const mockFindByPpid = jest
        .spyOn(chargerService, 'findByPpid')
        .mockResolvedValueOnce(charger1);
      const mockFindByPpidAndFields = jest
        .spyOn(chargerService, 'findChargerAttributesByPpid')
        .mockResolvedValueOnce(charger2);

      const result = await service.getCommissioningCertificate(
        'PG-123456',
        mockUser
      );

      expect(result).toEqual(
        setIn(expectedResult, ['ppids'], 'PG-123456, PG-678904')
      );
      expect(mockFindByPpid).toHaveBeenCalledWith(
        'PG-123456',
        'A',
        false,
        mockUser
      );
      expect(mockFindByPpidAndFields).toHaveBeenCalledWith(
        'PG-678904',
        mockUser,
        [
          'commercialAttributes',
          'connectivityStatus',
          'commercialAttributes',
          'installationDetails',
          'salesforceAsset',
          'summary',
        ]
      );
    });

    it('should return chargers where the install date matches', async () => {
      const pods = [
        {
          id: 1,
          ppid: 'PG-123456',
        },
        {
          id: 2,
          ppid: 'PG-678904',
        },
        {
          id: 3,
          ppid: 'PG-112233',
        },
      ];

      const charger1 = mockCommercialChargerMap
        .setIn(['commercialAttributes', 'site', 'pods'], pods)
        .setIn(['installationDetails', 0, 'completedAt'], '2023-01-01')
        .setIn(['summary', 'ppid'], 'PG-123456')
        .setIn(['salesforceAsset', 'Warranty_Start_Date__c'], '2023-01-01')
        .toJS() as Charger;

      const charger2 = mockCommercialChargerMap
        .setIn(['commercialAttributes', 'site', 'pods'], pods)
        .setIn(['installationDetails', 0, 'completedAt'], '2024-01-01')
        .setIn(['summary', 'ppid'], 'PG-678904')
        .setIn(['salesforceAsset', 'Warranty_Start_Date__c'], '2024-01-01')
        .toJS() as Charger;

      const charger3 = mockCommercialChargerMap
        .setIn(['commercialAttributes', 'site', 'pods'], pods)
        .setIn(['installationDetails', 0, 'completedAt'], '2023-01-01')
        .setIn(['summary', 'ppid'], 'PG-112233')
        .setIn(['salesforceAsset', 'Warranty_Start_Date__c'], '2025-01-01')
        .toJS() as Charger;

      const mockFindByPpid = jest
        .spyOn(chargerService, 'findByPpid')
        .mockResolvedValueOnce(charger1);
      const mockFindByPpidAndFields = jest
        .spyOn(chargerService, 'findChargerAttributesByPpid')
        .mockResolvedValueOnce(charger2)
        .mockResolvedValueOnce(charger3);

      const result = await service.getCommissioningCertificate(
        'PG-123456',
        mockUser
      );

      expect(result.ppids).toBe('PG-123456, PG-112233');
      expect(mockFindByPpid).toHaveBeenCalledWith(
        'PG-123456',
        'A',
        false,
        mockUser
      );
      expect(mockFindByPpidAndFields).toHaveBeenNthCalledWith(
        1,
        'PG-678904',
        mockUser,
        [
          'commercialAttributes',
          'connectivityStatus',
          'commercialAttributes',
          'installationDetails',
          'salesforceAsset',
          'summary',
        ]
      );
      expect(
        chargerService.findChargerAttributesByPpid
      ).toHaveBeenNthCalledWith(2, 'PG-112233', mockUser, [
        'commercialAttributes',
        'connectivityStatus',
        'commercialAttributes',
        'installationDetails',
        'salesforceAsset',
        'summary',
      ]);
    });

    it('should return chargers where the salesforce warranty date matches', async () => {
      const pods = [
        {
          id: 1,
          ppid: 'PG-123456',
        },
        {
          id: 2,
          ppid: 'PG-678904',
        },
        {
          id: 3,
          ppid: 'PG-112233',
        },
      ];

      const charger1 = mockCommercialChargerMap
        .setIn(['commercialAttributes', 'site', 'pods'], pods)
        .setIn(['installationDetails', 0, 'completedAt'], '2023-01-01')
        .setIn(['summary', 'ppid'], 'PG-123456')
        .setIn(['salesforceAsset', 'Warranty_Start_Date__c'], '2023-01-01')
        .toJS() as Charger;

      const charger2 = mockCommercialChargerMap
        .setIn(['commercialAttributes', 'site', 'pods'], pods)
        .setIn(['installationDetails', 0, 'completedAt'], '2024-01-01')
        .setIn(['summary', 'ppid'], 'PG-678904')
        .setIn(['salesforceAsset', 'Warranty_Start_Date__c'], '2023-01-01')
        .toJS() as Charger;

      const charger3 = mockCommercialChargerMap
        .setIn(['commercialAttributes', 'site', 'pods'], pods)
        .setIn(['installationDetails', 0, 'completedAt'], '2023-01-02')
        .setIn(['summary', 'ppid'], 'PG-112233')
        .setIn(['salesforceAsset', 'Warranty_Start_Date__c'], '2024-01-01')
        .toJS() as Charger;

      const mockFindByPpid = jest
        .spyOn(chargerService, 'findByPpid')
        .mockResolvedValueOnce(charger1);
      const mockFindByPpidAndFields = jest
        .spyOn(chargerService, 'findChargerAttributesByPpid')
        .mockResolvedValueOnce(charger2)
        .mockResolvedValueOnce(charger3);

      const result = await service.getCommissioningCertificate(
        'PG-123456',
        mockUser
      );

      expect(result.ppids).toBe('PG-123456, PG-678904');
      expect(mockFindByPpid).toHaveBeenCalledWith(
        'PG-123456',
        'A',
        false,
        mockUser
      );
      expect(mockFindByPpidAndFields).toHaveBeenNthCalledWith(
        1,
        'PG-678904',
        mockUser,
        [
          'commercialAttributes',
          'connectivityStatus',
          'commercialAttributes',
          'installationDetails',
          'salesforceAsset',
          'summary',
        ]
      );
      expect(mockFindByPpidAndFields).toHaveBeenNthCalledWith(
        2,
        'PG-112233',
        mockUser,
        [
          'commercialAttributes',
          'connectivityStatus',
          'commercialAttributes',
          'installationDetails',
          'salesforceAsset',
          'summary',
        ]
      );
    });

    it('should handle charger with no installation details', async () => {
      const testCharger = mockCommercialChargerMap
        .setIn(['installationDetails'], [])
        .toJS() as Charger;

      jest
        .spyOn(chargerService, 'findByPpid')
        .mockResolvedValueOnce(testCharger);

      const result = await service.getCommissioningCertificate(
        'PG-123456',
        mockUser
      );

      expect(result).toEqual({
        ...expectedResult,
        installedBy: '-',
        company: '-',
      });
    });

    it('should handle charger with no salesforce warranty date', async () => {
      const testCharger = mockCommercialChargerMap
        .setIn(['salesforceAsset', 'Warranty_Start_Date__c'], null)
        .toJS() as Charger;

      jest
        .spyOn(chargerService, 'findByPpid')
        .mockResolvedValueOnce(testCharger);

      const result = await service.getCommissioningCertificate(
        'PG-123456',
        mockUser
      );

      expect(result).toEqual({
        ...expectedResult,
        warrantyStartDate: '-',
      });
    });

    it('should handle offline charger', async () => {
      const offlineEvses = [
        {
          id: 1,
          connectivityState: {
            connectivityStatus: 'OFFLINE',
          },
        },
      ];

      const testCharger = mockCommercialChargerMap
        .setIn(['summary', 'ppid'], 'PG-123456')
        .set('name', 'Test Charger')
        .setIn(['connectivityStatus', 'evses'], offlineEvses)
        .toJS() as Charger;

      jest
        .spyOn(chargerService, 'findByPpid')
        .mockResolvedValueOnce(testCharger);

      const result = await service.getCommissioningCertificate(
        'PG-123456',
        mockUser
      );

      expect(result.connectivityStatusOnline).toBe(false);
    });

    it('should handle charger with all EVSEs online', async () => {
      const onlineEvses = [
        {
          id: 1,
          connectivityState: {
            connectivityStatus: 'ONLINE',
          },
        },
        {
          id: 2,
          connectivityState: {
            connectivityStatus: 'ONLINE',
          },
        },
      ];

      const testCharger = mockCommercialChargerMap
        .setIn(['connectivityStatus', 'evses'], onlineEvses)
        .toJS() as Charger;

      jest
        .spyOn(chargerService, 'findByPpid')
        .mockResolvedValueOnce(testCharger);

      const result = await service.getCommissioningCertificate(
        'PG-123456',
        mockUser
      );

      expect(result.connectivityStatusOnline).toBe(true);
    });

    it('should handle charger with mixed EVSE connectivity', async () => {
      const mixedEvses = [
        {
          id: 1,
          connectivityState: {
            connectivityStatus: 'ONLINE',
          },
        },
        {
          id: 2,
          connectivityState: {
            connectivityStatus: 'OFFLINE',
          },
        },
      ];

      const testCharger = mockCommercialChargerMap
        .setIn(['connectivityStatus', 'evses'], mixedEvses)
        .toJS() as Charger;

      jest
        .spyOn(chargerService, 'findByPpid')
        .mockResolvedValueOnce(testCharger);

      const result = await service.getCommissioningCertificate(
        'PG-123456',
        mockUser
      );

      expect(result.connectivityStatusOnline).toBe(false);
    });

    it('should handle charger with no EVSEs', async () => {
      const testCharger = mockCommercialChargerMap
        .setIn(['connectivityStatus', 'evses'], [])
        .toJS() as Charger;

      jest
        .spyOn(chargerService, 'findByPpid')
        .mockResolvedValueOnce(testCharger);

      const result = await service.getCommissioningCertificate(
        'PG-123456',
        mockUser
      );

      expect(result.connectivityStatusOnline).toBe(false);
    });

    it('should handle private charger', async () => {
      const testCharger = mockCommercialChargerMap
        .setIn(['summary', 'location', 'isPublic'], false)
        .toJS() as Charger;

      jest
        .spyOn(chargerService, 'findByPpid')
        .mockResolvedValueOnce(testCharger);

      const result = await service.getCommissioningCertificate(
        'PG-123456',
        mockUser
      );

      expect(result.isPublic).toBe(false);
    });

    it('should handle charger without confirm charge (Arch5)', async () => {
      const configurationKey = [{ key: 'ConfirmCharge', value: 'false' }];

      const testCharger = mockCommercialChargerMap
        .setIn(
          ['configuration', 'configuration', 'configurationKey'],
          configurationKey
        )
        .toJS() as Charger;

      jest
        .spyOn(chargerService, 'findByPpid')
        .mockResolvedValueOnce(testCharger);

      const result = await service.getCommissioningCertificate(
        'PG-123456',
        mockUser
      );

      expect(result.confirmChargeEnabled).toBe(false);
    });

    it('should handle charger with confirm charge enabled (Arch5)', async () => {
      const configurationKey = [{ key: 'ConfirmCharge', value: 'true' }];

      const testCharger = mockCommercialChargerMap
        .setIn(
          ['configuration', 'configuration', 'configurationKey'],
          configurationKey
        )
        .toJS() as Charger;

      jest
        .spyOn(chargerService, 'findByPpid')
        .mockResolvedValueOnce(testCharger);

      const result = await service.getCommissioningCertificate(
        'PG-123456',
        mockUser
      );

      expect(result.confirmChargeEnabled).toBe(true);
    });

    it('should handle charger with confirm charge enabled (non-Arch5)', async () => {
      const testCharger = mockCommercialChargerMap
        .setIn(['commercialAttributes', 'settings', 'confirmCharge'], true)
        .toJS() as Charger;

      jest
        .spyOn(chargerService, 'findByPpid')
        .mockResolvedValueOnce(testCharger);

      const result = await service.getCommissioningCertificate(
        'PG-123456',
        mockUser
      );

      expect(result.confirmChargeEnabled).toBe(true);
    });

    it('should handle charger without confirm charge (non-Arch5)', async () => {
      const testCharger = mockCommercialChargerMap
        .setIn(['commercialAttributes', 'settings', 'confirmCharge'], false)
        .toJS() as Charger;

      jest
        .spyOn(chargerService, 'findByPpid')
        .mockResolvedValueOnce(testCharger);

      const result = await service.getCommissioningCertificate(
        'PG-123456',
        mockUser
      );

      expect(result.confirmChargeEnabled).toBe(false);
    });

    it('should handle charger with tariff assigned', async () => {
      jest
        .spyOn(chargerService, 'findByPpid')
        .mockResolvedValueOnce(mockCommercialChargerMap.toJS() as Charger);

      const result = await service.getCommissioningCertificate(
        'PG-123456',
        mockUser
      );

      expect(result.hasTariff).toBe(true);
    });

    it('should handle charger without tariff', async () => {
      const testCharger = mockCommercialChargerMap
        .setIn(['commercialAttributes', 'tariff'], null)
        .toJS() as Charger;

      jest
        .spyOn(chargerService, 'findByPpid')
        .mockResolvedValueOnce(testCharger);

      const result = await service.getCommissioningCertificate(
        'PG-123456',
        mockUser
      );

      expect(result.hasTariff).toBe(false);
    });

    it('should throw error for non-commercial charger without commercial attributes', async () => {
      const testCharger = mockChargerMap
        .set('commercialAttributes', undefined)
        .setIn(['summary', 'location', 'type'], 'COMMERCIAL')
        .toJS() as Charger;

      jest
        .spyOn(chargerService, 'findByPpid')
        .mockResolvedValueOnce(testCharger);

      await expect(
        service.getCommissioningCertificate('PG-123456', mockUser)
      ).rejects.toThrow(CommissioningCertificateNotAvailableException);
    });

    it('should throw error for domestic charger with commercial attributes', async () => {
      const testCharger = mockChargerMap
        .setIn(['summary', 'location', 'type'], 'DOMESTIC')
        .toJS() as Charger;

      jest
        .spyOn(chargerService, 'findByPpid')
        .mockResolvedValueOnce(testCharger);

      await expect(
        service.getCommissioningCertificate('PG-123456', mockUser)
      ).rejects.toThrow(CommissioningCertificateNotAvailableException);
    });

    it('should throw error for domestic charger without commercial attributes', async () => {
      const testCharger = mockChargerMap
        .set('commercialAttributes', undefined)
        .setIn(['summary', 'location', 'type'], 'DOMESTIC')
        .toJS() as Charger;

      jest
        .spyOn(chargerService, 'findByPpid')
        .mockResolvedValueOnce(testCharger);

      await expect(
        service.getCommissioningCertificate('PG-123456', mockUser)
      ).rejects.toThrow(CommissioningCertificateNotAvailableException);
    });

    it('should return comma-separated list of emails when multiple admins exist', async () => {
      const testCharger = mockCommercialChargerMap
        .setIn(
          ['commercialAttributes', 'admins'],
          [
            { email: '<EMAIL>' },
            { email: '<EMAIL>' },
            { email: '<EMAIL>' },
          ]
        )
        .toJS() as Charger;

      jest.spyOn(chargerService, 'findByPpid').mockResolvedValue(testCharger);

      const result = await service.getCommissioningCertificate(
        'PSL-123456',
        mockUser
      );

      expect(result.adminEmailAddress).toBe(
        '<EMAIL>, <EMAIL>, <EMAIL>'
      );
    });

    it('should return single admin email when only one admin exists', async () => {
      const testCharger = mockCommercialChargerMap
        .setIn(
          ['commercialAttributes', 'admins'],
          [{ email: '<EMAIL>' }]
        )
        .toJS() as Charger;

      jest.spyOn(chargerService, 'findByPpid').mockResolvedValue(testCharger);

      const result = await service.getCommissioningCertificate(
        'PSL-123456',
        mockUser
      );

      expect(result.adminEmailAddress).toBe('<EMAIL>');
    });

    it('should return dash when no admins exist', async () => {
      const testCharger = mockCommercialChargerMap
        .setIn(['commercialAttributes', 'admins'], [])
        .toJS() as Charger;

      jest.spyOn(chargerService, 'findByPpid').mockResolvedValue(testCharger);

      const result = await service.getCommissioningCertificate(
        'PSL-123456',
        mockUser
      );

      expect(result.adminEmailAddress).toBe('-');
    });

    it('should return dash when admins array is undefined', async () => {
      const testCharger = mockCommercialChargerMap
        .setIn(['commercialAttributes', 'admins'], undefined)
        .toJS() as Charger;

      jest.spyOn(chargerService, 'findByPpid').mockResolvedValue(testCharger);

      const result = await service.getCommissioningCertificate(
        'PSL-123456',
        mockUser
      );

      expect(result.adminEmailAddress).toBe('-');
    });
  });
});
