import { APP_INTERCEPTOR } from '@nestjs/core';
import {
  AssetSummaryApi as AssetsApiClientAssetSummaryApi,
  Configuration as AssetsApiClientConfiguration,
  TagApi as AssetsApiClientTagApi,
} from '@experience/shared/axios/assets-api-client';
import {
  Configuration as AssetsConfigurationApiClientConfiguration,
  GetApi as AssetsConfigurationApiClientGetApi,
  GetRequestStatusApi as AssetsConfigurationApiClientGetRequestStatusApi,
  RefreshApi as AssetsConfigurationApiClientRefreshApi,
  SetApi as AssetsConfigurationApiClientSetApi,
} from '@experience/shared/axios/assets-configuration-api-client';
import {
  Configuration as AssetsProvisioningApiConfiguration,
  PcbSwapApi as AssetsProvisioningApiPcbSwapApi,
  WifiCredentialsApi as AssetsProvisioningApiWifiCredentialsApi,
} from '@experience/shared/axios/assets-provisioning-api-client';
import { CacheInterceptor, CacheModule } from '@nestjs/cache-manager';
import { ChargeSchedulesController } from './schedules/charge-schedules.controller';
import { ChargeSchedulesService } from './schedules/charge-schedules.service';
import { ChargerChargeOverridesController } from './charge-overrides/charger-charge-overrides.controller';
import { ChargerChargeOverridesService } from './charge-overrides/charger-charge-overrides.service';
import { ChargerChargesController } from './charges/charger-charges.controller';
import { ChargerChargesService } from './charges/charger-charges.service';
import { ChargerCommandsController } from './commands/charger-commands.controller';
import { ChargerCommandsService } from './commands/charger-commands.service';
import { ChargerCommissioningController } from './commissioning/charger-commissioning.controller';
import { ChargerCommissioningService } from './commissioning/charger-commissioning.service';
import { ChargerConfigController } from './config/charger-config.controller';
import { ChargerConfigService } from './config/charger-config.service';
import { ChargerController } from './charger.controller';
import { ChargerDelegatedControlController } from './delegated-control/charger-delegated-control.controller';
import { ChargerDelegatedControlService } from './delegated-control/charger-delegated-control.service';
import { ChargerFirmwareController } from './firmware/charger-firmware.controller';
import { ChargerFirmwareService } from './firmware/charger-firmware.service';
import { ChargerFlexController } from './flex/charger-flex.controller';
import { ChargerFlexService } from './flex/charger-flex.service';
import { ChargerLogsController } from './logs/charger-logs.controller';
import { ChargerLogsService } from './logs/charger-logs.service';
import { ChargerPcbController } from './pcbs/charger-pcb.controller';
import { ChargerPcbService } from './pcbs/charger-pcb.service';
import { ChargerService } from './charger.service';
import { ChargerTagsController } from './tags/charger-tags.controller';
import { ChargerTagsService } from './tags/charger-tags.service';
import { ChargerVehiclesController } from './vehicles/charger-vehicles.controller';
import { ChargerVehiclesService } from './vehicles/charger-vehicles.service';
import {
  ChargingStationsApi as CompetitionsServiceClientCompetitionApi,
  Configuration as CompetitionsServiceClientConfiguration,
  ProgrammesApi as CompetitionsServiceClientProgrammesApi,
} from '@experience/shared/axios/competitions-service-client';
import { ConfigService } from '@nestjs/config';
import {
  Configuration,
  SubscriptionsApi,
} from '@experience/mobile/subscriptions-api/axios';
import {
  Configuration as ConnectivityCommandsApiClientConfiguration,
  DefaultApi as ConnectivityCommandsApiClientDefaultApi,
} from '@experience/shared/axios/connectivity-commands-api-client';
import {
  Configuration as ConnectivityServiceClientConfiguration,
  ConnectivityStatusApi as ConnectivityServiceClientConnectivityStatusApi,
} from '@experience/shared/axios/connectivity-service-client';
import {
  ChargeCommandsApi as DataPlatformChargeCommandsApi,
  Configuration as DataPlatformConfiguration,
  ProjectionChargesApi as DataPlatformProjectionChargesApi,
} from '@experience/shared/axios/data-platform-api-client';
import {
  Configuration as DiagnosticsServiceClientConfiguration,
  LogsApi as DiagnosticsServiceClientLogsApi,
} from '@experience/shared/axios/diagnostics-service-client';
import {
  Configuration as DriverAccountApiClientConfiguration,
  UsersApi as DriverAccountApiClientUsersApi,
} from '@experience/driver-account-api/api-client';
import {
  Configuration as FirmwareUpgradeClientConfiguration,
  InfoApi as FirmwareUpgradeClientInfoApi,
  UpdatesApi as FirmwareUpgradeClientUpdatesApi,
} from '@experience/shared/axios/firmware-upgrade-client';
import {
  Configuration as InstallerApiConfiguration,
  InstallsApi as InstallerApiInstallsApi,
} from '@experience/installer/api/axios';
import { Module } from '@nestjs/common';
import { SalesforceClient } from '@experience/shared/salesforce/client';
import {
  Configuration as SiteAdminApiConfiguration,
  ChargerApi as SiteAdminChargerApi,
} from '@experience/shared/axios/internal-site-admin-api-client';
import {
  ChargeOverridesApi as SmartChargingServiceClientChargeOverridesApi,
  ChargeSchedulesAPI3Api as SmartChargingServiceClientChargeSchedulesAPI3Api,
  Configuration as SmartChargingServiceClientConfiguration,
  DelegatedControlChargingStationsApi as SmartChargingServiceClientDelegatedControlChargingStationsApi,
  EnergyOfferStatusApi as SmartChargingServiceClientEnergyOfferStatusApi,
  FlexibilityRequestsApi as SmartChargingServiceClientFlexibilityRequestsApi,
  DelegatedControlVehiclesApi as SmartChargingServiceClientVehiclesApi,
} from '@experience/shared/axios/smart-charging-service-client';
import {
  Configuration as TariffsApiConfiguration,
  ChargingStationsTariffsApi as TariffsChargingStationTariffsApi,
  SuppliersApi as TariffsSupplierApi,
} from '@experience/shared/axios/tariffs-api-client';
import axios from 'axios';

@Module({
  imports: [CacheModule.register({ max: 50, ttl: 5000 })],
  controllers: [
    ChargerController,
    ChargerChargeOverridesController,
    ChargerChargesController,
    ChargerConfigController,
    ChargerCommandsController,
    ChargerCommissioningController,
    ChargerDelegatedControlController,
    ChargerPcbController,
    ChargerFirmwareController,
    ChargerFlexController,
    ChargerLogsController,
    ChargeSchedulesController,
    ChargerTagsController,
    ChargerVehiclesController,
  ],
  providers: [
    { provide: APP_INTERCEPTOR, useClass: CacheInterceptor },
    ConfigService,
    ChargerService,
    ChargerChargeOverridesService,
    ChargerChargesService,
    ChargerConfigService,
    ChargerCommandsService,
    ChargerCommissioningService,
    ChargerDelegatedControlService,
    ChargerFirmwareService,
    ChargerFlexService,
    ChargerLogsService,
    ChargerPcbService,
    ChargeSchedulesService,
    ChargerTagsService,
    ChargerVehiclesService,
    {
      inject: [ConfigService],
      provide: AssetsApiClientAssetSummaryApi,
      useFactory: async (configService: ConfigService) => {
        const client = axios.create({
          timeout: configService.get('ASSETS_API_TIMEOUT', 10000),
        });
        return new AssetsApiClientAssetSummaryApi(
          new AssetsApiClientConfiguration(),
          configService.get('ASSETS_API_BASE_URL'),
          client
        );
      },
    },
    {
      inject: [ConfigService],
      provide: AssetsApiClientTagApi,
      useFactory: async (configService: ConfigService) => {
        const client = axios.create({
          timeout: configService.get('ASSETS_API_TIMEOUT', 10000),
        });
        return new AssetsApiClientTagApi(
          new AssetsApiClientConfiguration(),
          configService.get('ASSETS_API_BASE_URL'),
          client
        );
      },
    },
    {
      inject: [ConfigService],
      provide: AssetsConfigurationApiClientGetApi,
      useFactory: async (configService: ConfigService) => {
        const client = axios.create({
          timeout: configService.get('ASSETS_CONFIGURATION_API_TIMEOUT', 10000),
        });
        return new AssetsConfigurationApiClientGetApi(
          new AssetsConfigurationApiClientConfiguration(),
          configService.get('ASSETS_CONFIGURATION_API_BASE_URL'),
          client
        );
      },
    },
    {
      inject: [ConfigService],
      provide: AssetsConfigurationApiClientGetRequestStatusApi,
      useFactory: async (configService: ConfigService) => {
        const client = axios.create({
          timeout: configService.get('ASSETS_CONFIGURATION_API_TIMEOUT', 10000),
        });
        return new AssetsConfigurationApiClientGetRequestStatusApi(
          new AssetsConfigurationApiClientConfiguration(),
          configService.get('ASSETS_CONFIGURATION_API_BASE_URL'),
          client
        );
      },
    },
    {
      inject: [ConfigService],
      provide: AssetsConfigurationApiClientRefreshApi,
      useFactory: async (configService: ConfigService) => {
        const client = axios.create({
          timeout: configService.get('ASSETS_CONFIGURATION_API_TIMEOUT', 10000),
        });
        return new AssetsConfigurationApiClientRefreshApi(
          new AssetsConfigurationApiClientConfiguration(),
          configService.get('ASSETS_CONFIGURATION_API_BASE_URL'),
          client
        );
      },
    },
    {
      inject: [ConfigService],
      provide: AssetsConfigurationApiClientSetApi,
      useFactory: async (configService: ConfigService) => {
        const client = axios.create({
          timeout: configService.get('ASSETS_CONFIGURATION_API_TIMEOUT', 10000),
        });
        return new AssetsConfigurationApiClientSetApi(
          new AssetsConfigurationApiClientConfiguration(),
          configService.get('ASSETS_CONFIGURATION_API_BASE_URL'),
          client
        );
      },
    },
    {
      inject: [ConfigService],
      provide: AssetsProvisioningApiPcbSwapApi,
      useFactory: async (configService: ConfigService) => {
        const client = axios.create({
          timeout: configService.get('ASSETS_PROVISIONING_API_TIMEOUT', 10000),
        });
        return new AssetsProvisioningApiPcbSwapApi(
          new AssetsProvisioningApiConfiguration(),
          configService.get('ASSETS_PROVISIONING_API_BASE_URL'),
          client
        );
      },
    },
    {
      inject: [ConfigService],
      provide: AssetsProvisioningApiWifiCredentialsApi,
      useFactory: async (configService: ConfigService) => {
        const client = axios.create({
          timeout: configService.get('ASSETS_PROVISIONING_API_TIMEOUT', 10000),
        });
        return new AssetsProvisioningApiWifiCredentialsApi(
          new AssetsProvisioningApiConfiguration(),
          configService.get('ASSETS_PROVISIONING_API_BASE_URL'),
          client
        );
      },
    },
    {
      inject: [ConfigService],
      provide: CompetitionsServiceClientCompetitionApi,
      useFactory: async (configService: ConfigService) => {
        const client = axios.create({
          timeout: configService.get('COMPETITIONS_API_TIMEOUT', 10000),
        });
        return new CompetitionsServiceClientCompetitionApi(
          new CompetitionsServiceClientConfiguration(),
          configService.get('COMPETITIONS_API_BASE_URL'),
          client
        );
      },
    },
    {
      inject: [ConfigService],
      provide: CompetitionsServiceClientProgrammesApi,
      useFactory: async (configService: ConfigService) => {
        const client = axios.create({
          timeout: configService.get('COMPETITIONS_API_TIMEOUT', 10000),
        });
        return new CompetitionsServiceClientProgrammesApi(
          new CompetitionsServiceClientConfiguration(),
          configService.get('COMPETITIONS_API_BASE_URL'),
          client
        );
      },
    },
    {
      inject: [ConfigService],
      provide: ConnectivityCommandsApiClientDefaultApi,
      useFactory: async (configService: ConfigService) => {
        const client = axios.create({
          timeout: configService.get(
            'CONNECTIVITY_COMMANDS_API_TIMEOUT',
            10000
          ),
        });
        return new ConnectivityCommandsApiClientDefaultApi(
          new ConnectivityCommandsApiClientConfiguration(),
          configService.get('CONNECTIVITY_COMMANDS_API_BASE_URL'),
          client
        );
      },
    },
    {
      inject: [ConfigService],
      provide: ConnectivityServiceClientConnectivityStatusApi,
      useFactory: async (configService: ConfigService) => {
        const client = axios.create({
          timeout: configService.get('CONNECTIVITY_STATUS_API_TIMEOUT', 10000),
        });
        return new ConnectivityServiceClientConnectivityStatusApi(
          new ConnectivityServiceClientConfiguration(),
          configService.get('CONNECTIVITY_STATUS_API_BASE_URL'),
          client
        );
      },
    },
    {
      inject: [ConfigService],
      provide: DataPlatformChargeCommandsApi,
      useFactory: async (configService: ConfigService) => {
        const client = axios.create({
          timeout: configService.get('DATA_PLATFORM_API_TIMEOUT', 10000),
        });
        return new DataPlatformChargeCommandsApi(
          new DataPlatformConfiguration(),
          configService.get('DATA_PLATFORM_API_BASE_URL'),
          client
        );
      },
    },
    {
      inject: [ConfigService],
      provide: DataPlatformProjectionChargesApi,
      useFactory: async (configService: ConfigService) => {
        const client = axios.create({
          timeout: configService.get('DATA_PLATFORM_API_TIMEOUT', 10000),
        });
        return new DataPlatformProjectionChargesApi(
          new DataPlatformConfiguration(),
          configService.get('DATA_PLATFORM_API_BASE_URL'),
          client
        );
      },
    },
    {
      inject: [ConfigService],
      provide: DiagnosticsServiceClientLogsApi,
      useFactory: async (configService: ConfigService) => {
        const client = axios.create({
          timeout: configService.get('DIAGNOSTICS_API_TIMEOUT', 10000),
        });
        return new DiagnosticsServiceClientLogsApi(
          new DiagnosticsServiceClientConfiguration(),
          configService.get('DIAGNOSTICS_API_BASE_URL'),
          client
        );
      },
    },
    {
      inject: [ConfigService],
      provide: DriverAccountApiClientUsersApi,
      useFactory: async (configService: ConfigService) => {
        const client = axios.create({
          timeout: configService.get('DRIVER_ACCOUNT_API_TIMEOUT', 10000),
        });
        return new DriverAccountApiClientUsersApi(
          new DriverAccountApiClientConfiguration(),
          configService.get('DRIVER_ACCOUNT_API_BASE_URL'),
          client
        );
      },
    },
    {
      inject: [ConfigService],
      provide: FirmwareUpgradeClientInfoApi,
      useFactory: async (configService: ConfigService) => {
        const client = axios.create({
          timeout: configService.get('FIRMWARE_API_TIMEOUT', 10000),
        });
        return new FirmwareUpgradeClientInfoApi(
          new FirmwareUpgradeClientConfiguration(),
          configService.get('FIRMWARE_API_BASE_URL'),
          client
        );
      },
    },
    {
      inject: [ConfigService],
      provide: FirmwareUpgradeClientUpdatesApi,
      useFactory: async (configService: ConfigService) => {
        const client = axios.create({
          timeout: configService.get('FIRMWARE_API_TIMEOUT', 10000),
        });
        return new FirmwareUpgradeClientUpdatesApi(
          new FirmwareUpgradeClientConfiguration(),
          configService.get('FIRMWARE_API_BASE_URL'),
          client
        );
      },
    },
    {
      inject: [ConfigService],
      provide: InstallerApiInstallsApi,
      useFactory: async (configService: ConfigService) => {
        const client = axios.create({
          timeout: configService.get('INSTALLER_API_TIMEOUT', 10000),
        });
        return new InstallerApiInstallsApi(
          new InstallerApiConfiguration(),
          configService.get('INSTALLER_API_BASE_URL'),
          client
        );
      },
    },
    {
      inject: [ConfigService],
      provide: SalesforceClient,
      useFactory: async (configService: ConfigService) =>
        new SalesforceClient({
          audience: configService.getOrThrow(
            'SALESFORCE_AUTH_AUDIENCE'
          ) as string,
          clientId: configService.getOrThrow(
            'SALESFORCE_AUTH_CLIENT_ID'
          ) as string,
          instanceUrl: configService.getOrThrow(
            'SALESFORCE_INSTANCE_URL'
          ) as string,
          privateKey: configService
            .getOrThrow('SALESFORCE_AUTH_PRIVATE_KEY')
            .replace(/\\n/g, '\n'),
          username: configService.getOrThrow(
            'SALESFORCE_AUTH_USERNAME'
          ) as string,
          userAgent: 'PodPoint-SupportTool',
          maxRetry: 5,
        }),
    },
    {
      inject: [ConfigService],
      provide: SiteAdminChargerApi,
      useFactory: async (configService: ConfigService) => {
        const client = axios.create({
          timeout: configService.get('SITE_ADMIN_API_TIMEOUT', 10000),
        });
        return new SiteAdminChargerApi(
          new SiteAdminApiConfiguration(),
          configService.get('SITE_ADMIN_API_BASE_URL'),
          client
        );
      },
    },
    {
      inject: [ConfigService],
      provide: SmartChargingServiceClientChargeOverridesApi,
      useFactory: async (configService: ConfigService) => {
        const client = axios.create({
          timeout: configService.get('SMART_CHARGING_SERVICE_TIMEOUT', 10000),
        });
        return new SmartChargingServiceClientChargeOverridesApi(
          new SmartChargingServiceClientConfiguration(),
          configService.get('SMART_CHARGING_SERVICE_BASE_URL'),
          client
        );
      },
    },
    {
      inject: [ConfigService],
      provide: SmartChargingServiceClientChargeSchedulesAPI3Api,
      useFactory: async (configService: ConfigService) => {
        const client = axios.create({
          timeout: configService.get('SMART_CHARGING_SERVICE_TIMEOUT', 10000),
        });
        return new SmartChargingServiceClientChargeSchedulesAPI3Api(
          new SmartChargingServiceClientConfiguration(),
          configService.get('SMART_CHARGING_SERVICE_BASE_URL'),
          client
        );
      },
    },
    {
      inject: [ConfigService],
      provide: SmartChargingServiceClientDelegatedControlChargingStationsApi,
      useFactory: async (configService: ConfigService) => {
        const client = axios.create({
          timeout: configService.get('SMART_CHARGING_SERVICE_TIMEOUT', 10000),
        });
        return new SmartChargingServiceClientDelegatedControlChargingStationsApi(
          new SmartChargingServiceClientConfiguration(),
          configService.get('SMART_CHARGING_SERVICE_BASE_URL'),
          client
        );
      },
    },
    {
      inject: [ConfigService],
      provide: SmartChargingServiceClientVehiclesApi,
      useFactory: async (configService: ConfigService) => {
        const client = axios.create({
          timeout: configService.get('SMART_CHARGING_SERVICE_TIMEOUT', 10000),
        });
        return new SmartChargingServiceClientVehiclesApi(
          new SmartChargingServiceClientConfiguration(),
          configService.get('SMART_CHARGING_SERVICE_BASE_URL'),
          client
        );
      },
    },
    {
      inject: [ConfigService],
      provide: SmartChargingServiceClientEnergyOfferStatusApi,
      useFactory: async (configService: ConfigService) => {
        const client = axios.create({
          timeout: configService.get('SMART_CHARGING_SERVICE_TIMEOUT', 10000),
        });
        return new SmartChargingServiceClientEnergyOfferStatusApi(
          new SmartChargingServiceClientConfiguration(),
          configService.get('SMART_CHARGING_SERVICE_BASE_URL'),
          client
        );
      },
    },
    {
      inject: [ConfigService],
      provide: SmartChargingServiceClientFlexibilityRequestsApi,
      useFactory: async (configService: ConfigService) => {
        const client = axios.create({
          timeout: configService.get('SMART_CHARGING_SERVICE_TIMEOUT', 10000),
        });
        return new SmartChargingServiceClientFlexibilityRequestsApi(
          new SmartChargingServiceClientConfiguration(),
          configService.get('SMART_CHARGING_SERVICE_BASE_URL'),
          client
        );
      },
    },
    {
      provide: SubscriptionsApi,
      inject: [ConfigService],
      useFactory: (configService: ConfigService) =>
        new SubscriptionsApi(
          new Configuration({
            basePath: configService.get('SUBSCRIPTIONS_API_BASE_URL'),
          })
        ),
    },
    {
      provide: TariffsChargingStationTariffsApi,
      inject: [ConfigService],
      useFactory: (configService: ConfigService) =>
        new TariffsChargingStationTariffsApi(
          new TariffsApiConfiguration({
            basePath: configService.get('TARIFFS_API_BASE_URL'),
          })
        ),
    },
    {
      provide: TariffsSupplierApi,
      inject: [ConfigService],
      useFactory: (configService: ConfigService) =>
        new TariffsSupplierApi(
          new TariffsApiConfiguration({
            basePath: configService.get('TARIFFS_API_BASE_URL'),
          })
        ),
    },
  ],
  exports: [ChargerService],
})
export class ChargerModule {}
