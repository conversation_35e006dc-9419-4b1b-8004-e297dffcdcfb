import { AMAZON_OIDC_DATA_HEADER } from '@experience/shared/typescript/oidc-utils';
import { ChargerDelegatedControlController } from './charger-delegated-control.controller';
import {
  ChargerDelegatedControlErrorCodes,
  RemoveChargerFromDelegatedControlException,
} from './charger-delegated-control.exception';
import { ChargerDelegatedControlService } from './charger-delegated-control.service';
import { INestApplication } from '@nestjs/common';
import { REMOVE_CHARGER_FROM_DELEGATED_CONTROL_ERROR_MESSAGE } from '@experience/support/support-tool/shared';
import {
  TEST_OIDC_DATA,
  TEST_OIDC_USER,
} from '@experience/shared/typescript/oidc-utils/specs';
import { Test, TestingModule } from '@nestjs/testing';
import { useGlobalPipes } from '@experience/shared/nest/utils';
import request from 'supertest';

jest.mock('./charger-delegated-control.service');

describe('ChargerDelegatedControlController', () => {
  let app: INestApplication;
  let controller: ChargerDelegatedControlController;
  let service: ChargerDelegatedControlService;

  beforeAll(async () => {
    const module: TestingModule = await Test.createTestingModule({
      controllers: [ChargerDelegatedControlController],
      providers: [ChargerDelegatedControlService],
    }).compile();

    controller = module.get<ChargerDelegatedControlController>(
      ChargerDelegatedControlController
    );
    service = module.get<ChargerDelegatedControlService>(
      ChargerDelegatedControlService
    );

    app = module.createNestApplication();
    useGlobalPipes(app);
    await app.init();
  });

  it('should be defined', () => {
    expect(controller).toBeDefined();
    expect(service).toBeDefined();
  });

  it('should remove a charger from delegated control', async () => {
    const mockRemoveFromDelegatedControl = jest
      .spyOn(service, 'removeFromDelegatedControl')
      .mockResolvedValueOnce(undefined);

    await request(app.getHttpServer())
      .delete(`/chargers/PSL-123456/delegated-control`)
      .set(AMAZON_OIDC_DATA_HEADER, TEST_OIDC_DATA)
      .expect(204);

    expect(mockRemoveFromDelegatedControl).toHaveBeenCalledWith(
      'PSL-123456',
      TEST_OIDC_USER
    );
  });

  it('should throw an exception if there is an error removing the charger from delegated control', async () => {
    jest
      .spyOn(service, 'removeFromDelegatedControl')
      .mockRejectedValueOnce(new RemoveChargerFromDelegatedControlException());

    await request(app.getHttpServer())
      .delete(`/chargers/PSL-123456/delegated-control`)
      .set(AMAZON_OIDC_DATA_HEADER, TEST_OIDC_DATA)
      .expect(500)
      .expect({
        error:
          ChargerDelegatedControlErrorCodes.REMOVE_CHARGER_FROM_DELEGATED_CONTROL_ERROR,
        message: REMOVE_CHARGER_FROM_DELEGATED_CONTROL_ERROR_MESSAGE,
        statusCode: 500,
      });
  });
});
