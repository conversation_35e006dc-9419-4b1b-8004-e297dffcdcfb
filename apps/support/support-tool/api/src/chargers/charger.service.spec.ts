import { AssetSummaryApi as AssetsApiClientAssetSummaryApi } from '@experience/shared/axios/assets-api-client';
import { GetApi as AssetsConfigurationGetApi } from '@experience/shared/axios/assets-configuration-api-client';
import { WifiCredentialsApi as AssetsProvisioningApiWifiCredentialsApi } from '@experience/shared/axios/assets-provisioning-api-client';
import { AxiosResponse } from 'axios';
import { ChargerChargesService } from './charges/charger-charges.service';
import { ChargerConfigService } from './config/charger-config.service';
import { ChargerService } from './charger.service';
import { ChargingStationsApi as CompetitionsServiceClientCompetitionApi } from '@experience/shared/axios/competitions-service-client';
import { ConnectivityStatusApi as ConnectivityServiceClientConnectivityStatusApi } from '@experience/shared/axios/connectivity-service-client';
import { ProjectionChargesApi as DataPlatformProjectionChargesApi } from '@experience/shared/axios/data-platform-api-client';
import { UsersApi as DriverAccountApiClientUsersApi } from '@experience/driver-account-api/api-client';
import { InfoApi as FirmwareUpgradeClientInfoApi } from '@experience/shared/axios/firmware-upgrade-client';
import { InstallsApi as InstallerApiInstallsApi } from '@experience/installer/api/axios';
import { NotFoundException } from '@nestjs/common';
import { OidcRoles } from '@experience/shared/typescript/oidc-utils';
import { OidcUser } from '@experience/shared/nest/utils';
import { SalesforceClient } from '@experience/shared/salesforce/client';
import { ChargerApi as SiteAdminChargerApi } from '@experience/shared/axios/internal-site-admin-api-client';
import {
  ChargeOverridesApi as SmartChargingServiceClientChargeOverridesApi,
  ChargeSchedulesAPI3Api as SmartChargingServiceClientChargeSchedulesAPI3Api,
  DelegatedControlChargingStationsApi as SmartChargingServiceClientDelegatedControlChargingStationsApi,
  DelegatedControlVehiclesApi as SmartChargingServiceClientVehiclesApi,
} from '@experience/shared/axios/smart-charging-service-client';
import { SubscriptionsApi } from '@experience/mobile/subscriptions-api/axios';
import { TEST_OIDC_USER_WITH_ROLES } from '@experience/shared/typescript/oidc-utils/specs';
import {
  ChargingStationsTariffsApi as TariffsChargingStationTariffsApi,
  SuppliersApi as TariffsSuppliersApi,
} from '@experience/shared/axios/tariffs-api-client';
import { Test, TestingModule } from '@nestjs/testing';
import { faker } from '@faker-js/faker';
import {
  mockChargeHistory,
  mockChargeOverrides,
  mockChargeSchedules,
  mockCharger,
  mockChargingStationProgrammes,
  mockCommercialAttributes,
  mockConfiguration,
  mockConnectivityStatus,
  mockCurrentFirmware,
  mockDelegatedControlStatus,
  mockInstallationDetails,
  mockLinkedUsers,
  mockSalesforceAsset,
  mockSubscriptions,
  mockSummary,
  mockTariffSuppliers,
  mockTariffs,
  mockVehicles,
  mockWifiCredentials,
} from '@experience/support/support-tool/shared/specs';
import { setIn } from 'immutable';
import MockDate from 'mockdate';

jest.mock('./charges/charger-charges.service');
jest.mock('./config/charger-config.service');

const FRENCH_REGION_SKU = 'S22-UC-05-AMF-FR-0002';
const SPANISH_REGION_SKU = 'S22-UC-05-AMF-ES-0002';
const UK_REGION_SKU = 'S22-UC-05-AMF-UK-0002';
const NON_REGION_SKU = 'T7-S-07-AMC-TES';

describe('ChargerService', () => {
  let service: ChargerService;
  let assetSummaryApi: AssetsApiClientAssetSummaryApi;
  let chargeOverridesApi: SmartChargingServiceClientChargeOverridesApi;
  let chargeSchedulesAPI3Api: SmartChargingServiceClientChargeSchedulesAPI3Api;
  let chargerChargesService: ChargerChargesService;
  let chargerConfigService: ChargerConfigService;
  let competitionApi: CompetitionsServiceClientCompetitionApi;
  let connectivityStatusApi: ConnectivityServiceClientConnectivityStatusApi;
  let delegatedControlApi: SmartChargingServiceClientDelegatedControlChargingStationsApi;
  let firmwareInfoApi: FirmwareUpgradeClientInfoApi;
  let installsApi: InstallerApiInstallsApi;
  let salesforceClient: SalesforceClient;
  let siteAdminApi: SiteAdminChargerApi;
  let subscriptionsApi: SubscriptionsApi;
  let tariffsSuppliersApi: TariffsSuppliersApi;
  let tariffsChargingStationsApi: TariffsChargingStationTariffsApi;
  let usersApi: DriverAccountApiClientUsersApi;
  let vehiclesApi: SmartChargingServiceClientVehiclesApi;
  let wifiCredentialsApi: AssetsProvisioningApiWifiCredentialsApi;

  beforeAll(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        ChargerChargesService,
        ChargerConfigService,
        ChargerService,
        {
          provide: AssetsApiClientAssetSummaryApi,
          useValue: new AssetsApiClientAssetSummaryApi(),
        },
        {
          provide: AssetsConfigurationGetApi,
          useValue: new AssetsConfigurationGetApi(),
        },
        {
          provide: AssetsProvisioningApiWifiCredentialsApi,
          useValue: new AssetsProvisioningApiWifiCredentialsApi(),
        },
        {
          provide: CompetitionsServiceClientCompetitionApi,
          useValue: new CompetitionsServiceClientCompetitionApi(),
        },
        {
          provide: ConnectivityServiceClientConnectivityStatusApi,
          useValue: new ConnectivityServiceClientConnectivityStatusApi(),
        },
        {
          provide: DataPlatformProjectionChargesApi,
          useValue: new DataPlatformProjectionChargesApi(),
        },
        {
          provide: DriverAccountApiClientUsersApi,
          useValue: new DriverAccountApiClientUsersApi(),
        },
        {
          provide: FirmwareUpgradeClientInfoApi,
          useValue: new FirmwareUpgradeClientInfoApi(),
        },
        {
          provide: InstallerApiInstallsApi,
          useValue: new InstallerApiInstallsApi(),
        },
        {
          provide: SalesforceClient,
          useValue: new SalesforceClient({
            clientId: faker.string.uuid(),
            instanceUrl: faker.internet.url(),
            maxRetry: faker.number.int(),
            privateKey: faker.string.alphanumeric(),
            userAgent: faker.internet.userAgent(),
            username: faker.internet.email(),
          }),
        },
        {
          provide: SiteAdminChargerApi,
          useValue: new SiteAdminChargerApi(),
        },
        {
          provide: SmartChargingServiceClientChargeOverridesApi,
          useValue: new SmartChargingServiceClientChargeOverridesApi(),
        },
        {
          provide: SmartChargingServiceClientChargeSchedulesAPI3Api,
          useValue: new SmartChargingServiceClientChargeSchedulesAPI3Api(),
        },
        {
          provide:
            SmartChargingServiceClientDelegatedControlChargingStationsApi,
          useValue:
            new SmartChargingServiceClientDelegatedControlChargingStationsApi(),
        },
        {
          provide: SmartChargingServiceClientVehiclesApi,
          useValue: new SmartChargingServiceClientVehiclesApi(),
        },
        {
          provide: SubscriptionsApi,
          useValue: new SubscriptionsApi(),
        },
        {
          provide: TariffsSuppliersApi,
          useValue: new TariffsSuppliersApi(),
        },
        {
          provide: TariffsChargingStationTariffsApi,
          useValue: new TariffsChargingStationTariffsApi(),
        },
      ],
    }).compile();

    service = module.get<ChargerService>(ChargerService);
    assetSummaryApi = module.get<AssetsApiClientAssetSummaryApi>(
      AssetsApiClientAssetSummaryApi
    );
    chargeOverridesApi =
      module.get<SmartChargingServiceClientChargeOverridesApi>(
        SmartChargingServiceClientChargeOverridesApi
      );
    chargerChargesService = module.get<ChargerChargesService>(
      ChargerChargesService
    );
    chargerConfigService =
      module.get<ChargerConfigService>(ChargerConfigService);
    competitionApi = module.get<CompetitionsServiceClientCompetitionApi>(
      CompetitionsServiceClientCompetitionApi
    );
    connectivityStatusApi =
      module.get<ConnectivityServiceClientConnectivityStatusApi>(
        ConnectivityServiceClientConnectivityStatusApi
      );
    delegatedControlApi =
      module.get<SmartChargingServiceClientDelegatedControlChargingStationsApi>(
        SmartChargingServiceClientDelegatedControlChargingStationsApi
      );
    firmwareInfoApi = module.get<FirmwareUpgradeClientInfoApi>(
      FirmwareUpgradeClientInfoApi
    );
    installsApi = module.get<InstallerApiInstallsApi>(InstallerApiInstallsApi);
    salesforceClient = module.get<SalesforceClient>(SalesforceClient);
    siteAdminApi = module.get<SiteAdminChargerApi>(SiteAdminChargerApi);
    subscriptionsApi = module.get<SubscriptionsApi>(SubscriptionsApi);
    tariffsSuppliersApi = module.get<TariffsSuppliersApi>(TariffsSuppliersApi);
    tariffsChargingStationsApi = module.get<TariffsChargingStationTariffsApi>(
      TariffsChargingStationTariffsApi
    );
    usersApi = module.get<DriverAccountApiClientUsersApi>(
      DriverAccountApiClientUsersApi
    );
    vehiclesApi = module.get<SmartChargingServiceClientVehiclesApi>(
      SmartChargingServiceClientVehiclesApi
    );
    wifiCredentialsApi = module.get<AssetsProvisioningApiWifiCredentialsApi>(
      AssetsProvisioningApiWifiCredentialsApi
    );
    chargeSchedulesAPI3Api =
      module.get<SmartChargingServiceClientChargeSchedulesAPI3Api>(
        SmartChargingServiceClientChargeSchedulesAPI3Api
      );

    MockDate.set('2021-01-01');
  });

  afterEach(() => {
    jest.resetAllMocks();
  });

  afterAll(() => {
    MockDate.reset();
  });

  it('should be defined', () => {
    expect(service).toBeDefined();
    expect(assetSummaryApi).toBeDefined();
    expect(chargeOverridesApi).toBeDefined();
    expect(chargerChargesService).toBeDefined();
    expect(chargerConfigService).toBeDefined();
    expect(competitionApi).toBeDefined();
    expect(connectivityStatusApi).toBeDefined();
    expect(delegatedControlApi).toBeDefined();
    expect(firmwareInfoApi).toBeDefined();
    expect(installsApi).toBeDefined();
    expect(salesforceClient).toBeDefined();
    expect(siteAdminApi).toBeDefined();
    expect(subscriptionsApi).toBeDefined();
    expect(tariffsSuppliersApi).toBeDefined();
    expect(tariffsChargingStationsApi).toBeDefined();
    expect(usersApi).toBeDefined();
    expect(vehiclesApi).toBeDefined();
    expect(wifiCredentialsApi).toBeDefined();
  });

  it('should find by ppid', async () => {
    const {
      chargerControllerFindByIdentifier,
      getAsset,
      getChargingStation,
      getChargingStationChargeSchedules,
      getChargingStationProgrammes,
      getChargingStationStatus,
      getConfiguration,
      getCurrentFirmware,
      getDelegatedControlChargingStationByPpid,
      getRecentCharges,
      getVehicles,
      getWifiCredentials,
      installsControllerGetInstall,
      searchActiveOverrides,
      subscriptionsControllerSearch,
      tariffsSuppliersGet,
      tariffsChargingStationsGet,
      userControllerGetByFilter,
    } = setUpMocks();

    const { ppid } = mockSummary;
    const charger = await service.findByPpid(
      ppid,
      'A',
      false,
      TEST_OIDC_USER_WITH_ROLES
    );

    expect(charger).toEqual(mockCharger);
    expectMocksToHaveBeenCalledWith(ppid, TEST_OIDC_USER_WITH_ROLES, {
      chargerControllerFindByIdentifier,
      getAsset,
      getChargingStation,
      getChargingStationChargeSchedules,
      getChargingStationProgrammes,
      getChargingStationStatus,
      getConfiguration,
      getCurrentFirmware,
      getDelegatedControlChargingStationByPpid,
      getRecentCharges,
      getVehicles,
      getWifiCredentials,
      installsControllerGetInstall,
      searchActiveOverrides,
      subscriptionsControllerSearch,
      tariffsSuppliersGet,
      tariffsChargingStationsGet,
      userControllerGetByFilter,
    });
  });

  it.each([
    [FRENCH_REGION_SKU, [OidcRoles.CHARGER_FR_VIEW_ALL]],
    [SPANISH_REGION_SKU, [OidcRoles.CHARGER_ES_VIEW_ALL]],
    [FRENCH_REGION_SKU, [OidcRoles.CHARGER_VIEW_ALL]],
    [SPANISH_REGION_SKU, [OidcRoles.CHARGER_VIEW_ALL]],
    [UK_REGION_SKU, [OidcRoles.CHARGER_VIEW_ALL]],
    [NON_REGION_SKU, [OidcRoles.CHARGER_VIEW_ALL]],
    [undefined, [OidcRoles.CHARGER_VIEW_ALL]],
  ])(
    'should find by ppid when the region of the sku %s can be accessed by a user with role %s',
    async (sku, roles) => {
      const {
        chargerControllerFindByIdentifier,
        getAsset,
        getChargingStationChargeSchedules,
        getChargingStationProgrammes,
        getChargingStationStatus,
        getConfiguration,
        getCurrentFirmware,
        getDelegatedControlChargingStationByPpid,
        getRecentCharges,
        getVehicles,
        getWifiCredentials,
        installsControllerGetInstall,
        searchActiveOverrides,
        subscriptionsControllerSearch,
        tariffsSuppliersGet,
        tariffsChargingStationsGet,
        userControllerGetByFilter,
      } = setUpMocks();

      const mockSummaryWithSku = {
        ...mockSummary,
        model: { ...mockSummary.model, sku },
      };
      const getChargingStation = jest
        .spyOn(assetSummaryApi, 'getChargingStation')
        .mockResolvedValue({
          data: mockSummaryWithSku,
          status: 200,
        } as AxiosResponse);
      const { ppid } = mockSummaryWithSku;
      const user = {
        ...TEST_OIDC_USER_WITH_ROLES,
        roles,
      };

      const charger = await service.findByPpid(ppid, 'A', false, user);

      expect(charger).toEqual(
        setIn(mockCharger, ['summary', 'model', 'sku'], sku)
      );
      expectMocksToHaveBeenCalledWith(ppid, user, {
        chargerControllerFindByIdentifier,
        getAsset,
        getChargingStation,
        getChargingStationChargeSchedules,
        getChargingStationProgrammes,
        getChargingStationStatus,
        getConfiguration,
        getCurrentFirmware,
        getDelegatedControlChargingStationByPpid,
        getRecentCharges,
        getVehicles,
        getWifiCredentials,
        installsControllerGetInstall,
        searchActiveOverrides,
        subscriptionsControllerSearch,
        tariffsSuppliersGet,
        tariffsChargingStationsGet,
        userControllerGetByFilter,
      });
    }
  );

  it('should find by ppid and throw not found exception', async () => {
    jest.spyOn(assetSummaryApi, 'getChargingStation').mockRejectedValue({
      status: 404,
    } as AxiosResponse);

    await expect(
      service.findByPpid(
        mockSummary.ppid,
        'A',
        false,
        TEST_OIDC_USER_WITH_ROLES
      )
    ).rejects.toThrow(NotFoundException);
  });

  it.each(['pg-12345', 'pG-12345', 'psl-12345', 'pSL-12345'])(
    'should find by ppid and handle not found exception if ppid is not in upper case (%s)',
    async (ppid) => {
      const {
        chargerControllerFindByIdentifier,
        getAsset,
        getChargingStation,
        getChargingStationChargeSchedules,
        getChargingStationProgrammes,
        getChargingStationStatus,
        getConfiguration,
        getCurrentFirmware,
        getDelegatedControlChargingStationByPpid,
        getRecentCharges,
        getVehicles,
        getWifiCredentials,
        installsControllerGetInstall,
        searchActiveOverrides,
        subscriptionsControllerSearch,
        tariffsSuppliersGet,
        tariffsChargingStationsGet,
        userControllerGetByFilter,
      } = setUpMocks();

      getChargingStation
        .mockRejectedValueOnce({
          status: 404,
        } as AxiosResponse)
        .mockResolvedValue({
          data: { ...mockSummary, ppid },
          status: 200,
        } as AxiosResponse);

      await service.findByPpid(ppid, 'A', false, TEST_OIDC_USER_WITH_ROLES);

      expectMocksToHaveBeenCalledWith(ppid, TEST_OIDC_USER_WITH_ROLES, {
        chargerControllerFindByIdentifier,
        getAsset,
        getChargingStation,
        getChargingStationChargeSchedules,
        getChargingStationProgrammes,
        getChargingStationStatus,
        getConfiguration,
        getCurrentFirmware,
        getDelegatedControlChargingStationByPpid,
        getRecentCharges,
        getVehicles,
        getWifiCredentials,
        installsControllerGetInstall,
        searchActiveOverrides,
        subscriptionsControllerSearch,
        tariffsSuppliersGet,
        tariffsChargingStationsGet,
        userControllerGetByFilter,
      });
      expect(getChargingStation).toHaveBeenNthCalledWith(1, ppid);
      expect(getChargingStation).toHaveBeenNthCalledWith(2, ppid.toUpperCase());
    }
  );

  it.each(['VEEFIL-12345', 'T53_HU1_4718_012', 'T54_HU1_3822_035'])(
    'should find by ppid and handle not found exception if ppid is not in lower case (%s)',
    async (ppid) => {
      const {
        chargerControllerFindByIdentifier,
        getAsset,
        getChargingStation,
        getChargingStationChargeSchedules,
        getChargingStationProgrammes,
        getChargingStationStatus,
        getConfiguration,
        getCurrentFirmware,
        getDelegatedControlChargingStationByPpid,
        getRecentCharges,
        getVehicles,
        getWifiCredentials,
        installsControllerGetInstall,
        searchActiveOverrides,
        subscriptionsControllerSearch,
        tariffsSuppliersGet,
        tariffsChargingStationsGet,
        userControllerGetByFilter,
      } = setUpMocks();
      getChargingStation
        .mockRejectedValueOnce({
          status: 404,
        } as AxiosResponse)
        .mockResolvedValue({
          data: { ...mockSummary, ppid },
          status: 200,
        } as AxiosResponse);

      await service.findByPpid(ppid, 'A', false, TEST_OIDC_USER_WITH_ROLES);

      expectMocksToHaveBeenCalledWith(ppid, TEST_OIDC_USER_WITH_ROLES, {
        chargerControllerFindByIdentifier,
        getAsset,
        getChargingStation,
        getChargingStationChargeSchedules,
        getChargingStationProgrammes,
        getChargingStationStatus,
        getConfiguration,
        getCurrentFirmware,
        getDelegatedControlChargingStationByPpid,
        getRecentCharges,
        getVehicles,
        getWifiCredentials,
        installsControllerGetInstall,
        searchActiveOverrides,
        subscriptionsControllerSearch,
        tariffsSuppliersGet,
        tariffsChargingStationsGet,
        userControllerGetByFilter,
      });
      expect(getChargingStation).toHaveBeenNthCalledWith(1, ppid);
      expect(getChargingStation).toHaveBeenNthCalledWith(2, ppid.toLowerCase());
    }
  );

  it('should redact sensitive data from wifi credentials', async () => {
    setUpMocks();

    const charger = await service.findByPpid(
      mockSummary.ppid,
      'A',
      true,
      TEST_OIDC_USER_WITH_ROLES
    );

    expect(charger).toBeDefined();
    expect(charger.wifiCredentials).toBeDefined();
    expect(charger.wifiCredentials?.ssid).toBeDefined();
    expect(charger.wifiCredentials?.password).toBeUndefined();
  });

  it('should not redact sensitive data from wifi credentials', async () => {
    setUpMocks();

    const charger = await service.findByPpid(
      mockSummary.ppid,
      'A',
      false,
      TEST_OIDC_USER_WITH_ROLES
    );

    expect(charger).toBeDefined();
    expect(charger.wifiCredentials).toBeDefined();
    expect(charger.wifiCredentials?.ssid).toBeDefined();
    expect(charger.wifiCredentials?.password).toBeDefined();
  });

  it.each([
    [FRENCH_REGION_SKU, [OidcRoles.CHARGER_ES_VIEW_ALL]],
    [SPANISH_REGION_SKU, [OidcRoles.CHARGER_FR_VIEW_ALL]],
    [UK_REGION_SKU, [OidcRoles.CHARGER_FR_VIEW_ALL]],
    [UK_REGION_SKU, [OidcRoles.CHARGER_ES_VIEW_ALL]],
    [NON_REGION_SKU, [OidcRoles.CHARGER_ES_VIEW_ALL]],
    [NON_REGION_SKU, [OidcRoles.CHARGER_FR_VIEW_ALL]],
    [NON_REGION_SKU, []],
    [undefined, []],
  ])(
    'should throw not found exception if the user should not have access for sku %s with role %s',
    async (sku, roles) => {
      const mockSummaryWithSku = {
        ...mockSummary,
        model: { ...mockSummary.model, sku },
      };
      jest
        .spyOn(assetSummaryApi, 'getChargingStation')
        .mockResolvedValue({ data: mockSummary, status: 200 } as AxiosResponse);

      await expect(
        service.findByPpid(mockSummaryWithSku.ppid, 'A', false, {
          ...TEST_OIDC_USER_WITH_ROLES,
          roles,
        })
      ).rejects.toThrow(NotFoundException);
    }
  );

  it('should get the charger name by ppid if it is not present in commercial attributes', async () => {
    setUpMocks();
    jest
      .spyOn(siteAdminApi, 'chargerControllerFindByIdentifier')
      .mockResolvedValue({
        status: 404,
      } as AxiosResponse);
    const mockGetChargerName = jest
      .spyOn(siteAdminApi, 'chargerControllerFindChargerNameByPpid')
      .mockResolvedValue({
        data: 'Test-Name',
        status: 200,
      } as AxiosResponse);

    const charger = await service.findByPpid(
      mockSummary.ppid,
      'A',
      false,
      TEST_OIDC_USER_WITH_ROLES
    );

    expect(charger).toEqual({
      ...mockCharger,
      commercialAttributes: undefined,
      name: 'Test-Name',
    });
    expect(mockGetChargerName).toHaveBeenCalledWith(mockSummary.ppid);
  });

  it('should get a charger with a subset of attributes by ppid', async () => {
    setUpMocks();
    jest
      .spyOn(siteAdminApi, 'chargerControllerFindChargerNameByPpid')
      .mockResolvedValue({
        data: 'Test-Name',
        status: 200,
      } as AxiosResponse);
    const { ppid } = mockSummary;

    const charger = await service.findChargerAttributesByPpid(
      ppid,
      TEST_OIDC_USER_WITH_ROLES,
      ['name', 'summary', 'commercialAttributes']
    );

    expect(charger).toEqual({
      name: mockCharger.name,
      summary: mockCharger.summary,
      commercialAttributes: mockCharger.commercialAttributes,
    });
    expect(assetSummaryApi.getChargingStation).toHaveBeenCalledWith(ppid);
    expect(siteAdminApi.chargerControllerFindByIdentifier).toHaveBeenCalledWith(
      ppid
    );
  });

  const setUpMocks = () => {
    const chargerControllerFindByIdentifier = jest
      .spyOn(siteAdminApi, 'chargerControllerFindByIdentifier')
      .mockResolvedValue({
        data: mockCommercialAttributes,
        status: 200,
      } as AxiosResponse);
    const getAsset = jest
      .spyOn(salesforceClient, 'getAsset')
      .mockResolvedValueOnce(mockSalesforceAsset);
    const getChargingStationChargeSchedules = jest
      .spyOn(chargeSchedulesAPI3Api, 'getChargingStationChargeSchedules')
      .mockResolvedValue({
        data: mockChargeSchedules,
        status: 200,
      } as AxiosResponse);
    const getChargingStation = jest
      .spyOn(assetSummaryApi, 'getChargingStation')
      .mockResolvedValue({ data: mockSummary, status: 200 } as AxiosResponse);
    const getChargingStationProgrammes = jest
      .spyOn(competitionApi, 'getChargingStationProgrammes')
      .mockResolvedValue({
        data: mockChargingStationProgrammes,
        status: 200,
      } as AxiosResponse);
    const getChargingStationStatus = jest
      .spyOn(connectivityStatusApi, 'getChargingStationStatus')
      .mockResolvedValue({
        data: mockConnectivityStatus,
        status: 200,
      } as AxiosResponse);
    const getConfiguration = jest
      .spyOn(chargerConfigService, 'getConfiguration')
      .mockResolvedValue(mockConfiguration.data);
    const getCurrentFirmware = jest
      .spyOn(firmwareInfoApi, 'getCurrentFirmware')
      .mockResolvedValue({
        data: mockCurrentFirmware,
        status: 200,
      } as AxiosResponse);
    const getDelegatedControlChargingStationByPpid = jest
      .spyOn(delegatedControlApi, 'getDelegatedControlChargingStationByPpid')
      .mockResolvedValue({
        data: mockDelegatedControlStatus,
        status: 200,
      } as AxiosResponse);
    const getRecentCharges = jest
      .spyOn(chargerChargesService, 'getRecentCharges')
      .mockResolvedValue(mockChargeHistory.data);
    const getWifiCredentials = jest
      .spyOn(wifiCredentialsApi, 'getWifiCredentials')
      .mockResolvedValue({
        data: mockWifiCredentials,
        status: 200,
      } as AxiosResponse);
    const installsControllerGetInstall = jest
      .spyOn(installsApi, 'installsControllerGetInstall')
      .mockResolvedValue({
        data: mockInstallationDetails,
        status: 200,
      } as AxiosResponse);
    const searchActiveOverrides = jest
      .spyOn(chargeOverridesApi, 'searchActiveOverrides')
      .mockResolvedValue({
        data: mockChargeOverrides,
        status: 200,
      } as AxiosResponse);
    const subscriptionsControllerSearch = jest
      .spyOn(subscriptionsApi, 'subscriptionsControllerSearch')
      .mockResolvedValue({
        data: mockSubscriptions,
        status: 200,
      } as AxiosResponse);
    const tariffsSuppliersGet = jest
      .spyOn(tariffsSuppliersApi, 'getSuppliers')
      .mockResolvedValue({
        data: mockTariffSuppliers,
        status: 200,
      } as AxiosResponse);
    const tariffsChargingStationsGet = jest
      .spyOn(tariffsChargingStationsApi, 'getTariffsByPpid')
      .mockResolvedValue({
        data: mockTariffs,
        status: 200,
      } as AxiosResponse);
    const userControllerGetByFilter = jest
      .spyOn(usersApi, 'userControllerGetByFilter')
      .mockResolvedValue({
        data: [mockLinkedUsers[0]],
        status: 200,
      } as AxiosResponse);
    const getVehicles = jest
      .spyOn(vehiclesApi, 'getDelegatedControlChargingStationVehicles')
      .mockResolvedValue({
        data: mockVehicles,
        status: 200,
      } as AxiosResponse);

    return {
      chargerControllerFindByIdentifier,
      getAsset,
      getChargingStation,
      getChargingStationChargeSchedules,
      getChargingStationProgrammes,
      getChargingStationStatus,
      getConfiguration,
      getCurrentFirmware,
      getDelegatedControlChargingStationByPpid,
      getRecentCharges,
      getVehicles,
      getWifiCredentials,
      installsControllerGetInstall,
      searchActiveOverrides,
      subscriptionsControllerSearch,
      tariffsSuppliersGet,
      tariffsChargingStationsGet,
      userControllerGetByFilter,
    };
  };

  const expectMocksToHaveBeenCalledWith = (
    ppid: string,
    user: OidcUser,
    {
      chargerControllerFindByIdentifier,
      getAsset,
      getChargingStation,
      getChargingStationChargeSchedules,
      getChargingStationProgrammes,
      getChargingStationStatus,
      getConfiguration,
      getCurrentFirmware,
      getDelegatedControlChargingStationByPpid,
      getRecentCharges,
      getVehicles,
      getWifiCredentials,
      installsControllerGetInstall,
      searchActiveOverrides,
      subscriptionsControllerSearch,
      tariffsSuppliersGet,
      tariffsChargingStationsGet,
      userControllerGetByFilter,
    }: {
      chargerControllerFindByIdentifier: jest.SpyInstance;
      getAsset: jest.SpyInstance;
      getChargingStation: jest.SpyInstance;
      getChargingStationChargeSchedules: jest.SpyInstance;
      getChargingStationProgrammes: jest.SpyInstance;
      getChargingStationStatus: jest.SpyInstance;
      getConfiguration: jest.SpyInstance;
      getCurrentFirmware: jest.SpyInstance;
      getDelegatedControlChargingStationByPpid: jest.SpyInstance;
      getRecentCharges: jest.SpyInstance;
      getVehicles: jest.SpyInstance;
      getWifiCredentials: jest.SpyInstance;
      installsControllerGetInstall: jest.SpyInstance;
      searchActiveOverrides: jest.SpyInstance;
      subscriptionsControllerSearch: jest.SpyInstance;
      tariffsSuppliersGet: jest.SpyInstance;
      tariffsChargingStationsGet: jest.SpyInstance;
      userControllerGetByFilter: jest.SpyInstance;
    }
  ) => {
    expect(chargerControllerFindByIdentifier).toHaveBeenCalledWith(ppid);
    expect(getAsset).toHaveBeenCalledWith(ppid);
    expect(getChargingStation).toHaveBeenCalledWith(ppid);
    expect(getChargingStationChargeSchedules).toHaveBeenCalledWith(ppid);
    expect(getChargingStationProgrammes).toHaveBeenCalledWith(ppid, false);
    expect(getChargingStationStatus).toHaveBeenCalledWith(ppid);
    expect(getConfiguration).toHaveBeenCalledWith(ppid, 'A', false, user);
    expect(getCurrentFirmware).toHaveBeenCalledWith(ppid);
    expect(getDelegatedControlChargingStationByPpid).toHaveBeenCalledWith(ppid);
    expect(getRecentCharges).toHaveBeenCalledWith(ppid, user, '2020-12-01');
    expect(getVehicles).toHaveBeenCalledWith(ppid);
    expect(getWifiCredentials).toHaveBeenCalledWith(ppid);
    expect(installsControllerGetInstall).toHaveBeenCalledWith(ppid);
    expect(searchActiveOverrides).toHaveBeenCalledWith(ppid);
    expect(subscriptionsControllerSearch).toHaveBeenCalledWith(undefined, ppid);
    expect(tariffsSuppliersGet).toHaveBeenCalled();
    expect(tariffsChargingStationsGet).toHaveBeenCalledWith(
      ppid,
      '2021-01-01',
      '2021-01-01'
    );
    expect(userControllerGetByFilter).toHaveBeenCalledWith(
      ppid,
      undefined,
      undefined
    );
  };
});
