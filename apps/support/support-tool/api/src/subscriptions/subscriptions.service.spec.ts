import { AccountsService } from '../accounts/accounts.service';
import { AxiosResponse } from 'axios';
import { Logger } from '@nestjs/common';
import { SubscriptionsApi } from '@experience/mobile/subscriptions-api/axios';
import { SubscriptionsService } from './subscriptions.service';
import {
  TEST_ACCOUNT,
  TEST_SUBSCRIPTION,
} from '@experience/support/support-tool/shared/specs';
import { TEST_OIDC_USER } from '@experience/shared/typescript/oidc-utils/specs';
import { Test, TestingModule } from '@nestjs/testing';
import { mockDeep } from 'jest-mock-extended';

jest.mock('../accounts/accounts.service');

describe('subscriptions service', () => {
  let subscriptionsService: SubscriptionsService;
  let accountsService: AccountsService;
  let subscriptionsApi: SubscriptionsApi;
  const logger = mockDeep<Logger>();

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      imports: [],
      providers: [
        AccountsService,
        SubscriptionsService,
        {
          provide: SubscriptionsApi,
          useValue: new SubscriptionsApi(),
        },
      ],
    }).compile();

    subscriptionsService =
      module.get<SubscriptionsService>(SubscriptionsService);
    accountsService = module.get<AccountsService>(AccountsService);
    subscriptionsApi = module.get<SubscriptionsApi>(SubscriptionsApi);

    module.useLogger(logger);
  });

  it('should be defined', () => {
    expect(subscriptionsService).toBeDefined();
    expect(accountsService).toBeDefined();
    expect(subscriptionsApi).toBeDefined();
  });

  it('should find subscriptions', async () => {
    const mockFindAccounts = jest
      .spyOn(accountsService, 'searchForAccounts')
      .mockResolvedValue([TEST_ACCOUNT]);
    const mockSearchSubscriptions = jest
      .spyOn(subscriptionsApi, 'subscriptionsControllerSearch')
      .mockResolvedValueOnce({
        data: {
          subscriptions: [],
        },
      } as AxiosResponse)
      .mockResolvedValueOnce({
        data: {
          subscriptions: [
            {
              id: 's3daf6b8-1234-5678-90ab-cdef12345678',
              order: {
                email: TEST_ACCOUNT.email,
                firstName: TEST_ACCOUNT.firstName,
                lastName: TEST_ACCOUNT.lastName,
                orderedAt: '2025-06-01T00:00:00.000Z',
              },
              status: 'ACTIVE',
            },
          ],
        },
      } as AxiosResponse);

    const subscriptions = await subscriptionsService.find(TEST_ACCOUNT.email);

    expect(subscriptions).toEqual([TEST_SUBSCRIPTION]);
    expect(mockFindAccounts).toHaveBeenCalledWith(TEST_ACCOUNT.email);
    expect(mockSearchSubscriptions).toHaveBeenNthCalledWith(
      1,
      undefined,
      TEST_ACCOUNT.email
    );
    expect(mockSearchSubscriptions).toHaveBeenNthCalledWith(
      2,
      TEST_ACCOUNT.authId
    );
  });

  it('should find subscriptions by PPID', async () => {
    jest.spyOn(accountsService, 'searchForAccounts').mockResolvedValue([]);
    const mockSearchSubscriptions = jest
      .spyOn(subscriptionsApi, 'subscriptionsControllerSearch')
      .mockResolvedValue({
        data: {
          subscriptions: [
            {
              id: 's3daf6b8-1234-5678-90ab-cdef12345678',
              order: {
                email: TEST_ACCOUNT.email,
                firstName: TEST_ACCOUNT.firstName,
                lastName: TEST_ACCOUNT.lastName,
                orderedAt: '2025-06-01T00:00:00.000Z',
              },
              status: 'ACTIVE',
            },
          ],
        },
      } as AxiosResponse);

    const subscriptions = await subscriptionsService.find('PSL-1234567');

    expect(subscriptions).toEqual([TEST_SUBSCRIPTION]);
    expect(mockSearchSubscriptions).toHaveBeenCalledWith(
      undefined,
      'PSL-1234567'
    );
  });

  it('should return multiple subscriptions if multiple accounts are found with matching subscriptions', async () => {
    const mockFindAccounts = jest
      .spyOn(accountsService, 'searchForAccounts')
      .mockResolvedValue([
        TEST_ACCOUNT,
        { ...TEST_ACCOUNT, authId: 'fadb7184-9ffc-4950-8559-c9306456f6e4' },
      ]);
    const mockSearchSubscriptions = jest
      .spyOn(subscriptionsApi, 'subscriptionsControllerSearch')
      .mockResolvedValueOnce({
        data: {
          subscriptions: [],
        },
      } as AxiosResponse)
      .mockResolvedValueOnce({
        data: {
          subscriptions: [
            {
              id: 's3daf6b8-1234-5678-90ab-cdef12345678',
              order: {
                email: TEST_ACCOUNT.email,
                firstName: TEST_ACCOUNT.firstName,
                lastName: TEST_ACCOUNT.lastName,
                orderedAt: '2025-06-01T00:00:00.000Z',
              },
              status: 'ACTIVE',
            },
          ],
        },
      } as AxiosResponse)
      .mockResolvedValueOnce({
        data: {
          subscriptions: [
            {
              id: '7f1f6a53-8e07-4376-be95-e02bb1228461',
              order: {
                email: TEST_ACCOUNT.email,
                firstName: TEST_ACCOUNT.firstName,
                lastName: TEST_ACCOUNT.lastName,
                orderedAt: '2025-07-01T00:00:00.000Z',
              },
              status: 'ACTIVE',
            },
          ],
        },
      } as AxiosResponse);

    const subscriptions = await subscriptionsService.find(TEST_ACCOUNT.email);

    expect(subscriptions).toEqual([
      { ...TEST_SUBSCRIPTION, orderedAt: '2025-06-01T00:00:00.000Z' },
      {
        ...TEST_SUBSCRIPTION,
        id: '7f1f6a53-8e07-4376-be95-e02bb1228461',
        orderedAt: '2025-07-01T00:00:00.000Z',
      },
    ]);
    expect(mockFindAccounts).toHaveBeenCalledWith(TEST_ACCOUNT.email);
    expect(mockSearchSubscriptions).toHaveBeenNthCalledWith(
      1,
      undefined,
      TEST_ACCOUNT.email
    );
    expect(mockSearchSubscriptions).toHaveBeenNthCalledWith(
      2,
      TEST_ACCOUNT.authId
    );
    expect(mockSearchSubscriptions).toHaveBeenNthCalledWith(
      3,
      'fadb7184-9ffc-4950-8559-c9306456f6e4'
    );
  });

  it('should return empty list if no subscriptions are found when finding subscriptions for user email', async () => {
    jest
      .spyOn(accountsService, 'searchForAccounts')
      .mockResolvedValue([TEST_ACCOUNT]);
    jest
      .spyOn(subscriptionsApi, 'subscriptionsControllerSearch')
      .mockResolvedValue({
        data: { subscriptions: [] },
      } as AxiosResponse);

    expect(await subscriptionsService.find(TEST_ACCOUNT.email)).toEqual([]);
  });

  it('should find subscription by ID', async () => {
    const mockGetBySubscriptionId = jest
      .spyOn(subscriptionsApi, 'subscriptionsControllerGetBySubscriptionId')
      .mockResolvedValue({
        data: TEST_SUBSCRIPTION,
      } as AxiosResponse);

    const subscription = await subscriptionsService.findById(
      TEST_SUBSCRIPTION.id,
      TEST_OIDC_USER
    );

    expect(subscription).toEqual(TEST_SUBSCRIPTION);
    expect(mockGetBySubscriptionId).toHaveBeenCalledWith(TEST_SUBSCRIPTION.id);
  });

  it('should log an error and return undefined if an error occurs when finding subscription by ID', async () => {
    const error = new Error('Subscription not found');
    const mockGetBySubscriptionId = jest
      .spyOn(subscriptionsApi, 'subscriptionsControllerGetBySubscriptionId')
      .mockRejectedValue(error);

    const subscription = await subscriptionsService.findById(
      TEST_SUBSCRIPTION.id,
      TEST_OIDC_USER
    );

    expect(subscription).toBeUndefined();
    expect(mockGetBySubscriptionId).toHaveBeenCalledWith(TEST_SUBSCRIPTION.id);
    expect(logger.error).toHaveBeenCalledWith(
      {
        error,
        subscriptionId: TEST_SUBSCRIPTION.id,
        user: TEST_OIDC_USER,
      },
      'error finding subscription by ID',
      'SubscriptionsService'
    );
  });
});
