###################
# BUILD
###################
FROM 591651182110.dkr.ecr.eu-west-1.amazonaws.com/ecr-public/docker/library/node:22-alpine@sha256:5539840ce9d013fa13e3b9814c9353024be7ac75aca5db6d039504a56c04ea59 AS builder

WORKDIR /usr/src/app

COPY dist/apps/support/support-tool/api/package*.json ./

ENV PUPPETEER_SKIP_CHROMIUM_DOWNLOAD=true

RUN npm ci && npm cache clean --force

###################
# PRODUCTION
###################
FROM 591651182110.dkr.ecr.eu-west-1.amazonaws.com/ecr-public/docker/library/node:22-alpine@sha256:5539840ce9d013fa13e3b9814c9353024be7ac75aca5db6d039504a56c04ea59

ARG APPLICATION_VERSION
ARG SENTRY_RELEASE

ENV APPLICATION_VERSION=$APPLICATION_VERSION
ENV SENTRY_RELEASE=$SENTRY_RELEASE

# Add node modules from build image
COPY --chown=node:node --from=builder /usr/src/app/node_modules ./node_modules

# Add application source code
COPY --chown=node:node dist/apps/support/support-tool/api .

USER node

CMD [ "node", "main.js" ]

EXPOSE 7102
