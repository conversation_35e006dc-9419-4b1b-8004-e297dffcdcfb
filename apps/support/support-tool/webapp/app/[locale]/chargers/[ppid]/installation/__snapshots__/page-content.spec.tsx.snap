// Jest Snapshot v1, https://goo.gl/fbAQLP

exports[`Charger installation page content should match snapshot %s 1`] = `
<body>
  <div>
    <a
      class="text-primary underline hover:decoration-2 focus:decoration-2 focus:outline-none cursor-pointer flex items-center gap-1 mb-2 font-bold max-w-fit"
      href="/chargers/PSL-12345"
    >
      <svg
        class="fill-current w-4 h-4"
        viewBox="0 0 24 24"
        xmlns="http://www.w3.org/2000/svg"
      >
        <g
          fill="none"
        >
          <path
            d="M9.14 4L1 12.15L9.6 20.75"
            stroke="currentColor"
            stroke-linecap="round"
            stroke-linejoin="round"
            stroke-width="1.5"
          />
          <path
            d="M22.88 12.13H1"
            stroke="currentColor"
            stroke-linecap="round"
            stroke-linejoin="round"
            stroke-width="1.5"
          />
        </g>
      </svg>
      Back
    </a>
    <section
      class="p-3 rounded-sm bg-white"
    >
      <header>
        <div
          class="flex"
        >
          <div
            class="lg:flex items-center"
          >
            <div
              class="flex"
            >
              <h1
                class="text-xxl font-bold"
              >
                Installation details - PSL-12345 (Socket A)
              </h1>
            </div>
            <div
              class="lg:ml-6 my-4 lg:my-0"
            >
              <div
                class="flex space-x-4"
              >
                <button
                  class="px-2 font-semibold border-b outline-hidden focus-visible:ring-2 focus-visible:ring-info text-primary border-b-primary hover:text-primary cursor-default"
                  disabled=""
                >
                  Socket A
                </button>
                <button
                  class="px-2 font-semibold text-neutral hover:text-black border-b border-b-transparent outline-hidden focus-visible:ring-2 focus-visible:ring-info"
                >
                  Socket B
                </button>
                <button
                  class="px-2 font-semibold text-neutral hover:text-black border-b border-b-transparent outline-hidden focus-visible:ring-2 focus-visible:ring-info"
                >
                  Socket C
                </button>
                <button
                  class="px-2 font-semibold text-neutral hover:text-black border-b border-b-transparent outline-hidden focus-visible:ring-2 focus-visible:ring-info"
                >
                  Socket D
                </button>
                <button
                  class="px-2 font-semibold text-neutral hover:text-black border-b border-b-transparent outline-hidden focus-visible:ring-2 focus-visible:ring-info"
                >
                  Socket E
                </button>
                <button
                  class="px-2 font-semibold text-neutral hover:text-black border-b border-b-transparent outline-hidden focus-visible:ring-2 focus-visible:ring-info"
                >
                  Socket F
                </button>
                <button
                  class="px-2 font-semibold text-neutral hover:text-black border-b border-b-transparent outline-hidden focus-visible:ring-2 focus-visible:ring-info"
                >
                  Socket G
                </button>
                <button
                  class="px-2 font-semibold text-neutral hover:text-black border-b border-b-transparent outline-hidden focus-visible:ring-2 focus-visible:ring-info"
                >
                  Socket H
                </button>
              </div>
            </div>
          </div>
          <div
            class="ml-auto"
          />
        </div>
      </header>
    </section>
    <div
      class="pb-4"
    />
    <div
      class="rounded-md p-3 bg-info/10 border border-info"
    >
      <div
        class="flex"
      >
        <div
          class="shrink-0"
        >
          <span
            class="text-info"
          >
            <svg
              class="fill-current w-6 h-6"
              viewBox="0 0 32 32"
              xmlns="http://www.w3.org/2000/svg"
            >
              <g>
                <path
                  d="M16.1,29.43A13.43,13.43,0,1,1,29.53,16,13.45,13.45,0,0,1,16.1,29.43Zm0-24.86A11.43,11.43,0,1,0,27.53,16,11.45,11.45,0,0,0,16.1,4.57Z"
                />
                <path
                  d="M16,24.12a1,1,0,0,1-1-1V14.94a1,1,0,1,1,2,0v8.18A1,1,0,0,1,16,24.12Z"
                />
                <path
                  d="M16.1,10.89a1,1,0,0,1-1-1v-.2a1,1,0,0,1,2,0v.2A1,1,0,0,1,16.1,10.89Z"
                />
              </g>
            </svg>
          </span>
        </div>
        <div
          class="flex flex-col justify-center ml-4"
        >
          <p
            class="text-md font-normal sr-only break-words"
          >
            Alert
          </p>
          <div
            class="text-info"
          >
            <p
              class="text-md font-normal break-words"
            >
              These are the values recorded at the point the charger was installed. They may differ from the current values and cannot be updated.
            </p>
          </div>
        </div>
      </div>
    </div>
    <div
      class="pb-4"
    />
    <section
      class="p-3 rounded-sm bg-white"
    >
      <div
        class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-2"
      >
        <div
          class="pb-2 break-words"
        >
          <h5
            class="text-lg"
          >
            Installation date
          </h5>
          <span
            class="text-xl font-semibold"
          >
            7th July 2023
          </span>
        </div>
        <div
          class="pb-2 break-words"
        >
          <h5
            class="text-lg"
          >
            Installed by
          </h5>
          <span
            class="text-xl font-semibold"
          >
            Joe Spark
          </span>
        </div>
        <div
          class="pb-2 break-words"
        >
          <h5
            class="text-lg"
          >
            Company
          </h5>
          <span
            class="text-xl font-semibold"
          >
            Electrical Services Ltd.
          </span>
        </div>
        <div
          class="pb-2 break-words"
        >
          <h5
            class="text-lg"
          >
            Out of service
          </h5>
          <span
            class="text-xl font-semibold"
          >
            Yes
          </span>
        </div>
      </div>
      <div
        class="pb-4"
      />
      <div
        class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-2"
      >
        <div
          class="pb-2 break-words"
        >
          <h5
            class="text-lg"
          >
            Max supply at CT clamp (Amps)
          </h5>
          <span
            class="text-xl font-semibold"
          >
            32
          </span>
        </div>
        <div
          class="pb-2 break-words"
        >
          <h5
            class="text-lg"
          >
            EV charger RCBO rating (Amps)
          </h5>
          <span
            class="text-xl font-semibold"
          >
            100
          </span>
        </div>
        <div
          class="pb-2 break-words"
        >
          <h5
            class="text-lg"
          >
            Charger rating (Amps)
          </h5>
          <span
            class="text-xl font-semibold"
          >
            50
          </span>
        </div>
      </div>
      <div
        class="pb-4"
      />
      <div
        class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-2"
      >
        <div
          class="pb-2 break-words"
        >
          <h5
            class="text-lg"
          >
            Power balancing installed
          </h5>
          <span
            class="text-xl font-semibold"
          >
            No
          </span>
        </div>
        <div
          class="pb-2 break-words"
        >
          <h5
            class="text-lg"
          >
            Power balancing enabled
          </h5>
          <span
            class="text-xl font-semibold"
          >
            No
          </span>
        </div>
        <div
          class="pb-2 break-words"
        >
          <h5
            class="text-lg"
          >
            Power balancing sensor
          </h5>
          <span
            class="text-xl font-semibold"
          >
            None
          </span>
        </div>
      </div>
      <div
        class="pb-4"
      />
      <div
        class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-2"
      >
        <div
          class="pb-2 break-words"
        >
          <h5
            class="text-lg"
          >
            PV solar system installed
          </h5>
          <span
            class="text-xl font-semibold"
          >
            No
          </span>
        </div>
      </div>
      <div
        class="pb-6"
      />
      <h5
        class="text-xxl"
      >
        Installation images
      </h5>
      <div
        class="pb-4"
      />
      <div
        aria-label="Photo album"
        class="react-photo-album react-photo-album--rows"
        role="group"
      />
    </section>
  </div>
</body>
`;
