// Jest Snapshot v1, https://goo.gl/fbAQLP

exports[`Charger configuration page should match snapshot 1`] = `
<body>
  <div>
    <div
      class="rounded-md p-3 bg-info/10 border border-info"
    >
      <div
        class="flex"
      >
        <div
          class="shrink-0"
        >
          <span
            class="text-info"
          >
            <svg
              class="fill-current w-6 h-6"
              viewBox="0 0 32 32"
              xmlns="http://www.w3.org/2000/svg"
            >
              <g>
                <path
                  d="M16.1,29.43A13.43,13.43,0,1,1,29.53,16,13.45,13.45,0,0,1,16.1,29.43Zm0-24.86A11.43,11.43,0,1,0,27.53,16,11.45,11.45,0,0,0,16.1,4.57Z"
                />
                <path
                  d="M16,24.12a1,1,0,0,1-1-1V14.94a1,1,0,1,1,2,0v8.18A1,1,0,0,1,16,24.12Z"
                />
                <path
                  d="M16.1,10.89a1,1,0,0,1-1-1v-.2a1,1,0,0,1,2,0v.2A1,1,0,0,1,16.1,10.89Z"
                />
              </g>
            </svg>
          </span>
        </div>
        <div
          class="flex flex-col justify-center ml-4"
        >
          <p
            class="text-md font-normal sr-only break-words"
          >
            Alert
          </p>
          <div
            class="text-info"
          >
            <p
              class="text-md font-normal break-words"
            >
              You can search for this charger in MIS 
              <a
                class="text-primary underline hover:decoration-2 focus:decoration-2 focus:outline-none cursor-pointer font-bold"
                href="https://admin.pod-point.com/units?search=PSL-12345"
                target="_blank"
              >
                here
              </a>
               or view this charger in Grafana 
              <a
                class="text-primary underline hover:decoration-2 focus:decoration-2 focus:outline-none cursor-pointer font-bold"
                href="https://g-e728fe26ac.grafana-workspace.eu-west-1.amazonaws.com/d/fdtbud32bw45ce/5b147138-e9f2-5846-ba34-c2778f5a748f?var-ppid=PSL-12345"
                target="_blank"
              >
                here
              </a>
              .
            </p>
          </div>
        </div>
      </div>
    </div>
    <div
      class="pb-2"
    />
    <a
      class="text-primary underline hover:decoration-2 focus:decoration-2 focus:outline-none cursor-pointer flex items-center gap-1 mb-2 font-bold max-w-fit"
      href="/chargers/PSL-12345"
    >
      <svg
        class="fill-current w-4 h-4"
        viewBox="0 0 24 24"
        xmlns="http://www.w3.org/2000/svg"
      >
        <g
          fill="none"
        >
          <path
            d="M9.14 4L1 12.15L9.6 20.75"
            stroke="currentColor"
            stroke-linecap="round"
            stroke-linejoin="round"
            stroke-width="1.5"
          />
          <path
            d="M22.88 12.13H1"
            stroke="currentColor"
            stroke-linecap="round"
            stroke-linejoin="round"
            stroke-width="1.5"
          />
        </g>
      </svg>
      Back
    </a>
    <section
      class="p-3 rounded-sm bg-white"
    >
      <header>
        <div
          class="flex"
        >
          <div
            class="lg:flex items-center"
          >
            <div
              class="flex"
            >
              <h1
                class="text-xxl font-bold"
              >
                Configuration - PSL-12345 (Socket B)
              </h1>
            </div>
            <div
              class="lg:ml-6 my-4 lg:my-0"
            >
              <div
                class="flex space-x-4"
              >
                <button
                  class="px-2 font-semibold text-neutral hover:text-black border-b border-b-transparent outline-hidden focus-visible:ring-2 focus-visible:ring-info"
                >
                  Socket A
                </button>
                <button
                  class="px-2 font-semibold border-b outline-hidden focus-visible:ring-2 focus-visible:ring-info text-primary border-b-primary hover:text-primary cursor-default"
                  disabled=""
                >
                  Socket B
                </button>
                <button
                  class="px-2 font-semibold text-neutral hover:text-black border-b border-b-transparent outline-hidden focus-visible:ring-2 focus-visible:ring-info"
                >
                  Socket C
                </button>
                <button
                  class="px-2 font-semibold text-neutral hover:text-black border-b border-b-transparent outline-hidden focus-visible:ring-2 focus-visible:ring-info"
                >
                  Socket D
                </button>
                <button
                  class="px-2 font-semibold text-neutral hover:text-black border-b border-b-transparent outline-hidden focus-visible:ring-2 focus-visible:ring-info"
                >
                  Socket E
                </button>
                <button
                  class="px-2 font-semibold text-neutral hover:text-black border-b border-b-transparent outline-hidden focus-visible:ring-2 focus-visible:ring-info"
                >
                  Socket F
                </button>
                <button
                  class="px-2 font-semibold text-neutral hover:text-black border-b border-b-transparent outline-hidden focus-visible:ring-2 focus-visible:ring-info"
                >
                  Socket G
                </button>
                <button
                  class="px-2 font-semibold text-neutral hover:text-black border-b border-b-transparent outline-hidden focus-visible:ring-2 focus-visible:ring-info"
                >
                  Socket H
                </button>
              </div>
            </div>
          </div>
          <div
            class="ml-auto"
          />
        </div>
      </header>
    </section>
    <div
      class="pb-4"
    />
    <form>
      <input
        name="ppid"
        type="hidden"
        value="PSL-12345"
      />
      <input
        name="socket"
        type="hidden"
        value="B"
      />
      <div
        class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-2 gap-2"
      >
        <section
          class="p-3 rounded-sm bg-white"
        >
          <h2
            class="text-lg font-bold"
          >
            Charger configuration
          </h2>
          <div
            class="pb-4"
          />
          <div>
            <div
              class="flex justify-between w-full"
            >
              <div
                data-headlessui-state=""
              >
                <div
                  class="mr-2"
                >
                  <label
                    class="text-md font-bold"
                    for="power-balancing-enabled"
                    id="power-balancing-enabled-label"
                  >
                    Power balancing enabled
                  </label>
                </div>
                <button
                  aria-checked="true"
                  aria-label="power-balancing-enabled"
                  class="bg-primary group relative inline-flex h-6 w-11 shrink-0 cursor-pointer rounded-full border-2 border-transparent transition-colors duration-200 ease-in-out focus:outline-hidden focus:ring-2 focus:ring-primary/50 focus:ring-offset-2 data-[disabled]:bg-neutral/40 data-[disabled]:cursor-not-allowed"
                  data-checked=""
                  data-headlessui-state="checked"
                  id="power-balancing-enabled"
                  role="switch"
                  tabindex="0"
                  type="button"
                >
                  <span
                    aria-hidden="true"
                    class="translate-x-5 pointer-events-none inline-block h-5 w-5 transform rounded-full bg-white shadow-sm ring-0 transition duration-200 ease-in-out group-data-[disabled]:bg-neutral/20 group-data-[disabled]:cursor-default"
                  />
                </button>
                <span
                  hidden=""
                  style="position: fixed; top: 1px; left: 1px; width: 1px; height: 0px; padding: 0px; margin: -1px; overflow: hidden; clip: rect(0px, 0px, 0px, 0px); white-space: nowrap; border-width: 0px; display: none;"
                >
                  <input
                    hidden=""
                    readonly=""
                    style="position: fixed; top: 1px; left: 1px; width: 1px; height: 0px; padding: 0px; margin: -1px; overflow: hidden; clip: rect(0px, 0px, 0px, 0px); white-space: nowrap; border-width: 0px; display: none;"
                    type="hidden"
                  />
                  <input
                    checked=""
                    hidden=""
                    name="power-balancing-enabled"
                    readonly=""
                    style="position: fixed; top: 1px; left: 1px; width: 1px; height: 0px; padding: 0px; margin: -1px; overflow: hidden; clip: rect(0px, 0px, 0px, 0px); white-space: nowrap; border-width: 0px; display: none;"
                    type="checkbox"
                    value="on"
                  />
                </span>
              </div>
            </div>
            <div
              class="pb-4"
            />
            <div
              data-headlessui-state=""
            >
              <label
                aria-labelledby="power-balancing-sensor"
                class="block mb-2 text-md font-bold"
                data-headlessui-state=""
                for="power-balancing-sensor"
                id="headlessui-label-:test-id-3"
              >
                Power balancing sensor
              </label>
              <div
                class="relative mt-1"
                data-headlessui-state=""
              >
                <button
                  aria-expanded="false"
                  aria-haspopup="listbox"
                  aria-labelledby="headlessui-label-:test-id-3 power-balancing-sensor"
                  class="relative w-full focus:outline-hidden focus:ring-2 focus:ring-primary/50 text-left text-neutral disabled:bg-neutral/20 disabled:placeholder-neutral py-2 pl-1.5 pr-10 rounded-sm bg-neutral/10 w-full"
                  data-headlessui-state=""
                  id="power-balancing-sensor"
                  type="button"
                >
                  <span
                    class="block truncate"
                  >
                    None
                  </span>
                  <span
                    class="pointer-events-none absolute inset-y-0 right-0 flex items-center pr-2"
                  >
                    <svg
                      aria-hidden="true"
                      class="fill-current w-4 h-2.5 text-neutral"
                      viewBox="0 0 32 32"
                      xmlns="http://www.w3.org/2000/svg"
                    >
                      <title>
                        drop down
                      </title>
                      <g>
                        <path
                          d="m13.15,18.84c-.57.64-1.55.69-2.19.11-.04-.04-.08-.07-.11-.11L.4,7.23c-.57-.64-.52-1.61.12-2.19.28-.25.65-.4,1.03-.4h20.91c.85,0,1.55.69,1.55,1.55,0,.38-.14.75-.4,1.03l-10.45,11.62Z"
                        />
                      </g>
                    </svg>
                  </span>
                </button>
              </div>
              <span
                hidden=""
                style="position: fixed; top: 1px; left: 1px; width: 1px; height: 0px; padding: 0px; margin: -1px; overflow: hidden; clip: rect(0px, 0px, 0px, 0px); white-space: nowrap; border-width: 0px; display: none;"
              >
                <input
                  hidden=""
                  readonly=""
                  style="position: fixed; top: 1px; left: 1px; width: 1px; height: 0px; padding: 0px; margin: -1px; overflow: hidden; clip: rect(0px, 0px, 0px, 0px); white-space: nowrap; border-width: 0px; display: none;"
                  type="hidden"
                />
                <input
                  hidden=""
                  name="power-balancing-sensor"
                  readonly=""
                  style="position: fixed; top: 1px; left: 1px; width: 1px; height: 0px; padding: 0px; margin: -1px; overflow: hidden; clip: rect(0px, 0px, 0px, 0px); white-space: nowrap; border-width: 0px; display: none;"
                  type="hidden"
                  value="NONE"
                />
              </span>
            </div>
            <div
              class="pb-4"
            />
            <div
              class="flex justify-between w-full"
            >
              <div
                data-headlessui-state=""
              >
                <div
                  class="mr-2"
                >
                  <label
                    class="text-md font-bold"
                    for="linky-schedules-enabled"
                    id="linky-schedules-enabled-label"
                  >
                    Linky schedules enabled
                  </label>
                </div>
                <button
                  aria-checked="false"
                  aria-label="linky-schedules-enabled"
                  class="bg-neutral/20 group relative inline-flex h-6 w-11 shrink-0 cursor-pointer rounded-full border-2 border-transparent transition-colors duration-200 ease-in-out focus:outline-hidden focus:ring-2 focus:ring-primary/50 focus:ring-offset-2 data-[disabled]:bg-neutral/40 data-[disabled]:cursor-not-allowed"
                  data-headlessui-state=""
                  id="linky-schedules-enabled"
                  role="switch"
                  tabindex="0"
                  type="button"
                >
                  <span
                    aria-hidden="true"
                    class="translate-x-0 pointer-events-none inline-block h-5 w-5 transform rounded-full bg-white shadow-sm ring-0 transition duration-200 ease-in-out group-data-[disabled]:bg-neutral/20 group-data-[disabled]:cursor-default"
                  />
                </button>
                <span
                  hidden=""
                  style="position: fixed; top: 1px; left: 1px; width: 1px; height: 0px; padding: 0px; margin: -1px; overflow: hidden; clip: rect(0px, 0px, 0px, 0px); white-space: nowrap; border-width: 0px; display: none;"
                >
                  <input
                    hidden=""
                    readonly=""
                    style="position: fixed; top: 1px; left: 1px; width: 1px; height: 0px; padding: 0px; margin: -1px; overflow: hidden; clip: rect(0px, 0px, 0px, 0px); white-space: nowrap; border-width: 0px; display: none;"
                    type="hidden"
                  />
                  <input
                    hidden=""
                    name="linky-schedules-enabled"
                    readonly=""
                    style="position: fixed; top: 1px; left: 1px; width: 1px; height: 0px; padding: 0px; margin: -1px; overflow: hidden; clip: rect(0px, 0px, 0px, 0px); white-space: nowrap; border-width: 0px; display: none;"
                    type="checkbox"
                    value="on"
                  />
                </span>
              </div>
            </div>
            <div
              class="pb-4"
            />
            <div
              class=""
            >
              <label
                class="text-md font-bold block mb-2"
                for="power-balancing-current-limit"
              >
                Max supply at CT clamp (Amps)
              </label>
            </div>
            <input
              class="border-none pl-1.5 focus:outline-hidden focus:ring-2 focus:ring-primary/50 text-neutral placeholder-neutral disabled:bg-neutral/30 disabled:placeholder-neutral disabled:cursor-not-allowed py-2 rounded-sm bg-neutral/10 w-full"
              id="power-balancing-current-limit"
              name="power-balancing-current-limit"
              value="50"
            />
            <div
              class="pb-4"
            />
            <div
              class=""
            >
              <label
                class="text-md font-bold block mb-2"
                for="charge-current-limit"
              >
                Charger rating (Amps)
              </label>
            </div>
            <input
              class="border-none pl-1.5 focus:outline-hidden focus:ring-2 focus:ring-primary/50 text-neutral placeholder-neutral disabled:bg-neutral/30 disabled:placeholder-neutral disabled:cursor-not-allowed py-2 rounded-sm bg-neutral/10 w-full"
              id="charge-current-limit"
              name="charge-current-limit"
              value="32"
            />
            <div
              class="pb-4"
            />
            <div
              class=""
            >
              <label
                class="text-md font-bold block mb-2"
                for="rcd-breaker-size"
              >
                EV charger RCBO rating (Amps)
              </label>
            </div>
            <input
              class="border-none pl-1.5 focus:outline-hidden focus:ring-2 focus:ring-primary/50 text-neutral placeholder-neutral disabled:bg-neutral/30 disabled:placeholder-neutral disabled:cursor-not-allowed py-2 rounded-sm bg-neutral/10 w-full"
              id="rcd-breaker-size"
              name="rcd-breaker-size"
              value="10"
            />
            <div
              class="pb-4"
            />
            <div
              class="flex justify-between w-full"
            >
              <div
                data-headlessui-state=""
              >
                <div
                  class="mr-2"
                >
                  <label
                    class="text-md font-bold"
                    for="power-balancing-sensor-installed"
                    id="power-balancing-sensor-installed-label"
                  >
                    Power balancing installed
                  </label>
                </div>
                <button
                  aria-checked="true"
                  aria-label="power-balancing-sensor-installed"
                  class="bg-primary group relative inline-flex h-6 w-11 shrink-0 cursor-pointer rounded-full border-2 border-transparent transition-colors duration-200 ease-in-out focus:outline-hidden focus:ring-2 focus:ring-primary/50 focus:ring-offset-2 data-[disabled]:bg-neutral/40 data-[disabled]:cursor-not-allowed"
                  data-checked=""
                  data-headlessui-state="checked"
                  id="power-balancing-sensor-installed"
                  role="switch"
                  tabindex="0"
                  type="button"
                >
                  <span
                    aria-hidden="true"
                    class="translate-x-5 pointer-events-none inline-block h-5 w-5 transform rounded-full bg-white shadow-sm ring-0 transition duration-200 ease-in-out group-data-[disabled]:bg-neutral/20 group-data-[disabled]:cursor-default"
                  />
                </button>
                <span
                  hidden=""
                  style="position: fixed; top: 1px; left: 1px; width: 1px; height: 0px; padding: 0px; margin: -1px; overflow: hidden; clip: rect(0px, 0px, 0px, 0px); white-space: nowrap; border-width: 0px; display: none;"
                >
                  <input
                    hidden=""
                    readonly=""
                    style="position: fixed; top: 1px; left: 1px; width: 1px; height: 0px; padding: 0px; margin: -1px; overflow: hidden; clip: rect(0px, 0px, 0px, 0px); white-space: nowrap; border-width: 0px; display: none;"
                    type="hidden"
                  />
                  <input
                    checked=""
                    hidden=""
                    name="power-balancing-sensor-installed"
                    readonly=""
                    style="position: fixed; top: 1px; left: 1px; width: 1px; height: 0px; padding: 0px; margin: -1px; overflow: hidden; clip: rect(0px, 0px, 0px, 0px); white-space: nowrap; border-width: 0px; display: none;"
                    type="checkbox"
                    value="on"
                  />
                </span>
              </div>
            </div>
            <div
              class="pb-4"
            />
            <div
              class="flex justify-between w-full"
            >
              <div
                data-headlessui-state=""
              >
                <div
                  class="mr-2"
                >
                  <label
                    class="text-md font-bold"
                    for="power-balancing-sensor-polarity-inverted"
                    id="power-balancing-sensor-polarity-inverted-label"
                  >
                    Power balancing (CT clamp) polarity inverted
                  </label>
                </div>
                <button
                  aria-checked="true"
                  aria-label="power-balancing-sensor-polarity-inverted"
                  class="bg-primary group relative inline-flex h-6 w-11 shrink-0 cursor-pointer rounded-full border-2 border-transparent transition-colors duration-200 ease-in-out focus:outline-hidden focus:ring-2 focus:ring-primary/50 focus:ring-offset-2 data-[disabled]:bg-neutral/40 data-[disabled]:cursor-not-allowed"
                  data-checked=""
                  data-headlessui-state="checked"
                  id="power-balancing-sensor-polarity-inverted"
                  role="switch"
                  tabindex="0"
                  type="button"
                >
                  <span
                    aria-hidden="true"
                    class="translate-x-5 pointer-events-none inline-block h-5 w-5 transform rounded-full bg-white shadow-sm ring-0 transition duration-200 ease-in-out group-data-[disabled]:bg-neutral/20 group-data-[disabled]:cursor-default"
                  />
                </button>
                <span
                  hidden=""
                  style="position: fixed; top: 1px; left: 1px; width: 1px; height: 0px; padding: 0px; margin: -1px; overflow: hidden; clip: rect(0px, 0px, 0px, 0px); white-space: nowrap; border-width: 0px; display: none;"
                >
                  <input
                    hidden=""
                    readonly=""
                    style="position: fixed; top: 1px; left: 1px; width: 1px; height: 0px; padding: 0px; margin: -1px; overflow: hidden; clip: rect(0px, 0px, 0px, 0px); white-space: nowrap; border-width: 0px; display: none;"
                    type="hidden"
                  />
                  <input
                    checked=""
                    hidden=""
                    name="power-balancing-sensor-polarity-inverted"
                    readonly=""
                    style="position: fixed; top: 1px; left: 1px; width: 1px; height: 0px; padding: 0px; margin: -1px; overflow: hidden; clip: rect(0px, 0px, 0px, 0px); white-space: nowrap; border-width: 0px; display: none;"
                    type="checkbox"
                    value="on"
                  />
                </span>
              </div>
            </div>
            <div
              class="pb-4"
            />
            <div
              data-headlessui-state=""
            >
              <label
                aria-labelledby="pp-dev-clamp-fault-threshold"
                class="block mb-2 text-md font-bold"
                data-headlessui-state=""
                for="pp-dev-clamp-fault-threshold"
                id="headlessui-label-:test-id-17"
              >
                CT clamp fault threshold (Amps)
              </label>
              <div
                class="relative mt-1"
                data-headlessui-state=""
              >
                <button
                  aria-expanded="false"
                  aria-haspopup="listbox"
                  aria-labelledby="headlessui-label-:test-id-17 pp-dev-clamp-fault-threshold"
                  class="relative w-full focus:outline-hidden focus:ring-2 focus:ring-primary/50 text-left text-neutral disabled:bg-neutral/20 disabled:placeholder-neutral py-2 pl-1.5 pr-10 rounded-sm bg-neutral/10 w-full"
                  data-headlessui-state=""
                  id="pp-dev-clamp-fault-threshold"
                  type="button"
                >
                  <span
                    class="block truncate"
                  >
                    2
                  </span>
                  <span
                    class="pointer-events-none absolute inset-y-0 right-0 flex items-center pr-2"
                  >
                    <svg
                      aria-hidden="true"
                      class="fill-current w-4 h-2.5 text-neutral"
                      viewBox="0 0 32 32"
                      xmlns="http://www.w3.org/2000/svg"
                    >
                      <title>
                        drop down
                      </title>
                      <g>
                        <path
                          d="m13.15,18.84c-.57.64-1.55.69-2.19.11-.04-.04-.08-.07-.11-.11L.4,7.23c-.57-.64-.52-1.61.12-2.19.28-.25.65-.4,1.03-.4h20.91c.85,0,1.55.69,1.55,1.55,0,.38-.14.75-.4,1.03l-10.45,11.62Z"
                        />
                      </g>
                    </svg>
                  </span>
                </button>
              </div>
              <span
                hidden=""
                style="position: fixed; top: 1px; left: 1px; width: 1px; height: 0px; padding: 0px; margin: -1px; overflow: hidden; clip: rect(0px, 0px, 0px, 0px); white-space: nowrap; border-width: 0px; display: none;"
              >
                <input
                  hidden=""
                  readonly=""
                  style="position: fixed; top: 1px; left: 1px; width: 1px; height: 0px; padding: 0px; margin: -1px; overflow: hidden; clip: rect(0px, 0px, 0px, 0px); white-space: nowrap; border-width: 0px; display: none;"
                  type="hidden"
                />
                <input
                  hidden=""
                  name="pp-dev-clamp-fault-threshold"
                  readonly=""
                  style="position: fixed; top: 1px; left: 1px; width: 1px; height: 0px; padding: 0px; margin: -1px; overflow: hidden; clip: rect(0px, 0px, 0px, 0px); white-space: nowrap; border-width: 0px; display: none;"
                  type="hidden"
                  value="2"
                />
              </span>
            </div>
            <div
              class="pb-4"
            />
            <div
              data-headlessui-state=""
            >
              <label
                aria-labelledby="pen-fault-mode"
                class="block mb-2 text-md font-bold"
                data-headlessui-state=""
                for="pen-fault-mode"
                id="headlessui-label-:test-id-25"
              >
                Pen fault mode
              </label>
              <div
                class="relative mt-1"
                data-headlessui-state=""
              >
                <button
                  aria-expanded="false"
                  aria-haspopup="listbox"
                  aria-labelledby="headlessui-label-:test-id-25 pen-fault-mode"
                  class="relative w-full focus:outline-hidden focus:ring-2 focus:ring-primary/50 text-left text-neutral disabled:bg-neutral/20 disabled:placeholder-neutral py-2 pl-1.5 pr-10 rounded-sm bg-neutral/10 w-full"
                  data-headlessui-state=""
                  id="pen-fault-mode"
                  type="button"
                >
                  <span
                    class="text-neutral block truncate"
                  >
                    Select mode
                  </span>
                  <span
                    class="pointer-events-none absolute inset-y-0 right-0 flex items-center pr-2"
                  >
                    <svg
                      aria-hidden="true"
                      class="fill-current w-4 h-2.5 text-neutral"
                      viewBox="0 0 32 32"
                      xmlns="http://www.w3.org/2000/svg"
                    >
                      <title>
                        drop down
                      </title>
                      <g>
                        <path
                          d="m13.15,18.84c-.57.64-1.55.69-2.19.11-.04-.04-.08-.07-.11-.11L.4,7.23c-.57-.64-.52-1.61.12-2.19.28-.25.65-.4,1.03-.4h20.91c.85,0,1.55.69,1.55,1.55,0,.38-.14.75-.4,1.03l-10.45,11.62Z"
                        />
                      </g>
                    </svg>
                  </span>
                </button>
              </div>
              <span
                hidden=""
                style="position: fixed; top: 1px; left: 1px; width: 1px; height: 0px; padding: 0px; margin: -1px; overflow: hidden; clip: rect(0px, 0px, 0px, 0px); white-space: nowrap; border-width: 0px; display: none;"
              />
            </div>
            <div
              class="pb-4"
            />
            <input
              hidden=""
              name="unlock-connector-on-disconnect"
              value="on"
            />
            <input
              hidden=""
              name="offline-scheduling-enabled"
              value="on"
            />
          </div>
        </section>
        <section
          class="p-3 rounded-sm bg-white"
        >
          <h2
            class="text-lg font-bold"
          >
            Solar configuration
          </h2>
          <div
            class="pb-4"
          />
          <div>
            <div
              class="flex justify-between w-full"
            >
              <div
                data-headlessui-state=""
              >
                <div
                  class="mr-2 flex"
                >
                  <label
                    class="text-md font-bold"
                    for="solar-system-installed"
                    id="solar-system-installed-label"
                  >
                    PV solar system installed
                  </label>
                  <div
                    role="tooltip"
                    tabindex="0"
                  >
                    <svg
                      class="fill-current h-4 w-4 ml-0.5"
                      viewBox="0 0 32 32"
                      xmlns="http://www.w3.org/2000/svg"
                    >
                      <g>
                        <path
                          d="M16.1,29.43A13.43,13.43,0,1,1,29.53,16,13.45,13.45,0,0,1,16.1,29.43Zm0-24.86A11.43,11.43,0,1,0,27.53,16,11.45,11.45,0,0,0,16.1,4.57Z"
                        />
                        <path
                          d="M16,24.12a1,1,0,0,1-1-1V14.94a1,1,0,1,1,2,0v8.18A1,1,0,0,1,16,24.12Z"
                        />
                        <path
                          d="M16.1,10.89a1,1,0,0,1-1-1v-.2a1,1,0,0,1,2,0v.2A1,1,0,0,1,16.1,10.89Z"
                        />
                      </g>
                    </svg>
                  </div>
                </div>
                <button
                  aria-checked="true"
                  aria-label="solar-system-installed"
                  class="bg-primary group relative inline-flex h-6 w-11 shrink-0 cursor-pointer rounded-full border-2 border-transparent transition-colors duration-200 ease-in-out focus:outline-hidden focus:ring-2 focus:ring-primary/50 focus:ring-offset-2 data-[disabled]:bg-neutral/40 data-[disabled]:cursor-not-allowed"
                  data-checked=""
                  data-headlessui-state="checked"
                  id="solar-system-installed"
                  role="switch"
                  tabindex="0"
                  type="button"
                >
                  <span
                    aria-hidden="true"
                    class="translate-x-5 pointer-events-none inline-block h-5 w-5 transform rounded-full bg-white shadow-sm ring-0 transition duration-200 ease-in-out group-data-[disabled]:bg-neutral/20 group-data-[disabled]:cursor-default"
                  />
                </button>
                <span
                  hidden=""
                  style="position: fixed; top: 1px; left: 1px; width: 1px; height: 0px; padding: 0px; margin: -1px; overflow: hidden; clip: rect(0px, 0px, 0px, 0px); white-space: nowrap; border-width: 0px; display: none;"
                >
                  <input
                    hidden=""
                    readonly=""
                    style="position: fixed; top: 1px; left: 1px; width: 1px; height: 0px; padding: 0px; margin: -1px; overflow: hidden; clip: rect(0px, 0px, 0px, 0px); white-space: nowrap; border-width: 0px; display: none;"
                    type="hidden"
                  />
                  <input
                    checked=""
                    hidden=""
                    name="solar-system-installed"
                    readonly=""
                    style="position: fixed; top: 1px; left: 1px; width: 1px; height: 0px; padding: 0px; margin: -1px; overflow: hidden; clip: rect(0px, 0px, 0px, 0px); white-space: nowrap; border-width: 0px; display: none;"
                    type="checkbox"
                    value="on"
                  />
                </span>
              </div>
            </div>
            <div
              class="pb-4"
            />
            <div
              class="flex justify-between w-full"
            >
              <div
                data-headlessui-state=""
              >
                <div
                  class="mr-2 flex"
                >
                  <label
                    class="text-md font-bold"
                    for="solar-matching-enabled"
                    id="solar-matching-enabled-label"
                  >
                    Solar matching enabled
                  </label>
                  <div
                    role="tooltip"
                    tabindex="0"
                  >
                    <svg
                      class="fill-current h-4 w-4 ml-0.5"
                      viewBox="0 0 32 32"
                      xmlns="http://www.w3.org/2000/svg"
                    >
                      <g>
                        <path
                          d="M16.1,29.43A13.43,13.43,0,1,1,29.53,16,13.45,13.45,0,0,1,16.1,29.43Zm0-24.86A11.43,11.43,0,1,0,27.53,16,11.45,11.45,0,0,0,16.1,4.57Z"
                        />
                        <path
                          d="M16,24.12a1,1,0,0,1-1-1V14.94a1,1,0,1,1,2,0v8.18A1,1,0,0,1,16,24.12Z"
                        />
                        <path
                          d="M16.1,10.89a1,1,0,0,1-1-1v-.2a1,1,0,0,1,2,0v.2A1,1,0,0,1,16.1,10.89Z"
                        />
                      </g>
                    </svg>
                  </div>
                </div>
                <button
                  aria-checked="true"
                  aria-label="solar-matching-enabled"
                  class="bg-primary group relative inline-flex h-6 w-11 shrink-0 cursor-pointer rounded-full border-2 border-transparent transition-colors duration-200 ease-in-out focus:outline-hidden focus:ring-2 focus:ring-primary/50 focus:ring-offset-2 data-[disabled]:bg-neutral/40 data-[disabled]:cursor-not-allowed"
                  data-checked=""
                  data-headlessui-state="checked"
                  id="solar-matching-enabled"
                  role="switch"
                  tabindex="0"
                  type="button"
                >
                  <span
                    aria-hidden="true"
                    class="translate-x-5 pointer-events-none inline-block h-5 w-5 transform rounded-full bg-white shadow-sm ring-0 transition duration-200 ease-in-out group-data-[disabled]:bg-neutral/20 group-data-[disabled]:cursor-default"
                  />
                </button>
                <span
                  hidden=""
                  style="position: fixed; top: 1px; left: 1px; width: 1px; height: 0px; padding: 0px; margin: -1px; overflow: hidden; clip: rect(0px, 0px, 0px, 0px); white-space: nowrap; border-width: 0px; display: none;"
                >
                  <input
                    hidden=""
                    readonly=""
                    style="position: fixed; top: 1px; left: 1px; width: 1px; height: 0px; padding: 0px; margin: -1px; overflow: hidden; clip: rect(0px, 0px, 0px, 0px); white-space: nowrap; border-width: 0px; display: none;"
                    type="hidden"
                  />
                  <input
                    checked=""
                    hidden=""
                    name="solar-matching-enabled"
                    readonly=""
                    style="position: fixed; top: 1px; left: 1px; width: 1px; height: 0px; padding: 0px; margin: -1px; overflow: hidden; clip: rect(0px, 0px, 0px, 0px); white-space: nowrap; border-width: 0px; display: none;"
                    type="checkbox"
                    value="on"
                  />
                </span>
              </div>
            </div>
            <div
              class="pb-4"
            />
            <div
              class="flex"
            >
              <label
                class="text-md font-bold block mb-2"
                for="solar-max-grid-import"
              >
                Max grid import (Amps)
              </label>
              <div
                role="tooltip"
                tabindex="0"
              >
                <svg
                  class="fill-current h-4 w-4 ml-0.5"
                  viewBox="0 0 32 32"
                  xmlns="http://www.w3.org/2000/svg"
                >
                  <g>
                    <path
                      d="M16.1,29.43A13.43,13.43,0,1,1,29.53,16,13.45,13.45,0,0,1,16.1,29.43Zm0-24.86A11.43,11.43,0,1,0,27.53,16,11.45,11.45,0,0,0,16.1,4.57Z"
                    />
                    <path
                      d="M16,24.12a1,1,0,0,1-1-1V14.94a1,1,0,1,1,2,0v8.18A1,1,0,0,1,16,24.12Z"
                    />
                    <path
                      d="M16.1,10.89a1,1,0,0,1-1-1v-.2a1,1,0,0,1,2,0v.2A1,1,0,0,1,16.1,10.89Z"
                    />
                  </g>
                </svg>
              </div>
            </div>
            <input
              class="border-none pl-1.5 focus:outline-hidden focus:ring-2 focus:ring-primary/50 text-neutral placeholder-neutral disabled:bg-neutral/30 disabled:placeholder-neutral disabled:cursor-not-allowed py-2 rounded-sm bg-neutral/10 w-full"
              id="solar-max-grid-import"
              name="solar-max-grid-import"
              value="4.1"
            />
            <div
              class="pb-4"
            />
            <div
              class="flex"
            >
              <label
                class="text-md font-bold block mb-2"
                for="solar-max-grid-import-converted"
              >
                Max grid import (kW)
              </label>
              <div
                role="tooltip"
                tabindex="0"
              >
                <svg
                  class="fill-current h-4 w-4 ml-0.5"
                  viewBox="0 0 32 32"
                  xmlns="http://www.w3.org/2000/svg"
                >
                  <g>
                    <path
                      d="M16.1,29.43A13.43,13.43,0,1,1,29.53,16,13.45,13.45,0,0,1,16.1,29.43Zm0-24.86A11.43,11.43,0,1,0,27.53,16,11.45,11.45,0,0,0,16.1,4.57Z"
                    />
                    <path
                      d="M16,24.12a1,1,0,0,1-1-1V14.94a1,1,0,1,1,2,0v8.18A1,1,0,0,1,16,24.12Z"
                    />
                    <path
                      d="M16.1,10.89a1,1,0,0,1-1-1v-.2a1,1,0,0,1,2,0v.2A1,1,0,0,1,16.1,10.89Z"
                    />
                  </g>
                </svg>
              </div>
            </div>
            <input
              class="border-none pl-1.5 focus:outline-hidden focus:ring-2 focus:ring-primary/50 text-neutral placeholder-neutral disabled:bg-neutral/30 disabled:placeholder-neutral disabled:cursor-not-allowed py-2 rounded-sm bg-neutral/10 w-full"
              disabled=""
              id="solar-max-grid-import-converted"
              readonly=""
              value="0.9"
            />
            <div
              class="pb-4"
            />
            <div
              class="flex"
            >
              <label
                class="text-md font-bold block mb-2"
                for="solar-start-delay"
              >
                Solar start delay (seconds)
              </label>
              <div
                role="tooltip"
                tabindex="0"
              >
                <svg
                  class="fill-current h-4 w-4 ml-0.5"
                  viewBox="0 0 32 32"
                  xmlns="http://www.w3.org/2000/svg"
                >
                  <g>
                    <path
                      d="M16.1,29.43A13.43,13.43,0,1,1,29.53,16,13.45,13.45,0,0,1,16.1,29.43Zm0-24.86A11.43,11.43,0,1,0,27.53,16,11.45,11.45,0,0,0,16.1,4.57Z"
                    />
                    <path
                      d="M16,24.12a1,1,0,0,1-1-1V14.94a1,1,0,1,1,2,0v8.18A1,1,0,0,1,16,24.12Z"
                    />
                    <path
                      d="M16.1,10.89a1,1,0,0,1-1-1v-.2a1,1,0,0,1,2,0v.2A1,1,0,0,1,16.1,10.89Z"
                    />
                  </g>
                </svg>
              </div>
            </div>
            <input
              class="border-none pl-1.5 focus:outline-hidden focus:ring-2 focus:ring-primary/50 text-neutral placeholder-neutral disabled:bg-neutral/30 disabled:placeholder-neutral disabled:cursor-not-allowed py-2 rounded-sm bg-neutral/10 w-full"
              id="solar-start-delay"
              name="solar-start-delay"
              value="10"
            />
            <div
              class="pb-4"
            />
            <div
              class="flex"
            >
              <label
                class="text-md font-bold block mb-2"
                for="solar-stop-delay"
              >
                Solar stop delay (seconds)
              </label>
              <div
                role="tooltip"
                tabindex="0"
              >
                <svg
                  class="fill-current h-4 w-4 ml-0.5"
                  viewBox="0 0 32 32"
                  xmlns="http://www.w3.org/2000/svg"
                >
                  <g>
                    <path
                      d="M16.1,29.43A13.43,13.43,0,1,1,29.53,16,13.45,13.45,0,0,1,16.1,29.43Zm0-24.86A11.43,11.43,0,1,0,27.53,16,11.45,11.45,0,0,0,16.1,4.57Z"
                    />
                    <path
                      d="M16,24.12a1,1,0,0,1-1-1V14.94a1,1,0,1,1,2,0v8.18A1,1,0,0,1,16,24.12Z"
                    />
                    <path
                      d="M16.1,10.89a1,1,0,0,1-1-1v-.2a1,1,0,0,1,2,0v.2A1,1,0,0,1,16.1,10.89Z"
                    />
                  </g>
                </svg>
              </div>
            </div>
            <input
              class="border-none pl-1.5 focus:outline-hidden focus:ring-2 focus:ring-primary/50 text-neutral placeholder-neutral disabled:bg-neutral/30 disabled:placeholder-neutral disabled:cursor-not-allowed py-2 rounded-sm bg-neutral/10 w-full"
              id="solar-stop-delay"
              name="solar-stop-delay"
              value="64"
            />
            <div
              class="pb-4"
            />
            <div
              class="flex"
            >
              <label
                class="text-md font-bold block mb-2"
                for="solar-export-margin"
              >
                Solar export margin (Amps)
              </label>
              <div
                role="tooltip"
                tabindex="0"
              >
                <svg
                  class="fill-current h-4 w-4 ml-0.5"
                  viewBox="0 0 32 32"
                  xmlns="http://www.w3.org/2000/svg"
                >
                  <g>
                    <path
                      d="M16.1,29.43A13.43,13.43,0,1,1,29.53,16,13.45,13.45,0,0,1,16.1,29.43Zm0-24.86A11.43,11.43,0,1,0,27.53,16,11.45,11.45,0,0,0,16.1,4.57Z"
                    />
                    <path
                      d="M16,24.12a1,1,0,0,1-1-1V14.94a1,1,0,1,1,2,0v8.18A1,1,0,0,1,16,24.12Z"
                    />
                    <path
                      d="M16.1,10.89a1,1,0,0,1-1-1v-.2a1,1,0,0,1,2,0v.2A1,1,0,0,1,16.1,10.89Z"
                    />
                  </g>
                </svg>
              </div>
            </div>
            <input
              class="border-none pl-1.5 focus:outline-hidden focus:ring-2 focus:ring-primary/50 text-neutral placeholder-neutral disabled:bg-neutral/30 disabled:placeholder-neutral disabled:cursor-not-allowed py-2 rounded-sm bg-neutral/10 w-full"
              id="solar-export-margin"
              name="solar-export-margin"
              value="5.0"
            />
          </div>
        </section>
      </div>
      <div
        class="pb-4"
      />
      <button
        class="border-2 border-solid rounded-sm font-bold text-center disabled:no-underline transition ease-in-out duration-200 cursor-pointer disabled:cursor-not-allowed bg-black border-black text-white hover:bg-charcoal hover:border-charcoal focus:bg-charcoal focus:border-charcoal active:bg-charcoal active:border-charcoal disabled:bg-neutral/20 disabled:border-neutral/20 disabled:text-neutral py-1.5 px-3.75"
        type="submit"
      >
        Save changes
      </button>
    </form>
  </div>
</body>
`;
