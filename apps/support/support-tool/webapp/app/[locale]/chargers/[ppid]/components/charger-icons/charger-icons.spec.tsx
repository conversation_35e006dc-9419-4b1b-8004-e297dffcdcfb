import 'whatwg-fetch';
import { ChargerIcons, ChargerIconsProps } from './charger-icons';
import { LocationType } from '@experience/shared/axios/assets-api-client';
import { Map, setIn } from 'immutable';
import { PersistedSubscriptionDTO } from '@experience/mobile/subscriptions-api/axios';
import {
  mockCommercialAttributes,
  mockConfiguration,
  mockSummary,
} from '@experience/support/support-tool/shared/specs';
import { renderWithProviders } from '../../../../test-utils';
import { screen } from '@testing-library/react';

const defaultProps: ChargerIconsProps = Map({
  commercialAttributes: mockCommercialAttributes,
  configuration: mockConfiguration.data,
  subscriptions: [],
  summary: mockSummary,
})
  .setIn(['configuration', 'configuration', 'configurationKey'], [])
  .setIn(['commercialAttributes', 'settings', 'confirmCharge'], false)
  .setIn(['commercialAttributes', 'tariff'], null)
  .setIn(['summary', 'location', 'isPublic'], false)
  .setIn(['summary', 'location', 'type'], 'COMMERCIAL')
  .setIn(['summary', 'rfid'], null)
  .toObject() as unknown as ChargerIconsProps;

describe('icons display', () => {
  it('should render correctly', () => {
    const { baseElement } = renderWithProviders(
      <ChargerIcons {...defaultProps} />
    );
    expect(baseElement).toBeInTheDocument();
  });

  it.each([
    [
      'with public icon',
      setIn(defaultProps, ['summary', 'location', 'isPublic'], true),
    ],
    [
      'with confirm charge icon',
      setIn(
        defaultProps,
        ['commercialAttributes', 'settings', 'confirmCharge'],
        true
      ),
    ],
    [
      'with tariff icon',
      setIn(
        defaultProps,
        ['commercialAttributes', 'tariff'],
        mockCommercialAttributes.tariff
      ),
    ],
    [
      'with commercial icon',
      setIn(
        defaultProps,
        ['summary', 'location', 'type'],
        LocationType.Commercial
      ),
    ],
    [
      'with domestic icon',
      setIn(
        defaultProps,
        ['summary', 'location', 'type'],
        LocationType.Domestic
      ),
    ],
    [
      'with no location',
      setIn(defaultProps, ['summary', 'location'], undefined),
    ],
    [
      'with subscription icon',
      setIn(
        defaultProps,
        ['subscriptions'],
        [{ id: '1', name: 'Test Subscription' }]
      ),
    ],
    [
      'with rfid icon',
      setIn(defaultProps, ['summary', 'rfid'], mockSummary.rfid),
    ],
    [
      'with rfid icon (solo pro)',
      setIn(
        defaultProps,
        ['configuration', 'configuration', 'configurationKey'],
        [{ key: 'RfidEnabled', value: 'true' }]
      ),
    ],
    [
      'with confirm charge on in configuration',
      setIn(
        defaultProps,
        ['configuration', 'configuration', 'configurationKey'],
        [{ key: 'ConfirmCharge', value: 'true' }]
      ),
    ],
    [
      'with confirm charge off in configuration',
      setIn(
        defaultProps,
        ['configuration', 'configuration', 'configurationKey'],
        [{ key: 'ConfirmCharge', value: 'false' }]
      ),
    ],
    [
      'with midmeter enabled',
      setIn(
        defaultProps,
        ['configuration', 'configuration', 'configurationKey'],
        [
          { key: 'MidCapable', value: 'true' },
          { key: 'MidEnabled', value: 'Enabled' },
        ]
      ),
    ],
    [
      'with midmeter disabled',
      setIn(
        defaultProps,
        ['configuration', 'configuration', 'configurationKey'],
        [
          { key: 'MidCapable', value: 'true' },
          { key: 'MidEnabled', value: 'Disabled' },
        ]
      ),
    ],
    [
      'with midmeter unset',
      setIn(
        defaultProps,
        ['configuration', 'configuration', 'configurationKey'],
        [
          { key: 'MidCapable', value: 'true' },
          { key: 'MidEnabled', value: 'Unset' },
        ]
      ),
    ],
    [
      'with a commercial subscription',
      setIn(
        defaultProps,
        ['commercialAttributes', 'billing', 'subscriptionChargers'],
        [{ ppid: mockCommercialAttributes.ppid, socket: 'A' }]
      ),
    ],
  ])('should match snapshot %s', (_, props) => {
    const { baseElement } = renderWithProviders(<ChargerIcons {...props} />);
    expect(baseElement).toMatchSnapshot();
  });

  it('should have link to subscription status if charger has a pod drive subscription', () => {
    renderWithProviders(
      <ChargerIcons
        {...defaultProps}
        subscriptions={[{ id: '1' } as PersistedSubscriptionDTO]}
      />
    );
    expect(
      screen.getByRole('link', { name: 'Pod Drive subscription' })
    ).toHaveAttribute('href', '/subscriptions/1/status');
  });
});
