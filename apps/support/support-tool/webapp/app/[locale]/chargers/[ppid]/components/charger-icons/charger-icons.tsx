'use client';

import {
  ChargingStationSummary,
  LocationType,
} from '@experience/shared/axios/assets-api-client';
import { Charger as CommercialAttributes } from '@experience/shared/axios/internal-site-admin-api-client';
import { DoorConfigurationResponseData } from '@experience/shared/axios/assets-configuration-api-client';
import { Feature } from 'flagged';
import { PersistedSubscriptionDTO } from '@experience/mobile/subscriptions-api/axios';
import { extractConfigurationValues } from '../../page-content';
import { useTranslations } from 'next-intl';
import IconWithText from '../icon-with-text/icon-with-text';

export interface ChargerIconsProps {
  commercialAttributes: CommercialAttributes | undefined;
  configuration: DoorConfigurationResponseData | undefined;
  subscriptions: PersistedSubscriptionDTO[] | undefined;
  summary: ChargingStationSummary;
}

export const ChargerIcons = ({
  commercialAttributes,
  configuration,
  subscriptions,
  summary,
}: ChargerIconsProps) => {
  const t = useTranslations('Chargers.Components.ChargerIcons');

  const getLocationIcon = (
    locationType?: LocationType
  ): React.JSX.Element | null => {
    switch (locationType) {
      case LocationType.Commercial:
        return (
          <IconWithText
            icon={'OrganisationIcon'}
            text={t('locationIconTextCommercial')}
            title={t('locationIconTitleCommercial')}
          />
        );
      case LocationType.Domestic:
        return (
          <IconWithText
            icon={'HomePlugIcon'}
            text={t('locationIconTextDomestic')}
            title={t('locationIconTitleDomestic')}
          />
        );
      default:
        return null;
    }
  };

  const getPodDriveSubscriptionIcon = (): React.JSX.Element | null => {
    if (!subscriptions || subscriptions.length === 0) {
      return null;
    }

    const [subscription] = subscriptions;
    return (
      <IconWithText
        href={`/subscriptions/${subscription.id}/status`}
        icon={'AllInclusivePackageIcon'}
        text={t('podDriveSubscriptionIconText')}
        title={t('podDriveSubscriptionIconTitle')}
      />
    );
  };

  const getIsPublicIcon = (
    isPublic: boolean,
    locationType?: LocationType
  ): React.JSX.Element | null =>
    locationType === LocationType.Commercial ? (
      <IconWithText
        icon={isPublic ? 'CheckmarkCircleIcon' : 'CrossCircleIcon'}
        iconClassName={isPublic ? 'text-success' : 'text-error'}
        reverse
        text={t('isPublicIconText')}
        title={isPublic ? t('isPublicIconValueYes') : t('isPublicIconValueNo')}
      />
    ) : null;

  const getIsConfirmChargeIcon = (
    confirmCharge: boolean,
    locationType?: LocationType
  ): React.JSX.Element | null =>
    locationType === LocationType.Commercial ? (
      <IconWithText
        icon={confirmCharge ? 'CheckmarkCircleIcon' : 'CrossCircleIcon'}
        iconClassName={confirmCharge ? 'text-success' : 'text-error'}
        reverse
        text={t('confirmIconText')}
        title={
          confirmCharge ? t('confirmIconValueYes') : t('confirmIconValueNo')
        }
      />
    ) : null;

  const getHasTariffIcon = (
    hasTariff: boolean,
    locationType?: LocationType
  ): React.JSX.Element | null =>
    locationType === LocationType.Commercial ? (
      <IconWithText
        icon={hasTariff ? 'CheckmarkCircleIcon' : 'CrossCircleIcon'}
        iconClassName={hasTariff ? 'text-success' : 'text-error'}
        reverse
        text={t('hasTariffIconText')}
        title={
          hasTariff ? t('hasTariffIconValueYes') : t('hasTariffIconValueNo')
        }
      />
    ) : null;

  const getHasRfidReaderIcon = (
    hasRfidReader: boolean,
    locationType?: LocationType
  ): React.JSX.Element | null =>
    locationType === LocationType.Commercial ? (
      <IconWithText
        icon={hasRfidReader ? 'CheckmarkCircleIcon' : 'CrossCircleIcon'}
        iconClassName={hasRfidReader ? 'text-success' : 'text-error'}
        reverse
        text={t('hasRfidReaderIconText')}
        title={
          hasRfidReader
            ? t('hasRfidReaderIconValueYes')
            : t('hasRfidReaderIconValueNo')
        }
      />
    ) : null;

  const getMidmeterEnabledIcon = (
    isMidmeterEnabled: boolean,
    showIcon: boolean
  ): React.JSX.Element | null =>
    showIcon ? (
      <IconWithText
        icon={isMidmeterEnabled ? 'CheckmarkCircleIcon' : 'CrossCircleIcon'}
        iconClassName={isMidmeterEnabled ? 'text-success' : 'text-error'}
        reverse
        text={t('hasMidmeterEnabledIconText')}
        title={
          isMidmeterEnabled
            ? t('hasMidmeterEnabledIconValueYes')
            : t('hasMidmeterEnabledIconValueNo')
        }
      />
    ) : null;

  const getHasCommercialSubscriptionIcon = (
    hasCommercialSubscription: boolean
  ): React.JSX.Element | null => (
    <Feature name="chargersOnSubscription">
      <IconWithText
        icon={
          hasCommercialSubscription ? 'CheckmarkCircleIcon' : 'CrossCircleIcon'
        }
        iconClassName={
          hasCommercialSubscription ? 'text-success' : 'text-error'
        }
        reverse
        text={t('hasSubscriptionIconText')}
        title={
          hasCommercialSubscription
            ? t('hasSubscriptionIconValueYes')
            : t('hasSubscriptionIconValueNo')
        }
      />
    </Feature>
  );

  const configurationValues = extractConfigurationValues(configuration);

  const confirmCharge = configurationValues?.ConfirmCharge
    ? configurationValues.ConfirmCharge === 'true'
    : commercialAttributes?.settings.confirmCharge ?? false;

  const hasRfidReader = configurationValues?.RfidEnabled
    ? configurationValues.RfidEnabled === 'true'
    : !!summary.rfid;

  const hasTariff = !!commercialAttributes?.tariff;
  const groupSubscriptionChargers =
    commercialAttributes?.billing.subscription.chargers ?? [];
  const hasCommercialSubscription = groupSubscriptionChargers?.some(
    (subscriptionCharger) =>
      subscriptionCharger.ppid === commercialAttributes?.ppid
  );
  const isPublic = summary.location?.isPublic ?? false;
  const locationType = summary.location?.type;

  const showMidmeterEnabledIcon =
    LocationType.Commercial && configurationValues?.MidCapable === 'true';
  const isMidmeterEnabled = configurationValues?.MidEnabled === 'Enabled';

  return (
    <div className="flex max-md:flex-col max-md:space-y-2 md:space-x-6">
      {getLocationIcon(locationType)}
      {getPodDriveSubscriptionIcon()}
      {getHasCommercialSubscriptionIcon(hasCommercialSubscription)}
      {getIsPublicIcon(isPublic, locationType)}
      {getIsConfirmChargeIcon(confirmCharge, locationType)}
      {getHasTariffIcon(hasTariff, locationType)}
      {getHasRfidReaderIcon(hasRfidReader, locationType)}
      {getMidmeterEnabledIcon(isMidmeterEnabled, showMidmeterEnabledIcon)}
    </div>
  );
};
