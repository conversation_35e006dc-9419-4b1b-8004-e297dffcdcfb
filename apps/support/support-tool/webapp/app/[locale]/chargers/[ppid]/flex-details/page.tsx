import {
  ChargingStationProgramme,
  ProgrammeResponse,
} from '@experience/shared/axios/competitions-service-client';
import { FlexRequestResponse } from '@experience/shared/axios/smart-charging-service-client';
import {
  MetadataProps,
  generatePageMetadata,
} from '@experience/support/support-tool/next';
import { appRequestHandler } from '@experience/commercial/next/app-request-utils';
import PageContent from './page-content';

export const generateMetadata = async (props: MetadataProps) => {
  const params = await props.params;

  return await generatePageMetadata({
    namespace: 'Chargers.FlexDetailsPage',
    ppid: params.ppid,
  });
};

export const dynamic = 'force-dynamic';
export const revalidate = 0;

const ChargerFlexDetailsPage = async (props: {
  params: Promise<{ ppid: string }>;
}) => {
  const params = await props.params;
  const events = await appRequestHandler<FlexRequestResponse[]>(
    `${process.env.SUPPORT_TOOL_API_URL}/chargers/${params.ppid}/flex/events`
  );

  const programmes =
    (await appRequestHandler<(ChargingStationProgramme & ProgrammeResponse)[]>(
      `${process.env.SUPPORT_TOOL_API_URL}/chargers/${params.ppid}/flex/programmes`
    )) ?? [];

  return (
    <PageContent events={events} programmes={programmes} ppid={params.ppid} />
  );
};
export default ChargerFlexDetailsPage;
