// Jest Snapshot v1, https://goo.gl/fbAQLP

exports[`Charger page content should match snapshot when only summary is present 1`] = `
<body>
  <div>
    <div
      class="rounded-md p-3 bg-info/10 border border-info"
    >
      <div
        class="flex"
      >
        <div
          class="shrink-0"
        >
          <span
            class="text-info"
          >
            <svg
              class="fill-current w-6 h-6"
              viewBox="0 0 32 32"
              xmlns="http://www.w3.org/2000/svg"
            >
              <g>
                <path
                  d="M16.1,29.43A13.43,13.43,0,1,1,29.53,16,13.45,13.45,0,0,1,16.1,29.43Zm0-24.86A11.43,11.43,0,1,0,27.53,16,11.45,11.45,0,0,0,16.1,4.57Z"
                />
                <path
                  d="M16,24.12a1,1,0,0,1-1-1V14.94a1,1,0,1,1,2,0v8.18A1,1,0,0,1,16,24.12Z"
                />
                <path
                  d="M16.1,10.89a1,1,0,0,1-1-1v-.2a1,1,0,0,1,2,0v.2A1,1,0,0,1,16.1,10.89Z"
                />
              </g>
            </svg>
          </span>
        </div>
        <div
          class="flex flex-col justify-center ml-4"
        >
          <p
            class="text-md font-normal sr-only break-words"
          >
            Alert
          </p>
          <div
            class="text-info"
          >
            <p
              class="text-md font-normal break-words"
            >
              You can search for this charger in MIS 
              <a
                class="text-primary underline hover:decoration-2 focus:decoration-2 focus:outline-none cursor-pointer font-bold"
                href="https://admin.pod-point.com/units?search=PSL-12345"
                target="_blank"
              >
                here
              </a>
               or view this charger in Grafana 
              <a
                class="text-primary underline hover:decoration-2 focus:decoration-2 focus:outline-none cursor-pointer font-bold"
                href="https://g-e728fe26ac.grafana-workspace.eu-west-1.amazonaws.com/d/fdtbud32bw45ce/5b147138-e9f2-5846-ba34-c2778f5a748f?var-ppid=PSL-12345"
                target="_blank"
              >
                here
              </a>
              .
            </p>
          </div>
        </div>
      </div>
    </div>
    <div
      class="pb-2"
    />
    <section
      class="p-3 rounded-sm bg-white"
    >
      <header>
        <div
          class="flex"
        >
          <div
            class="lg:flex items-center"
          >
            <div
              class="flex"
            >
              <h1
                class="text-xxl font-bold"
              >
                PSL-12345
              </h1>
              <button
                aria-label="Copy PSL-12345"
                class="border-2 border-solid rounded-sm font-bold text-center disabled:no-underline transition ease-in-out duration-200 cursor-pointer disabled:cursor-not-allowed border-transparent text-primary hover:underline hover:text-primary active:font-bold active:text-primary disabled:text-neutral/40 disabled:hover:no-underline disabled:hover:border-transparent p-1.5 ml-1"
                type="button"
              >
                <svg
                  class="fill-current h-4 w-4"
                  viewBox="0 0 24 24"
                  xmlns="http://www.w3.org/2000/svg"
                >
                  <defs>
                    <style>
                      .cls-1 
                    </style>
                  </defs>
                  <path
                    d="M18.73,14.5h1.83c1.35,0,2.44-1.09,2.44-2.44V3.5c0-1.35-1.09-2.44-2.44-2.44h-8.56c-1.35,0-2.44,1.09-2.44,2.44v1.83M3.45,9.61h8.56c1.35,0,2.44,1.09,2.44,2.44v8.56c0,1.35-1.09,2.44-2.44,2.44H3.45c-1.35,0-2.44-1.09-2.44-2.44v-8.56c0-1.35,1.09-2.44,2.44-2.44Z"
                    style="fill: none; stroke: currentColor; stroke-linecap: round; stroke-linejoin: round; stroke-width: 1.5px;"
                  />
                </svg>
              </button>
            </div>
            <div
              class="lg:ml-6 my-4 lg:my-0"
            >
              <div
                class="flex space-x-4"
              >
                <button
                  class="px-2 font-semibold border-b outline-hidden focus-visible:ring-2 focus-visible:ring-info text-primary border-b-primary hover:text-primary cursor-default"
                  disabled=""
                >
                  Socket A
                </button>
                <button
                  class="px-2 font-semibold text-neutral hover:text-black border-b border-b-transparent outline-hidden focus-visible:ring-2 focus-visible:ring-info"
                >
                  Socket B
                </button>
                <button
                  class="px-2 font-semibold text-neutral hover:text-black border-b border-b-transparent outline-hidden focus-visible:ring-2 focus-visible:ring-info"
                >
                  Socket C
                </button>
                <button
                  class="px-2 font-semibold text-neutral hover:text-black border-b border-b-transparent outline-hidden focus-visible:ring-2 focus-visible:ring-info"
                >
                  Socket D
                </button>
                <button
                  class="px-2 font-semibold text-neutral hover:text-black border-b border-b-transparent outline-hidden focus-visible:ring-2 focus-visible:ring-info"
                >
                  Socket E
                </button>
                <button
                  class="px-2 font-semibold text-neutral hover:text-black border-b border-b-transparent outline-hidden focus-visible:ring-2 focus-visible:ring-info"
                >
                  Socket F
                </button>
                <button
                  class="px-2 font-semibold text-neutral hover:text-black border-b border-b-transparent outline-hidden focus-visible:ring-2 focus-visible:ring-info"
                >
                  Socket G
                </button>
                <button
                  class="px-2 font-semibold text-neutral hover:text-black border-b border-b-transparent outline-hidden focus-visible:ring-2 focus-visible:ring-info"
                >
                  Socket H
                </button>
              </div>
            </div>
          </div>
          <div
            class="ml-auto"
          >
            <div
              class="relative inline-block text-left"
              data-headlessui-state=""
            >
              <button
                aria-expanded="false"
                aria-haspopup="menu"
                class="bg-black border-black border-2 border-solid rounded-sm px-4 py-1.5 hover:bg-charcoal hover:border-charcoal focus:bg-charcoal focus:border-charcoal active:bg-charcoal active:border-charcoal disabled:bg-neutral/20 disabled:border-neutral/20 disabled:text-neutral font-bold inline-flex items-center text-white cursor-pointer disabled:cursor-not-allowed"
                data-headlessui-state=""
                id="headlessui-menu-button-:test-id-2"
                type="button"
              >
                <span>
                  Commands
                </span>
                <svg
                  class="fill-current stroke-current h-2 w-2 min-w-0 ml-2"
                  viewBox="0 0 24 24"
                  xmlns="http://www.w3.org/2000/svg"
                >
                  <g>
                    <path
                      d="m13.15,18.84c-.57.64-1.55.69-2.19.11-.04-.04-.08-.07-.11-.11L.4,7.23c-.57-.64-.52-1.61.12-2.19.28-.25.65-.4,1.03-.4h20.91c.85,0,1.55.69,1.55,1.55,0,.38-.14.75-.4,1.03l-10.45,11.62Z"
                    />
                  </g>
                </svg>
              </button>
            </div>
          </div>
        </div>
        <div>
          <div
            class="flex max-md:flex-col max-md:space-y-2 md:space-x-6"
          >
            <div
              class="flex items-center"
            >
              <svg
                class="fill-current w-6 h-6"
                viewBox="0 0 24 24"
                xmlns="http://www.w3.org/2000/svg"
              >
                <title>
                  Domestic location
                </title>
                <g>
                  <path
                    d="m22.07,6.92L14.05.84c-1.21-.92-2.89-.92-4.1,0L1.93,6.92c-.84.64-1.34,1.65-1.34,2.7v10.84c0,1.87,1.52,3.39,3.39,3.39h5.04c.41,0,.75-.34.75-.75s-.34-.75-.75-.75H3.98c-1.04,0-1.89-.85-1.89-1.89v-10.84c0-.59.28-1.15.75-1.5L10.86,2.03c.67-.51,1.61-.51,2.28,0l8.02,6.08c.47.36.74.92.74,1.5v10.84c0,1.04-.85,1.89-1.89,1.89h-5.53c-.96,0-1.74-.78-1.74-1.74v-2.63c1.85-.35,3.25-1.98,3.25-3.93v-3.25c0-.41-.34-.75-.75-.75h-.81v-1.88c0-.41-.34-.75-.75-.75s-.75.34-.75.75v1.88h-1.77v-1.88c0-.41-.34-.75-.75-.75s-.75.34-.75.75v1.88h-.93c-.41,0-.75.34-.75.75v3.25c0,1.95,1.4,3.57,3.25,3.93v2.63c0,1.79,1.45,3.24,3.24,3.24h5.53c1.87,0,3.39-1.52,3.39-3.39v-10.84c0-1.05-.5-2.06-1.34-2.7Zm-12.58,7.13v-2.5h5.01v2.5c0,1.38-1.12,2.5-2.5,2.5s-2.5-1.12-2.5-2.5Z"
                  />
                </g>
              </svg>
              <span
                class="ml-2"
              >
                Domestic
              </span>
            </div>
          </div>
        </div>
      </header>
    </section>
    <div
      class="pb-2"
    />
    <div
      class="flex flex-row flex-wrap gap-x-4 gap-y-2"
    />
    <div
      class="pb-2"
    />
    <div
      class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 print:grid-cols-3 gap-2"
    >
      <section
        class="p-3 rounded-sm bg-white md:col-span-2 lg:col-span-1"
      >
        <div
          class="flex justify-between items-baseline"
        >
          <h3
            class="text-lg font-bold pb-4"
          >
            Charger information
          </h3>
          <a
            class="text-primary underline hover:decoration-2 focus:decoration-2 focus:outline-none cursor-pointer text-right ml-2"
            href="/chargers/PSL-12345/logs/diagnostic?socket=A"
          >
            View diagnostic logs
          </a>
        </div>
        <div
          class="flex flex-col bg-white rounded-sm overflow-hidden overflow-x-auto"
        >
          <div
            class="inline-block min-w-full -my-2 py-2 align-middle"
          >
            <table
              class="min-w-full"
              tabindex="0"
            >
              <caption
                class="sr-only"
              >
                Charger information table
              </caption>
              <tbody
                class="divide-y"
              >
                <tr
                  class="border-b-neutral/20"
                >
                  <td
                    class="whitespace-normal px-3 py-4 pl-0 pr-2 pt-2 pb-2 whitespace-pre"
                  >
                    Model SKU
                  </td>
                  <td
                    class="whitespace-normal px-3 py-4 pr-0 pl-2 pt-2 pb-2 text-neutral text-right"
                  >
                    T7-S-07-AMC-BLK
                  </td>
                </tr>
                <tr
                  class="border-b-neutral/20"
                >
                  <td
                    class="whitespace-normal px-3 py-4 pl-0 pr-2 pt-2 pb-2 whitespace-pre"
                  >
                    Arch version
                  </td>
                  <td
                    class="whitespace-normal px-3 py-4 pr-0 pl-2 pt-2 pb-2 text-neutral text-right"
                  >
                    5.0
                  </td>
                </tr>
                <tr
                  class="border-b-neutral/20"
                >
                  <td
                    class="whitespace-normal px-3 py-4 pl-0 pr-2 pt-2 pb-2 whitespace-pre"
                  >
                    Firmware
                  </td>
                  <td
                    class="whitespace-normal px-3 py-4 pr-0 pl-2 pt-2 pb-2 text-neutral text-right"
                  >
                    -
                  </td>
                </tr>
                <tr
                  class="border-b-neutral/20"
                >
                  <td
                    class="whitespace-normal px-3 py-4 pl-0 pr-2 pt-2 pb-2 whitespace-pre"
                  >
                    Manufactured
                  </td>
                  <td
                    class="whitespace-normal px-3 py-4 pr-0 pl-2 pt-2 pb-2 text-neutral text-right"
                  >
                    1 June 2023
                  </td>
                </tr>
                <tr
                  class="border-b-neutral/20"
                >
                  <td
                    class="whitespace-normal px-3 py-4 pl-0 pr-2 pt-2 pb-2 whitespace-pre"
                  >
                    Operator
                  </td>
                  <td
                    class="whitespace-normal px-3 py-4 pr-0 pl-2 pt-2 pb-2 text-neutral text-right"
                  >
                    Pod Point
                  </td>
                </tr>
                <tr
                  class="border-b-neutral/20"
                >
                  <td
                    class="whitespace-normal px-3 py-4 pl-0 pr-2 pt-2 pb-2 whitespace-pre"
                  >
                    MAC address
                  </td>
                  <td
                    class="whitespace-normal px-3 py-4 pr-0 pl-2 pt-2 pb-2 text-neutral text-right"
                  >
                    0cb2b703c778
                  </td>
                </tr>
                <tr
                  class="border-b-neutral/20"
                >
                  <td
                    class="whitespace-normal px-3 py-4 pl-0 pr-2 pt-2 pb-2 whitespace-pre"
                  >
                    PCBA serial number
                  </td>
                  <td
                    class="whitespace-normal px-3 py-4 pr-0 pl-2 pt-2 pb-2 text-neutral text-right"
                  >
                    30303030
                  </td>
                </tr>
              </tbody>
            </table>
          </div>
        </div>
        <div
          class="flex flex-row-reverse"
        >
          <a
            class="text-primary underline hover:decoration-2 focus:decoration-2 focus:outline-none cursor-pointer text-right ml-2"
            href="/chargers/PSL-12345/pcbs/history?socket=A"
          >
            History
          </a>
        </div>
      </section>
      <section
        class="p-3 rounded-sm bg-white"
      >
        <h3
          class="text-lg font-bold pb-4"
        >
          Charger connectivity
        </h3>
        <div
          class="flex flex-col bg-white rounded-sm overflow-hidden overflow-x-auto"
        >
          <div
            class="inline-block min-w-full -my-2 py-2 align-middle"
          >
            <table
              class="min-w-full"
              tabindex="0"
            >
              <caption
                class="sr-only"
              >
                Charger connectivity table
              </caption>
              <tbody
                class="divide-y"
              >
                <tr
                  class="border-b-neutral/20"
                >
                  <td
                    class="whitespace-normal px-3 py-4 pl-0 pr-2 pt-2 pb-2 whitespace-pre"
                  >
                    Status
                  </td>
                  <td
                    class="whitespace-normal px-3 py-4 pr-0 pl-2 pt-2 pb-2 text-neutral text-right"
                  >
                    -
                  </td>
                </tr>
                <tr
                  class="border-b-neutral/20"
                >
                  <td
                    class="whitespace-normal px-3 py-4 pl-0 pr-2 pt-2 pb-2 whitespace-pre"
                  >
                    Charging status
                  </td>
                  <td
                    class="whitespace-normal px-3 py-4 pr-0 pl-2 pt-2 pb-2 text-neutral text-right"
                  >
                    -
                  </td>
                </tr>
                <tr
                  class="border-b-neutral/20"
                >
                  <td
                    class="whitespace-normal px-3 py-4 pl-0 pr-2 pt-2 pb-2 whitespace-pre"
                  >
                    Last connection started
                  </td>
                  <td
                    class="whitespace-normal px-3 py-4 pr-0 pl-2 pt-2 pb-2 text-neutral text-right"
                  >
                    -
                  </td>
                </tr>
                <tr
                  class="border-b-neutral/20"
                >
                  <td
                    class="whitespace-normal px-3 py-4 pl-0 pr-2 pt-2 pb-2 whitespace-pre"
                  >
                    Connection quality
(scale 1-5)
                  </td>
                  <td
                    class="whitespace-normal px-3 py-4 pr-0 pl-2 pt-2 pb-2 text-neutral text-right"
                  >
                    -
                  </td>
                </tr>
                <tr
                  class="border-b-neutral/20"
                >
                  <td
                    class="whitespace-normal px-3 py-4 pl-0 pr-2 pt-2 pb-2 whitespace-pre"
                  >
                    Router serial number
                  </td>
                  <td
                    class="whitespace-normal px-3 py-4 pr-0 pl-2 pt-2 pb-2 text-neutral text-right"
                  >
                    1102538429
                  </td>
                </tr>
                <tr
                  class="border-b-neutral/20"
                >
                  <td
                    class="whitespace-normal px-3 py-4 pl-0 pr-2 pt-2 pb-2 whitespace-pre"
                  >
                    Router MAC address
                  </td>
                  <td
                    class="whitespace-normal px-3 py-4 pr-0 pl-2 pt-2 pb-2 text-neutral text-right"
                  >
                    001E4223CF13
                  </td>
                </tr>
                <tr
                  class="border-b-neutral/20"
                >
                  <td
                    class="whitespace-normal px-3 py-4 pl-0 pr-2 pt-2 pb-2 whitespace-pre"
                  >
                    Router SIM number
                  </td>
                  <td
                    class="whitespace-normal px-3 py-4 pr-0 pl-2 pt-2 pb-2 text-neutral text-right"
                  >
                    123456789123000043
                  </td>
                </tr>
                <tr
                  class="border-b-neutral/20"
                >
                  <td
                    class="whitespace-normal px-3 py-4 pl-0 pr-2 pt-2 pb-2 whitespace-pre"
                  >
                    Router model
                  </td>
                  <td
                    class="whitespace-normal px-3 py-4 pr-0 pl-2 pt-2 pb-2 text-neutral text-right"
                  >
                    RUT241
                  </td>
                </tr>
              </tbody>
            </table>
          </div>
        </div>
      </section>
      <section
        class="p-3 rounded-sm bg-white"
      >
        <div
          class="flex justify-between items-baseline"
        >
          <h3
            class="text-lg font-bold pb-4"
          >
            Charger access point (AP)
          </h3>
        </div>
        <div
          class="flex flex-col bg-white rounded-sm overflow-hidden overflow-x-auto"
        >
          <div
            class="inline-block min-w-full -my-2 py-2 align-middle"
          >
            <table
              class="min-w-full"
              tabindex="0"
            >
              <caption
                class="sr-only"
              >
                Charger access point
              </caption>
              <tbody
                class="divide-y"
              >
                <tr
                  class="border-b-neutral/20"
                >
                  <td
                    class="whitespace-normal px-3 py-4 pl-0 pr-2 pt-2 pb-2 whitespace-pre"
                  >
                    SSID
                  </td>
                  <td
                    class="whitespace-normal px-3 py-4 pr-0 pl-2 pt-2 pb-2 text-neutral text-right"
                  >
                    -
                  </td>
                </tr>
                <tr
                  class="border-b-neutral/20"
                >
                  <td
                    class="whitespace-normal px-3 py-4 pl-0 pr-2 pt-2 pb-2 whitespace-pre"
                  >
                    Password
                  </td>
                  <td
                    class="whitespace-normal px-3 py-4 pr-0 pl-2 pt-2 pb-2 text-neutral text-right"
                  >
                    -
                  </td>
                </tr>
              </tbody>
            </table>
          </div>
        </div>
      </section>
    </div>
    <div
      class="pb-2"
    />
    <div
      class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-2 gap-2"
    >
      <section
        class="p-3 rounded-sm bg-white"
      >
        <div
          class="flex justify-between items-center"
        >
          <h3
            class="text-lg font-bold pb-4"
          >
            Charger configuration
          </h3>
          <button
            aria-label="Edit charger configuration"
            class="border-2 border-solid rounded-sm font-bold text-center disabled:no-underline transition ease-in-out duration-200 cursor-pointer disabled:cursor-not-allowed border-transparent text-primary hover:underline hover:text-primary active:font-bold active:text-primary disabled:text-neutral/40 disabled:hover:no-underline disabled:hover:border-transparent p-1.5"
            data-testid="edit-charger-configuration"
            id="edit-charger-configuration"
            name="edit-charger-configuration"
            type="button"
          >
            <svg
              class="fill-current h-4 w-4"
              viewBox="0 0 32 32"
              xmlns="http://www.w3.org/2000/svg"
            >
              <g>
                <path
                  d="M1.16,30.97c-.26,0-.52-.1-.71-.29-.24-.24-.34-.57-.27-.9l1.51-7.67c.04-.19,.13-.37,.27-.51L21.08,2.48c2.09-2.09,5.49-2.09,7.57,0,2.09,2.09,2.09,5.48,0,7.57L9.54,29.16c-.14,.14-.32,.24-.51,.27l-7.67,1.51c-.06,.01-.13,.02-.19,.02Zm2.44-8.18l-1.17,5.91,5.91-1.17L27.24,8.64c1.31-1.31,1.31-3.44,0-4.74-1.31-1.31-3.44-1.31-4.74,0L3.6,22.79Zm5.24,5.67h0Z"
                />
                <path
                  d="M30.84,31.09H12.58c-.55,0-1-.45-1-1s.45-1,1-1H30.84c.55,0,1,.45,1,1s-.45,1-1,1Z"
                />
              </g>
            </svg>
          </button>
        </div>
        <div
          class="flex flex-col bg-white rounded-sm overflow-hidden overflow-x-auto"
        >
          <div
            class="inline-block min-w-full -my-2 py-2 align-middle"
          >
            <table
              class="min-w-full"
              tabindex="0"
            >
              <caption
                class="sr-only"
              >
                Charger configuration table
              </caption>
              <tbody
                class="divide-y"
              >
                <tr
                  class="border-b-neutral/20"
                >
                  <td
                    class="whitespace-normal px-3 py-4 pl-0 pr-2 pt-2 pb-2 whitespace-pre"
                  >
                    Max supply at CT clamp (Amps)
                  </td>
                  <td
                    class="whitespace-normal px-3 py-4 pr-0 pl-2 pt-2 pb-2 text-neutral text-right"
                  >
                    -
                  </td>
                </tr>
                <tr
                  class="border-b-neutral/20"
                >
                  <td
                    class="whitespace-normal px-3 py-4 pl-0 pr-2 pt-2 pb-2 whitespace-pre"
                  >
                    Charger rating (Amps)
                  </td>
                  <td
                    class="whitespace-normal px-3 py-4 pr-0 pl-2 pt-2 pb-2 text-neutral text-right"
                  >
                    -
                  </td>
                </tr>
                <tr
                  class="border-b-neutral/20"
                >
                  <td
                    class="whitespace-normal px-3 py-4 pl-0 pr-2 pt-2 pb-2 whitespace-pre"
                  >
                    Last retrieved
                  </td>
                  <td
                    class="whitespace-normal px-3 py-4 pr-0 pl-2 pt-2 pb-2 text-neutral text-right"
                  >
                    -
                  </td>
                </tr>
              </tbody>
            </table>
          </div>
        </div>
      </section>
      <section
        class="p-3 rounded-sm bg-white"
      >
        <div
          class="flex justify-between items-center"
        >
          <h3
            class="text-lg font-bold pb-4"
          >
            Solar configuration
          </h3>
          <button
            aria-label="Edit solar configuration"
            class="border-2 border-solid rounded-sm font-bold text-center disabled:no-underline transition ease-in-out duration-200 cursor-pointer disabled:cursor-not-allowed border-transparent text-primary hover:underline hover:text-primary active:font-bold active:text-primary disabled:text-neutral/40 disabled:hover:no-underline disabled:hover:border-transparent p-1.5"
            data-testid="edit-solar-configuration"
            id="edit-solar-configuration"
            name="edit-solar-configuration"
            type="button"
          >
            <svg
              class="fill-current h-4 w-4"
              viewBox="0 0 32 32"
              xmlns="http://www.w3.org/2000/svg"
            >
              <g>
                <path
                  d="M1.16,30.97c-.26,0-.52-.1-.71-.29-.24-.24-.34-.57-.27-.9l1.51-7.67c.04-.19,.13-.37,.27-.51L21.08,2.48c2.09-2.09,5.49-2.09,7.57,0,2.09,2.09,2.09,5.48,0,7.57L9.54,29.16c-.14,.14-.32,.24-.51,.27l-7.67,1.51c-.06,.01-.13,.02-.19,.02Zm2.44-8.18l-1.17,5.91,5.91-1.17L27.24,8.64c1.31-1.31,1.31-3.44,0-4.74-1.31-1.31-3.44-1.31-4.74,0L3.6,22.79Zm5.24,5.67h0Z"
                />
                <path
                  d="M30.84,31.09H12.58c-.55,0-1-.45-1-1s.45-1,1-1H30.84c.55,0,1,.45,1,1s-.45,1-1,1Z"
                />
              </g>
            </svg>
          </button>
        </div>
        <div
          class="flex flex-col bg-white rounded-sm overflow-hidden overflow-x-auto"
        >
          <div
            class="inline-block min-w-full -my-2 py-2 align-middle"
          >
            <table
              class="min-w-full"
              tabindex="0"
            >
              <caption
                class="sr-only"
              >
                Solar configuration table
              </caption>
              <tbody
                class="divide-y"
              >
                <tr
                  class="border-b-neutral/20"
                >
                  <td
                    class="whitespace-normal px-3 py-4 pl-0 pr-2 pt-2 pb-2 whitespace-pre"
                  >
                    PV solar system installed
                  </td>
                  <td
                    class="whitespace-normal px-3 py-4 pr-0 pl-2 pt-2 pb-2 text-neutral text-right"
                  >
                    -
                  </td>
                </tr>
                <tr
                  class="border-b-neutral/20"
                >
                  <td
                    class="whitespace-normal px-3 py-4 pl-0 pr-2 pt-2 pb-2 whitespace-pre"
                  >
                    Solar matching enabled
                  </td>
                  <td
                    class="whitespace-normal px-3 py-4 pr-0 pl-2 pt-2 pb-2 text-neutral text-right"
                  >
                    -
                  </td>
                </tr>
                <tr
                  class="border-b-neutral/20"
                >
                  <td
                    class="whitespace-normal px-3 py-4 pl-0 pr-2 pt-2 pb-2 whitespace-pre"
                  >
                    Max grid import (Amps)
                  </td>
                  <td
                    class="whitespace-normal px-3 py-4 pr-0 pl-2 pt-2 pb-2 text-neutral text-right"
                  >
                    -
                  </td>
                </tr>
              </tbody>
            </table>
          </div>
        </div>
      </section>
    </div>
    <div
      class="pb-2"
    />
    <div
      class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 print:grid-cols-3 gap-2"
    >
      <section
        class="p-3 rounded-sm bg-white"
      >
        <div
          class="flex justify-between items-baseline"
        >
          <h3
            class="text-lg font-bold pb-4"
          >
            Installation details
          </h3>
        </div>
        <div
          class="flex flex-col bg-white rounded-sm overflow-hidden overflow-x-auto"
        >
          <div
            class="inline-block min-w-full -my-2 py-2 align-middle"
          >
            <table
              class="min-w-full"
              tabindex="0"
            >
              <caption
                class="sr-only"
              >
                Installation details table
              </caption>
              <tbody
                class="divide-y"
              >
                <tr
                  class="border-b-neutral/20"
                >
                  <td
                    class="whitespace-normal px-3 py-4 pl-0 pr-2 pt-2 pb-2 whitespace-pre"
                  >
                    Install date
                  </td>
                  <td
                    class="whitespace-normal px-3 py-4 pr-0 pl-2 pt-2 pb-2 text-neutral text-right"
                  >
                    -
                  </td>
                </tr>
                <tr
                  class="border-b-neutral/20"
                >
                  <td
                    class="whitespace-normal px-3 py-4 pl-0 pr-2 pt-2 pb-2 whitespace-pre"
                  >
                    Installed by
                  </td>
                  <td
                    class="whitespace-normal px-3 py-4 pr-0 pl-2 pt-2 pb-2 text-neutral text-right"
                  >
                    -
                  </td>
                </tr>
                <tr
                  class="border-b-neutral/20"
                >
                  <td
                    class="whitespace-normal px-3 py-4 pl-0 pr-2 pt-2 pb-2 whitespace-pre"
                  >
                    Company
                  </td>
                  <td
                    class="whitespace-normal px-3 py-4 pr-0 pl-2 pt-2 pb-2 text-neutral text-right"
                  >
                    -
                  </td>
                </tr>
              </tbody>
            </table>
          </div>
        </div>
      </section>
      <section
        class="p-3 rounded-sm bg-white"
      >
        <h3
          class="text-lg font-bold pb-4"
        >
          Warranty details
        </h3>
        <div
          class="flex flex-col bg-white rounded-sm overflow-hidden overflow-x-auto"
        >
          <div
            class="inline-block min-w-full -my-2 py-2 align-middle"
          >
            <table
              class="min-w-full"
              tabindex="0"
            >
              <caption
                class="sr-only"
              >
                Warranty details table
              </caption>
              <tbody
                class="divide-y"
              >
                <tr
                  class="border-b-neutral/20"
                >
                  <td
                    class="whitespace-normal px-3 py-4 pl-0 pr-2 pt-2 pb-2 whitespace-pre"
                  >
                    Warranty start date
                  </td>
                  <td
                    class="whitespace-normal px-3 py-4 pr-0 pl-2 pt-2 pb-2 text-neutral text-right"
                  >
                    -
                  </td>
                </tr>
                <tr
                  class="border-b-neutral/20"
                >
                  <td
                    class="whitespace-normal px-3 py-4 pl-0 pr-2 pt-2 pb-2 whitespace-pre"
                  >
                    Warranty end date
                  </td>
                  <td
                    class="whitespace-normal px-3 py-4 pr-0 pl-2 pt-2 pb-2 text-neutral text-right"
                  >
                    -
                  </td>
                </tr>
                <tr
                  class="border-b-neutral/20"
                >
                  <td
                    class="whitespace-normal px-3 py-4 pl-0 pr-2 pt-2 pb-2 whitespace-pre"
                  >
                    Warranty status
                  </td>
                  <td
                    class="whitespace-normal px-3 py-4 pr-0 pl-2 pt-2 pb-2 text-neutral text-right"
                  >
                    -
                  </td>
                </tr>
              </tbody>
            </table>
          </div>
        </div>
      </section>
      <section
        class="p-3 rounded-sm bg-white"
      >
        <div
          class="flex justify-between items-baseline"
        >
          <h3
            class="text-lg font-bold pb-4"
          >
            Last complete charge
          </h3>
          <a
            class="text-primary underline hover:decoration-2 focus:decoration-2 focus:outline-none cursor-pointer text-right ml-2"
            href="/chargers/PSL-12345/recent-charges?socket=A"
          >
            Recent charges
          </a>
        </div>
        <div
          class="flex flex-col bg-white rounded-sm overflow-hidden overflow-x-auto"
        >
          <div
            class="inline-block min-w-full -my-2 py-2 align-middle"
          >
            <table
              class="min-w-full"
              tabindex="0"
            >
              <caption
                class="sr-only"
              >
                Last complete charge table
              </caption>
              <tbody
                class="divide-y"
              >
                <tr
                  class="border-b-neutral/20"
                >
                  <td
                    class="whitespace-normal px-3 py-4 pl-0 pr-2 pt-2 pb-2 whitespace-pre"
                  >
                    Started at
                  </td>
                  <td
                    class="whitespace-normal px-3 py-4 pr-0 pl-2 pt-2 pb-2 text-neutral text-right"
                  >
                    -
                  </td>
                </tr>
                <tr
                  class="border-b-neutral/20"
                >
                  <td
                    class="whitespace-normal px-3 py-4 pl-0 pr-2 pt-2 pb-2 whitespace-pre"
                  >
                    Ended at
                  </td>
                  <td
                    class="whitespace-normal px-3 py-4 pr-0 pl-2 pt-2 pb-2 text-neutral text-right"
                  >
                    -
                  </td>
                </tr>
                <tr
                  class="border-b-neutral/20"
                >
                  <td
                    class="whitespace-normal px-3 py-4 pl-0 pr-2 pt-2 pb-2 whitespace-pre"
                  >
                    kWh delivered
                  </td>
                  <td
                    class="whitespace-normal px-3 py-4 pr-0 pl-2 pt-2 pb-2 text-neutral text-right"
                  >
                    -
                  </td>
                </tr>
              </tbody>
            </table>
          </div>
        </div>
      </section>
    </div>
    <div
      class="pb-2"
    />
    <section
      class="p-3 rounded-sm bg-white"
    >
      <div
        class="flex justify-between items-baseline"
      >
        <h3
          class="text-lg font-bold pb-4"
        >
          Modes
        </h3>
        <a
          class="text-primary underline hover:decoration-2 focus:decoration-2 focus:outline-none cursor-pointer text-right ml-2"
          href="/chargers/PSL-12345/mode-history"
        >
          View history
        </a>
      </div>
      <div
        class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 print:grid-cols-3 gap-2"
      >
        <div
          class="pb-2 break-words"
        >
          <div
            class="flex"
          >
            <div
              data-headlessui-state=""
            >
              <div
                class="mr-2"
              >
                <label
                  class="text-lg font-normal block mb-1.5"
                  for="toggle-smart-mode"
                  id="toggle-smart-mode-label"
                >
                  Smart mode
                </label>
              </div>
              <button
                aria-checked="true"
                aria-label="toggle-smart-mode"
                class="bg-primary group relative inline-flex h-6 w-11 shrink-0 cursor-pointer rounded-full border-2 border-transparent transition-colors duration-200 ease-in-out focus:outline-hidden focus:ring-2 focus:ring-primary/50 focus:ring-offset-2 data-[disabled]:bg-neutral/40 data-[disabled]:cursor-not-allowed"
                data-checked=""
                data-headlessui-state="checked"
                id="toggle-smart-mode"
                role="switch"
                tabindex="0"
                type="button"
              >
                <span
                  aria-hidden="true"
                  class="translate-x-5 pointer-events-none inline-block h-5 w-5 transform rounded-full bg-white shadow-sm ring-0 transition duration-200 ease-in-out group-data-[disabled]:bg-neutral/20 group-data-[disabled]:cursor-default"
                />
              </button>
              <span
                hidden=""
                style="position: fixed; top: 1px; left: 1px; width: 1px; height: 0px; padding: 0px; margin: -1px; overflow: hidden; clip: rect(0px, 0px, 0px, 0px); white-space: nowrap; border-width: 0px; display: none;"
              />
            </div>
          </div>
        </div>
        <div
          class="pb-2 break-words"
        >
          <div
            class="flex"
          >
            <div
              data-headlessui-state=""
            >
              <div
                class="mr-2"
              >
                <label
                  class="text-lg font-normal block mb-1.5"
                  for="toggle-charge-now"
                  id="toggle-charge-now-label"
                >
                  Charge now
                </label>
              </div>
              <button
                aria-checked="false"
                aria-label="toggle-charge-now"
                class="bg-neutral/20 group relative inline-flex h-6 w-11 shrink-0 cursor-pointer rounded-full border-2 border-transparent transition-colors duration-200 ease-in-out focus:outline-hidden focus:ring-2 focus:ring-primary/50 focus:ring-offset-2 data-[disabled]:bg-neutral/40 data-[disabled]:cursor-not-allowed"
                data-headlessui-state=""
                id="toggle-charge-now"
                role="switch"
                tabindex="0"
                type="button"
              >
                <span
                  aria-hidden="true"
                  class="translate-x-0 pointer-events-none inline-block h-5 w-5 transform rounded-full bg-white shadow-sm ring-0 transition duration-200 ease-in-out group-data-[disabled]:bg-neutral/20 group-data-[disabled]:cursor-default"
                />
              </button>
              <span
                hidden=""
                style="position: fixed; top: 1px; left: 1px; width: 1px; height: 0px; padding: 0px; margin: -1px; overflow: hidden; clip: rect(0px, 0px, 0px, 0px); white-space: nowrap; border-width: 0px; display: none;"
              />
            </div>
          </div>
        </div>
        <div
          class="pb-2 break-words"
        >
          <h5
            class="text-lg"
          >
            Flex
          </h5>
          <span
            class="text-xl font-semibold"
          >
            <a
              class="text-primary underline hover:decoration-2 focus:decoration-2 focus:outline-none text-xl font-semibold hover:underline cursor-pointer"
              href="/chargers/PSL-12345/flex-details"
            >
              Unenrolled
            </a>
          </span>
        </div>
        <div
          class="flex"
        >
          <div
            class="pb-2 break-words"
          >
            <h5
              class="text-lg"
            >
              Delegated control
            </h5>
            <span
              class="text-xl font-semibold"
            >
              N/A
            </span>
          </div>
        </div>
      </div>
    </section>
    <div
      class="pb-2"
    />
    <section
      class="p-3 rounded-sm bg-white"
    >
      <div
        class="flex justify-between items-baseline"
      >
        <h3
          class="text-lg font-bold pb-4"
        >
          Schedules
        </h3>
        <a
          class="text-primary underline hover:decoration-2 focus:decoration-2 focus:outline-none cursor-pointer text-right ml-2"
          href="/chargers/PSL-12345/schedules"
        >
          Add schedules
        </a>
      </div>
      <p
        class="text-md font-normal break-words"
      >
        This charger can be used at all times, there are no charge schedules.
      </p>
    </section>
    <div
      class="pb-2"
    />
    <section
      class="p-3 rounded-sm bg-white"
    >
      <h3
        class="text-lg font-bold pb-4"
      >
        Tags
      </h3>
      <div
        class="flex flex-col bg-white rounded-sm overflow-hidden overflow-x-auto"
      >
        <div
          class="inline-block min-w-full -my-2 py-2 align-middle"
        >
          <table
            class="min-w-full"
            tabindex="0"
          >
            <caption
              class="sr-only"
            >
              Table of tags
            </caption>
            <tbody
              class="divide-y"
            >
              <tr
                class="border-b-neutral/20"
              >
                <td
                  class="whitespace-normal px-3 py-4 pl-0 pr-2 pt-2 pb-2 whitespace-pre"
                >
                  cur tollo claudeo
                </td>
                <td
                  class="whitespace-normal px-3 py-4 pr-0 pl-2 pt-2 pb-2 text-neutral text-left"
                >
                  sponte totus dedico
                </td>
              </tr>
              <tr
                class="border-b-neutral/20"
              >
                <td
                  class="whitespace-normal px-3 py-4 pl-0 pr-2 pt-2 pb-2 whitespace-pre"
                >
                  terreo cinis utilis
                </td>
                <td
                  class="whitespace-normal px-3 py-4 pr-0 pl-2 pt-2 pb-2 text-neutral text-left"
                >
                  tametsi vilitas adduco
                </td>
              </tr>
              <tr
                class="border-b-neutral/20"
              >
                <td
                  class="whitespace-normal px-3 py-4 pl-0 pr-2 pt-2 pb-2 whitespace-pre"
                >
                  sollers depromo vesica
                </td>
                <td
                  class="whitespace-normal px-3 py-4 pr-0 pl-2 pt-2 pb-2 text-neutral text-left"
                >
                  similique candidus ulterius
                </td>
              </tr>
              <tr
                class="border-b-neutral/20"
              >
                <td
                  class="whitespace-normal px-3 py-4 pl-0 pr-2 pt-2 pb-2 whitespace-pre"
                >
                  quod texo dapifer
                </td>
                <td
                  class="whitespace-normal px-3 py-4 pr-0 pl-2 pt-2 pb-2 text-neutral text-left"
                >
                  termes catena hic
                </td>
              </tr>
              <tr
                class="border-b-neutral/20"
              >
                <td
                  class="whitespace-normal px-3 py-4 pl-0 pr-2 pt-2 pb-2 whitespace-pre"
                >
                  tabgo adaugeo thesis
                </td>
                <td
                  class="whitespace-normal px-3 py-4 pr-0 pl-2 pt-2 pb-2 text-neutral text-left"
                >
                  celo excepturi curvo
                </td>
              </tr>
              <tr
                class="border-b-neutral/20"
              >
                <td
                  class="whitespace-normal px-3 py-4 pl-0 pr-2 pt-2 pb-2 whitespace-pre"
                >
                  asperiores currus theatrum
                </td>
                <td
                  class="whitespace-normal px-3 py-4 pr-0 pl-2 pt-2 pb-2 text-neutral text-left"
                >
                  thorax vinum deporto
                </td>
              </tr>
            </tbody>
          </table>
        </div>
      </div>
    </section>
    <div
      class="pb-2"
    />
  </div>
</body>
`;

exports[`Charger page content should match snapshot when the show tariffs and vehicles flag is true 1`] = `
<body>
  <div>
    <div
      class="rounded-md p-3 bg-warning/10 border border-warning"
    >
      <div
        class="flex"
      >
        <div
          class="shrink-0"
        >
          <span
            class="text-error/70"
          >
            <svg
              class="fill-current w-6 h-6"
              viewBox="0 0 32 32"
              xmlns="http://www.w3.org/2000/svg"
            >
              <g>
                <path
                  d="M16.1,29.43A13.43,13.43,0,1,1,29.53,16,13.45,13.45,0,0,1,16.1,29.43Zm0-24.86A11.43,11.43,0,1,0,27.53,16,11.45,11.45,0,0,0,16.1,4.57Z"
                />
                <path
                  d="M16,24.12a1,1,0,0,1-1-1V14.94a1,1,0,1,1,2,0v8.18A1,1,0,0,1,16,24.12Z"
                />
                <path
                  d="M16.1,10.89a1,1,0,0,1-1-1v-.2a1,1,0,0,1,2,0v.2A1,1,0,0,1,16.1,10.89Z"
                />
              </g>
            </svg>
          </span>
        </div>
        <div
          class="flex flex-col justify-center ml-4"
        >
          <p
            class="text-md font-normal sr-only break-words"
          >
            Alert
          </p>
          <div
            class="text-error/70"
          >
            <p
              class="text-md font-normal break-words"
            >
              It is not currently possible to make changes or send commands to this charger. You can search for this charger in MIS 
              <a
                class="text-primary underline hover:decoration-2 focus:decoration-2 focus:outline-none cursor-pointer"
                href="https://admin.pod-point.com/units?search=PSL-12345"
                target="_blank"
              >
                here
              </a>
               or view this charger in Grafana 
              <a
                class="text-primary underline hover:decoration-2 focus:decoration-2 focus:outline-none cursor-pointer"
                href="https://g-e728fe26ac.grafana-workspace.eu-west-1.amazonaws.com/d/fdtbud32bw45ce/5b147138-e9f2-5846-ba34-c2778f5a748f?var-ppid=PSL-12345"
                target="_blank"
              >
                here
              </a>
              .
            </p>
          </div>
        </div>
      </div>
    </div>
    <div
      class="pb-2"
    />
    <section
      class="p-3 rounded-sm bg-white"
    >
      <header>
        <div
          class="flex"
        >
          <div
            class="lg:flex space-x-1 items-center"
          >
            <div
              class="flex"
            >
              <h1
                class="text-xxl font-bold"
              >
                PSL-12345
              </h1>
              <button
                aria-label="Copy PSL-12345"
                class="border-2 border-solid rounded-sm font-bold text-center disabled:no-underline transition ease-in-out duration-200 cursor-pointer disabled:cursor-not-allowed border-transparent text-primary hover:underline hover:text-primary active:font-bold active:text-primary disabled:text-neutral/40 disabled:hover:no-underline disabled:hover:border-transparent p-1.5 ml-1"
                type="button"
              >
                <svg
                  class="fill-current h-4 w-4"
                  viewBox="0 0 24 24"
                  xmlns="http://www.w3.org/2000/svg"
                >
                  <defs>
                    <style>
                      .cls-1 
                    </style>
                  </defs>
                  <path
                    d="M18.73,14.5h1.83c1.35,0,2.44-1.09,2.44-2.44V3.5c0-1.35-1.09-2.44-2.44-2.44h-8.56c-1.35,0-2.44,1.09-2.44,2.44v1.83M3.45,9.61h8.56c1.35,0,2.44,1.09,2.44,2.44v8.56c0,1.35-1.09,2.44-2.44,2.44H3.45c-1.35,0-2.44-1.09-2.44-2.44v-8.56c0-1.35,1.09-2.44,2.44-2.44Z"
                    style="fill: none; stroke: currentColor; stroke-linecap: round; stroke-linejoin: round; stroke-width: 1.5px;"
                  />
                </svg>
              </button>
            </div>
          </div>
          <div
            class="ml-auto"
          >
            <div
              class="relative inline-block text-left"
              data-headlessui-state=""
            >
              <button
                aria-expanded="false"
                aria-haspopup="menu"
                class="bg-black border-black border-2 border-solid rounded-sm px-4 py-1.5 hover:bg-charcoal hover:border-charcoal focus:bg-charcoal focus:border-charcoal active:bg-charcoal active:border-charcoal disabled:bg-neutral/20 disabled:border-neutral/20 disabled:text-neutral font-bold inline-flex items-center text-white cursor-pointer disabled:cursor-not-allowed"
                data-headlessui-state=""
                id="headlessui-menu-button-:test-id-2"
                type="button"
              >
                <span>
                  Commands
                </span>
                <svg
                  class="fill-current stroke-current h-2 w-2 min-w-0 ml-2"
                  viewBox="0 0 24 24"
                  xmlns="http://www.w3.org/2000/svg"
                >
                  <g>
                    <path
                      d="m13.15,18.84c-.57.64-1.55.69-2.19.11-.04-.04-.08-.07-.11-.11L.4,7.23c-.57-.64-.52-1.61.12-2.19.28-.25.65-.4,1.03-.4h20.91c.85,0,1.55.69,1.55,1.55,0,.38-.14.75-.4,1.03l-10.45,11.62Z"
                    />
                  </g>
                </svg>
              </button>
            </div>
          </div>
        </div>
        <p
          class="text-md font-semibold break-words"
        >
          Test-Name
        </p>
        <div>
          <div
            class="flex items-center"
          >
            <a
              class="text-primary underline hover:decoration-2 focus:decoration-2 focus:outline-none cursor-pointer block mr-2"
              href="/accounts/a3450f38-217a-4d0c-8eec-b7d63c6fd2e0/details"
            >
              John Doe - <EMAIL>
            </a>
            <button
              aria-label="Unlink user John Doe"
              class="border-2 border-solid rounded-sm font-bold text-center disabled:no-underline transition ease-in-out duration-200 cursor-pointer disabled:cursor-not-allowed border-transparent text-primary hover:underline hover:text-primary active:font-bold active:text-primary disabled:text-neutral/40 disabled:hover:no-underline disabled:hover:border-transparent p-1.5"
              data-testid="unlink-user-a3450f38-217a-4d0c-8eec-b7d63c6fd2e0"
              id="unlink-user-a3450f38-217a-4d0c-8eec-b7d63c6fd2e0"
              name="unlink-user-a3450f38-217a-4d0c-8eec-b7d63c6fd2e0"
              type="button"
            >
              <svg
                class="fill-current h-4 w-4"
                viewBox="0 0 24 24"
                xmlns="http://www.w3.org/2000/svg"
              >
                <g>
                  <path
                    d="m9.94,14.21c3.38,0,6.12-2.75,6.12-6.12S13.32,1.96,9.94,1.96s-6.12,2.75-6.12,6.12,2.75,6.12,6.12,6.12Zm0-10.75c2.55,0,4.62,2.07,4.62,4.62s-2.07,4.62-4.62,4.62-4.62-2.07-4.62-4.62,2.07-4.62,4.62-4.62Z"
                  />
                  <path
                    d="m9.94,14.73C2.66,14.73.21,20.96.18,21.02c-.15.39.05.82.43.97.39.15.82-.04.97-.43.08-.22,2.13-5.33,8.36-5.33s8.77,5.16,8.87,5.38c.13.27.4.43.68.43.11,0,.21-.02.32-.07.37-.17.54-.62.36-.99-.12-.26-2.98-6.25-10.23-6.25Z"
                  />
                  <path
                    d="m21.42,9.31l2.23-2.23c.29-.29.29-.77,0-1.06s-.77-.29-1.06,0l-2.23,2.23-2.23-2.23c-.29-.29-.77-.29-1.06,0s-.29.77,0,1.06l2.23,2.23-2.23,2.23c-.29.29-.29.77,0,1.06.15.15.34.22.53.22s.38-.07.53-.22l2.23-2.23,2.23,2.23c.15.15.34.22.53.22s.38-.07.53-.22c.29-.29.29-.77,0-1.06l-2.23-2.23Z"
                  />
                </g>
              </svg>
            </button>
          </div>
          <div
            class="flex items-center"
          >
            <a
              class="text-primary underline hover:decoration-2 focus:decoration-2 focus:outline-none cursor-pointer block mr-2"
              href="/accounts/77b7c00e-8890-4a76-b4e6-def3b226d47b/details"
            >
              John Doe - <EMAIL>
            </a>
            <button
              aria-label="Unlink user John Doe"
              class="border-2 border-solid rounded-sm font-bold text-center disabled:no-underline transition ease-in-out duration-200 cursor-pointer disabled:cursor-not-allowed border-transparent text-primary hover:underline hover:text-primary active:font-bold active:text-primary disabled:text-neutral/40 disabled:hover:no-underline disabled:hover:border-transparent p-1.5"
              data-testid="unlink-user-77b7c00e-8890-4a76-b4e6-def3b226d47b"
              id="unlink-user-77b7c00e-8890-4a76-b4e6-def3b226d47b"
              name="unlink-user-77b7c00e-8890-4a76-b4e6-def3b226d47b"
              type="button"
            >
              <svg
                class="fill-current h-4 w-4"
                viewBox="0 0 24 24"
                xmlns="http://www.w3.org/2000/svg"
              >
                <g>
                  <path
                    d="m9.94,14.21c3.38,0,6.12-2.75,6.12-6.12S13.32,1.96,9.94,1.96s-6.12,2.75-6.12,6.12,2.75,6.12,6.12,6.12Zm0-10.75c2.55,0,4.62,2.07,4.62,4.62s-2.07,4.62-4.62,4.62-4.62-2.07-4.62-4.62,2.07-4.62,4.62-4.62Z"
                  />
                  <path
                    d="m9.94,14.73C2.66,14.73.21,20.96.18,21.02c-.15.39.05.82.43.97.39.15.82-.04.97-.43.08-.22,2.13-5.33,8.36-5.33s8.77,5.16,8.87,5.38c.13.27.4.43.68.43.11,0,.21-.02.32-.07.37-.17.54-.62.36-.99-.12-.26-2.98-6.25-10.23-6.25Z"
                  />
                  <path
                    d="m21.42,9.31l2.23-2.23c.29-.29.29-.77,0-1.06s-.77-.29-1.06,0l-2.23,2.23-2.23-2.23c-.29-.29-.77-.29-1.06,0s-.29.77,0,1.06l2.23,2.23-2.23,2.23c-.29.29-.29.77,0,1.06.15.15.34.22.53.22s.38-.07.53-.22l2.23-2.23,2.23,2.23c.15.15.34.22.53.22s.38-.07.53-.22c.29-.29.29-.77,0-1.06l-2.23-2.23Z"
                  />
                </g>
              </svg>
            </button>
          </div>
          <div
            class="flex items-center"
          >
            <a
              class="text-primary underline hover:decoration-2 focus:decoration-2 focus:outline-none cursor-pointer block mr-2"
              href="/accounts/5411de0c-d46b-48c3-b6d2-e6bc3a1f7049/details"
            >
              John Doe - <EMAIL>
            </a>
            <button
              aria-label="Unlink user John Doe"
              class="border-2 border-solid rounded-sm font-bold text-center disabled:no-underline transition ease-in-out duration-200 cursor-pointer disabled:cursor-not-allowed border-transparent text-primary hover:underline hover:text-primary active:font-bold active:text-primary disabled:text-neutral/40 disabled:hover:no-underline disabled:hover:border-transparent p-1.5"
              data-testid="unlink-user-5411de0c-d46b-48c3-b6d2-e6bc3a1f7049"
              id="unlink-user-5411de0c-d46b-48c3-b6d2-e6bc3a1f7049"
              name="unlink-user-5411de0c-d46b-48c3-b6d2-e6bc3a1f7049"
              type="button"
            >
              <svg
                class="fill-current h-4 w-4"
                viewBox="0 0 24 24"
                xmlns="http://www.w3.org/2000/svg"
              >
                <g>
                  <path
                    d="m9.94,14.21c3.38,0,6.12-2.75,6.12-6.12S13.32,1.96,9.94,1.96s-6.12,2.75-6.12,6.12,2.75,6.12,6.12,6.12Zm0-10.75c2.55,0,4.62,2.07,4.62,4.62s-2.07,4.62-4.62,4.62-4.62-2.07-4.62-4.62,2.07-4.62,4.62-4.62Z"
                  />
                  <path
                    d="m9.94,14.73C2.66,14.73.21,20.96.18,21.02c-.15.39.05.82.43.97.39.15.82-.04.97-.43.08-.22,2.13-5.33,8.36-5.33s8.77,5.16,8.87,5.38c.13.27.4.43.68.43.11,0,.21-.02.32-.07.37-.17.54-.62.36-.99-.12-.26-2.98-6.25-10.23-6.25Z"
                  />
                  <path
                    d="m21.42,9.31l2.23-2.23c.29-.29.29-.77,0-1.06s-.77-.29-1.06,0l-2.23,2.23-2.23-2.23c-.29-.29-.77-.29-1.06,0s-.29.77,0,1.06l2.23,2.23-2.23,2.23c-.29.29-.29.77,0,1.06.15.15.34.22.53.22s.38-.07.53-.22l2.23-2.23,2.23,2.23c.15.15.34.22.53.22s.38-.07.53-.22c.29-.29.29-.77,0-1.06l-2.23-2.23Z"
                  />
                </g>
              </svg>
            </button>
          </div>
          <div
            class="flex items-center"
          >
            <a
              class="text-primary underline hover:decoration-2 focus:decoration-2 focus:outline-none cursor-pointer block mr-2"
              href="/accounts/3482bea8-17d5-470e-83bd-4e897b8b11ee/details"
            >
              John Doe - <EMAIL>
            </a>
            <button
              aria-label="Unlink user John Doe"
              class="border-2 border-solid rounded-sm font-bold text-center disabled:no-underline transition ease-in-out duration-200 cursor-pointer disabled:cursor-not-allowed border-transparent text-primary hover:underline hover:text-primary active:font-bold active:text-primary disabled:text-neutral/40 disabled:hover:no-underline disabled:hover:border-transparent p-1.5"
              data-testid="unlink-user-3482bea8-17d5-470e-83bd-4e897b8b11ee"
              id="unlink-user-3482bea8-17d5-470e-83bd-4e897b8b11ee"
              name="unlink-user-3482bea8-17d5-470e-83bd-4e897b8b11ee"
              type="button"
            >
              <svg
                class="fill-current h-4 w-4"
                viewBox="0 0 24 24"
                xmlns="http://www.w3.org/2000/svg"
              >
                <g>
                  <path
                    d="m9.94,14.21c3.38,0,6.12-2.75,6.12-6.12S13.32,1.96,9.94,1.96s-6.12,2.75-6.12,6.12,2.75,6.12,6.12,6.12Zm0-10.75c2.55,0,4.62,2.07,4.62,4.62s-2.07,4.62-4.62,4.62-4.62-2.07-4.62-4.62,2.07-4.62,4.62-4.62Z"
                  />
                  <path
                    d="m9.94,14.73C2.66,14.73.21,20.96.18,21.02c-.15.39.05.82.43.97.39.15.82-.04.97-.43.08-.22,2.13-5.33,8.36-5.33s8.77,5.16,8.87,5.38c.13.27.4.43.68.43.11,0,.21-.02.32-.07.37-.17.54-.62.36-.99-.12-.26-2.98-6.25-10.23-6.25Z"
                  />
                  <path
                    d="m21.42,9.31l2.23-2.23c.29-.29.29-.77,0-1.06s-.77-.29-1.06,0l-2.23,2.23-2.23-2.23c-.29-.29-.77-.29-1.06,0s-.29.77,0,1.06l2.23,2.23-2.23,2.23c-.29.29-.29.77,0,1.06.15.15.34.22.53.22s.38-.07.53-.22l2.23-2.23,2.23,2.23c.15.15.34.22.53.22s.38-.07.53-.22c.29-.29.29-.77,0-1.06l-2.23-2.23Z"
                  />
                </g>
              </svg>
            </button>
          </div>
          <div
            class="flex items-center"
          >
            <a
              class="text-primary underline hover:decoration-2 focus:decoration-2 focus:outline-none cursor-pointer block mr-2"
              href="/accounts/609cf19a-c8c9-43f8-9042-59b5527e5f73/details"
            >
              John Doe - <EMAIL>
            </a>
            <button
              aria-label="Unlink user John Doe"
              class="border-2 border-solid rounded-sm font-bold text-center disabled:no-underline transition ease-in-out duration-200 cursor-pointer disabled:cursor-not-allowed border-transparent text-primary hover:underline hover:text-primary active:font-bold active:text-primary disabled:text-neutral/40 disabled:hover:no-underline disabled:hover:border-transparent p-1.5"
              data-testid="unlink-user-609cf19a-c8c9-43f8-9042-59b5527e5f73"
              id="unlink-user-609cf19a-c8c9-43f8-9042-59b5527e5f73"
              name="unlink-user-609cf19a-c8c9-43f8-9042-59b5527e5f73"
              type="button"
            >
              <svg
                class="fill-current h-4 w-4"
                viewBox="0 0 24 24"
                xmlns="http://www.w3.org/2000/svg"
              >
                <g>
                  <path
                    d="m9.94,14.21c3.38,0,6.12-2.75,6.12-6.12S13.32,1.96,9.94,1.96s-6.12,2.75-6.12,6.12,2.75,6.12,6.12,6.12Zm0-10.75c2.55,0,4.62,2.07,4.62,4.62s-2.07,4.62-4.62,4.62-4.62-2.07-4.62-4.62,2.07-4.62,4.62-4.62Z"
                  />
                  <path
                    d="m9.94,14.73C2.66,14.73.21,20.96.18,21.02c-.15.39.05.82.43.97.39.15.82-.04.97-.43.08-.22,2.13-5.33,8.36-5.33s8.77,5.16,8.87,5.38c.13.27.4.43.68.43.11,0,.21-.02.32-.07.37-.17.54-.62.36-.99-.12-.26-2.98-6.25-10.23-6.25Z"
                  />
                  <path
                    d="m21.42,9.31l2.23-2.23c.29-.29.29-.77,0-1.06s-.77-.29-1.06,0l-2.23,2.23-2.23-2.23c-.29-.29-.77-.29-1.06,0s-.29.77,0,1.06l2.23,2.23-2.23,2.23c-.29.29-.29.77,0,1.06.15.15.34.22.53.22s.38-.07.53-.22l2.23-2.23,2.23,2.23c.15.15.34.22.53.22s.38-.07.53-.22c.29-.29.29-.77,0-1.06l-2.23-2.23Z"
                  />
                </g>
              </svg>
            </button>
          </div>
          <div
            class="flex items-center"
          >
            <a
              class="text-primary underline hover:decoration-2 focus:decoration-2 focus:outline-none cursor-pointer block mr-2"
              href="/accounts/910b444d-f474-46c3-b368-c43b170dbda9/details"
            >
              John Doe - <EMAIL>
            </a>
            <button
              aria-label="Unlink user John Doe"
              class="border-2 border-solid rounded-sm font-bold text-center disabled:no-underline transition ease-in-out duration-200 cursor-pointer disabled:cursor-not-allowed border-transparent text-primary hover:underline hover:text-primary active:font-bold active:text-primary disabled:text-neutral/40 disabled:hover:no-underline disabled:hover:border-transparent p-1.5"
              data-testid="unlink-user-910b444d-f474-46c3-b368-c43b170dbda9"
              id="unlink-user-910b444d-f474-46c3-b368-c43b170dbda9"
              name="unlink-user-910b444d-f474-46c3-b368-c43b170dbda9"
              type="button"
            >
              <svg
                class="fill-current h-4 w-4"
                viewBox="0 0 24 24"
                xmlns="http://www.w3.org/2000/svg"
              >
                <g>
                  <path
                    d="m9.94,14.21c3.38,0,6.12-2.75,6.12-6.12S13.32,1.96,9.94,1.96s-6.12,2.75-6.12,6.12,2.75,6.12,6.12,6.12Zm0-10.75c2.55,0,4.62,2.07,4.62,4.62s-2.07,4.62-4.62,4.62-4.62-2.07-4.62-4.62,2.07-4.62,4.62-4.62Z"
                  />
                  <path
                    d="m9.94,14.73C2.66,14.73.21,20.96.18,21.02c-.15.39.05.82.43.97.39.15.82-.04.97-.43.08-.22,2.13-5.33,8.36-5.33s8.77,5.16,8.87,5.38c.13.27.4.43.68.43.11,0,.21-.02.32-.07.37-.17.54-.62.36-.99-.12-.26-2.98-6.25-10.23-6.25Z"
                  />
                  <path
                    d="m21.42,9.31l2.23-2.23c.29-.29.29-.77,0-1.06s-.77-.29-1.06,0l-2.23,2.23-2.23-2.23c-.29-.29-.77-.29-1.06,0s-.29.77,0,1.06l2.23,2.23-2.23,2.23c-.29.29-.29.77,0,1.06.15.15.34.22.53.22s.38-.07.53-.22l2.23-2.23,2.23,2.23c.15.15.34.22.53.22s.38-.07.53-.22c.29-.29.29-.77,0-1.06l-2.23-2.23Z"
                  />
                </g>
              </svg>
            </button>
          </div>
          <div
            class="pb-2"
          />
          <div
            class="flex max-md:flex-col max-md:space-y-2 md:space-x-6"
          >
            <div
              class="flex items-center"
            >
              <svg
                class="fill-current w-6 h-6"
                viewBox="0 0 24 24"
                xmlns="http://www.w3.org/2000/svg"
              >
                <title>
                  Domestic location
                </title>
                <g>
                  <path
                    d="m22.07,6.92L14.05.84c-1.21-.92-2.89-.92-4.1,0L1.93,6.92c-.84.64-1.34,1.65-1.34,2.7v10.84c0,1.87,1.52,3.39,3.39,3.39h5.04c.41,0,.75-.34.75-.75s-.34-.75-.75-.75H3.98c-1.04,0-1.89-.85-1.89-1.89v-10.84c0-.59.28-1.15.75-1.5L10.86,2.03c.67-.51,1.61-.51,2.28,0l8.02,6.08c.47.36.74.92.74,1.5v10.84c0,1.04-.85,1.89-1.89,1.89h-5.53c-.96,0-1.74-.78-1.74-1.74v-2.63c1.85-.35,3.25-1.98,3.25-3.93v-3.25c0-.41-.34-.75-.75-.75h-.81v-1.88c0-.41-.34-.75-.75-.75s-.75.34-.75.75v1.88h-1.77v-1.88c0-.41-.34-.75-.75-.75s-.75.34-.75.75v1.88h-.93c-.41,0-.75.34-.75.75v3.25c0,1.95,1.4,3.57,3.25,3.93v2.63c0,1.79,1.45,3.24,3.24,3.24h5.53c1.87,0,3.39-1.52,3.39-3.39v-10.84c0-1.05-.5-2.06-1.34-2.7Zm-12.58,7.13v-2.5h5.01v2.5c0,1.38-1.12,2.5-2.5,2.5s-2.5-1.12-2.5-2.5Z"
                  />
                </g>
              </svg>
              <span
                class="ml-2"
              >
                Domestic
              </span>
            </div>
          </div>
        </div>
      </header>
    </section>
    <div
      class="pb-2"
    />
    <div
      class="flex flex-row flex-wrap gap-x-4 gap-y-2"
    >
      <span
        aria-label="Fault vendorErrorCode4 - Invalid Date"
        class="font-bold px-2.5 py-1 rounded-xs inline-flex items-center tracking-wide bg-warning/10 border border-error/70 text-error/70"
        role="status"
      >
        Fault vendorErrorCode4 - Invalid Date
      </span>
      <span
        aria-label="Fault vendorErrorCode3 - Invalid Date"
        class="font-bold px-2.5 py-1 rounded-xs inline-flex items-center tracking-wide bg-warning/10 border border-error/70 text-error/70"
        role="status"
      >
        Fault vendorErrorCode3 - Invalid Date
      </span>
      <span
        aria-label="Fault vendorErrorCode2 - Invalid Date"
        class="font-bold px-2.5 py-1 rounded-xs inline-flex items-center tracking-wide bg-warning/10 border border-error/70 text-error/70"
        role="status"
      >
        Fault vendorErrorCode2 - Invalid Date
      </span>
      <span
        aria-label="Fault vendorErrorCode1 - Invalid Date"
        class="font-bold px-2.5 py-1 rounded-xs inline-flex items-center tracking-wide bg-warning/10 border border-error/70 text-error/70"
        role="status"
      >
        Fault vendorErrorCode1 - Invalid Date
      </span>
    </div>
    <div
      class="pb-2"
    />
    <div
      class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 print:grid-cols-3 gap-2"
    >
      <section
        class="p-3 rounded-sm bg-white md:col-span-2 lg:col-span-1"
      >
        <div
          class="flex justify-between items-baseline"
        >
          <h3
            class="text-lg font-bold pb-4"
          >
            Charger information
          </h3>
          <a
            class="text-primary underline hover:decoration-2 focus:decoration-2 focus:outline-none cursor-pointer text-right ml-2"
            href="/chargers/PSL-12345/logs/diagnostic?socket=A"
          >
            View diagnostic logs
          </a>
        </div>
        <div
          class="flex flex-col bg-white rounded-sm overflow-hidden overflow-x-auto"
        >
          <div
            class="inline-block min-w-full -my-2 py-2 align-middle"
          >
            <table
              class="min-w-full"
              tabindex="0"
            >
              <caption
                class="sr-only"
              >
                Charger information table
              </caption>
              <tbody
                class="divide-y"
              >
                <tr
                  class="border-b-neutral/20"
                >
                  <td
                    class="whitespace-normal px-3 py-4 pl-0 pr-2 pt-2 pb-2 whitespace-pre"
                  >
                    Model SKU
                  </td>
                  <td
                    class="whitespace-normal px-3 py-4 pr-0 pl-2 pt-2 pb-2 text-neutral text-right"
                  >
                    T7-S-07-AMC-BLK
                  </td>
                </tr>
                <tr
                  class="border-b-neutral/20"
                >
                  <td
                    class="whitespace-normal px-3 py-4 pl-0 pr-2 pt-2 pb-2 whitespace-pre"
                  >
                    Arch version
                  </td>
                  <td
                    class="whitespace-normal px-3 py-4 pr-0 pl-2 pt-2 pb-2 text-neutral text-right"
                  >
                    -
                  </td>
                </tr>
                <tr
                  class="border-b-neutral/20"
                >
                  <td
                    class="whitespace-normal px-3 py-4 pl-0 pr-2 pt-2 pb-2 whitespace-pre"
                  >
                    Firmware
                  </td>
                  <td
                    class="whitespace-normal px-3 py-4 pr-0 pl-2 pt-2 pb-2 text-neutral text-right"
                  >
                    A50P-X.Y.Z-0000P
                  </td>
                </tr>
                <tr
                  class="border-b-neutral/20"
                >
                  <td
                    class="whitespace-normal px-3 py-4 pl-0 pr-2 pt-2 pb-2 whitespace-pre"
                  >
                    Manufactured
                  </td>
                  <td
                    class="whitespace-normal px-3 py-4 pr-0 pl-2 pt-2 pb-2 text-neutral text-right"
                  >
                    1 June 2023
                  </td>
                </tr>
                <tr
                  class="border-b-neutral/20"
                >
                  <td
                    class="whitespace-normal px-3 py-4 pl-0 pr-2 pt-2 pb-2 whitespace-pre"
                  >
                    Operator
                  </td>
                  <td
                    class="whitespace-normal px-3 py-4 pr-0 pl-2 pt-2 pb-2 text-neutral text-right"
                  >
                    Pod Point
                  </td>
                </tr>
                <tr
                  class="border-b-neutral/20"
                >
                  <td
                    class="whitespace-normal px-3 py-4 pl-0 pr-2 pt-2 pb-2 whitespace-pre"
                  >
                    MAC address
                  </td>
                  <td
                    class="whitespace-normal px-3 py-4 pr-0 pl-2 pt-2 pb-2 text-neutral text-right"
                  >
                    -
                  </td>
                </tr>
                <tr
                  class="border-b-neutral/20"
                >
                  <td
                    class="whitespace-normal px-3 py-4 pl-0 pr-2 pt-2 pb-2 whitespace-pre"
                  >
                    PCBA serial number
                  </td>
                  <td
                    class="whitespace-normal px-3 py-4 pr-0 pl-2 pt-2 pb-2 text-neutral text-right"
                  >
                    -
                  </td>
                </tr>
              </tbody>
            </table>
          </div>
        </div>
        <div
          class="flex flex-row-reverse"
        >
          <a
            class="text-primary underline hover:decoration-2 focus:decoration-2 focus:outline-none cursor-pointer text-right ml-2"
            href="/chargers/PSL-12345/pcbs/history?socket=A"
          >
            History
          </a>
        </div>
      </section>
      <section
        class="p-3 rounded-sm bg-white"
      >
        <h3
          class="text-lg font-bold pb-4"
        >
          Charger connectivity
        </h3>
        <div
          class="flex flex-col bg-white rounded-sm overflow-hidden overflow-x-auto"
        >
          <div
            class="inline-block min-w-full -my-2 py-2 align-middle"
          >
            <table
              class="min-w-full"
              tabindex="0"
            >
              <caption
                class="sr-only"
              >
                Charger connectivity table
              </caption>
              <tbody
                class="divide-y"
              >
                <tr
                  class="border-b-neutral/20"
                >
                  <td
                    class="whitespace-normal px-3 py-4 pl-0 pr-2 pt-2 pb-2 whitespace-pre"
                  >
                    Status
                  </td>
                  <td
                    class="whitespace-normal px-3 py-4 pr-0 pl-2 pt-2 pb-2 text-neutral text-right"
                  >
                    <span
                      aria-label="connectivityStatus"
                      class="font-bold px-2.5 rounded-xs inline-flex items-center tracking-wide bg-success/10 border border-success text-success py-px text-xs"
                      role="status"
                    >
                      connectivityStatus
                    </span>
                  </td>
                </tr>
                <tr
                  class="border-b-neutral/20"
                >
                  <td
                    class="whitespace-normal px-3 py-4 pl-0 pr-2 pt-2 pb-2 whitespace-pre"
                  >
                    Charging status
                  </td>
                  <td
                    class="whitespace-normal px-3 py-4 pr-0 pl-2 pt-2 pb-2 text-neutral text-right"
                  >
                    chargingState
                  </td>
                </tr>
                <tr
                  class="border-b-neutral/20"
                >
                  <td
                    class="whitespace-normal px-3 py-4 pl-0 pr-2 pt-2 pb-2 whitespace-pre"
                  >
                    Last message
                  </td>
                  <td
                    class="whitespace-normal px-3 py-4 pr-0 pl-2 pt-2 pb-2 text-neutral text-right"
                  >
                    30 June 2024 at 22:00:00
                  </td>
                </tr>
                <tr
                  class="border-b-neutral/20"
                >
                  <td
                    class="whitespace-normal px-3 py-4 pl-0 pr-2 pt-2 pb-2 whitespace-pre"
                  >
                    Connection quality
(scale 1-5)
                  </td>
                  <td
                    class="whitespace-normal px-3 py-4 pr-0 pl-2 pt-2 pb-2 text-neutral text-right"
                  >
                    0
                  </td>
                </tr>
                <tr
                  class="border-b-neutral/20"
                >
                  <td
                    class="whitespace-normal px-3 py-4 pl-0 pr-2 pt-2 pb-2 whitespace-pre"
                  >
                    Router serial number
                  </td>
                  <td
                    class="whitespace-normal px-3 py-4 pr-0 pl-2 pt-2 pb-2 text-neutral text-right"
                  >
                    1102538429
                  </td>
                </tr>
                <tr
                  class="border-b-neutral/20"
                >
                  <td
                    class="whitespace-normal px-3 py-4 pl-0 pr-2 pt-2 pb-2 whitespace-pre"
                  >
                    Router MAC address
                  </td>
                  <td
                    class="whitespace-normal px-3 py-4 pr-0 pl-2 pt-2 pb-2 text-neutral text-right"
                  >
                    001E4223CF13
                  </td>
                </tr>
                <tr
                  class="border-b-neutral/20"
                >
                  <td
                    class="whitespace-normal px-3 py-4 pl-0 pr-2 pt-2 pb-2 whitespace-pre"
                  >
                    Router SIM number
                  </td>
                  <td
                    class="whitespace-normal px-3 py-4 pr-0 pl-2 pt-2 pb-2 text-neutral text-right"
                  >
                    123456789123000043
                  </td>
                </tr>
                <tr
                  class="border-b-neutral/20"
                >
                  <td
                    class="whitespace-normal px-3 py-4 pl-0 pr-2 pt-2 pb-2 whitespace-pre"
                  >
                    Router model
                  </td>
                  <td
                    class="whitespace-normal px-3 py-4 pr-0 pl-2 pt-2 pb-2 text-neutral text-right"
                  >
                    RUT241
                  </td>
                </tr>
              </tbody>
            </table>
          </div>
        </div>
      </section>
      <section
        class="p-3 rounded-sm bg-white"
      >
        <div
          class="flex justify-between items-baseline"
        >
          <h3
            class="text-lg font-bold pb-4"
          >
            Charger access point (AP)
          </h3>
          <a
            class="text-primary underline hover:decoration-2 focus:decoration-2 focus:outline-none cursor-pointer text-right ml-2"
            href="/chargers/PSL-12345?redact=true"
          >
            Hide password
          </a>
        </div>
        <div
          class="flex flex-col bg-white rounded-sm overflow-hidden overflow-x-auto"
        >
          <div
            class="inline-block min-w-full -my-2 py-2 align-middle"
          >
            <table
              class="min-w-full"
              tabindex="0"
            >
              <caption
                class="sr-only"
              >
                Charger access point
              </caption>
              <tbody
                class="divide-y"
              >
                <tr
                  class="border-b-neutral/20"
                >
                  <td
                    class="whitespace-normal px-3 py-4 pl-0 pr-2 pt-2 pb-2 whitespace-pre"
                  >
                    SSID
                  </td>
                  <td
                    class="whitespace-normal px-3 py-4 pr-0 pl-2 pt-2 pb-2 text-neutral text-right"
                  >
                    PP-123456
                  </td>
                </tr>
                <tr
                  class="border-b-neutral/20"
                >
                  <td
                    class="whitespace-normal px-3 py-4 pl-0 pr-2 pt-2 pb-2 whitespace-pre"
                  >
                    Password
                  </td>
                  <td
                    class="whitespace-normal px-3 py-4 pr-0 pl-2 pt-2 pb-2 text-neutral text-right"
                  >
                    password123
                  </td>
                </tr>
              </tbody>
            </table>
          </div>
        </div>
        <div
          class="border-2 border-dotted border-neutral/20 mt-2 h-32 p-3"
        >
          <svg
            class="w-full h-full"
            height="256"
            viewBox="0 0 25 25"
            width="256"
            xmlns="http://www.w3.org/2000/svg"
          >
            <path
              d="       M 7 0 l 1 0 0 1 -1 0 Z   M 10 0 l 1 0 0 1 -1 0 Z M 11 0 l 1 0 0 1 -1 0 Z   M 14 0 l 1 0 0 1 -1 0 Z M 15 0 l 1 0 0 1 -1 0 Z  M 17 0 l 1 0 0 1 -1 0 Z         M 1 1 l 1 0 0 1 -1 0 Z M 2 1 l 1 0 0 1 -1 0 Z M 3 1 l 1 0 0 1 -1 0 Z M 4 1 l 1 0 0 1 -1 0 Z M 5 1 l 1 0 0 1 -1 0 Z  M 7 1 l 1 0 0 1 -1 0 Z   M 10 1 l 1 0 0 1 -1 0 Z M 11 1 l 1 0 0 1 -1 0 Z  M 13 1 l 1 0 0 1 -1 0 Z  M 15 1 l 1 0 0 1 -1 0 Z M 16 1 l 1 0 0 1 -1 0 Z M 17 1 l 1 0 0 1 -1 0 Z  M 19 1 l 1 0 0 1 -1 0 Z M 20 1 l 1 0 0 1 -1 0 Z M 21 1 l 1 0 0 1 -1 0 Z M 22 1 l 1 0 0 1 -1 0 Z M 23 1 l 1 0 0 1 -1 0 Z   M 1 2 l 1 0 0 1 -1 0 Z    M 5 2 l 1 0 0 1 -1 0 Z  M 7 2 l 1 0 0 1 -1 0 Z M 8 2 l 1 0 0 1 -1 0 Z      M 14 2 l 1 0 0 1 -1 0 Z  M 16 2 l 1 0 0 1 -1 0 Z M 17 2 l 1 0 0 1 -1 0 Z  M 19 2 l 1 0 0 1 -1 0 Z    M 23 2 l 1 0 0 1 -1 0 Z   M 1 3 l 1 0 0 1 -1 0 Z    M 5 3 l 1 0 0 1 -1 0 Z  M 7 3 l 1 0 0 1 -1 0 Z M 8 3 l 1 0 0 1 -1 0 Z  M 10 3 l 1 0 0 1 -1 0 Z M 11 3 l 1 0 0 1 -1 0 Z   M 14 3 l 1 0 0 1 -1 0 Z M 15 3 l 1 0 0 1 -1 0 Z  M 17 3 l 1 0 0 1 -1 0 Z  M 19 3 l 1 0 0 1 -1 0 Z    M 23 3 l 1 0 0 1 -1 0 Z   M 1 4 l 1 0 0 1 -1 0 Z    M 5 4 l 1 0 0 1 -1 0 Z  M 7 4 l 1 0 0 1 -1 0 Z   M 10 4 l 1 0 0 1 -1 0 Z M 11 4 l 1 0 0 1 -1 0 Z   M 14 4 l 1 0 0 1 -1 0 Z M 15 4 l 1 0 0 1 -1 0 Z  M 17 4 l 1 0 0 1 -1 0 Z  M 19 4 l 1 0 0 1 -1 0 Z    M 23 4 l 1 0 0 1 -1 0 Z   M 1 5 l 1 0 0 1 -1 0 Z M 2 5 l 1 0 0 1 -1 0 Z M 3 5 l 1 0 0 1 -1 0 Z M 4 5 l 1 0 0 1 -1 0 Z M 5 5 l 1 0 0 1 -1 0 Z  M 7 5 l 1 0 0 1 -1 0 Z   M 10 5 l 1 0 0 1 -1 0 Z M 11 5 l 1 0 0 1 -1 0 Z M 12 5 l 1 0 0 1 -1 0 Z   M 15 5 l 1 0 0 1 -1 0 Z  M 17 5 l 1 0 0 1 -1 0 Z  M 19 5 l 1 0 0 1 -1 0 Z M 20 5 l 1 0 0 1 -1 0 Z M 21 5 l 1 0 0 1 -1 0 Z M 22 5 l 1 0 0 1 -1 0 Z M 23 5 l 1 0 0 1 -1 0 Z         M 7 6 l 1 0 0 1 -1 0 Z  M 9 6 l 1 0 0 1 -1 0 Z  M 11 6 l 1 0 0 1 -1 0 Z  M 13 6 l 1 0 0 1 -1 0 Z  M 15 6 l 1 0 0 1 -1 0 Z  M 17 6 l 1 0 0 1 -1 0 Z        M 0 7 l 1 0 0 1 -1 0 Z M 1 7 l 1 0 0 1 -1 0 Z M 2 7 l 1 0 0 1 -1 0 Z M 3 7 l 1 0 0 1 -1 0 Z M 4 7 l 1 0 0 1 -1 0 Z M 5 7 l 1 0 0 1 -1 0 Z M 6 7 l 1 0 0 1 -1 0 Z M 7 7 l 1 0 0 1 -1 0 Z  M 9 7 l 1 0 0 1 -1 0 Z  M 11 7 l 1 0 0 1 -1 0 Z  M 13 7 l 1 0 0 1 -1 0 Z M 14 7 l 1 0 0 1 -1 0 Z  M 16 7 l 1 0 0 1 -1 0 Z M 17 7 l 1 0 0 1 -1 0 Z M 18 7 l 1 0 0 1 -1 0 Z M 19 7 l 1 0 0 1 -1 0 Z M 20 7 l 1 0 0 1 -1 0 Z M 21 7 l 1 0 0 1 -1 0 Z M 22 7 l 1 0 0 1 -1 0 Z M 23 7 l 1 0 0 1 -1 0 Z M 24 7 l 1 0 0 1 -1 0 Z    M 3 8 l 1 0 0 1 -1 0 Z M 4 8 l 1 0 0 1 -1 0 Z   M 7 8 l 1 0 0 1 -1 0 Z      M 13 8 l 1 0 0 1 -1 0 Z   M 16 8 l 1 0 0 1 -1 0 Z     M 21 8 l 1 0 0 1 -1 0 Z M 22 8 l 1 0 0 1 -1 0 Z    M 1 9 l 1 0 0 1 -1 0 Z   M 4 9 l 1 0 0 1 -1 0 Z M 5 9 l 1 0 0 1 -1 0 Z M 6 9 l 1 0 0 1 -1 0 Z    M 10 9 l 1 0 0 1 -1 0 Z    M 14 9 l 1 0 0 1 -1 0 Z  M 16 9 l 1 0 0 1 -1 0 Z M 17 9 l 1 0 0 1 -1 0 Z  M 19 9 l 1 0 0 1 -1 0 Z M 20 9 l 1 0 0 1 -1 0 Z   M 23 9 l 1 0 0 1 -1 0 Z M 24 9 l 1 0 0 1 -1 0 Z M 0 10 l 1 0 0 1 -1 0 Z  M 2 10 l 1 0 0 1 -1 0 Z   M 5 10 l 1 0 0 1 -1 0 Z    M 9 10 l 1 0 0 1 -1 0 Z M 10 10 l 1 0 0 1 -1 0 Z    M 14 10 l 1 0 0 1 -1 0 Z   M 17 10 l 1 0 0 1 -1 0 Z M 18 10 l 1 0 0 1 -1 0 Z M 19 10 l 1 0 0 1 -1 0 Z    M 23 10 l 1 0 0 1 -1 0 Z  M 0 11 l 1 0 0 1 -1 0 Z  M 2 11 l 1 0 0 1 -1 0 Z   M 5 11 l 1 0 0 1 -1 0 Z M 6 11 l 1 0 0 1 -1 0 Z M 7 11 l 1 0 0 1 -1 0 Z M 8 11 l 1 0 0 1 -1 0 Z  M 10 11 l 1 0 0 1 -1 0 Z  M 12 11 l 1 0 0 1 -1 0 Z M 13 11 l 1 0 0 1 -1 0 Z M 14 11 l 1 0 0 1 -1 0 Z  M 16 11 l 1 0 0 1 -1 0 Z M 17 11 l 1 0 0 1 -1 0 Z M 18 11 l 1 0 0 1 -1 0 Z  M 20 11 l 1 0 0 1 -1 0 Z M 21 11 l 1 0 0 1 -1 0 Z M 22 11 l 1 0 0 1 -1 0 Z   M 0 12 l 1 0 0 1 -1 0 Z M 1 12 l 1 0 0 1 -1 0 Z  M 3 12 l 1 0 0 1 -1 0 Z M 4 12 l 1 0 0 1 -1 0 Z      M 10 12 l 1 0 0 1 -1 0 Z  M 12 12 l 1 0 0 1 -1 0 Z M 13 12 l 1 0 0 1 -1 0 Z M 14 12 l 1 0 0 1 -1 0 Z  M 16 12 l 1 0 0 1 -1 0 Z M 17 12 l 1 0 0 1 -1 0 Z  M 19 12 l 1 0 0 1 -1 0 Z M 20 12 l 1 0 0 1 -1 0 Z  M 22 12 l 1 0 0 1 -1 0 Z   M 0 13 l 1 0 0 1 -1 0 Z  M 2 13 l 1 0 0 1 -1 0 Z   M 5 13 l 1 0 0 1 -1 0 Z M 6 13 l 1 0 0 1 -1 0 Z M 7 13 l 1 0 0 1 -1 0 Z   M 10 13 l 1 0 0 1 -1 0 Z   M 13 13 l 1 0 0 1 -1 0 Z   M 16 13 l 1 0 0 1 -1 0 Z  M 18 13 l 1 0 0 1 -1 0 Z M 19 13 l 1 0 0 1 -1 0 Z M 20 13 l 1 0 0 1 -1 0 Z    M 24 13 l 1 0 0 1 -1 0 Z      M 5 14 l 1 0 0 1 -1 0 Z  M 7 14 l 1 0 0 1 -1 0 Z  M 9 14 l 1 0 0 1 -1 0 Z M 10 14 l 1 0 0 1 -1 0 Z  M 12 14 l 1 0 0 1 -1 0 Z    M 16 14 l 1 0 0 1 -1 0 Z     M 21 14 l 1 0 0 1 -1 0 Z  M 23 14 l 1 0 0 1 -1 0 Z  M 0 15 l 1 0 0 1 -1 0 Z M 1 15 l 1 0 0 1 -1 0 Z   M 4 15 l 1 0 0 1 -1 0 Z M 5 15 l 1 0 0 1 -1 0 Z M 6 15 l 1 0 0 1 -1 0 Z M 7 15 l 1 0 0 1 -1 0 Z  M 9 15 l 1 0 0 1 -1 0 Z    M 13 15 l 1 0 0 1 -1 0 Z M 14 15 l 1 0 0 1 -1 0 Z M 15 15 l 1 0 0 1 -1 0 Z M 16 15 l 1 0 0 1 -1 0 Z M 17 15 l 1 0 0 1 -1 0 Z M 18 15 l 1 0 0 1 -1 0 Z  M 20 15 l 1 0 0 1 -1 0 Z M 21 15 l 1 0 0 1 -1 0 Z M 22 15 l 1 0 0 1 -1 0 Z M 23 15 l 1 0 0 1 -1 0 Z M 24 15 l 1 0 0 1 -1 0 Z   M 2 16 l 1 0 0 1 -1 0 Z   M 5 16 l 1 0 0 1 -1 0 Z  M 7 16 l 1 0 0 1 -1 0 Z  M 9 16 l 1 0 0 1 -1 0 Z  M 11 16 l 1 0 0 1 -1 0 Z  M 13 16 l 1 0 0 1 -1 0 Z M 14 16 l 1 0 0 1 -1 0 Z       M 21 16 l 1 0 0 1 -1 0 Z M 22 16 l 1 0 0 1 -1 0 Z   M 0 17 l 1 0 0 1 -1 0 Z M 1 17 l 1 0 0 1 -1 0 Z M 2 17 l 1 0 0 1 -1 0 Z M 3 17 l 1 0 0 1 -1 0 Z M 4 17 l 1 0 0 1 -1 0 Z M 5 17 l 1 0 0 1 -1 0 Z M 6 17 l 1 0 0 1 -1 0 Z M 7 17 l 1 0 0 1 -1 0 Z      M 13 17 l 1 0 0 1 -1 0 Z M 14 17 l 1 0 0 1 -1 0 Z   M 17 17 l 1 0 0 1 -1 0 Z M 18 17 l 1 0 0 1 -1 0 Z M 19 17 l 1 0 0 1 -1 0 Z  M 21 17 l 1 0 0 1 -1 0 Z M 22 17 l 1 0 0 1 -1 0 Z  M 24 17 l 1 0 0 1 -1 0 Z        M 7 18 l 1 0 0 1 -1 0 Z M 8 18 l 1 0 0 1 -1 0 Z M 9 18 l 1 0 0 1 -1 0 Z   M 12 18 l 1 0 0 1 -1 0 Z   M 15 18 l 1 0 0 1 -1 0 Z  M 17 18 l 1 0 0 1 -1 0 Z  M 19 18 l 1 0 0 1 -1 0 Z  M 21 18 l 1 0 0 1 -1 0 Z  M 23 18 l 1 0 0 1 -1 0 Z   M 1 19 l 1 0 0 1 -1 0 Z M 2 19 l 1 0 0 1 -1 0 Z M 3 19 l 1 0 0 1 -1 0 Z M 4 19 l 1 0 0 1 -1 0 Z M 5 19 l 1 0 0 1 -1 0 Z  M 7 19 l 1 0 0 1 -1 0 Z      M 13 19 l 1 0 0 1 -1 0 Z  M 15 19 l 1 0 0 1 -1 0 Z  M 17 19 l 1 0 0 1 -1 0 Z M 18 19 l 1 0 0 1 -1 0 Z M 19 19 l 1 0 0 1 -1 0 Z  M 21 19 l 1 0 0 1 -1 0 Z M 22 19 l 1 0 0 1 -1 0 Z    M 1 20 l 1 0 0 1 -1 0 Z    M 5 20 l 1 0 0 1 -1 0 Z  M 7 20 l 1 0 0 1 -1 0 Z M 8 20 l 1 0 0 1 -1 0 Z  M 10 20 l 1 0 0 1 -1 0 Z  M 12 20 l 1 0 0 1 -1 0 Z M 13 20 l 1 0 0 1 -1 0 Z M 14 20 l 1 0 0 1 -1 0 Z       M 21 20 l 1 0 0 1 -1 0 Z M 22 20 l 1 0 0 1 -1 0 Z M 23 20 l 1 0 0 1 -1 0 Z M 24 20 l 1 0 0 1 -1 0 Z  M 1 21 l 1 0 0 1 -1 0 Z    M 5 21 l 1 0 0 1 -1 0 Z  M 7 21 l 1 0 0 1 -1 0 Z M 8 21 l 1 0 0 1 -1 0 Z M 9 21 l 1 0 0 1 -1 0 Z    M 13 21 l 1 0 0 1 -1 0 Z   M 16 21 l 1 0 0 1 -1 0 Z  M 18 21 l 1 0 0 1 -1 0 Z   M 21 21 l 1 0 0 1 -1 0 Z M 22 21 l 1 0 0 1 -1 0 Z  M 24 21 l 1 0 0 1 -1 0 Z  M 1 22 l 1 0 0 1 -1 0 Z    M 5 22 l 1 0 0 1 -1 0 Z  M 7 22 l 1 0 0 1 -1 0 Z  M 9 22 l 1 0 0 1 -1 0 Z   M 12 22 l 1 0 0 1 -1 0 Z M 13 22 l 1 0 0 1 -1 0 Z  M 15 22 l 1 0 0 1 -1 0 Z M 16 22 l 1 0 0 1 -1 0 Z M 17 22 l 1 0 0 1 -1 0 Z     M 22 22 l 1 0 0 1 -1 0 Z    M 1 23 l 1 0 0 1 -1 0 Z M 2 23 l 1 0 0 1 -1 0 Z M 3 23 l 1 0 0 1 -1 0 Z M 4 23 l 1 0 0 1 -1 0 Z M 5 23 l 1 0 0 1 -1 0 Z  M 7 23 l 1 0 0 1 -1 0 Z   M 10 23 l 1 0 0 1 -1 0 Z  M 12 23 l 1 0 0 1 -1 0 Z M 13 23 l 1 0 0 1 -1 0 Z  M 15 23 l 1 0 0 1 -1 0 Z  M 17 23 l 1 0 0 1 -1 0 Z M 18 23 l 1 0 0 1 -1 0 Z   M 21 23 l 1 0 0 1 -1 0 Z M 22 23 l 1 0 0 1 -1 0 Z M 23 23 l 1 0 0 1 -1 0 Z M 24 23 l 1 0 0 1 -1 0 Z        M 7 24 l 1 0 0 1 -1 0 Z  M 9 24 l 1 0 0 1 -1 0 Z M 10 24 l 1 0 0 1 -1 0 Z  M 12 24 l 1 0 0 1 -1 0 Z M 13 24 l 1 0 0 1 -1 0 Z M 14 24 l 1 0 0 1 -1 0 Z       M 21 24 l 1 0 0 1 -1 0 Z M 22 24 l 1 0 0 1 -1 0 Z M 23 24 l 1 0 0 1 -1 0 Z "
              fill="#FFFFFF"
            />
            <path
              d="M 0 0 l 1 0 0 1 -1 0 Z M 1 0 l 1 0 0 1 -1 0 Z M 2 0 l 1 0 0 1 -1 0 Z M 3 0 l 1 0 0 1 -1 0 Z M 4 0 l 1 0 0 1 -1 0 Z M 5 0 l 1 0 0 1 -1 0 Z M 6 0 l 1 0 0 1 -1 0 Z  M 8 0 l 1 0 0 1 -1 0 Z M 9 0 l 1 0 0 1 -1 0 Z   M 12 0 l 1 0 0 1 -1 0 Z M 13 0 l 1 0 0 1 -1 0 Z   M 16 0 l 1 0 0 1 -1 0 Z  M 18 0 l 1 0 0 1 -1 0 Z M 19 0 l 1 0 0 1 -1 0 Z M 20 0 l 1 0 0 1 -1 0 Z M 21 0 l 1 0 0 1 -1 0 Z M 22 0 l 1 0 0 1 -1 0 Z M 23 0 l 1 0 0 1 -1 0 Z M 24 0 l 1 0 0 1 -1 0 Z M 0 1 l 1 0 0 1 -1 0 Z      M 6 1 l 1 0 0 1 -1 0 Z  M 8 1 l 1 0 0 1 -1 0 Z M 9 1 l 1 0 0 1 -1 0 Z   M 12 1 l 1 0 0 1 -1 0 Z  M 14 1 l 1 0 0 1 -1 0 Z    M 18 1 l 1 0 0 1 -1 0 Z      M 24 1 l 1 0 0 1 -1 0 Z M 0 2 l 1 0 0 1 -1 0 Z  M 2 2 l 1 0 0 1 -1 0 Z M 3 2 l 1 0 0 1 -1 0 Z M 4 2 l 1 0 0 1 -1 0 Z  M 6 2 l 1 0 0 1 -1 0 Z   M 9 2 l 1 0 0 1 -1 0 Z M 10 2 l 1 0 0 1 -1 0 Z M 11 2 l 1 0 0 1 -1 0 Z M 12 2 l 1 0 0 1 -1 0 Z M 13 2 l 1 0 0 1 -1 0 Z  M 15 2 l 1 0 0 1 -1 0 Z   M 18 2 l 1 0 0 1 -1 0 Z  M 20 2 l 1 0 0 1 -1 0 Z M 21 2 l 1 0 0 1 -1 0 Z M 22 2 l 1 0 0 1 -1 0 Z  M 24 2 l 1 0 0 1 -1 0 Z M 0 3 l 1 0 0 1 -1 0 Z  M 2 3 l 1 0 0 1 -1 0 Z M 3 3 l 1 0 0 1 -1 0 Z M 4 3 l 1 0 0 1 -1 0 Z  M 6 3 l 1 0 0 1 -1 0 Z   M 9 3 l 1 0 0 1 -1 0 Z   M 12 3 l 1 0 0 1 -1 0 Z M 13 3 l 1 0 0 1 -1 0 Z   M 16 3 l 1 0 0 1 -1 0 Z  M 18 3 l 1 0 0 1 -1 0 Z  M 20 3 l 1 0 0 1 -1 0 Z M 21 3 l 1 0 0 1 -1 0 Z M 22 3 l 1 0 0 1 -1 0 Z  M 24 3 l 1 0 0 1 -1 0 Z M 0 4 l 1 0 0 1 -1 0 Z  M 2 4 l 1 0 0 1 -1 0 Z M 3 4 l 1 0 0 1 -1 0 Z M 4 4 l 1 0 0 1 -1 0 Z  M 6 4 l 1 0 0 1 -1 0 Z  M 8 4 l 1 0 0 1 -1 0 Z M 9 4 l 1 0 0 1 -1 0 Z   M 12 4 l 1 0 0 1 -1 0 Z M 13 4 l 1 0 0 1 -1 0 Z   M 16 4 l 1 0 0 1 -1 0 Z  M 18 4 l 1 0 0 1 -1 0 Z  M 20 4 l 1 0 0 1 -1 0 Z M 21 4 l 1 0 0 1 -1 0 Z M 22 4 l 1 0 0 1 -1 0 Z  M 24 4 l 1 0 0 1 -1 0 Z M 0 5 l 1 0 0 1 -1 0 Z      M 6 5 l 1 0 0 1 -1 0 Z  M 8 5 l 1 0 0 1 -1 0 Z M 9 5 l 1 0 0 1 -1 0 Z    M 13 5 l 1 0 0 1 -1 0 Z M 14 5 l 1 0 0 1 -1 0 Z  M 16 5 l 1 0 0 1 -1 0 Z  M 18 5 l 1 0 0 1 -1 0 Z      M 24 5 l 1 0 0 1 -1 0 Z M 0 6 l 1 0 0 1 -1 0 Z M 1 6 l 1 0 0 1 -1 0 Z M 2 6 l 1 0 0 1 -1 0 Z M 3 6 l 1 0 0 1 -1 0 Z M 4 6 l 1 0 0 1 -1 0 Z M 5 6 l 1 0 0 1 -1 0 Z M 6 6 l 1 0 0 1 -1 0 Z  M 8 6 l 1 0 0 1 -1 0 Z  M 10 6 l 1 0 0 1 -1 0 Z  M 12 6 l 1 0 0 1 -1 0 Z  M 14 6 l 1 0 0 1 -1 0 Z  M 16 6 l 1 0 0 1 -1 0 Z  M 18 6 l 1 0 0 1 -1 0 Z M 19 6 l 1 0 0 1 -1 0 Z M 20 6 l 1 0 0 1 -1 0 Z M 21 6 l 1 0 0 1 -1 0 Z M 22 6 l 1 0 0 1 -1 0 Z M 23 6 l 1 0 0 1 -1 0 Z M 24 6 l 1 0 0 1 -1 0 Z         M 8 7 l 1 0 0 1 -1 0 Z  M 10 7 l 1 0 0 1 -1 0 Z  M 12 7 l 1 0 0 1 -1 0 Z   M 15 7 l 1 0 0 1 -1 0 Z          M 0 8 l 1 0 0 1 -1 0 Z M 1 8 l 1 0 0 1 -1 0 Z M 2 8 l 1 0 0 1 -1 0 Z   M 5 8 l 1 0 0 1 -1 0 Z M 6 8 l 1 0 0 1 -1 0 Z  M 8 8 l 1 0 0 1 -1 0 Z M 9 8 l 1 0 0 1 -1 0 Z M 10 8 l 1 0 0 1 -1 0 Z M 11 8 l 1 0 0 1 -1 0 Z M 12 8 l 1 0 0 1 -1 0 Z  M 14 8 l 1 0 0 1 -1 0 Z M 15 8 l 1 0 0 1 -1 0 Z  M 17 8 l 1 0 0 1 -1 0 Z M 18 8 l 1 0 0 1 -1 0 Z M 19 8 l 1 0 0 1 -1 0 Z M 20 8 l 1 0 0 1 -1 0 Z   M 23 8 l 1 0 0 1 -1 0 Z M 24 8 l 1 0 0 1 -1 0 Z M 0 9 l 1 0 0 1 -1 0 Z  M 2 9 l 1 0 0 1 -1 0 Z M 3 9 l 1 0 0 1 -1 0 Z    M 7 9 l 1 0 0 1 -1 0 Z M 8 9 l 1 0 0 1 -1 0 Z M 9 9 l 1 0 0 1 -1 0 Z  M 11 9 l 1 0 0 1 -1 0 Z M 12 9 l 1 0 0 1 -1 0 Z M 13 9 l 1 0 0 1 -1 0 Z  M 15 9 l 1 0 0 1 -1 0 Z   M 18 9 l 1 0 0 1 -1 0 Z   M 21 9 l 1 0 0 1 -1 0 Z M 22 9 l 1 0 0 1 -1 0 Z    M 1 10 l 1 0 0 1 -1 0 Z  M 3 10 l 1 0 0 1 -1 0 Z M 4 10 l 1 0 0 1 -1 0 Z  M 6 10 l 1 0 0 1 -1 0 Z M 7 10 l 1 0 0 1 -1 0 Z M 8 10 l 1 0 0 1 -1 0 Z   M 11 10 l 1 0 0 1 -1 0 Z M 12 10 l 1 0 0 1 -1 0 Z M 13 10 l 1 0 0 1 -1 0 Z  M 15 10 l 1 0 0 1 -1 0 Z M 16 10 l 1 0 0 1 -1 0 Z    M 20 10 l 1 0 0 1 -1 0 Z M 21 10 l 1 0 0 1 -1 0 Z M 22 10 l 1 0 0 1 -1 0 Z  M 24 10 l 1 0 0 1 -1 0 Z  M 1 11 l 1 0 0 1 -1 0 Z  M 3 11 l 1 0 0 1 -1 0 Z M 4 11 l 1 0 0 1 -1 0 Z     M 9 11 l 1 0 0 1 -1 0 Z  M 11 11 l 1 0 0 1 -1 0 Z    M 15 11 l 1 0 0 1 -1 0 Z    M 19 11 l 1 0 0 1 -1 0 Z    M 23 11 l 1 0 0 1 -1 0 Z M 24 11 l 1 0 0 1 -1 0 Z   M 2 12 l 1 0 0 1 -1 0 Z   M 5 12 l 1 0 0 1 -1 0 Z M 6 12 l 1 0 0 1 -1 0 Z M 7 12 l 1 0 0 1 -1 0 Z M 8 12 l 1 0 0 1 -1 0 Z M 9 12 l 1 0 0 1 -1 0 Z  M 11 12 l 1 0 0 1 -1 0 Z    M 15 12 l 1 0 0 1 -1 0 Z   M 18 12 l 1 0 0 1 -1 0 Z   M 21 12 l 1 0 0 1 -1 0 Z  M 23 12 l 1 0 0 1 -1 0 Z M 24 12 l 1 0 0 1 -1 0 Z  M 1 13 l 1 0 0 1 -1 0 Z  M 3 13 l 1 0 0 1 -1 0 Z M 4 13 l 1 0 0 1 -1 0 Z    M 8 13 l 1 0 0 1 -1 0 Z M 9 13 l 1 0 0 1 -1 0 Z  M 11 13 l 1 0 0 1 -1 0 Z M 12 13 l 1 0 0 1 -1 0 Z  M 14 13 l 1 0 0 1 -1 0 Z M 15 13 l 1 0 0 1 -1 0 Z  M 17 13 l 1 0 0 1 -1 0 Z    M 21 13 l 1 0 0 1 -1 0 Z M 22 13 l 1 0 0 1 -1 0 Z M 23 13 l 1 0 0 1 -1 0 Z  M 0 14 l 1 0 0 1 -1 0 Z M 1 14 l 1 0 0 1 -1 0 Z M 2 14 l 1 0 0 1 -1 0 Z M 3 14 l 1 0 0 1 -1 0 Z M 4 14 l 1 0 0 1 -1 0 Z  M 6 14 l 1 0 0 1 -1 0 Z  M 8 14 l 1 0 0 1 -1 0 Z   M 11 14 l 1 0 0 1 -1 0 Z  M 13 14 l 1 0 0 1 -1 0 Z M 14 14 l 1 0 0 1 -1 0 Z M 15 14 l 1 0 0 1 -1 0 Z  M 17 14 l 1 0 0 1 -1 0 Z M 18 14 l 1 0 0 1 -1 0 Z M 19 14 l 1 0 0 1 -1 0 Z M 20 14 l 1 0 0 1 -1 0 Z  M 22 14 l 1 0 0 1 -1 0 Z  M 24 14 l 1 0 0 1 -1 0 Z   M 2 15 l 1 0 0 1 -1 0 Z M 3 15 l 1 0 0 1 -1 0 Z     M 8 15 l 1 0 0 1 -1 0 Z  M 10 15 l 1 0 0 1 -1 0 Z M 11 15 l 1 0 0 1 -1 0 Z M 12 15 l 1 0 0 1 -1 0 Z       M 19 15 l 1 0 0 1 -1 0 Z      M 0 16 l 1 0 0 1 -1 0 Z M 1 16 l 1 0 0 1 -1 0 Z  M 3 16 l 1 0 0 1 -1 0 Z M 4 16 l 1 0 0 1 -1 0 Z  M 6 16 l 1 0 0 1 -1 0 Z  M 8 16 l 1 0 0 1 -1 0 Z  M 10 16 l 1 0 0 1 -1 0 Z  M 12 16 l 1 0 0 1 -1 0 Z   M 15 16 l 1 0 0 1 -1 0 Z M 16 16 l 1 0 0 1 -1 0 Z M 17 16 l 1 0 0 1 -1 0 Z M 18 16 l 1 0 0 1 -1 0 Z M 19 16 l 1 0 0 1 -1 0 Z M 20 16 l 1 0 0 1 -1 0 Z   M 23 16 l 1 0 0 1 -1 0 Z M 24 16 l 1 0 0 1 -1 0 Z         M 8 17 l 1 0 0 1 -1 0 Z M 9 17 l 1 0 0 1 -1 0 Z M 10 17 l 1 0 0 1 -1 0 Z M 11 17 l 1 0 0 1 -1 0 Z M 12 17 l 1 0 0 1 -1 0 Z   M 15 17 l 1 0 0 1 -1 0 Z M 16 17 l 1 0 0 1 -1 0 Z    M 20 17 l 1 0 0 1 -1 0 Z   M 23 17 l 1 0 0 1 -1 0 Z  M 0 18 l 1 0 0 1 -1 0 Z M 1 18 l 1 0 0 1 -1 0 Z M 2 18 l 1 0 0 1 -1 0 Z M 3 18 l 1 0 0 1 -1 0 Z M 4 18 l 1 0 0 1 -1 0 Z M 5 18 l 1 0 0 1 -1 0 Z M 6 18 l 1 0 0 1 -1 0 Z    M 10 18 l 1 0 0 1 -1 0 Z M 11 18 l 1 0 0 1 -1 0 Z  M 13 18 l 1 0 0 1 -1 0 Z M 14 18 l 1 0 0 1 -1 0 Z  M 16 18 l 1 0 0 1 -1 0 Z  M 18 18 l 1 0 0 1 -1 0 Z  M 20 18 l 1 0 0 1 -1 0 Z  M 22 18 l 1 0 0 1 -1 0 Z  M 24 18 l 1 0 0 1 -1 0 Z M 0 19 l 1 0 0 1 -1 0 Z      M 6 19 l 1 0 0 1 -1 0 Z  M 8 19 l 1 0 0 1 -1 0 Z M 9 19 l 1 0 0 1 -1 0 Z M 10 19 l 1 0 0 1 -1 0 Z M 11 19 l 1 0 0 1 -1 0 Z M 12 19 l 1 0 0 1 -1 0 Z  M 14 19 l 1 0 0 1 -1 0 Z  M 16 19 l 1 0 0 1 -1 0 Z    M 20 19 l 1 0 0 1 -1 0 Z   M 23 19 l 1 0 0 1 -1 0 Z M 24 19 l 1 0 0 1 -1 0 Z M 0 20 l 1 0 0 1 -1 0 Z  M 2 20 l 1 0 0 1 -1 0 Z M 3 20 l 1 0 0 1 -1 0 Z M 4 20 l 1 0 0 1 -1 0 Z  M 6 20 l 1 0 0 1 -1 0 Z   M 9 20 l 1 0 0 1 -1 0 Z  M 11 20 l 1 0 0 1 -1 0 Z    M 15 20 l 1 0 0 1 -1 0 Z M 16 20 l 1 0 0 1 -1 0 Z M 17 20 l 1 0 0 1 -1 0 Z M 18 20 l 1 0 0 1 -1 0 Z M 19 20 l 1 0 0 1 -1 0 Z M 20 20 l 1 0 0 1 -1 0 Z     M 0 21 l 1 0 0 1 -1 0 Z  M 2 21 l 1 0 0 1 -1 0 Z M 3 21 l 1 0 0 1 -1 0 Z M 4 21 l 1 0 0 1 -1 0 Z  M 6 21 l 1 0 0 1 -1 0 Z    M 10 21 l 1 0 0 1 -1 0 Z M 11 21 l 1 0 0 1 -1 0 Z M 12 21 l 1 0 0 1 -1 0 Z  M 14 21 l 1 0 0 1 -1 0 Z M 15 21 l 1 0 0 1 -1 0 Z  M 17 21 l 1 0 0 1 -1 0 Z  M 19 21 l 1 0 0 1 -1 0 Z M 20 21 l 1 0 0 1 -1 0 Z   M 23 21 l 1 0 0 1 -1 0 Z  M 0 22 l 1 0 0 1 -1 0 Z  M 2 22 l 1 0 0 1 -1 0 Z M 3 22 l 1 0 0 1 -1 0 Z M 4 22 l 1 0 0 1 -1 0 Z  M 6 22 l 1 0 0 1 -1 0 Z  M 8 22 l 1 0 0 1 -1 0 Z  M 10 22 l 1 0 0 1 -1 0 Z M 11 22 l 1 0 0 1 -1 0 Z   M 14 22 l 1 0 0 1 -1 0 Z    M 18 22 l 1 0 0 1 -1 0 Z M 19 22 l 1 0 0 1 -1 0 Z M 20 22 l 1 0 0 1 -1 0 Z M 21 22 l 1 0 0 1 -1 0 Z  M 23 22 l 1 0 0 1 -1 0 Z M 24 22 l 1 0 0 1 -1 0 Z M 0 23 l 1 0 0 1 -1 0 Z      M 6 23 l 1 0 0 1 -1 0 Z  M 8 23 l 1 0 0 1 -1 0 Z M 9 23 l 1 0 0 1 -1 0 Z  M 11 23 l 1 0 0 1 -1 0 Z   M 14 23 l 1 0 0 1 -1 0 Z  M 16 23 l 1 0 0 1 -1 0 Z   M 19 23 l 1 0 0 1 -1 0 Z M 20 23 l 1 0 0 1 -1 0 Z     M 0 24 l 1 0 0 1 -1 0 Z M 1 24 l 1 0 0 1 -1 0 Z M 2 24 l 1 0 0 1 -1 0 Z M 3 24 l 1 0 0 1 -1 0 Z M 4 24 l 1 0 0 1 -1 0 Z M 5 24 l 1 0 0 1 -1 0 Z M 6 24 l 1 0 0 1 -1 0 Z  M 8 24 l 1 0 0 1 -1 0 Z   M 11 24 l 1 0 0 1 -1 0 Z    M 15 24 l 1 0 0 1 -1 0 Z M 16 24 l 1 0 0 1 -1 0 Z M 17 24 l 1 0 0 1 -1 0 Z M 18 24 l 1 0 0 1 -1 0 Z M 19 24 l 1 0 0 1 -1 0 Z M 20 24 l 1 0 0 1 -1 0 Z    M 24 24 l 1 0 0 1 -1 0 Z"
              fill="#000000"
            />
          </svg>
        </div>
      </section>
    </div>
    <div
      class="pb-2"
    />
    <div
      class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 print:grid-cols-3 gap-2"
    >
      <section
        class="p-3 rounded-sm bg-white"
      >
        <div
          class="flex justify-between items-baseline"
        >
          <h3
            class="text-lg font-bold pb-4"
          >
            Installation details
          </h3>
          <a
            class="text-primary underline hover:decoration-2 focus:decoration-2 focus:outline-none cursor-pointer text-right ml-2"
            href="/chargers/PSL-12345/installation?socket=A"
          >
            View installation details
          </a>
        </div>
        <div
          class="flex flex-col bg-white rounded-sm overflow-hidden overflow-x-auto"
        >
          <div
            class="inline-block min-w-full -my-2 py-2 align-middle"
          >
            <table
              class="min-w-full"
              tabindex="0"
            >
              <caption
                class="sr-only"
              >
                Installation details table
              </caption>
              <tbody
                class="divide-y"
              >
                <tr
                  class="border-b-neutral/20"
                >
                  <td
                    class="whitespace-normal px-3 py-4 pl-0 pr-2 pt-2 pb-2 whitespace-pre"
                  >
                    Install date
                  </td>
                  <td
                    class="whitespace-normal px-3 py-4 pr-0 pl-2 pt-2 pb-2 text-neutral text-right"
                  >
                    7 July 2023
                  </td>
                </tr>
                <tr
                  class="border-b-neutral/20"
                >
                  <td
                    class="whitespace-normal px-3 py-4 pl-0 pr-2 pt-2 pb-2 whitespace-pre"
                  >
                    Installed by
                  </td>
                  <td
                    class="whitespace-normal px-3 py-4 pr-0 pl-2 pt-2 pb-2 text-neutral text-right"
                  >
                    Joe Spark
                  </td>
                </tr>
                <tr
                  class="border-b-neutral/20"
                >
                  <td
                    class="whitespace-normal px-3 py-4 pl-0 pr-2 pt-2 pb-2 whitespace-pre"
                  >
                    Company
                  </td>
                  <td
                    class="whitespace-normal px-3 py-4 pr-0 pl-2 pt-2 pb-2 text-neutral text-right"
                  >
                    Electrical Services Ltd.
                  </td>
                </tr>
              </tbody>
            </table>
          </div>
        </div>
      </section>
      <section
        class="p-3 rounded-sm bg-white"
      >
        <h3
          class="text-lg font-bold pb-4"
        >
          Warranty details
        </h3>
        <div
          class="flex flex-col bg-white rounded-sm overflow-hidden overflow-x-auto"
        >
          <div
            class="inline-block min-w-full -my-2 py-2 align-middle"
          >
            <table
              class="min-w-full"
              tabindex="0"
            >
              <caption
                class="sr-only"
              >
                Warranty details table
              </caption>
              <tbody
                class="divide-y"
              >
                <tr
                  class="border-b-neutral/20"
                >
                  <td
                    class="whitespace-normal px-3 py-4 pl-0 pr-2 pt-2 pb-2 whitespace-pre"
                  >
                    Warranty start date
                  </td>
                  <td
                    class="whitespace-normal px-3 py-4 pr-0 pl-2 pt-2 pb-2 text-neutral text-right"
                  >
                    1 August 2024
                  </td>
                </tr>
                <tr
                  class="border-b-neutral/20"
                >
                  <td
                    class="whitespace-normal px-3 py-4 pl-0 pr-2 pt-2 pb-2 whitespace-pre"
                  >
                    Warranty end date
                  </td>
                  <td
                    class="whitespace-normal px-3 py-4 pr-0 pl-2 pt-2 pb-2 text-neutral text-right"
                  >
                    1st August 2027
                  </td>
                </tr>
                <tr
                  class="border-b-neutral/20"
                >
                  <td
                    class="whitespace-normal px-3 py-4 pl-0 pr-2 pt-2 pb-2 whitespace-pre"
                  >
                    Warranty status
                  </td>
                  <td
                    class="whitespace-normal px-3 py-4 pr-0 pl-2 pt-2 pb-2 text-neutral text-right"
                  >
                    <span
                      aria-label="Active"
                      class="font-bold px-2.5 py-1 rounded-xs inline-flex items-center tracking-wide bg-success/10 border border-success text-success"
                      role="status"
                    >
                      Active
                    </span>
                  </td>
                </tr>
              </tbody>
            </table>
          </div>
        </div>
      </section>
      <section
        class="p-3 rounded-sm bg-white"
      >
        <div
          class="flex justify-between items-baseline"
        >
          <h3
            class="text-lg font-bold pb-4"
          >
            Last complete charge
          </h3>
          <a
            class="text-primary underline hover:decoration-2 focus:decoration-2 focus:outline-none cursor-pointer text-right ml-2"
            href="/chargers/PSL-12345/recent-charges?socket=A"
          >
            Recent charges
          </a>
        </div>
        <div
          class="flex flex-col bg-white rounded-sm overflow-hidden overflow-x-auto"
        >
          <div
            class="inline-block min-w-full -my-2 py-2 align-middle"
          >
            <table
              class="min-w-full"
              tabindex="0"
            >
              <caption
                class="sr-only"
              >
                Last complete charge table
              </caption>
              <tbody
                class="divide-y"
              >
                <tr
                  class="border-b-neutral/20"
                >
                  <td
                    class="whitespace-normal px-3 py-4 pl-0 pr-2 pt-2 pb-2 whitespace-pre"
                  >
                    Started at
                  </td>
                  <td
                    class="whitespace-normal px-3 py-4 pr-0 pl-2 pt-2 pb-2 text-neutral text-right"
                  >
                    30 June 2024 at 19:00:00
                  </td>
                </tr>
                <tr
                  class="border-b-neutral/20"
                >
                  <td
                    class="whitespace-normal px-3 py-4 pl-0 pr-2 pt-2 pb-2 whitespace-pre"
                  >
                    Ended at
                  </td>
                  <td
                    class="whitespace-normal px-3 py-4 pr-0 pl-2 pt-2 pb-2 text-neutral text-right"
                  >
                    30 June 2024 at 21:00:00
                  </td>
                </tr>
                <tr
                  class="border-b-neutral/20"
                >
                  <td
                    class="whitespace-normal px-3 py-4 pl-0 pr-2 pt-2 pb-2 whitespace-pre"
                  >
                    kWh delivered
                  </td>
                  <td
                    class="whitespace-normal px-3 py-4 pr-0 pl-2 pt-2 pb-2 text-neutral text-right"
                  >
                    567.89 kWh
                  </td>
                </tr>
              </tbody>
            </table>
          </div>
        </div>
      </section>
    </div>
    <div
      class="pb-2"
    />
    <section
      class="p-3 rounded-sm bg-white"
    >
      <div
        class="flex justify-between items-baseline"
      >
        <h3
          class="text-lg font-bold pb-4"
        >
          Modes
        </h3>
        <a
          class="text-primary underline hover:decoration-2 focus:decoration-2 focus:outline-none cursor-pointer text-right ml-2"
          href="/chargers/PSL-12345/mode-history"
        >
          View history
        </a>
      </div>
      <div
        class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 print:grid-cols-3 gap-2"
      >
        <div
          class="pb-2 break-words"
        >
          <div
            class="flex"
          >
            <div
              data-headlessui-state=""
            >
              <div
                class="mr-2"
              >
                <label
                  class="text-lg font-normal block mb-1.5"
                  for="toggle-smart-mode"
                  id="toggle-smart-mode-label"
                >
                  Smart mode
                </label>
              </div>
              <button
                aria-checked="true"
                aria-label="toggle-smart-mode"
                class="bg-primary group relative inline-flex h-6 w-11 shrink-0 cursor-pointer rounded-full border-2 border-transparent transition-colors duration-200 ease-in-out focus:outline-hidden focus:ring-2 focus:ring-primary/50 focus:ring-offset-2 data-[disabled]:bg-neutral/40 data-[disabled]:cursor-not-allowed"
                data-checked=""
                data-headlessui-state="checked"
                id="toggle-smart-mode"
                role="switch"
                tabindex="0"
                type="button"
              >
                <span
                  aria-hidden="true"
                  class="translate-x-5 pointer-events-none inline-block h-5 w-5 transform rounded-full bg-white shadow-sm ring-0 transition duration-200 ease-in-out group-data-[disabled]:bg-neutral/20 group-data-[disabled]:cursor-default"
                />
              </button>
              <span
                hidden=""
                style="position: fixed; top: 1px; left: 1px; width: 1px; height: 0px; padding: 0px; margin: -1px; overflow: hidden; clip: rect(0px, 0px, 0px, 0px); white-space: nowrap; border-width: 0px; display: none;"
              />
            </div>
          </div>
        </div>
        <div
          class="pb-2 break-words"
        >
          <div
            class="flex"
          >
            <div
              data-headlessui-state=""
            >
              <div
                class="mr-2"
              >
                <label
                  class="text-lg font-normal block mb-1.5"
                  for="toggle-off-mode"
                  id="toggle-off-mode-label"
                >
                  Off mode
                </label>
              </div>
              <button
                aria-checked="false"
                aria-label="toggle-off-mode"
                class="bg-neutral/20 group relative inline-flex h-6 w-11 shrink-0 cursor-pointer rounded-full border-2 border-transparent transition-colors duration-200 ease-in-out focus:outline-hidden focus:ring-2 focus:ring-primary/50 focus:ring-offset-2 data-[disabled]:bg-neutral/40 data-[disabled]:cursor-not-allowed"
                data-headlessui-state=""
                id="toggle-off-mode"
                role="switch"
                tabindex="0"
                type="button"
              >
                <span
                  aria-hidden="true"
                  class="translate-x-0 pointer-events-none inline-block h-5 w-5 transform rounded-full bg-white shadow-sm ring-0 transition duration-200 ease-in-out group-data-[disabled]:bg-neutral/20 group-data-[disabled]:cursor-default"
                />
              </button>
              <span
                hidden=""
                style="position: fixed; top: 1px; left: 1px; width: 1px; height: 0px; padding: 0px; margin: -1px; overflow: hidden; clip: rect(0px, 0px, 0px, 0px); white-space: nowrap; border-width: 0px; display: none;"
              />
            </div>
          </div>
        </div>
        <div
          class="pb-2 break-words"
        >
          <div
            class="flex"
          >
            <div
              data-headlessui-state=""
            >
              <div
                class="mr-2"
              >
                <label
                  class="text-lg font-normal block mb-1.5"
                  for="toggle-charge-now"
                  id="toggle-charge-now-label"
                >
                  Charge now
                </label>
              </div>
              <button
                aria-checked="false"
                aria-label="toggle-charge-now"
                class="bg-neutral/20 group relative inline-flex h-6 w-11 shrink-0 cursor-pointer rounded-full border-2 border-transparent transition-colors duration-200 ease-in-out focus:outline-hidden focus:ring-2 focus:ring-primary/50 focus:ring-offset-2 data-[disabled]:bg-neutral/40 data-[disabled]:cursor-not-allowed"
                data-headlessui-state=""
                id="toggle-charge-now"
                role="switch"
                tabindex="0"
                type="button"
              >
                <span
                  aria-hidden="true"
                  class="translate-x-0 pointer-events-none inline-block h-5 w-5 transform rounded-full bg-white shadow-sm ring-0 transition duration-200 ease-in-out group-data-[disabled]:bg-neutral/20 group-data-[disabled]:cursor-default"
                />
              </button>
              <span
                hidden=""
                style="position: fixed; top: 1px; left: 1px; width: 1px; height: 0px; padding: 0px; margin: -1px; overflow: hidden; clip: rect(0px, 0px, 0px, 0px); white-space: nowrap; border-width: 0px; display: none;"
              />
            </div>
          </div>
        </div>
        <div
          class="pb-2 break-words"
        >
          <h5
            class="text-lg"
          >
            Flex
          </h5>
          <span
            class="text-xl font-semibold"
          >
            <a
              class="text-primary underline hover:decoration-2 focus:decoration-2 focus:outline-none text-xl font-semibold hover:underline cursor-pointer"
              href="/chargers/PSL-12345/flex-details"
            >
              Enrolled
            </a>
          </span>
        </div>
        <div
          class="flex"
        >
          <div
            class="pb-2 break-words"
          >
            <h5
              class="text-lg"
            >
              Delegated control
            </h5>
            <div
              class="flex justify-between items-center"
            >
              <span
                class="text-xl font-semibold"
              >
                Active since 30 June 2024 at 21:00:00 (3rd Party)
              </span>
              <button
                aria-label="Remove delegated control"
                class="border-2 border-solid rounded-sm font-bold text-center disabled:no-underline transition ease-in-out duration-200 cursor-pointer disabled:cursor-not-allowed border-transparent text-primary hover:underline hover:text-primary active:font-bold active:text-primary disabled:text-neutral/40 disabled:hover:no-underline disabled:hover:border-transparent p-1.5"
                type="button"
              >
                <svg
                  class="h-4 w-4 stroke-1 stroke-current fill-current"
                  viewBox="0 0 32 32"
                  xmlns="http://www.w3.org/2000/svg"
                >
                  <title>
                    Cross
                  </title>
                  <g>
                    <path
                      d="M16,.07C7.22,.07,.07,7.22,.07,16s7.15,15.93,15.93,15.93,15.93-7.15,15.93-15.93S24.78,.07,16,.07Zm0,29.86c-7.68,0-13.93-6.25-13.93-13.93S8.32,2.07,16,2.07s13.93,6.25,13.93,13.93-6.25,13.93-13.93,13.93Z"
                    />
                    <path
                      d="M23.38,8.62c-.39-.39-1.02-.39-1.41,0l-5.96,5.96-5.96-5.96c-.39-.39-1.02-.39-1.41,0s-.39,1.02,0,1.41l5.96,5.96-5.96,5.96c-.39,.39-.39,1.02,0,1.41,.2,.2,.45,.29,.71,.29s.51-.1,.71-.29l5.96-5.96,5.96,5.96c.2,.2,.45,.29,.71,.29s.51-.1,.71-.29c.39-.39,.39-1.02,0-1.41l-5.96-5.96,5.96-5.96c.39-.39,.39-1.02,0-1.41Z"
                    />
                  </g>
                </svg>
              </button>
            </div>
          </div>
        </div>
      </div>
    </section>
    <div
      class="pb-2"
    />
    <div
      class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-2 gap-2"
    >
      <section
        class="p-3 rounded-sm bg-white"
      >
        <div
          class="flex justify-between items-baseline"
        >
          <h3
            class="text-lg font-bold pb-4"
          >
            Tariffs
          </h3>
        </div>
        <div
          class="flex flex-col bg-white rounded-sm overflow-hidden overflow-x-auto"
        >
          <div
            class="inline-block min-w-full -my-2 py-2 align-middle"
          >
            <table
              class="min-w-full"
              tabindex="0"
            >
              <caption
                class="sr-only"
              >
                Tariffs
              </caption>
              <tbody
                class="divide-y"
              >
                <tr
                  class="border-b-neutral/20"
                >
                  <td
                    class="whitespace-normal px-3 py-4 pl-0 pr-2 pt-2 pb-2 whitespace-pre"
                  >
                    Energy provider
                  </td>
                  <td
                    class="whitespace-normal px-3 py-4 pr-0 pl-2 pt-2 pb-2 text-neutral text-right"
                  >
                    -
                  </td>
                </tr>
                <tr
                  class="border-b-neutral/20"
                >
                  <td
                    class="whitespace-normal px-3 py-4 pl-0 pr-2 pt-2 pb-2 whitespace-pre"
                  >
                    Peak rate
                  </td>
                  <td
                    class="whitespace-normal px-3 py-4 pr-0 pl-2 pt-2 pb-2 text-neutral text-right"
                  >
                    <div
                      class="flex flex-col"
                    >
                      <span>
                        All day
                      </span>
                    </div>
                  </td>
                </tr>
                <tr
                  class="border-b-neutral/20"
                >
                  <td
                    class="whitespace-normal px-3 py-4 pl-0 pr-2 pt-2 pb-2 whitespace-pre"
                  >
                    Peak rate
                  </td>
                  <td
                    class="whitespace-normal px-3 py-4 pr-0 pl-2 pt-2 pb-2 text-neutral text-right"
                  >
                    <div
                      class="flex flex-col"
                    >
                      <span>
                        All day
                      </span>
                    </div>
                  </td>
                </tr>
                <tr
                  class="border-b-neutral/20"
                >
                  <td
                    class="whitespace-normal px-3 py-4 pl-0 pr-2 pt-2 pb-2 whitespace-pre"
                  >
                    Peak rate
                  </td>
                  <td
                    class="whitespace-normal px-3 py-4 pr-0 pl-2 pt-2 pb-2 text-neutral text-right"
                  >
                    <div
                      class="flex flex-col"
                    >
                      <span>
                        All day
                      </span>
                    </div>
                  </td>
                </tr>
                <tr
                  class="border-b-neutral/20"
                >
                  <td
                    class="whitespace-normal px-3 py-4 pl-0 pr-2 pt-2 pb-2 whitespace-pre"
                  >
                    Peak rate
                  </td>
                  <td
                    class="whitespace-normal px-3 py-4 pr-0 pl-2 pt-2 pb-2 text-neutral text-right"
                  >
                    <div
                      class="flex flex-col"
                    >
                      <span>
                        All day
                      </span>
                    </div>
                  </td>
                </tr>
                <tr
                  class="border-b-neutral/20"
                >
                  <td
                    class="whitespace-normal px-3 py-4 pl-0 pr-2 pt-2 pb-2 whitespace-pre"
                  >
                    Peak rate
                  </td>
                  <td
                    class="whitespace-normal px-3 py-4 pr-0 pl-2 pt-2 pb-2 text-neutral text-right"
                  >
                    <div
                      class="flex flex-col"
                    >
                      <span>
                        All day
                      </span>
                    </div>
                  </td>
                </tr>
                <tr
                  class="border-b-neutral/20"
                >
                  <td
                    class="whitespace-normal px-3 py-4 pl-0 pr-2 pt-2 pb-2 whitespace-pre"
                  >
                    Peak rate
                  </td>
                  <td
                    class="whitespace-normal px-3 py-4 pr-0 pl-2 pt-2 pb-2 text-neutral text-right"
                  >
                    <div
                      class="flex flex-col"
                    >
                      <span>
                        All day
                      </span>
                    </div>
                  </td>
                </tr>
                <tr
                  class="border-b-neutral/20"
                >
                  <td
                    class="whitespace-normal px-3 py-4 pl-0 pr-2 pt-2 pb-2 whitespace-pre"
                  >
                    Peak rate
                  </td>
                  <td
                    class="whitespace-normal px-3 py-4 pr-0 pl-2 pt-2 pb-2 text-neutral text-right"
                  >
                    <div
                      class="flex flex-col"
                    >
                      <span>
                        All day
                      </span>
                    </div>
                  </td>
                </tr>
                <tr
                  class="border-b-neutral/20"
                >
                  <td
                    class="whitespace-normal px-3 py-4 pl-0 pr-2 pt-2 pb-2 whitespace-pre"
                  >
                    Peak rate
                  </td>
                  <td
                    class="whitespace-normal px-3 py-4 pr-0 pl-2 pt-2 pb-2 text-neutral text-right"
                  >
                    <div
                      class="flex flex-col"
                    >
                      <span>
                        All day
                      </span>
                    </div>
                  </td>
                </tr>
                <tr
                  class="border-b-neutral/20"
                >
                  <td
                    class="whitespace-normal px-3 py-4 pl-0 pr-2 pt-2 pb-2 whitespace-pre"
                  >
                    Peak rate
                  </td>
                  <td
                    class="whitespace-normal px-3 py-4 pr-0 pl-2 pt-2 pb-2 text-neutral text-right"
                  >
                    <div
                      class="flex flex-col"
                    >
                      <span>
                        All day
                      </span>
                    </div>
                  </td>
                </tr>
                <tr
                  class="border-b-neutral/20"
                >
                  <td
                    class="whitespace-normal px-3 py-4 pl-0 pr-2 pt-2 pb-2 whitespace-pre"
                  >
                    Peak rate
                  </td>
                  <td
                    class="whitespace-normal px-3 py-4 pr-0 pl-2 pt-2 pb-2 text-neutral text-right"
                  >
                    <div
                      class="flex flex-col"
                    >
                      <span>
                        All day
                      </span>
                    </div>
                  </td>
                </tr>
                <tr
                  class="border-b-neutral/20"
                >
                  <td
                    class="whitespace-normal px-3 py-4 pl-0 pr-2 pt-2 pb-2 whitespace-pre"
                  >
                    Peak rate
                  </td>
                  <td
                    class="whitespace-normal px-3 py-4 pr-0 pl-2 pt-2 pb-2 text-neutral text-right"
                  >
                    <div
                      class="flex flex-col"
                    >
                      <span>
                        All day
                      </span>
                    </div>
                  </td>
                </tr>
                <tr
                  class="border-b-neutral/20"
                >
                  <td
                    class="whitespace-normal px-3 py-4 pl-0 pr-2 pt-2 pb-2 whitespace-pre"
                  >
                    Peak rate
                  </td>
                  <td
                    class="whitespace-normal px-3 py-4 pr-0 pl-2 pt-2 pb-2 text-neutral text-right"
                  >
                    <div
                      class="flex flex-col"
                    >
                      <span>
                        All day
                      </span>
                    </div>
                  </td>
                </tr>
                <tr
                  class="border-b-neutral/20"
                >
                  <td
                    class="whitespace-normal px-3 py-4 pl-0 pr-2 pt-2 pb-2 whitespace-pre"
                  >
                    Peak rate
                  </td>
                  <td
                    class="whitespace-normal px-3 py-4 pr-0 pl-2 pt-2 pb-2 text-neutral text-right"
                  >
                    <div
                      class="flex flex-col"
                    >
                      <span>
                        All day
                      </span>
                    </div>
                  </td>
                </tr>
                <tr
                  class="border-b-neutral/20"
                >
                  <td
                    class="whitespace-normal px-3 py-4 pl-0 pr-2 pt-2 pb-2 whitespace-pre"
                  >
                    Peak rate
                  </td>
                  <td
                    class="whitespace-normal px-3 py-4 pr-0 pl-2 pt-2 pb-2 text-neutral text-right"
                  >
                    <div
                      class="flex flex-col"
                    >
                      <span>
                        All day
                      </span>
                    </div>
                  </td>
                </tr>
                <tr
                  class="border-b-neutral/20"
                >
                  <td
                    class="whitespace-normal px-3 py-4 pl-0 pr-2 pt-2 pb-2 whitespace-pre"
                  >
                    Peak rate
                  </td>
                  <td
                    class="whitespace-normal px-3 py-4 pr-0 pl-2 pt-2 pb-2 text-neutral text-right"
                  >
                    <div
                      class="flex flex-col"
                    >
                      <span>
                        All day
                      </span>
                    </div>
                  </td>
                </tr>
                <tr
                  class="border-b-neutral/20"
                >
                  <td
                    class="whitespace-normal px-3 py-4 pl-0 pr-2 pt-2 pb-2 whitespace-pre"
                  >
                    Peak rate
                  </td>
                  <td
                    class="whitespace-normal px-3 py-4 pr-0 pl-2 pt-2 pb-2 text-neutral text-right"
                  >
                    <div
                      class="flex flex-col"
                    >
                      <span>
                        All day
                      </span>
                    </div>
                  </td>
                </tr>
                <tr
                  class="border-b-neutral/20"
                >
                  <td
                    class="whitespace-normal px-3 py-4 pl-0 pr-2 pt-2 pb-2 whitespace-pre"
                  >
                    Effective from
                  </td>
                  <td
                    class="whitespace-normal px-3 py-4 pr-0 pl-2 pt-2 pb-2 text-neutral text-right"
                  >
                    1 January 2021
                  </td>
                </tr>
              </tbody>
            </table>
          </div>
        </div>
      </section>
      <section
        class="p-3 rounded-sm bg-white"
      >
        <div
          class="flex justify-between items-baseline"
        >
          <h3
            class="text-lg font-bold pb-4"
          >
            Vehicle
          </h3>
          <a
            class="text-primary underline hover:decoration-2 focus:decoration-2 focus:outline-none cursor-pointer text-right ml-2"
            href="/chargers/PSL-12345/vehicles"
          >
            View vehicles
          </a>
        </div>
        <div
          class="flex flex-col bg-white rounded-sm overflow-hidden overflow-x-auto"
        >
          <div
            class="inline-block min-w-full -my-2 py-2 align-middle"
          >
            <table
              class="min-w-full"
              tabindex="0"
            >
              <caption
                class="sr-only"
              >
                Vehicle
              </caption>
              <tbody
                class="divide-y"
              >
                <tr
                  class="border-b-neutral/20"
                >
                  <td
                    class="whitespace-normal px-3 py-4 pl-0 pr-2 pt-2 pb-2 whitespace-pre"
                  >
                    Make and model
                  </td>
                  <td
                    class="whitespace-normal px-3 py-4 pr-0 pl-2 pt-2 pb-2 text-neutral text-right"
                  >
                    Polestar 2 (Long range)
                  </td>
                </tr>
                <tr
                  class="border-b-neutral/20"
                >
                  <td
                    class="whitespace-normal px-3 py-4 pl-0 pr-2 pt-2 pb-2 whitespace-pre"
                  >
                    eNode connected
                  </td>
                  <td
                    class="whitespace-normal px-3 py-4 pr-0 pl-2 pt-2 pb-2 text-neutral text-right"
                  >
                    Yes
                  </td>
                </tr>
                <tr
                  class="border-b-neutral/20"
                >
                  <td
                    class="whitespace-normal px-3 py-4 pl-0 pr-2 pt-2 pb-2 whitespace-pre"
                  >
                    Plugged in
                  </td>
                  <td
                    class="whitespace-normal px-3 py-4 pr-0 pl-2 pt-2 pb-2 text-neutral text-right"
                  >
                    Yes
                  </td>
                </tr>
                <tr
                  class="border-b-neutral/20"
                >
                  <td
                    class="whitespace-normal px-3 py-4 pl-0 pr-2 pt-2 pb-2 whitespace-pre"
                  >
                    Current battery level
                  </td>
                  <td
                    class="whitespace-normal px-3 py-4 pr-0 pl-2 pt-2 pb-2 text-neutral text-right"
                  >
                    78%
                  </td>
                </tr>
                <tr
                  class="border-b-neutral/20"
                >
                  <td
                    class="whitespace-normal px-3 py-4 pl-0 pr-2 pt-2 pb-2 whitespace-pre"
                  >
                    Charge limit
                  </td>
                  <td
                    class="whitespace-normal px-3 py-4 pr-0 pl-2 pt-2 pb-2 text-neutral text-right"
                  >
                    78%
                  </td>
                </tr>
              </tbody>
            </table>
          </div>
        </div>
      </section>
    </div>
    <div
      class="pb-2"
    />
    <section
      class="p-3 rounded-sm bg-white"
    >
      <div
        class="flex justify-between items-baseline"
      >
        <h3
          class="text-lg font-bold pb-4"
        >
          Schedules
        </h3>
        <a
          class="text-primary underline hover:decoration-2 focus:decoration-2 focus:outline-none cursor-pointer text-right ml-2"
          href="/chargers/PSL-12345/schedules"
        >
          View schedules
        </a>
      </div>
      <div
        class="rounded-md p-3 bg-info/10 border border-info"
      >
        <div
          class="flex"
        >
          <div
            class="shrink-0"
          >
            <span
              class="text-info"
            >
              <svg
                class="fill-current w-6 h-6"
                viewBox="0 0 32 32"
                xmlns="http://www.w3.org/2000/svg"
              >
                <g>
                  <path
                    d="M16.1,29.43A13.43,13.43,0,1,1,29.53,16,13.45,13.45,0,0,1,16.1,29.43Zm0-24.86A11.43,11.43,0,1,0,27.53,16,11.45,11.45,0,0,0,16.1,4.57Z"
                  />
                  <path
                    d="M16,24.12a1,1,0,0,1-1-1V14.94a1,1,0,1,1,2,0v8.18A1,1,0,0,1,16,24.12Z"
                  />
                  <path
                    d="M16.1,10.89a1,1,0,0,1-1-1v-.2a1,1,0,0,1,2,0v.2A1,1,0,0,1,16.1,10.89Z"
                  />
                </g>
              </svg>
            </span>
          </div>
          <div
            class="flex flex-col justify-center ml-4"
          >
            <p
              class="text-md font-normal sr-only break-words"
            >
              Alert
            </p>
            <div
              class="text-info"
            >
              <p
                class="text-md font-normal break-words"
              >
                This charger is currently under delegated control. The below schedules are indicative only and subject to change.
              </p>
            </div>
          </div>
        </div>
      </div>
      <div
        class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-2 gap-2"
      >
        <div
          class="flex flex-col bg-white rounded-sm overflow-hidden overflow-x-auto"
        >
          <div
            class="inline-block min-w-full -my-2 py-2 align-middle"
          >
            <table
              class="min-w-full"
              tabindex="0"
            >
              <caption
                class="sr-only"
              >
                Table of charge schedules and modes
              </caption>
              <thead
                class="border-b border-b-neutral/20"
              >
                <tr
                  class="border-b-neutral/20"
                >
                  <th
                    class="text-left p-3"
                    scope="col"
                  >
                    Start
                  </th>
                  <th
                    class="text-left p-3"
                    scope="col"
                  >
                    Duration
                  </th>
                  <th
                    class="text-left text-center p-3"
                    scope="col"
                  >
                    Active
                  </th>
                </tr>
              </thead>
              <tbody
                class="divide-y border-b border-b-neutral/20"
              >
                <tr
                  class="border-b-neutral/20"
                >
                  <td
                    class="whitespace-normal px-3 py-4"
                  >
                    <p
                      class="text-md font-normal break-words"
                    >
                      Monday
                    </p>
                    <p
                      class="text-md font-normal break-words"
                    >
                      10:00:00
                    </p>
                  </td>
                  <td
                    class="whitespace-normal px-3 py-4"
                  >
                    <p
                      class="text-md font-normal break-words"
                    >
                      4 hours 
                    </p>
                  </td>
                  <td
                    class="whitespace-normal px-3 py-4"
                  >
                    <svg
                      class="fill-current h-6 w-6 mx-auto text-success"
                      viewBox="0 0 32 32"
                      xmlns="http://www.w3.org/2000/svg"
                    >
                      <title>
                        Yes
                      </title>
                      <g>
                        <path
                          d="M16,31.85C7.26,31.85,.15,24.74,.15,16S7.26,.15,16,.15s15.85,7.11,15.85,15.85-7.11,15.85-15.85,15.85Zm0-29.7c-7.64,0-13.85,6.21-13.85,13.85s6.21,13.85,13.85,13.85,13.85-6.21,13.85-13.85S23.64,2.15,16,2.15Z"
                        />
                        <path
                          d="M13.12,22.93c-.27,0-.52-.11-.71-.29l-5.16-5.16c-.39-.39-.39-1.02,0-1.41s1.02-.39,1.41,0l4.45,4.45,9.77-9.77c.39-.39,1.02-.39,1.41,0s.39,1.02,0,1.41l-10.47,10.47c-.19,.19-.44,.29-.71,.29Z"
                        />
                      </g>
                    </svg>
                  </td>
                </tr>
                <tr
                  class="border-b-neutral/20"
                >
                  <td
                    class="whitespace-normal px-3 py-4"
                  >
                    <p
                      class="text-md font-normal break-words"
                    >
                      Tuesday
                    </p>
                    <p
                      class="text-md font-normal break-words"
                    >
                      20:35:00
                    </p>
                  </td>
                  <td
                    class="whitespace-normal px-3 py-4"
                  >
                    <p
                      class="text-md font-normal break-words"
                    >
                      3 hours 15 minutes
                    </p>
                  </td>
                  <td
                    class="whitespace-normal px-3 py-4"
                  >
                    <svg
                      class="fill-current h-6 w-6 mx-auto text-success"
                      viewBox="0 0 32 32"
                      xmlns="http://www.w3.org/2000/svg"
                    >
                      <title>
                        Yes
                      </title>
                      <g>
                        <path
                          d="M16,31.85C7.26,31.85,.15,24.74,.15,16S7.26,.15,16,.15s15.85,7.11,15.85,15.85-7.11,15.85-15.85,15.85Zm0-29.7c-7.64,0-13.85,6.21-13.85,13.85s6.21,13.85,13.85,13.85,13.85-6.21,13.85-13.85S23.64,2.15,16,2.15Z"
                        />
                        <path
                          d="M13.12,22.93c-.27,0-.52-.11-.71-.29l-5.16-5.16c-.39-.39-.39-1.02,0-1.41s1.02-.39,1.41,0l4.45,4.45,9.77-9.77c.39-.39,1.02-.39,1.41,0s.39,1.02,0,1.41l-10.47,10.47c-.19,.19-.44,.29-.71,.29Z"
                        />
                      </g>
                    </svg>
                  </td>
                </tr>
                <tr
                  class="border-b-neutral/20"
                >
                  <td
                    class="whitespace-normal px-3 py-4"
                  >
                    <p
                      class="text-md font-normal break-words"
                    >
                      Wednesday
                    </p>
                    <p
                      class="text-md font-normal break-words"
                    >
                      00:00:00
                    </p>
                  </td>
                  <td
                    class="whitespace-normal px-3 py-4"
                  >
                    <p
                      class="text-md font-normal break-words"
                    >
                      5 hours 
                    </p>
                  </td>
                  <td
                    class="whitespace-normal px-3 py-4"
                  >
                    <svg
                      class="fill-current h-6 w-6 mx-auto text-success"
                      viewBox="0 0 32 32"
                      xmlns="http://www.w3.org/2000/svg"
                    >
                      <title>
                        Yes
                      </title>
                      <g>
                        <path
                          d="M16,31.85C7.26,31.85,.15,24.74,.15,16S7.26,.15,16,.15s15.85,7.11,15.85,15.85-7.11,15.85-15.85,15.85Zm0-29.7c-7.64,0-13.85,6.21-13.85,13.85s6.21,13.85,13.85,13.85,13.85-6.21,13.85-13.85S23.64,2.15,16,2.15Z"
                        />
                        <path
                          d="M13.12,22.93c-.27,0-.52-.11-.71-.29l-5.16-5.16c-.39-.39-.39-1.02,0-1.41s1.02-.39,1.41,0l4.45,4.45,9.77-9.77c.39-.39,1.02-.39,1.41,0s.39,1.02,0,1.41l-10.47,10.47c-.19,.19-.44,.29-.71,.29Z"
                        />
                      </g>
                    </svg>
                  </td>
                </tr>
                <tr
                  class="border-b-neutral/20"
                >
                  <td
                    class="whitespace-normal px-3 py-4"
                  >
                    <p
                      class="text-md font-normal break-words"
                    >
                      Thursday
                    </p>
                    <p
                      class="text-md font-normal break-words"
                    >
                      16:10:00
                    </p>
                  </td>
                  <td
                    class="whitespace-normal px-3 py-4"
                  >
                    <p
                      class="text-md font-normal break-words"
                    >
                      1 hour 
                    </p>
                  </td>
                  <td
                    class="whitespace-normal px-3 py-4"
                  >
                    <svg
                      class="fill-current h-6 w-6 mx-auto text-success"
                      viewBox="0 0 32 32"
                      xmlns="http://www.w3.org/2000/svg"
                    >
                      <title>
                        Yes
                      </title>
                      <g>
                        <path
                          d="M16,31.85C7.26,31.85,.15,24.74,.15,16S7.26,.15,16,.15s15.85,7.11,15.85,15.85-7.11,15.85-15.85,15.85Zm0-29.7c-7.64,0-13.85,6.21-13.85,13.85s6.21,13.85,13.85,13.85,13.85-6.21,13.85-13.85S23.64,2.15,16,2.15Z"
                        />
                        <path
                          d="M13.12,22.93c-.27,0-.52-.11-.71-.29l-5.16-5.16c-.39-.39-.39-1.02,0-1.41s1.02-.39,1.41,0l4.45,4.45,9.77-9.77c.39-.39,1.02-.39,1.41,0s.39,1.02,0,1.41l-10.47,10.47c-.19,.19-.44,.29-.71,.29Z"
                        />
                      </g>
                    </svg>
                  </td>
                </tr>
                <tr
                  class="border-b-neutral/20"
                >
                  <td
                    class="whitespace-normal px-3 py-4"
                  >
                    <p
                      class="text-md font-normal break-words"
                    >
                      Friday
                    </p>
                    <p
                      class="text-md font-normal break-words"
                    >
                      00:00:00
                    </p>
                  </td>
                  <td
                    class="whitespace-normal px-3 py-4"
                  >
                    <p
                      class="text-md font-normal break-words"
                    >
                      5 hours 
                    </p>
                  </td>
                  <td
                    class="whitespace-normal px-3 py-4"
                  >
                    <svg
                      class="fill-current h-6 w-6 mx-auto text-success"
                      viewBox="0 0 32 32"
                      xmlns="http://www.w3.org/2000/svg"
                    >
                      <title>
                        Yes
                      </title>
                      <g>
                        <path
                          d="M16,31.85C7.26,31.85,.15,24.74,.15,16S7.26,.15,16,.15s15.85,7.11,15.85,15.85-7.11,15.85-15.85,15.85Zm0-29.7c-7.64,0-13.85,6.21-13.85,13.85s6.21,13.85,13.85,13.85,13.85-6.21,13.85-13.85S23.64,2.15,16,2.15Z"
                        />
                        <path
                          d="M13.12,22.93c-.27,0-.52-.11-.71-.29l-5.16-5.16c-.39-.39-.39-1.02,0-1.41s1.02-.39,1.41,0l4.45,4.45,9.77-9.77c.39-.39,1.02-.39,1.41,0s.39,1.02,0,1.41l-10.47,10.47c-.19,.19-.44,.29-.71,.29Z"
                        />
                      </g>
                    </svg>
                  </td>
                </tr>
                <tr
                  class="border-b-neutral/20"
                >
                  <td
                    class="whitespace-normal px-3 py-4"
                  >
                    <p
                      class="text-md font-normal break-words"
                    >
                      Saturday
                    </p>
                    <p
                      class="text-md font-normal break-words"
                    >
                      00:00:00
                    </p>
                  </td>
                  <td
                    class="whitespace-normal px-3 py-4"
                  >
                    <p
                      class="text-md font-normal break-words"
                    >
                      5 hours 
                    </p>
                  </td>
                  <td
                    class="whitespace-normal px-3 py-4"
                  >
                    <svg
                      class="fill-current h-6 w-6 mx-auto text-error"
                      viewBox="0 0 32 32"
                      xmlns="http://www.w3.org/2000/svg"
                    >
                      <title>
                        No
                      </title>
                      <g>
                        <path
                          d="M16,.07C7.22,.07,.07,7.22,.07,16s7.15,15.93,15.93,15.93,15.93-7.15,15.93-15.93S24.78,.07,16,.07Zm0,29.86c-7.68,0-13.93-6.25-13.93-13.93S8.32,2.07,16,2.07s13.93,6.25,13.93,13.93-6.25,13.93-13.93,13.93Z"
                        />
                        <path
                          d="M23.38,8.62c-.39-.39-1.02-.39-1.41,0l-5.96,5.96-5.96-5.96c-.39-.39-1.02-.39-1.41,0s-.39,1.02,0,1.41l5.96,5.96-5.96,5.96c-.39,.39-.39,1.02,0,1.41,.2,.2,.45,.29,.71,.29s.51-.1,.71-.29l5.96-5.96,5.96,5.96c.2,.2,.45,.29,.71,.29s.51-.1,.71-.29c.39-.39,.39-1.02,0-1.41l-5.96-5.96,5.96-5.96c.39-.39,.39-1.02,0-1.41Z"
                        />
                      </g>
                    </svg>
                  </td>
                </tr>
                <tr
                  class="border-b-neutral/20"
                >
                  <td
                    class="whitespace-normal px-3 py-4"
                  >
                    <p
                      class="text-md font-normal break-words"
                    >
                      Sunday
                    </p>
                    <p
                      class="text-md font-normal break-words"
                    >
                      00:00:00
                    </p>
                  </td>
                  <td
                    class="whitespace-normal px-3 py-4"
                  >
                    <p
                      class="text-md font-normal break-words"
                    >
                      5 hours 
                    </p>
                  </td>
                  <td
                    class="whitespace-normal px-3 py-4"
                  >
                    <svg
                      class="fill-current h-6 w-6 mx-auto text-error"
                      viewBox="0 0 32 32"
                      xmlns="http://www.w3.org/2000/svg"
                    >
                      <title>
                        No
                      </title>
                      <g>
                        <path
                          d="M16,.07C7.22,.07,.07,7.22,.07,16s7.15,15.93,15.93,15.93,15.93-7.15,15.93-15.93S24.78,.07,16,.07Zm0,29.86c-7.68,0-13.93-6.25-13.93-13.93S8.32,2.07,16,2.07s13.93,6.25,13.93,13.93-6.25,13.93-13.93,13.93Z"
                        />
                        <path
                          d="M23.38,8.62c-.39-.39-1.02-.39-1.41,0l-5.96,5.96-5.96-5.96c-.39-.39-1.02-.39-1.41,0s-.39,1.02,0,1.41l5.96,5.96-5.96,5.96c-.39,.39-.39,1.02,0,1.41,.2,.2,.45,.29,.71,.29s.51-.1,.71-.29l5.96-5.96,5.96,5.96c.2,.2,.45,.29,.71,.29s.51-.1,.71-.29c.39-.39,.39-1.02,0-1.41l-5.96-5.96,5.96-5.96c.39-.39,.39-1.02,0-1.41Z"
                        />
                      </g>
                    </svg>
                  </td>
                </tr>
              </tbody>
            </table>
          </div>
        </div>
        <div
          class="recharts-responsive-container"
          style="width: 100%; height: 320px; min-width: 0;"
        >
          <div
            style="width: 0px; height: 0px; overflow: visible;"
          />
        </div>
      </div>
    </section>
    <div
      class="pb-2"
    />
    <section
      class="p-3 rounded-sm bg-white"
    >
      <h3
        class="text-lg font-bold pb-4"
      >
        Other configuration values
      </h3>
      <div
        class="flex flex-col bg-white rounded-sm overflow-hidden overflow-x-auto"
      >
        <div
          class="inline-block min-w-full -my-2 py-2 align-middle"
        >
          <table
            class="min-w-full"
            tabindex="0"
          >
            <caption
              class="sr-only"
            >
              Table of other configuration values
            </caption>
            <tbody
              class="divide-y"
            >
              <tr
                class="border-b-neutral/20"
              >
                <td
                  class="whitespace-normal px-3 py-4 pl-0 pr-2 pt-2 pb-2 whitespace-pre"
                >
                  ChargeCurrentLimitA
                </td>
                <td
                  class="whitespace-normal px-3 py-4 pr-0 pl-2 pt-2 pb-2 text-neutral text-left"
                >
                  32
                </td>
              </tr>
              <tr
                class="border-b-neutral/20"
              >
                <td
                  class="whitespace-normal px-3 py-4 pl-0 pr-2 pt-2 pb-2 whitespace-pre"
                >
                  OffMode
                </td>
                <td
                  class="whitespace-normal px-3 py-4 pr-0 pl-2 pt-2 pb-2 text-neutral text-left"
                >
                  false
                </td>
              </tr>
              <tr
                class="border-b-neutral/20"
              >
                <td
                  class="whitespace-normal px-3 py-4 pl-0 pr-2 pt-2 pb-2 whitespace-pre"
                >
                  OfflineSchedulingEnabled
                </td>
                <td
                  class="whitespace-normal px-3 py-4 pr-0 pl-2 pt-2 pb-2 text-neutral text-left"
                >
                  true
                </td>
              </tr>
              <tr
                class="border-b-neutral/20"
              >
                <td
                  class="whitespace-normal px-3 py-4 pl-0 pr-2 pt-2 pb-2 whitespace-pre"
                >
                  PPDevClampFaultThreshold
                </td>
                <td
                  class="whitespace-normal px-3 py-4 pr-0 pl-2 pt-2 pb-2 text-neutral text-left"
                >
                  2
                </td>
              </tr>
              <tr
                class="border-b-neutral/20"
              >
                <td
                  class="whitespace-normal px-3 py-4 pl-0 pr-2 pt-2 pb-2 whitespace-pre"
                >
                  PowerBalancingCurrentLimitImportA
                </td>
                <td
                  class="whitespace-normal px-3 py-4 pr-0 pl-2 pt-2 pb-2 text-neutral text-left"
                >
                  50
                </td>
              </tr>
              <tr
                class="border-b-neutral/20"
              >
                <td
                  class="whitespace-normal px-3 py-4 pl-0 pr-2 pt-2 pb-2 whitespace-pre"
                >
                  PowerBalancingEnabled
                </td>
                <td
                  class="whitespace-normal px-3 py-4 pr-0 pl-2 pt-2 pb-2 text-neutral text-left"
                >
                  true
                </td>
              </tr>
              <tr
                class="border-b-neutral/20"
              >
                <td
                  class="whitespace-normal px-3 py-4 pl-0 pr-2 pt-2 pb-2 whitespace-pre"
                >
                  PowerBalancingSensor
                </td>
                <td
                  class="whitespace-normal px-3 py-4 pr-0 pl-2 pt-2 pb-2 text-neutral text-left"
                >
                  NONE
                </td>
              </tr>
              <tr
                class="border-b-neutral/20"
              >
                <td
                  class="whitespace-normal px-3 py-4 pl-0 pr-2 pt-2 pb-2 whitespace-pre"
                >
                  PowerBalancingSensorInstalled
                </td>
                <td
                  class="whitespace-normal px-3 py-4 pr-0 pl-2 pt-2 pb-2 text-neutral text-left"
                >
                  true
                </td>
              </tr>
              <tr
                class="border-b-neutral/20"
              >
                <td
                  class="whitespace-normal px-3 py-4 pl-0 pr-2 pt-2 pb-2 whitespace-pre"
                >
                  PowerBalancingSensorPolarityInverted
                </td>
                <td
                  class="whitespace-normal px-3 py-4 pr-0 pl-2 pt-2 pb-2 text-neutral text-left"
                >
                  true
                </td>
              </tr>
              <tr
                class="border-b-neutral/20"
              >
                <td
                  class="whitespace-normal px-3 py-4 pl-0 pr-2 pt-2 pb-2 whitespace-pre"
                >
                  SolarExportMargin
                </td>
                <td
                  class="whitespace-normal px-3 py-4 pr-0 pl-2 pt-2 pb-2 text-neutral text-left"
                >
                  5
                </td>
              </tr>
              <tr
                class="border-b-neutral/20"
              >
                <td
                  class="whitespace-normal px-3 py-4 pl-0 pr-2 pt-2 pb-2 whitespace-pre"
                >
                  SolarMatchingEnabled
                </td>
                <td
                  class="whitespace-normal px-3 py-4 pr-0 pl-2 pt-2 pb-2 text-neutral text-left"
                >
                  true
                </td>
              </tr>
              <tr
                class="border-b-neutral/20"
              >
                <td
                  class="whitespace-normal px-3 py-4 pl-0 pr-2 pt-2 pb-2 whitespace-pre"
                >
                  SolarMaxGridImport
                </td>
                <td
                  class="whitespace-normal px-3 py-4 pr-0 pl-2 pt-2 pb-2 text-neutral text-left"
                >
                  4.1
                </td>
              </tr>
              <tr
                class="border-b-neutral/20"
              >
                <td
                  class="whitespace-normal px-3 py-4 pl-0 pr-2 pt-2 pb-2 whitespace-pre"
                >
                  SolarStartHysteresis
                </td>
                <td
                  class="whitespace-normal px-3 py-4 pr-0 pl-2 pt-2 pb-2 text-neutral text-left"
                >
                  10
                </td>
              </tr>
              <tr
                class="border-b-neutral/20"
              >
                <td
                  class="whitespace-normal px-3 py-4 pl-0 pr-2 pt-2 pb-2 whitespace-pre"
                >
                  SolarStopHysteresis
                </td>
                <td
                  class="whitespace-normal px-3 py-4 pr-0 pl-2 pt-2 pb-2 text-neutral text-left"
                >
                  64
                </td>
              </tr>
              <tr
                class="border-b-neutral/20"
              >
                <td
                  class="whitespace-normal px-3 py-4 pl-0 pr-2 pt-2 pb-2 whitespace-pre"
                >
                  SolarSystemInstalled
                </td>
                <td
                  class="whitespace-normal px-3 py-4 pr-0 pl-2 pt-2 pb-2 text-neutral text-left"
                >
                  true
                </td>
              </tr>
              <tr
                class="border-b-neutral/20"
              >
                <td
                  class="whitespace-normal px-3 py-4 pl-0 pr-2 pt-2 pb-2 whitespace-pre"
                >
                  FirmwareVersion
                </td>
                <td
                  class="whitespace-normal px-3 py-4 pr-0 pl-2 pt-2 pb-2 text-neutral text-left"
                >
                  1.0.0
                </td>
              </tr>
              <tr
                class="border-b-neutral/20"
              >
                <td
                  class="whitespace-normal px-3 py-4 pl-0 pr-2 pt-2 pb-2 whitespace-pre"
                >
                  LinkyScheduleEnabled
                </td>
                <td
                  class="whitespace-normal px-3 py-4 pr-0 pl-2 pt-2 pb-2 text-neutral text-left"
                >
                  false
                </td>
              </tr>
              <tr
                class="border-b-neutral/20"
              >
                <td
                  class="whitespace-normal px-3 py-4 pl-0 pr-2 pt-2 pb-2 whitespace-pre"
                >
                  RcdBreakerSize
                </td>
                <td
                  class="whitespace-normal px-3 py-4 pr-0 pl-2 pt-2 pb-2 text-neutral text-left"
                >
                  10
                </td>
              </tr>
              <tr
                class="border-b-neutral/20"
              >
                <td
                  class="whitespace-normal px-3 py-4 pl-0 pr-2 pt-2 pb-2 whitespace-pre"
                >
                  UnlockConnectorOnEVSideDisconnect
                </td>
                <td
                  class="whitespace-normal px-3 py-4 pr-0 pl-2 pt-2 pb-2 text-neutral text-left"
                >
                  true
                </td>
              </tr>
            </tbody>
          </table>
        </div>
      </div>
    </section>
    <div
      class="pb-2"
    />
    <section
      class="p-3 rounded-sm bg-white"
    >
      <h3
        class="text-lg font-bold pb-4"
      >
        Tags
      </h3>
      <div
        class="flex flex-col bg-white rounded-sm overflow-hidden overflow-x-auto"
      >
        <div
          class="inline-block min-w-full -my-2 py-2 align-middle"
        >
          <table
            class="min-w-full"
            tabindex="0"
          >
            <caption
              class="sr-only"
            >
              Table of tags
            </caption>
            <tbody
              class="divide-y"
            >
              <tr
                class="border-b-neutral/20"
              >
                <td
                  class="whitespace-normal px-3 py-4 pl-0 pr-2 pt-2 pb-2 whitespace-pre"
                >
                  cur tollo claudeo
                </td>
                <td
                  class="whitespace-normal px-3 py-4 pr-0 pl-2 pt-2 pb-2 text-neutral text-left"
                >
                  sponte totus dedico
                </td>
              </tr>
              <tr
                class="border-b-neutral/20"
              >
                <td
                  class="whitespace-normal px-3 py-4 pl-0 pr-2 pt-2 pb-2 whitespace-pre"
                >
                  terreo cinis utilis
                </td>
                <td
                  class="whitespace-normal px-3 py-4 pr-0 pl-2 pt-2 pb-2 text-neutral text-left"
                >
                  tametsi vilitas adduco
                </td>
              </tr>
              <tr
                class="border-b-neutral/20"
              >
                <td
                  class="whitespace-normal px-3 py-4 pl-0 pr-2 pt-2 pb-2 whitespace-pre"
                >
                  sollers depromo vesica
                </td>
                <td
                  class="whitespace-normal px-3 py-4 pr-0 pl-2 pt-2 pb-2 text-neutral text-left"
                >
                  similique candidus ulterius
                </td>
              </tr>
              <tr
                class="border-b-neutral/20"
              >
                <td
                  class="whitespace-normal px-3 py-4 pl-0 pr-2 pt-2 pb-2 whitespace-pre"
                >
                  quod texo dapifer
                </td>
                <td
                  class="whitespace-normal px-3 py-4 pr-0 pl-2 pt-2 pb-2 text-neutral text-left"
                >
                  termes catena hic
                </td>
              </tr>
              <tr
                class="border-b-neutral/20"
              >
                <td
                  class="whitespace-normal px-3 py-4 pl-0 pr-2 pt-2 pb-2 whitespace-pre"
                >
                  tabgo adaugeo thesis
                </td>
                <td
                  class="whitespace-normal px-3 py-4 pr-0 pl-2 pt-2 pb-2 text-neutral text-left"
                >
                  celo excepturi curvo
                </td>
              </tr>
              <tr
                class="border-b-neutral/20"
              >
                <td
                  class="whitespace-normal px-3 py-4 pl-0 pr-2 pt-2 pb-2 whitespace-pre"
                >
                  asperiores currus theatrum
                </td>
                <td
                  class="whitespace-normal px-3 py-4 pr-0 pl-2 pt-2 pb-2 text-neutral text-left"
                >
                  thorax vinum deporto
                </td>
              </tr>
            </tbody>
          </table>
        </div>
      </div>
    </section>
    <div
      class="pb-2"
    />
  </div>
</body>
`;

exports[`Charger page content should match snapshot with a single socket 1`] = `
<body>
  <div>
    <div
      class="rounded-md p-3 bg-warning/10 border border-warning"
    >
      <div
        class="flex"
      >
        <div
          class="shrink-0"
        >
          <span
            class="text-error/70"
          >
            <svg
              class="fill-current w-6 h-6"
              viewBox="0 0 32 32"
              xmlns="http://www.w3.org/2000/svg"
            >
              <g>
                <path
                  d="M16.1,29.43A13.43,13.43,0,1,1,29.53,16,13.45,13.45,0,0,1,16.1,29.43Zm0-24.86A11.43,11.43,0,1,0,27.53,16,11.45,11.45,0,0,0,16.1,4.57Z"
                />
                <path
                  d="M16,24.12a1,1,0,0,1-1-1V14.94a1,1,0,1,1,2,0v8.18A1,1,0,0,1,16,24.12Z"
                />
                <path
                  d="M16.1,10.89a1,1,0,0,1-1-1v-.2a1,1,0,0,1,2,0v.2A1,1,0,0,1,16.1,10.89Z"
                />
              </g>
            </svg>
          </span>
        </div>
        <div
          class="flex flex-col justify-center ml-4"
        >
          <p
            class="text-md font-normal sr-only break-words"
          >
            Alert
          </p>
          <div
            class="text-error/70"
          >
            <p
              class="text-md font-normal break-words"
            >
              It is not currently possible to make changes or send commands to this charger. You can search for this charger in MIS 
              <a
                class="text-primary underline hover:decoration-2 focus:decoration-2 focus:outline-none cursor-pointer"
                href="https://admin.pod-point.com/units?search=PSL-12345"
                target="_blank"
              >
                here
              </a>
               or view this charger in Grafana 
              <a
                class="text-primary underline hover:decoration-2 focus:decoration-2 focus:outline-none cursor-pointer"
                href="https://g-e728fe26ac.grafana-workspace.eu-west-1.amazonaws.com/d/fdtbud32bw45ce/5b147138-e9f2-5846-ba34-c2778f5a748f?var-ppid=PSL-12345"
                target="_blank"
              >
                here
              </a>
              .
            </p>
          </div>
        </div>
      </div>
    </div>
    <div
      class="pb-2"
    />
    <section
      class="p-3 rounded-sm bg-white"
    >
      <header>
        <div
          class="flex"
        >
          <div
            class="lg:flex space-x-1 items-center"
          >
            <div
              class="flex"
            >
              <h1
                class="text-xxl font-bold"
              >
                PSL-12345
              </h1>
              <button
                aria-label="Copy PSL-12345"
                class="border-2 border-solid rounded-sm font-bold text-center disabled:no-underline transition ease-in-out duration-200 cursor-pointer disabled:cursor-not-allowed border-transparent text-primary hover:underline hover:text-primary active:font-bold active:text-primary disabled:text-neutral/40 disabled:hover:no-underline disabled:hover:border-transparent p-1.5 ml-1"
                type="button"
              >
                <svg
                  class="fill-current h-4 w-4"
                  viewBox="0 0 24 24"
                  xmlns="http://www.w3.org/2000/svg"
                >
                  <defs>
                    <style>
                      .cls-1 
                    </style>
                  </defs>
                  <path
                    d="M18.73,14.5h1.83c1.35,0,2.44-1.09,2.44-2.44V3.5c0-1.35-1.09-2.44-2.44-2.44h-8.56c-1.35,0-2.44,1.09-2.44,2.44v1.83M3.45,9.61h8.56c1.35,0,2.44,1.09,2.44,2.44v8.56c0,1.35-1.09,2.44-2.44,2.44H3.45c-1.35,0-2.44-1.09-2.44-2.44v-8.56c0-1.35,1.09-2.44,2.44-2.44Z"
                    style="fill: none; stroke: currentColor; stroke-linecap: round; stroke-linejoin: round; stroke-width: 1.5px;"
                  />
                </svg>
              </button>
            </div>
          </div>
          <div
            class="ml-auto"
          >
            <div
              class="relative inline-block text-left"
              data-headlessui-state=""
            >
              <button
                aria-expanded="false"
                aria-haspopup="menu"
                class="bg-black border-black border-2 border-solid rounded-sm px-4 py-1.5 hover:bg-charcoal hover:border-charcoal focus:bg-charcoal focus:border-charcoal active:bg-charcoal active:border-charcoal disabled:bg-neutral/20 disabled:border-neutral/20 disabled:text-neutral font-bold inline-flex items-center text-white cursor-pointer disabled:cursor-not-allowed"
                data-headlessui-state=""
                id="headlessui-menu-button-:test-id-2"
                type="button"
              >
                <span>
                  Commands
                </span>
                <svg
                  class="fill-current stroke-current h-2 w-2 min-w-0 ml-2"
                  viewBox="0 0 24 24"
                  xmlns="http://www.w3.org/2000/svg"
                >
                  <g>
                    <path
                      d="m13.15,18.84c-.57.64-1.55.69-2.19.11-.04-.04-.08-.07-.11-.11L.4,7.23c-.57-.64-.52-1.61.12-2.19.28-.25.65-.4,1.03-.4h20.91c.85,0,1.55.69,1.55,1.55,0,.38-.14.75-.4,1.03l-10.45,11.62Z"
                    />
                  </g>
                </svg>
              </button>
            </div>
          </div>
        </div>
        <p
          class="text-md font-semibold break-words"
        >
          Test-Name
        </p>
        <div>
          <div
            class="flex items-center"
          >
            <a
              class="text-primary underline hover:decoration-2 focus:decoration-2 focus:outline-none cursor-pointer block mr-2"
              href="/accounts/a3450f38-217a-4d0c-8eec-b7d63c6fd2e0/details"
            >
              John Doe - <EMAIL>
            </a>
            <button
              aria-label="Unlink user John Doe"
              class="border-2 border-solid rounded-sm font-bold text-center disabled:no-underline transition ease-in-out duration-200 cursor-pointer disabled:cursor-not-allowed border-transparent text-primary hover:underline hover:text-primary active:font-bold active:text-primary disabled:text-neutral/40 disabled:hover:no-underline disabled:hover:border-transparent p-1.5"
              data-testid="unlink-user-a3450f38-217a-4d0c-8eec-b7d63c6fd2e0"
              id="unlink-user-a3450f38-217a-4d0c-8eec-b7d63c6fd2e0"
              name="unlink-user-a3450f38-217a-4d0c-8eec-b7d63c6fd2e0"
              type="button"
            >
              <svg
                class="fill-current h-4 w-4"
                viewBox="0 0 24 24"
                xmlns="http://www.w3.org/2000/svg"
              >
                <g>
                  <path
                    d="m9.94,14.21c3.38,0,6.12-2.75,6.12-6.12S13.32,1.96,9.94,1.96s-6.12,2.75-6.12,6.12,2.75,6.12,6.12,6.12Zm0-10.75c2.55,0,4.62,2.07,4.62,4.62s-2.07,4.62-4.62,4.62-4.62-2.07-4.62-4.62,2.07-4.62,4.62-4.62Z"
                  />
                  <path
                    d="m9.94,14.73C2.66,14.73.21,20.96.18,21.02c-.15.39.05.82.43.97.39.15.82-.04.97-.43.08-.22,2.13-5.33,8.36-5.33s8.77,5.16,8.87,5.38c.13.27.4.43.68.43.11,0,.21-.02.32-.07.37-.17.54-.62.36-.99-.12-.26-2.98-6.25-10.23-6.25Z"
                  />
                  <path
                    d="m21.42,9.31l2.23-2.23c.29-.29.29-.77,0-1.06s-.77-.29-1.06,0l-2.23,2.23-2.23-2.23c-.29-.29-.77-.29-1.06,0s-.29.77,0,1.06l2.23,2.23-2.23,2.23c-.29.29-.29.77,0,1.06.15.15.34.22.53.22s.38-.07.53-.22l2.23-2.23,2.23,2.23c.15.15.34.22.53.22s.38-.07.53-.22c.29-.29.29-.77,0-1.06l-2.23-2.23Z"
                  />
                </g>
              </svg>
            </button>
          </div>
          <div
            class="flex items-center"
          >
            <a
              class="text-primary underline hover:decoration-2 focus:decoration-2 focus:outline-none cursor-pointer block mr-2"
              href="/accounts/77b7c00e-8890-4a76-b4e6-def3b226d47b/details"
            >
              John Doe - <EMAIL>
            </a>
            <button
              aria-label="Unlink user John Doe"
              class="border-2 border-solid rounded-sm font-bold text-center disabled:no-underline transition ease-in-out duration-200 cursor-pointer disabled:cursor-not-allowed border-transparent text-primary hover:underline hover:text-primary active:font-bold active:text-primary disabled:text-neutral/40 disabled:hover:no-underline disabled:hover:border-transparent p-1.5"
              data-testid="unlink-user-77b7c00e-8890-4a76-b4e6-def3b226d47b"
              id="unlink-user-77b7c00e-8890-4a76-b4e6-def3b226d47b"
              name="unlink-user-77b7c00e-8890-4a76-b4e6-def3b226d47b"
              type="button"
            >
              <svg
                class="fill-current h-4 w-4"
                viewBox="0 0 24 24"
                xmlns="http://www.w3.org/2000/svg"
              >
                <g>
                  <path
                    d="m9.94,14.21c3.38,0,6.12-2.75,6.12-6.12S13.32,1.96,9.94,1.96s-6.12,2.75-6.12,6.12,2.75,6.12,6.12,6.12Zm0-10.75c2.55,0,4.62,2.07,4.62,4.62s-2.07,4.62-4.62,4.62-4.62-2.07-4.62-4.62,2.07-4.62,4.62-4.62Z"
                  />
                  <path
                    d="m9.94,14.73C2.66,14.73.21,20.96.18,21.02c-.15.39.05.82.43.97.39.15.82-.04.97-.43.08-.22,2.13-5.33,8.36-5.33s8.77,5.16,8.87,5.38c.13.27.4.43.68.43.11,0,.21-.02.32-.07.37-.17.54-.62.36-.99-.12-.26-2.98-6.25-10.23-6.25Z"
                  />
                  <path
                    d="m21.42,9.31l2.23-2.23c.29-.29.29-.77,0-1.06s-.77-.29-1.06,0l-2.23,2.23-2.23-2.23c-.29-.29-.77-.29-1.06,0s-.29.77,0,1.06l2.23,2.23-2.23,2.23c-.29.29-.29.77,0,1.06.15.15.34.22.53.22s.38-.07.53-.22l2.23-2.23,2.23,2.23c.15.15.34.22.53.22s.38-.07.53-.22c.29-.29.29-.77,0-1.06l-2.23-2.23Z"
                  />
                </g>
              </svg>
            </button>
          </div>
          <div
            class="flex items-center"
          >
            <a
              class="text-primary underline hover:decoration-2 focus:decoration-2 focus:outline-none cursor-pointer block mr-2"
              href="/accounts/5411de0c-d46b-48c3-b6d2-e6bc3a1f7049/details"
            >
              John Doe - <EMAIL>
            </a>
            <button
              aria-label="Unlink user John Doe"
              class="border-2 border-solid rounded-sm font-bold text-center disabled:no-underline transition ease-in-out duration-200 cursor-pointer disabled:cursor-not-allowed border-transparent text-primary hover:underline hover:text-primary active:font-bold active:text-primary disabled:text-neutral/40 disabled:hover:no-underline disabled:hover:border-transparent p-1.5"
              data-testid="unlink-user-5411de0c-d46b-48c3-b6d2-e6bc3a1f7049"
              id="unlink-user-5411de0c-d46b-48c3-b6d2-e6bc3a1f7049"
              name="unlink-user-5411de0c-d46b-48c3-b6d2-e6bc3a1f7049"
              type="button"
            >
              <svg
                class="fill-current h-4 w-4"
                viewBox="0 0 24 24"
                xmlns="http://www.w3.org/2000/svg"
              >
                <g>
                  <path
                    d="m9.94,14.21c3.38,0,6.12-2.75,6.12-6.12S13.32,1.96,9.94,1.96s-6.12,2.75-6.12,6.12,2.75,6.12,6.12,6.12Zm0-10.75c2.55,0,4.62,2.07,4.62,4.62s-2.07,4.62-4.62,4.62-4.62-2.07-4.62-4.62,2.07-4.62,4.62-4.62Z"
                  />
                  <path
                    d="m9.94,14.73C2.66,14.73.21,20.96.18,21.02c-.15.39.05.82.43.97.39.15.82-.04.97-.43.08-.22,2.13-5.33,8.36-5.33s8.77,5.16,8.87,5.38c.13.27.4.43.68.43.11,0,.21-.02.32-.07.37-.17.54-.62.36-.99-.12-.26-2.98-6.25-10.23-6.25Z"
                  />
                  <path
                    d="m21.42,9.31l2.23-2.23c.29-.29.29-.77,0-1.06s-.77-.29-1.06,0l-2.23,2.23-2.23-2.23c-.29-.29-.77-.29-1.06,0s-.29.77,0,1.06l2.23,2.23-2.23,2.23c-.29.29-.29.77,0,1.06.15.15.34.22.53.22s.38-.07.53-.22l2.23-2.23,2.23,2.23c.15.15.34.22.53.22s.38-.07.53-.22c.29-.29.29-.77,0-1.06l-2.23-2.23Z"
                  />
                </g>
              </svg>
            </button>
          </div>
          <div
            class="flex items-center"
          >
            <a
              class="text-primary underline hover:decoration-2 focus:decoration-2 focus:outline-none cursor-pointer block mr-2"
              href="/accounts/3482bea8-17d5-470e-83bd-4e897b8b11ee/details"
            >
              John Doe - <EMAIL>
            </a>
            <button
              aria-label="Unlink user John Doe"
              class="border-2 border-solid rounded-sm font-bold text-center disabled:no-underline transition ease-in-out duration-200 cursor-pointer disabled:cursor-not-allowed border-transparent text-primary hover:underline hover:text-primary active:font-bold active:text-primary disabled:text-neutral/40 disabled:hover:no-underline disabled:hover:border-transparent p-1.5"
              data-testid="unlink-user-3482bea8-17d5-470e-83bd-4e897b8b11ee"
              id="unlink-user-3482bea8-17d5-470e-83bd-4e897b8b11ee"
              name="unlink-user-3482bea8-17d5-470e-83bd-4e897b8b11ee"
              type="button"
            >
              <svg
                class="fill-current h-4 w-4"
                viewBox="0 0 24 24"
                xmlns="http://www.w3.org/2000/svg"
              >
                <g>
                  <path
                    d="m9.94,14.21c3.38,0,6.12-2.75,6.12-6.12S13.32,1.96,9.94,1.96s-6.12,2.75-6.12,6.12,2.75,6.12,6.12,6.12Zm0-10.75c2.55,0,4.62,2.07,4.62,4.62s-2.07,4.62-4.62,4.62-4.62-2.07-4.62-4.62,2.07-4.62,4.62-4.62Z"
                  />
                  <path
                    d="m9.94,14.73C2.66,14.73.21,20.96.18,21.02c-.15.39.05.82.43.97.39.15.82-.04.97-.43.08-.22,2.13-5.33,8.36-5.33s8.77,5.16,8.87,5.38c.13.27.4.43.68.43.11,0,.21-.02.32-.07.37-.17.54-.62.36-.99-.12-.26-2.98-6.25-10.23-6.25Z"
                  />
                  <path
                    d="m21.42,9.31l2.23-2.23c.29-.29.29-.77,0-1.06s-.77-.29-1.06,0l-2.23,2.23-2.23-2.23c-.29-.29-.77-.29-1.06,0s-.29.77,0,1.06l2.23,2.23-2.23,2.23c-.29.29-.29.77,0,1.06.15.15.34.22.53.22s.38-.07.53-.22l2.23-2.23,2.23,2.23c.15.15.34.22.53.22s.38-.07.53-.22c.29-.29.29-.77,0-1.06l-2.23-2.23Z"
                  />
                </g>
              </svg>
            </button>
          </div>
          <div
            class="flex items-center"
          >
            <a
              class="text-primary underline hover:decoration-2 focus:decoration-2 focus:outline-none cursor-pointer block mr-2"
              href="/accounts/609cf19a-c8c9-43f8-9042-59b5527e5f73/details"
            >
              John Doe - <EMAIL>
            </a>
            <button
              aria-label="Unlink user John Doe"
              class="border-2 border-solid rounded-sm font-bold text-center disabled:no-underline transition ease-in-out duration-200 cursor-pointer disabled:cursor-not-allowed border-transparent text-primary hover:underline hover:text-primary active:font-bold active:text-primary disabled:text-neutral/40 disabled:hover:no-underline disabled:hover:border-transparent p-1.5"
              data-testid="unlink-user-609cf19a-c8c9-43f8-9042-59b5527e5f73"
              id="unlink-user-609cf19a-c8c9-43f8-9042-59b5527e5f73"
              name="unlink-user-609cf19a-c8c9-43f8-9042-59b5527e5f73"
              type="button"
            >
              <svg
                class="fill-current h-4 w-4"
                viewBox="0 0 24 24"
                xmlns="http://www.w3.org/2000/svg"
              >
                <g>
                  <path
                    d="m9.94,14.21c3.38,0,6.12-2.75,6.12-6.12S13.32,1.96,9.94,1.96s-6.12,2.75-6.12,6.12,2.75,6.12,6.12,6.12Zm0-10.75c2.55,0,4.62,2.07,4.62,4.62s-2.07,4.62-4.62,4.62-4.62-2.07-4.62-4.62,2.07-4.62,4.62-4.62Z"
                  />
                  <path
                    d="m9.94,14.73C2.66,14.73.21,20.96.18,21.02c-.15.39.05.82.43.97.39.15.82-.04.97-.43.08-.22,2.13-5.33,8.36-5.33s8.77,5.16,8.87,5.38c.13.27.4.43.68.43.11,0,.21-.02.32-.07.37-.17.54-.62.36-.99-.12-.26-2.98-6.25-10.23-6.25Z"
                  />
                  <path
                    d="m21.42,9.31l2.23-2.23c.29-.29.29-.77,0-1.06s-.77-.29-1.06,0l-2.23,2.23-2.23-2.23c-.29-.29-.77-.29-1.06,0s-.29.77,0,1.06l2.23,2.23-2.23,2.23c-.29.29-.29.77,0,1.06.15.15.34.22.53.22s.38-.07.53-.22l2.23-2.23,2.23,2.23c.15.15.34.22.53.22s.38-.07.53-.22c.29-.29.29-.77,0-1.06l-2.23-2.23Z"
                  />
                </g>
              </svg>
            </button>
          </div>
          <div
            class="flex items-center"
          >
            <a
              class="text-primary underline hover:decoration-2 focus:decoration-2 focus:outline-none cursor-pointer block mr-2"
              href="/accounts/910b444d-f474-46c3-b368-c43b170dbda9/details"
            >
              John Doe - <EMAIL>
            </a>
            <button
              aria-label="Unlink user John Doe"
              class="border-2 border-solid rounded-sm font-bold text-center disabled:no-underline transition ease-in-out duration-200 cursor-pointer disabled:cursor-not-allowed border-transparent text-primary hover:underline hover:text-primary active:font-bold active:text-primary disabled:text-neutral/40 disabled:hover:no-underline disabled:hover:border-transparent p-1.5"
              data-testid="unlink-user-910b444d-f474-46c3-b368-c43b170dbda9"
              id="unlink-user-910b444d-f474-46c3-b368-c43b170dbda9"
              name="unlink-user-910b444d-f474-46c3-b368-c43b170dbda9"
              type="button"
            >
              <svg
                class="fill-current h-4 w-4"
                viewBox="0 0 24 24"
                xmlns="http://www.w3.org/2000/svg"
              >
                <g>
                  <path
                    d="m9.94,14.21c3.38,0,6.12-2.75,6.12-6.12S13.32,1.96,9.94,1.96s-6.12,2.75-6.12,6.12,2.75,6.12,6.12,6.12Zm0-10.75c2.55,0,4.62,2.07,4.62,4.62s-2.07,4.62-4.62,4.62-4.62-2.07-4.62-4.62,2.07-4.62,4.62-4.62Z"
                  />
                  <path
                    d="m9.94,14.73C2.66,14.73.21,20.96.18,21.02c-.15.39.05.82.43.97.39.15.82-.04.97-.43.08-.22,2.13-5.33,8.36-5.33s8.77,5.16,8.87,5.38c.13.27.4.43.68.43.11,0,.21-.02.32-.07.37-.17.54-.62.36-.99-.12-.26-2.98-6.25-10.23-6.25Z"
                  />
                  <path
                    d="m21.42,9.31l2.23-2.23c.29-.29.29-.77,0-1.06s-.77-.29-1.06,0l-2.23,2.23-2.23-2.23c-.29-.29-.77-.29-1.06,0s-.29.77,0,1.06l2.23,2.23-2.23,2.23c-.29.29-.29.77,0,1.06.15.15.34.22.53.22s.38-.07.53-.22l2.23-2.23,2.23,2.23c.15.15.34.22.53.22s.38-.07.53-.22c.29-.29.29-.77,0-1.06l-2.23-2.23Z"
                  />
                </g>
              </svg>
            </button>
          </div>
          <div
            class="pb-2"
          />
          <div
            class="flex max-md:flex-col max-md:space-y-2 md:space-x-6"
          >
            <div
              class="flex items-center"
            >
              <svg
                class="fill-current w-6 h-6"
                viewBox="0 0 24 24"
                xmlns="http://www.w3.org/2000/svg"
              >
                <title>
                  Domestic location
                </title>
                <g>
                  <path
                    d="m22.07,6.92L14.05.84c-1.21-.92-2.89-.92-4.1,0L1.93,6.92c-.84.64-1.34,1.65-1.34,2.7v10.84c0,1.87,1.52,3.39,3.39,3.39h5.04c.41,0,.75-.34.75-.75s-.34-.75-.75-.75H3.98c-1.04,0-1.89-.85-1.89-1.89v-10.84c0-.59.28-1.15.75-1.5L10.86,2.03c.67-.51,1.61-.51,2.28,0l8.02,6.08c.47.36.74.92.74,1.5v10.84c0,1.04-.85,1.89-1.89,1.89h-5.53c-.96,0-1.74-.78-1.74-1.74v-2.63c1.85-.35,3.25-1.98,3.25-3.93v-3.25c0-.41-.34-.75-.75-.75h-.81v-1.88c0-.41-.34-.75-.75-.75s-.75.34-.75.75v1.88h-1.77v-1.88c0-.41-.34-.75-.75-.75s-.75.34-.75.75v1.88h-.93c-.41,0-.75.34-.75.75v3.25c0,1.95,1.4,3.57,3.25,3.93v2.63c0,1.79,1.45,3.24,3.24,3.24h5.53c1.87,0,3.39-1.52,3.39-3.39v-10.84c0-1.05-.5-2.06-1.34-2.7Zm-12.58,7.13v-2.5h5.01v2.5c0,1.38-1.12,2.5-2.5,2.5s-2.5-1.12-2.5-2.5Z"
                  />
                </g>
              </svg>
              <span
                class="ml-2"
              >
                Domestic
              </span>
            </div>
          </div>
        </div>
      </header>
    </section>
    <div
      class="pb-2"
    />
    <div
      class="flex flex-row flex-wrap gap-x-4 gap-y-2"
    >
      <span
        aria-label="Fault vendorErrorCode4 - Invalid Date"
        class="font-bold px-2.5 py-1 rounded-xs inline-flex items-center tracking-wide bg-warning/10 border border-error/70 text-error/70"
        role="status"
      >
        Fault vendorErrorCode4 - Invalid Date
      </span>
      <span
        aria-label="Fault vendorErrorCode3 - Invalid Date"
        class="font-bold px-2.5 py-1 rounded-xs inline-flex items-center tracking-wide bg-warning/10 border border-error/70 text-error/70"
        role="status"
      >
        Fault vendorErrorCode3 - Invalid Date
      </span>
      <span
        aria-label="Fault vendorErrorCode2 - Invalid Date"
        class="font-bold px-2.5 py-1 rounded-xs inline-flex items-center tracking-wide bg-warning/10 border border-error/70 text-error/70"
        role="status"
      >
        Fault vendorErrorCode2 - Invalid Date
      </span>
      <span
        aria-label="Fault vendorErrorCode1 - Invalid Date"
        class="font-bold px-2.5 py-1 rounded-xs inline-flex items-center tracking-wide bg-warning/10 border border-error/70 text-error/70"
        role="status"
      >
        Fault vendorErrorCode1 - Invalid Date
      </span>
    </div>
    <div
      class="pb-2"
    />
    <div
      class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 print:grid-cols-3 gap-2"
    >
      <section
        class="p-3 rounded-sm bg-white md:col-span-2 lg:col-span-1"
      >
        <div
          class="flex justify-between items-baseline"
        >
          <h3
            class="text-lg font-bold pb-4"
          >
            Charger information
          </h3>
          <a
            class="text-primary underline hover:decoration-2 focus:decoration-2 focus:outline-none cursor-pointer text-right ml-2"
            href="/chargers/PSL-12345/logs/diagnostic?socket=A"
          >
            View diagnostic logs
          </a>
        </div>
        <div
          class="flex flex-col bg-white rounded-sm overflow-hidden overflow-x-auto"
        >
          <div
            class="inline-block min-w-full -my-2 py-2 align-middle"
          >
            <table
              class="min-w-full"
              tabindex="0"
            >
              <caption
                class="sr-only"
              >
                Charger information table
              </caption>
              <tbody
                class="divide-y"
              >
                <tr
                  class="border-b-neutral/20"
                >
                  <td
                    class="whitespace-normal px-3 py-4 pl-0 pr-2 pt-2 pb-2 whitespace-pre"
                  >
                    Model SKU
                  </td>
                  <td
                    class="whitespace-normal px-3 py-4 pr-0 pl-2 pt-2 pb-2 text-neutral text-right"
                  >
                    T7-S-07-AMC-BLK
                  </td>
                </tr>
                <tr
                  class="border-b-neutral/20"
                >
                  <td
                    class="whitespace-normal px-3 py-4 pl-0 pr-2 pt-2 pb-2 whitespace-pre"
                  >
                    Arch version
                  </td>
                  <td
                    class="whitespace-normal px-3 py-4 pr-0 pl-2 pt-2 pb-2 text-neutral text-right"
                  >
                    -
                  </td>
                </tr>
                <tr
                  class="border-b-neutral/20"
                >
                  <td
                    class="whitespace-normal px-3 py-4 pl-0 pr-2 pt-2 pb-2 whitespace-pre"
                  >
                    Firmware
                  </td>
                  <td
                    class="whitespace-normal px-3 py-4 pr-0 pl-2 pt-2 pb-2 text-neutral text-right"
                  >
                    A50P-X.Y.Z-0000P
                  </td>
                </tr>
                <tr
                  class="border-b-neutral/20"
                >
                  <td
                    class="whitespace-normal px-3 py-4 pl-0 pr-2 pt-2 pb-2 whitespace-pre"
                  >
                    Manufactured
                  </td>
                  <td
                    class="whitespace-normal px-3 py-4 pr-0 pl-2 pt-2 pb-2 text-neutral text-right"
                  >
                    1 June 2023
                  </td>
                </tr>
                <tr
                  class="border-b-neutral/20"
                >
                  <td
                    class="whitespace-normal px-3 py-4 pl-0 pr-2 pt-2 pb-2 whitespace-pre"
                  >
                    Operator
                  </td>
                  <td
                    class="whitespace-normal px-3 py-4 pr-0 pl-2 pt-2 pb-2 text-neutral text-right"
                  >
                    Pod Point
                  </td>
                </tr>
                <tr
                  class="border-b-neutral/20"
                >
                  <td
                    class="whitespace-normal px-3 py-4 pl-0 pr-2 pt-2 pb-2 whitespace-pre"
                  >
                    MAC address
                  </td>
                  <td
                    class="whitespace-normal px-3 py-4 pr-0 pl-2 pt-2 pb-2 text-neutral text-right"
                  >
                    -
                  </td>
                </tr>
                <tr
                  class="border-b-neutral/20"
                >
                  <td
                    class="whitespace-normal px-3 py-4 pl-0 pr-2 pt-2 pb-2 whitespace-pre"
                  >
                    PCBA serial number
                  </td>
                  <td
                    class="whitespace-normal px-3 py-4 pr-0 pl-2 pt-2 pb-2 text-neutral text-right"
                  >
                    -
                  </td>
                </tr>
              </tbody>
            </table>
          </div>
        </div>
        <div
          class="flex flex-row-reverse"
        >
          <a
            class="text-primary underline hover:decoration-2 focus:decoration-2 focus:outline-none cursor-pointer text-right ml-2"
            href="/chargers/PSL-12345/pcbs/history?socket=A"
          >
            History
          </a>
        </div>
      </section>
      <section
        class="p-3 rounded-sm bg-white"
      >
        <h3
          class="text-lg font-bold pb-4"
        >
          Charger connectivity
        </h3>
        <div
          class="flex flex-col bg-white rounded-sm overflow-hidden overflow-x-auto"
        >
          <div
            class="inline-block min-w-full -my-2 py-2 align-middle"
          >
            <table
              class="min-w-full"
              tabindex="0"
            >
              <caption
                class="sr-only"
              >
                Charger connectivity table
              </caption>
              <tbody
                class="divide-y"
              >
                <tr
                  class="border-b-neutral/20"
                >
                  <td
                    class="whitespace-normal px-3 py-4 pl-0 pr-2 pt-2 pb-2 whitespace-pre"
                  >
                    Status
                  </td>
                  <td
                    class="whitespace-normal px-3 py-4 pr-0 pl-2 pt-2 pb-2 text-neutral text-right"
                  >
                    <span
                      aria-label="connectivityStatus"
                      class="font-bold px-2.5 rounded-xs inline-flex items-center tracking-wide bg-success/10 border border-success text-success py-px text-xs"
                      role="status"
                    >
                      connectivityStatus
                    </span>
                  </td>
                </tr>
                <tr
                  class="border-b-neutral/20"
                >
                  <td
                    class="whitespace-normal px-3 py-4 pl-0 pr-2 pt-2 pb-2 whitespace-pre"
                  >
                    Charging status
                  </td>
                  <td
                    class="whitespace-normal px-3 py-4 pr-0 pl-2 pt-2 pb-2 text-neutral text-right"
                  >
                    chargingState
                  </td>
                </tr>
                <tr
                  class="border-b-neutral/20"
                >
                  <td
                    class="whitespace-normal px-3 py-4 pl-0 pr-2 pt-2 pb-2 whitespace-pre"
                  >
                    Last message
                  </td>
                  <td
                    class="whitespace-normal px-3 py-4 pr-0 pl-2 pt-2 pb-2 text-neutral text-right"
                  >
                    30 June 2024 at 22:00:00
                  </td>
                </tr>
                <tr
                  class="border-b-neutral/20"
                >
                  <td
                    class="whitespace-normal px-3 py-4 pl-0 pr-2 pt-2 pb-2 whitespace-pre"
                  >
                    Connection quality
(scale 1-5)
                  </td>
                  <td
                    class="whitespace-normal px-3 py-4 pr-0 pl-2 pt-2 pb-2 text-neutral text-right"
                  >
                    0
                  </td>
                </tr>
                <tr
                  class="border-b-neutral/20"
                >
                  <td
                    class="whitespace-normal px-3 py-4 pl-0 pr-2 pt-2 pb-2 whitespace-pre"
                  >
                    Router serial number
                  </td>
                  <td
                    class="whitespace-normal px-3 py-4 pr-0 pl-2 pt-2 pb-2 text-neutral text-right"
                  >
                    1102538429
                  </td>
                </tr>
                <tr
                  class="border-b-neutral/20"
                >
                  <td
                    class="whitespace-normal px-3 py-4 pl-0 pr-2 pt-2 pb-2 whitespace-pre"
                  >
                    Router MAC address
                  </td>
                  <td
                    class="whitespace-normal px-3 py-4 pr-0 pl-2 pt-2 pb-2 text-neutral text-right"
                  >
                    001E4223CF13
                  </td>
                </tr>
                <tr
                  class="border-b-neutral/20"
                >
                  <td
                    class="whitespace-normal px-3 py-4 pl-0 pr-2 pt-2 pb-2 whitespace-pre"
                  >
                    Router SIM number
                  </td>
                  <td
                    class="whitespace-normal px-3 py-4 pr-0 pl-2 pt-2 pb-2 text-neutral text-right"
                  >
                    123456789123000043
                  </td>
                </tr>
                <tr
                  class="border-b-neutral/20"
                >
                  <td
                    class="whitespace-normal px-3 py-4 pl-0 pr-2 pt-2 pb-2 whitespace-pre"
                  >
                    Router model
                  </td>
                  <td
                    class="whitespace-normal px-3 py-4 pr-0 pl-2 pt-2 pb-2 text-neutral text-right"
                  >
                    RUT241
                  </td>
                </tr>
              </tbody>
            </table>
          </div>
        </div>
      </section>
      <section
        class="p-3 rounded-sm bg-white"
      >
        <div
          class="flex justify-between items-baseline"
        >
          <h3
            class="text-lg font-bold pb-4"
          >
            Charger access point (AP)
          </h3>
          <a
            class="text-primary underline hover:decoration-2 focus:decoration-2 focus:outline-none cursor-pointer text-right ml-2"
            href="/chargers/PSL-12345?redact=true"
          >
            Hide password
          </a>
        </div>
        <div
          class="flex flex-col bg-white rounded-sm overflow-hidden overflow-x-auto"
        >
          <div
            class="inline-block min-w-full -my-2 py-2 align-middle"
          >
            <table
              class="min-w-full"
              tabindex="0"
            >
              <caption
                class="sr-only"
              >
                Charger access point
              </caption>
              <tbody
                class="divide-y"
              >
                <tr
                  class="border-b-neutral/20"
                >
                  <td
                    class="whitespace-normal px-3 py-4 pl-0 pr-2 pt-2 pb-2 whitespace-pre"
                  >
                    SSID
                  </td>
                  <td
                    class="whitespace-normal px-3 py-4 pr-0 pl-2 pt-2 pb-2 text-neutral text-right"
                  >
                    PP-123456
                  </td>
                </tr>
                <tr
                  class="border-b-neutral/20"
                >
                  <td
                    class="whitespace-normal px-3 py-4 pl-0 pr-2 pt-2 pb-2 whitespace-pre"
                  >
                    Password
                  </td>
                  <td
                    class="whitespace-normal px-3 py-4 pr-0 pl-2 pt-2 pb-2 text-neutral text-right"
                  >
                    password123
                  </td>
                </tr>
              </tbody>
            </table>
          </div>
        </div>
        <div
          class="border-2 border-dotted border-neutral/20 mt-2 h-32 p-3"
        >
          <svg
            class="w-full h-full"
            height="256"
            viewBox="0 0 25 25"
            width="256"
            xmlns="http://www.w3.org/2000/svg"
          >
            <path
              d="       M 7 0 l 1 0 0 1 -1 0 Z   M 10 0 l 1 0 0 1 -1 0 Z M 11 0 l 1 0 0 1 -1 0 Z   M 14 0 l 1 0 0 1 -1 0 Z M 15 0 l 1 0 0 1 -1 0 Z  M 17 0 l 1 0 0 1 -1 0 Z         M 1 1 l 1 0 0 1 -1 0 Z M 2 1 l 1 0 0 1 -1 0 Z M 3 1 l 1 0 0 1 -1 0 Z M 4 1 l 1 0 0 1 -1 0 Z M 5 1 l 1 0 0 1 -1 0 Z  M 7 1 l 1 0 0 1 -1 0 Z   M 10 1 l 1 0 0 1 -1 0 Z M 11 1 l 1 0 0 1 -1 0 Z  M 13 1 l 1 0 0 1 -1 0 Z  M 15 1 l 1 0 0 1 -1 0 Z M 16 1 l 1 0 0 1 -1 0 Z M 17 1 l 1 0 0 1 -1 0 Z  M 19 1 l 1 0 0 1 -1 0 Z M 20 1 l 1 0 0 1 -1 0 Z M 21 1 l 1 0 0 1 -1 0 Z M 22 1 l 1 0 0 1 -1 0 Z M 23 1 l 1 0 0 1 -1 0 Z   M 1 2 l 1 0 0 1 -1 0 Z    M 5 2 l 1 0 0 1 -1 0 Z  M 7 2 l 1 0 0 1 -1 0 Z M 8 2 l 1 0 0 1 -1 0 Z      M 14 2 l 1 0 0 1 -1 0 Z  M 16 2 l 1 0 0 1 -1 0 Z M 17 2 l 1 0 0 1 -1 0 Z  M 19 2 l 1 0 0 1 -1 0 Z    M 23 2 l 1 0 0 1 -1 0 Z   M 1 3 l 1 0 0 1 -1 0 Z    M 5 3 l 1 0 0 1 -1 0 Z  M 7 3 l 1 0 0 1 -1 0 Z M 8 3 l 1 0 0 1 -1 0 Z  M 10 3 l 1 0 0 1 -1 0 Z M 11 3 l 1 0 0 1 -1 0 Z   M 14 3 l 1 0 0 1 -1 0 Z M 15 3 l 1 0 0 1 -1 0 Z  M 17 3 l 1 0 0 1 -1 0 Z  M 19 3 l 1 0 0 1 -1 0 Z    M 23 3 l 1 0 0 1 -1 0 Z   M 1 4 l 1 0 0 1 -1 0 Z    M 5 4 l 1 0 0 1 -1 0 Z  M 7 4 l 1 0 0 1 -1 0 Z   M 10 4 l 1 0 0 1 -1 0 Z M 11 4 l 1 0 0 1 -1 0 Z   M 14 4 l 1 0 0 1 -1 0 Z M 15 4 l 1 0 0 1 -1 0 Z  M 17 4 l 1 0 0 1 -1 0 Z  M 19 4 l 1 0 0 1 -1 0 Z    M 23 4 l 1 0 0 1 -1 0 Z   M 1 5 l 1 0 0 1 -1 0 Z M 2 5 l 1 0 0 1 -1 0 Z M 3 5 l 1 0 0 1 -1 0 Z M 4 5 l 1 0 0 1 -1 0 Z M 5 5 l 1 0 0 1 -1 0 Z  M 7 5 l 1 0 0 1 -1 0 Z   M 10 5 l 1 0 0 1 -1 0 Z M 11 5 l 1 0 0 1 -1 0 Z M 12 5 l 1 0 0 1 -1 0 Z   M 15 5 l 1 0 0 1 -1 0 Z  M 17 5 l 1 0 0 1 -1 0 Z  M 19 5 l 1 0 0 1 -1 0 Z M 20 5 l 1 0 0 1 -1 0 Z M 21 5 l 1 0 0 1 -1 0 Z M 22 5 l 1 0 0 1 -1 0 Z M 23 5 l 1 0 0 1 -1 0 Z         M 7 6 l 1 0 0 1 -1 0 Z  M 9 6 l 1 0 0 1 -1 0 Z  M 11 6 l 1 0 0 1 -1 0 Z  M 13 6 l 1 0 0 1 -1 0 Z  M 15 6 l 1 0 0 1 -1 0 Z  M 17 6 l 1 0 0 1 -1 0 Z        M 0 7 l 1 0 0 1 -1 0 Z M 1 7 l 1 0 0 1 -1 0 Z M 2 7 l 1 0 0 1 -1 0 Z M 3 7 l 1 0 0 1 -1 0 Z M 4 7 l 1 0 0 1 -1 0 Z M 5 7 l 1 0 0 1 -1 0 Z M 6 7 l 1 0 0 1 -1 0 Z M 7 7 l 1 0 0 1 -1 0 Z  M 9 7 l 1 0 0 1 -1 0 Z  M 11 7 l 1 0 0 1 -1 0 Z  M 13 7 l 1 0 0 1 -1 0 Z M 14 7 l 1 0 0 1 -1 0 Z  M 16 7 l 1 0 0 1 -1 0 Z M 17 7 l 1 0 0 1 -1 0 Z M 18 7 l 1 0 0 1 -1 0 Z M 19 7 l 1 0 0 1 -1 0 Z M 20 7 l 1 0 0 1 -1 0 Z M 21 7 l 1 0 0 1 -1 0 Z M 22 7 l 1 0 0 1 -1 0 Z M 23 7 l 1 0 0 1 -1 0 Z M 24 7 l 1 0 0 1 -1 0 Z    M 3 8 l 1 0 0 1 -1 0 Z M 4 8 l 1 0 0 1 -1 0 Z   M 7 8 l 1 0 0 1 -1 0 Z      M 13 8 l 1 0 0 1 -1 0 Z   M 16 8 l 1 0 0 1 -1 0 Z     M 21 8 l 1 0 0 1 -1 0 Z M 22 8 l 1 0 0 1 -1 0 Z    M 1 9 l 1 0 0 1 -1 0 Z   M 4 9 l 1 0 0 1 -1 0 Z M 5 9 l 1 0 0 1 -1 0 Z M 6 9 l 1 0 0 1 -1 0 Z    M 10 9 l 1 0 0 1 -1 0 Z    M 14 9 l 1 0 0 1 -1 0 Z  M 16 9 l 1 0 0 1 -1 0 Z M 17 9 l 1 0 0 1 -1 0 Z  M 19 9 l 1 0 0 1 -1 0 Z M 20 9 l 1 0 0 1 -1 0 Z   M 23 9 l 1 0 0 1 -1 0 Z M 24 9 l 1 0 0 1 -1 0 Z M 0 10 l 1 0 0 1 -1 0 Z  M 2 10 l 1 0 0 1 -1 0 Z   M 5 10 l 1 0 0 1 -1 0 Z    M 9 10 l 1 0 0 1 -1 0 Z M 10 10 l 1 0 0 1 -1 0 Z    M 14 10 l 1 0 0 1 -1 0 Z   M 17 10 l 1 0 0 1 -1 0 Z M 18 10 l 1 0 0 1 -1 0 Z M 19 10 l 1 0 0 1 -1 0 Z    M 23 10 l 1 0 0 1 -1 0 Z  M 0 11 l 1 0 0 1 -1 0 Z  M 2 11 l 1 0 0 1 -1 0 Z   M 5 11 l 1 0 0 1 -1 0 Z M 6 11 l 1 0 0 1 -1 0 Z M 7 11 l 1 0 0 1 -1 0 Z M 8 11 l 1 0 0 1 -1 0 Z  M 10 11 l 1 0 0 1 -1 0 Z  M 12 11 l 1 0 0 1 -1 0 Z M 13 11 l 1 0 0 1 -1 0 Z M 14 11 l 1 0 0 1 -1 0 Z  M 16 11 l 1 0 0 1 -1 0 Z M 17 11 l 1 0 0 1 -1 0 Z M 18 11 l 1 0 0 1 -1 0 Z  M 20 11 l 1 0 0 1 -1 0 Z M 21 11 l 1 0 0 1 -1 0 Z M 22 11 l 1 0 0 1 -1 0 Z   M 0 12 l 1 0 0 1 -1 0 Z M 1 12 l 1 0 0 1 -1 0 Z  M 3 12 l 1 0 0 1 -1 0 Z M 4 12 l 1 0 0 1 -1 0 Z      M 10 12 l 1 0 0 1 -1 0 Z  M 12 12 l 1 0 0 1 -1 0 Z M 13 12 l 1 0 0 1 -1 0 Z M 14 12 l 1 0 0 1 -1 0 Z  M 16 12 l 1 0 0 1 -1 0 Z M 17 12 l 1 0 0 1 -1 0 Z  M 19 12 l 1 0 0 1 -1 0 Z M 20 12 l 1 0 0 1 -1 0 Z  M 22 12 l 1 0 0 1 -1 0 Z   M 0 13 l 1 0 0 1 -1 0 Z  M 2 13 l 1 0 0 1 -1 0 Z   M 5 13 l 1 0 0 1 -1 0 Z M 6 13 l 1 0 0 1 -1 0 Z M 7 13 l 1 0 0 1 -1 0 Z   M 10 13 l 1 0 0 1 -1 0 Z   M 13 13 l 1 0 0 1 -1 0 Z   M 16 13 l 1 0 0 1 -1 0 Z  M 18 13 l 1 0 0 1 -1 0 Z M 19 13 l 1 0 0 1 -1 0 Z M 20 13 l 1 0 0 1 -1 0 Z    M 24 13 l 1 0 0 1 -1 0 Z      M 5 14 l 1 0 0 1 -1 0 Z  M 7 14 l 1 0 0 1 -1 0 Z  M 9 14 l 1 0 0 1 -1 0 Z M 10 14 l 1 0 0 1 -1 0 Z  M 12 14 l 1 0 0 1 -1 0 Z    M 16 14 l 1 0 0 1 -1 0 Z     M 21 14 l 1 0 0 1 -1 0 Z  M 23 14 l 1 0 0 1 -1 0 Z  M 0 15 l 1 0 0 1 -1 0 Z M 1 15 l 1 0 0 1 -1 0 Z   M 4 15 l 1 0 0 1 -1 0 Z M 5 15 l 1 0 0 1 -1 0 Z M 6 15 l 1 0 0 1 -1 0 Z M 7 15 l 1 0 0 1 -1 0 Z  M 9 15 l 1 0 0 1 -1 0 Z    M 13 15 l 1 0 0 1 -1 0 Z M 14 15 l 1 0 0 1 -1 0 Z M 15 15 l 1 0 0 1 -1 0 Z M 16 15 l 1 0 0 1 -1 0 Z M 17 15 l 1 0 0 1 -1 0 Z M 18 15 l 1 0 0 1 -1 0 Z  M 20 15 l 1 0 0 1 -1 0 Z M 21 15 l 1 0 0 1 -1 0 Z M 22 15 l 1 0 0 1 -1 0 Z M 23 15 l 1 0 0 1 -1 0 Z M 24 15 l 1 0 0 1 -1 0 Z   M 2 16 l 1 0 0 1 -1 0 Z   M 5 16 l 1 0 0 1 -1 0 Z  M 7 16 l 1 0 0 1 -1 0 Z  M 9 16 l 1 0 0 1 -1 0 Z  M 11 16 l 1 0 0 1 -1 0 Z  M 13 16 l 1 0 0 1 -1 0 Z M 14 16 l 1 0 0 1 -1 0 Z       M 21 16 l 1 0 0 1 -1 0 Z M 22 16 l 1 0 0 1 -1 0 Z   M 0 17 l 1 0 0 1 -1 0 Z M 1 17 l 1 0 0 1 -1 0 Z M 2 17 l 1 0 0 1 -1 0 Z M 3 17 l 1 0 0 1 -1 0 Z M 4 17 l 1 0 0 1 -1 0 Z M 5 17 l 1 0 0 1 -1 0 Z M 6 17 l 1 0 0 1 -1 0 Z M 7 17 l 1 0 0 1 -1 0 Z      M 13 17 l 1 0 0 1 -1 0 Z M 14 17 l 1 0 0 1 -1 0 Z   M 17 17 l 1 0 0 1 -1 0 Z M 18 17 l 1 0 0 1 -1 0 Z M 19 17 l 1 0 0 1 -1 0 Z  M 21 17 l 1 0 0 1 -1 0 Z M 22 17 l 1 0 0 1 -1 0 Z  M 24 17 l 1 0 0 1 -1 0 Z        M 7 18 l 1 0 0 1 -1 0 Z M 8 18 l 1 0 0 1 -1 0 Z M 9 18 l 1 0 0 1 -1 0 Z   M 12 18 l 1 0 0 1 -1 0 Z   M 15 18 l 1 0 0 1 -1 0 Z  M 17 18 l 1 0 0 1 -1 0 Z  M 19 18 l 1 0 0 1 -1 0 Z  M 21 18 l 1 0 0 1 -1 0 Z  M 23 18 l 1 0 0 1 -1 0 Z   M 1 19 l 1 0 0 1 -1 0 Z M 2 19 l 1 0 0 1 -1 0 Z M 3 19 l 1 0 0 1 -1 0 Z M 4 19 l 1 0 0 1 -1 0 Z M 5 19 l 1 0 0 1 -1 0 Z  M 7 19 l 1 0 0 1 -1 0 Z      M 13 19 l 1 0 0 1 -1 0 Z  M 15 19 l 1 0 0 1 -1 0 Z  M 17 19 l 1 0 0 1 -1 0 Z M 18 19 l 1 0 0 1 -1 0 Z M 19 19 l 1 0 0 1 -1 0 Z  M 21 19 l 1 0 0 1 -1 0 Z M 22 19 l 1 0 0 1 -1 0 Z    M 1 20 l 1 0 0 1 -1 0 Z    M 5 20 l 1 0 0 1 -1 0 Z  M 7 20 l 1 0 0 1 -1 0 Z M 8 20 l 1 0 0 1 -1 0 Z  M 10 20 l 1 0 0 1 -1 0 Z  M 12 20 l 1 0 0 1 -1 0 Z M 13 20 l 1 0 0 1 -1 0 Z M 14 20 l 1 0 0 1 -1 0 Z       M 21 20 l 1 0 0 1 -1 0 Z M 22 20 l 1 0 0 1 -1 0 Z M 23 20 l 1 0 0 1 -1 0 Z M 24 20 l 1 0 0 1 -1 0 Z  M 1 21 l 1 0 0 1 -1 0 Z    M 5 21 l 1 0 0 1 -1 0 Z  M 7 21 l 1 0 0 1 -1 0 Z M 8 21 l 1 0 0 1 -1 0 Z M 9 21 l 1 0 0 1 -1 0 Z    M 13 21 l 1 0 0 1 -1 0 Z   M 16 21 l 1 0 0 1 -1 0 Z  M 18 21 l 1 0 0 1 -1 0 Z   M 21 21 l 1 0 0 1 -1 0 Z M 22 21 l 1 0 0 1 -1 0 Z  M 24 21 l 1 0 0 1 -1 0 Z  M 1 22 l 1 0 0 1 -1 0 Z    M 5 22 l 1 0 0 1 -1 0 Z  M 7 22 l 1 0 0 1 -1 0 Z  M 9 22 l 1 0 0 1 -1 0 Z   M 12 22 l 1 0 0 1 -1 0 Z M 13 22 l 1 0 0 1 -1 0 Z  M 15 22 l 1 0 0 1 -1 0 Z M 16 22 l 1 0 0 1 -1 0 Z M 17 22 l 1 0 0 1 -1 0 Z     M 22 22 l 1 0 0 1 -1 0 Z    M 1 23 l 1 0 0 1 -1 0 Z M 2 23 l 1 0 0 1 -1 0 Z M 3 23 l 1 0 0 1 -1 0 Z M 4 23 l 1 0 0 1 -1 0 Z M 5 23 l 1 0 0 1 -1 0 Z  M 7 23 l 1 0 0 1 -1 0 Z   M 10 23 l 1 0 0 1 -1 0 Z  M 12 23 l 1 0 0 1 -1 0 Z M 13 23 l 1 0 0 1 -1 0 Z  M 15 23 l 1 0 0 1 -1 0 Z  M 17 23 l 1 0 0 1 -1 0 Z M 18 23 l 1 0 0 1 -1 0 Z   M 21 23 l 1 0 0 1 -1 0 Z M 22 23 l 1 0 0 1 -1 0 Z M 23 23 l 1 0 0 1 -1 0 Z M 24 23 l 1 0 0 1 -1 0 Z        M 7 24 l 1 0 0 1 -1 0 Z  M 9 24 l 1 0 0 1 -1 0 Z M 10 24 l 1 0 0 1 -1 0 Z  M 12 24 l 1 0 0 1 -1 0 Z M 13 24 l 1 0 0 1 -1 0 Z M 14 24 l 1 0 0 1 -1 0 Z       M 21 24 l 1 0 0 1 -1 0 Z M 22 24 l 1 0 0 1 -1 0 Z M 23 24 l 1 0 0 1 -1 0 Z "
              fill="#FFFFFF"
            />
            <path
              d="M 0 0 l 1 0 0 1 -1 0 Z M 1 0 l 1 0 0 1 -1 0 Z M 2 0 l 1 0 0 1 -1 0 Z M 3 0 l 1 0 0 1 -1 0 Z M 4 0 l 1 0 0 1 -1 0 Z M 5 0 l 1 0 0 1 -1 0 Z M 6 0 l 1 0 0 1 -1 0 Z  M 8 0 l 1 0 0 1 -1 0 Z M 9 0 l 1 0 0 1 -1 0 Z   M 12 0 l 1 0 0 1 -1 0 Z M 13 0 l 1 0 0 1 -1 0 Z   M 16 0 l 1 0 0 1 -1 0 Z  M 18 0 l 1 0 0 1 -1 0 Z M 19 0 l 1 0 0 1 -1 0 Z M 20 0 l 1 0 0 1 -1 0 Z M 21 0 l 1 0 0 1 -1 0 Z M 22 0 l 1 0 0 1 -1 0 Z M 23 0 l 1 0 0 1 -1 0 Z M 24 0 l 1 0 0 1 -1 0 Z M 0 1 l 1 0 0 1 -1 0 Z      M 6 1 l 1 0 0 1 -1 0 Z  M 8 1 l 1 0 0 1 -1 0 Z M 9 1 l 1 0 0 1 -1 0 Z   M 12 1 l 1 0 0 1 -1 0 Z  M 14 1 l 1 0 0 1 -1 0 Z    M 18 1 l 1 0 0 1 -1 0 Z      M 24 1 l 1 0 0 1 -1 0 Z M 0 2 l 1 0 0 1 -1 0 Z  M 2 2 l 1 0 0 1 -1 0 Z M 3 2 l 1 0 0 1 -1 0 Z M 4 2 l 1 0 0 1 -1 0 Z  M 6 2 l 1 0 0 1 -1 0 Z   M 9 2 l 1 0 0 1 -1 0 Z M 10 2 l 1 0 0 1 -1 0 Z M 11 2 l 1 0 0 1 -1 0 Z M 12 2 l 1 0 0 1 -1 0 Z M 13 2 l 1 0 0 1 -1 0 Z  M 15 2 l 1 0 0 1 -1 0 Z   M 18 2 l 1 0 0 1 -1 0 Z  M 20 2 l 1 0 0 1 -1 0 Z M 21 2 l 1 0 0 1 -1 0 Z M 22 2 l 1 0 0 1 -1 0 Z  M 24 2 l 1 0 0 1 -1 0 Z M 0 3 l 1 0 0 1 -1 0 Z  M 2 3 l 1 0 0 1 -1 0 Z M 3 3 l 1 0 0 1 -1 0 Z M 4 3 l 1 0 0 1 -1 0 Z  M 6 3 l 1 0 0 1 -1 0 Z   M 9 3 l 1 0 0 1 -1 0 Z   M 12 3 l 1 0 0 1 -1 0 Z M 13 3 l 1 0 0 1 -1 0 Z   M 16 3 l 1 0 0 1 -1 0 Z  M 18 3 l 1 0 0 1 -1 0 Z  M 20 3 l 1 0 0 1 -1 0 Z M 21 3 l 1 0 0 1 -1 0 Z M 22 3 l 1 0 0 1 -1 0 Z  M 24 3 l 1 0 0 1 -1 0 Z M 0 4 l 1 0 0 1 -1 0 Z  M 2 4 l 1 0 0 1 -1 0 Z M 3 4 l 1 0 0 1 -1 0 Z M 4 4 l 1 0 0 1 -1 0 Z  M 6 4 l 1 0 0 1 -1 0 Z  M 8 4 l 1 0 0 1 -1 0 Z M 9 4 l 1 0 0 1 -1 0 Z   M 12 4 l 1 0 0 1 -1 0 Z M 13 4 l 1 0 0 1 -1 0 Z   M 16 4 l 1 0 0 1 -1 0 Z  M 18 4 l 1 0 0 1 -1 0 Z  M 20 4 l 1 0 0 1 -1 0 Z M 21 4 l 1 0 0 1 -1 0 Z M 22 4 l 1 0 0 1 -1 0 Z  M 24 4 l 1 0 0 1 -1 0 Z M 0 5 l 1 0 0 1 -1 0 Z      M 6 5 l 1 0 0 1 -1 0 Z  M 8 5 l 1 0 0 1 -1 0 Z M 9 5 l 1 0 0 1 -1 0 Z    M 13 5 l 1 0 0 1 -1 0 Z M 14 5 l 1 0 0 1 -1 0 Z  M 16 5 l 1 0 0 1 -1 0 Z  M 18 5 l 1 0 0 1 -1 0 Z      M 24 5 l 1 0 0 1 -1 0 Z M 0 6 l 1 0 0 1 -1 0 Z M 1 6 l 1 0 0 1 -1 0 Z M 2 6 l 1 0 0 1 -1 0 Z M 3 6 l 1 0 0 1 -1 0 Z M 4 6 l 1 0 0 1 -1 0 Z M 5 6 l 1 0 0 1 -1 0 Z M 6 6 l 1 0 0 1 -1 0 Z  M 8 6 l 1 0 0 1 -1 0 Z  M 10 6 l 1 0 0 1 -1 0 Z  M 12 6 l 1 0 0 1 -1 0 Z  M 14 6 l 1 0 0 1 -1 0 Z  M 16 6 l 1 0 0 1 -1 0 Z  M 18 6 l 1 0 0 1 -1 0 Z M 19 6 l 1 0 0 1 -1 0 Z M 20 6 l 1 0 0 1 -1 0 Z M 21 6 l 1 0 0 1 -1 0 Z M 22 6 l 1 0 0 1 -1 0 Z M 23 6 l 1 0 0 1 -1 0 Z M 24 6 l 1 0 0 1 -1 0 Z         M 8 7 l 1 0 0 1 -1 0 Z  M 10 7 l 1 0 0 1 -1 0 Z  M 12 7 l 1 0 0 1 -1 0 Z   M 15 7 l 1 0 0 1 -1 0 Z          M 0 8 l 1 0 0 1 -1 0 Z M 1 8 l 1 0 0 1 -1 0 Z M 2 8 l 1 0 0 1 -1 0 Z   M 5 8 l 1 0 0 1 -1 0 Z M 6 8 l 1 0 0 1 -1 0 Z  M 8 8 l 1 0 0 1 -1 0 Z M 9 8 l 1 0 0 1 -1 0 Z M 10 8 l 1 0 0 1 -1 0 Z M 11 8 l 1 0 0 1 -1 0 Z M 12 8 l 1 0 0 1 -1 0 Z  M 14 8 l 1 0 0 1 -1 0 Z M 15 8 l 1 0 0 1 -1 0 Z  M 17 8 l 1 0 0 1 -1 0 Z M 18 8 l 1 0 0 1 -1 0 Z M 19 8 l 1 0 0 1 -1 0 Z M 20 8 l 1 0 0 1 -1 0 Z   M 23 8 l 1 0 0 1 -1 0 Z M 24 8 l 1 0 0 1 -1 0 Z M 0 9 l 1 0 0 1 -1 0 Z  M 2 9 l 1 0 0 1 -1 0 Z M 3 9 l 1 0 0 1 -1 0 Z    M 7 9 l 1 0 0 1 -1 0 Z M 8 9 l 1 0 0 1 -1 0 Z M 9 9 l 1 0 0 1 -1 0 Z  M 11 9 l 1 0 0 1 -1 0 Z M 12 9 l 1 0 0 1 -1 0 Z M 13 9 l 1 0 0 1 -1 0 Z  M 15 9 l 1 0 0 1 -1 0 Z   M 18 9 l 1 0 0 1 -1 0 Z   M 21 9 l 1 0 0 1 -1 0 Z M 22 9 l 1 0 0 1 -1 0 Z    M 1 10 l 1 0 0 1 -1 0 Z  M 3 10 l 1 0 0 1 -1 0 Z M 4 10 l 1 0 0 1 -1 0 Z  M 6 10 l 1 0 0 1 -1 0 Z M 7 10 l 1 0 0 1 -1 0 Z M 8 10 l 1 0 0 1 -1 0 Z   M 11 10 l 1 0 0 1 -1 0 Z M 12 10 l 1 0 0 1 -1 0 Z M 13 10 l 1 0 0 1 -1 0 Z  M 15 10 l 1 0 0 1 -1 0 Z M 16 10 l 1 0 0 1 -1 0 Z    M 20 10 l 1 0 0 1 -1 0 Z M 21 10 l 1 0 0 1 -1 0 Z M 22 10 l 1 0 0 1 -1 0 Z  M 24 10 l 1 0 0 1 -1 0 Z  M 1 11 l 1 0 0 1 -1 0 Z  M 3 11 l 1 0 0 1 -1 0 Z M 4 11 l 1 0 0 1 -1 0 Z     M 9 11 l 1 0 0 1 -1 0 Z  M 11 11 l 1 0 0 1 -1 0 Z    M 15 11 l 1 0 0 1 -1 0 Z    M 19 11 l 1 0 0 1 -1 0 Z    M 23 11 l 1 0 0 1 -1 0 Z M 24 11 l 1 0 0 1 -1 0 Z   M 2 12 l 1 0 0 1 -1 0 Z   M 5 12 l 1 0 0 1 -1 0 Z M 6 12 l 1 0 0 1 -1 0 Z M 7 12 l 1 0 0 1 -1 0 Z M 8 12 l 1 0 0 1 -1 0 Z M 9 12 l 1 0 0 1 -1 0 Z  M 11 12 l 1 0 0 1 -1 0 Z    M 15 12 l 1 0 0 1 -1 0 Z   M 18 12 l 1 0 0 1 -1 0 Z   M 21 12 l 1 0 0 1 -1 0 Z  M 23 12 l 1 0 0 1 -1 0 Z M 24 12 l 1 0 0 1 -1 0 Z  M 1 13 l 1 0 0 1 -1 0 Z  M 3 13 l 1 0 0 1 -1 0 Z M 4 13 l 1 0 0 1 -1 0 Z    M 8 13 l 1 0 0 1 -1 0 Z M 9 13 l 1 0 0 1 -1 0 Z  M 11 13 l 1 0 0 1 -1 0 Z M 12 13 l 1 0 0 1 -1 0 Z  M 14 13 l 1 0 0 1 -1 0 Z M 15 13 l 1 0 0 1 -1 0 Z  M 17 13 l 1 0 0 1 -1 0 Z    M 21 13 l 1 0 0 1 -1 0 Z M 22 13 l 1 0 0 1 -1 0 Z M 23 13 l 1 0 0 1 -1 0 Z  M 0 14 l 1 0 0 1 -1 0 Z M 1 14 l 1 0 0 1 -1 0 Z M 2 14 l 1 0 0 1 -1 0 Z M 3 14 l 1 0 0 1 -1 0 Z M 4 14 l 1 0 0 1 -1 0 Z  M 6 14 l 1 0 0 1 -1 0 Z  M 8 14 l 1 0 0 1 -1 0 Z   M 11 14 l 1 0 0 1 -1 0 Z  M 13 14 l 1 0 0 1 -1 0 Z M 14 14 l 1 0 0 1 -1 0 Z M 15 14 l 1 0 0 1 -1 0 Z  M 17 14 l 1 0 0 1 -1 0 Z M 18 14 l 1 0 0 1 -1 0 Z M 19 14 l 1 0 0 1 -1 0 Z M 20 14 l 1 0 0 1 -1 0 Z  M 22 14 l 1 0 0 1 -1 0 Z  M 24 14 l 1 0 0 1 -1 0 Z   M 2 15 l 1 0 0 1 -1 0 Z M 3 15 l 1 0 0 1 -1 0 Z     M 8 15 l 1 0 0 1 -1 0 Z  M 10 15 l 1 0 0 1 -1 0 Z M 11 15 l 1 0 0 1 -1 0 Z M 12 15 l 1 0 0 1 -1 0 Z       M 19 15 l 1 0 0 1 -1 0 Z      M 0 16 l 1 0 0 1 -1 0 Z M 1 16 l 1 0 0 1 -1 0 Z  M 3 16 l 1 0 0 1 -1 0 Z M 4 16 l 1 0 0 1 -1 0 Z  M 6 16 l 1 0 0 1 -1 0 Z  M 8 16 l 1 0 0 1 -1 0 Z  M 10 16 l 1 0 0 1 -1 0 Z  M 12 16 l 1 0 0 1 -1 0 Z   M 15 16 l 1 0 0 1 -1 0 Z M 16 16 l 1 0 0 1 -1 0 Z M 17 16 l 1 0 0 1 -1 0 Z M 18 16 l 1 0 0 1 -1 0 Z M 19 16 l 1 0 0 1 -1 0 Z M 20 16 l 1 0 0 1 -1 0 Z   M 23 16 l 1 0 0 1 -1 0 Z M 24 16 l 1 0 0 1 -1 0 Z         M 8 17 l 1 0 0 1 -1 0 Z M 9 17 l 1 0 0 1 -1 0 Z M 10 17 l 1 0 0 1 -1 0 Z M 11 17 l 1 0 0 1 -1 0 Z M 12 17 l 1 0 0 1 -1 0 Z   M 15 17 l 1 0 0 1 -1 0 Z M 16 17 l 1 0 0 1 -1 0 Z    M 20 17 l 1 0 0 1 -1 0 Z   M 23 17 l 1 0 0 1 -1 0 Z  M 0 18 l 1 0 0 1 -1 0 Z M 1 18 l 1 0 0 1 -1 0 Z M 2 18 l 1 0 0 1 -1 0 Z M 3 18 l 1 0 0 1 -1 0 Z M 4 18 l 1 0 0 1 -1 0 Z M 5 18 l 1 0 0 1 -1 0 Z M 6 18 l 1 0 0 1 -1 0 Z    M 10 18 l 1 0 0 1 -1 0 Z M 11 18 l 1 0 0 1 -1 0 Z  M 13 18 l 1 0 0 1 -1 0 Z M 14 18 l 1 0 0 1 -1 0 Z  M 16 18 l 1 0 0 1 -1 0 Z  M 18 18 l 1 0 0 1 -1 0 Z  M 20 18 l 1 0 0 1 -1 0 Z  M 22 18 l 1 0 0 1 -1 0 Z  M 24 18 l 1 0 0 1 -1 0 Z M 0 19 l 1 0 0 1 -1 0 Z      M 6 19 l 1 0 0 1 -1 0 Z  M 8 19 l 1 0 0 1 -1 0 Z M 9 19 l 1 0 0 1 -1 0 Z M 10 19 l 1 0 0 1 -1 0 Z M 11 19 l 1 0 0 1 -1 0 Z M 12 19 l 1 0 0 1 -1 0 Z  M 14 19 l 1 0 0 1 -1 0 Z  M 16 19 l 1 0 0 1 -1 0 Z    M 20 19 l 1 0 0 1 -1 0 Z   M 23 19 l 1 0 0 1 -1 0 Z M 24 19 l 1 0 0 1 -1 0 Z M 0 20 l 1 0 0 1 -1 0 Z  M 2 20 l 1 0 0 1 -1 0 Z M 3 20 l 1 0 0 1 -1 0 Z M 4 20 l 1 0 0 1 -1 0 Z  M 6 20 l 1 0 0 1 -1 0 Z   M 9 20 l 1 0 0 1 -1 0 Z  M 11 20 l 1 0 0 1 -1 0 Z    M 15 20 l 1 0 0 1 -1 0 Z M 16 20 l 1 0 0 1 -1 0 Z M 17 20 l 1 0 0 1 -1 0 Z M 18 20 l 1 0 0 1 -1 0 Z M 19 20 l 1 0 0 1 -1 0 Z M 20 20 l 1 0 0 1 -1 0 Z     M 0 21 l 1 0 0 1 -1 0 Z  M 2 21 l 1 0 0 1 -1 0 Z M 3 21 l 1 0 0 1 -1 0 Z M 4 21 l 1 0 0 1 -1 0 Z  M 6 21 l 1 0 0 1 -1 0 Z    M 10 21 l 1 0 0 1 -1 0 Z M 11 21 l 1 0 0 1 -1 0 Z M 12 21 l 1 0 0 1 -1 0 Z  M 14 21 l 1 0 0 1 -1 0 Z M 15 21 l 1 0 0 1 -1 0 Z  M 17 21 l 1 0 0 1 -1 0 Z  M 19 21 l 1 0 0 1 -1 0 Z M 20 21 l 1 0 0 1 -1 0 Z   M 23 21 l 1 0 0 1 -1 0 Z  M 0 22 l 1 0 0 1 -1 0 Z  M 2 22 l 1 0 0 1 -1 0 Z M 3 22 l 1 0 0 1 -1 0 Z M 4 22 l 1 0 0 1 -1 0 Z  M 6 22 l 1 0 0 1 -1 0 Z  M 8 22 l 1 0 0 1 -1 0 Z  M 10 22 l 1 0 0 1 -1 0 Z M 11 22 l 1 0 0 1 -1 0 Z   M 14 22 l 1 0 0 1 -1 0 Z    M 18 22 l 1 0 0 1 -1 0 Z M 19 22 l 1 0 0 1 -1 0 Z M 20 22 l 1 0 0 1 -1 0 Z M 21 22 l 1 0 0 1 -1 0 Z  M 23 22 l 1 0 0 1 -1 0 Z M 24 22 l 1 0 0 1 -1 0 Z M 0 23 l 1 0 0 1 -1 0 Z      M 6 23 l 1 0 0 1 -1 0 Z  M 8 23 l 1 0 0 1 -1 0 Z M 9 23 l 1 0 0 1 -1 0 Z  M 11 23 l 1 0 0 1 -1 0 Z   M 14 23 l 1 0 0 1 -1 0 Z  M 16 23 l 1 0 0 1 -1 0 Z   M 19 23 l 1 0 0 1 -1 0 Z M 20 23 l 1 0 0 1 -1 0 Z     M 0 24 l 1 0 0 1 -1 0 Z M 1 24 l 1 0 0 1 -1 0 Z M 2 24 l 1 0 0 1 -1 0 Z M 3 24 l 1 0 0 1 -1 0 Z M 4 24 l 1 0 0 1 -1 0 Z M 5 24 l 1 0 0 1 -1 0 Z M 6 24 l 1 0 0 1 -1 0 Z  M 8 24 l 1 0 0 1 -1 0 Z   M 11 24 l 1 0 0 1 -1 0 Z    M 15 24 l 1 0 0 1 -1 0 Z M 16 24 l 1 0 0 1 -1 0 Z M 17 24 l 1 0 0 1 -1 0 Z M 18 24 l 1 0 0 1 -1 0 Z M 19 24 l 1 0 0 1 -1 0 Z M 20 24 l 1 0 0 1 -1 0 Z    M 24 24 l 1 0 0 1 -1 0 Z"
              fill="#000000"
            />
          </svg>
        </div>
      </section>
    </div>
    <div
      class="pb-2"
    />
    <div
      class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 print:grid-cols-3 gap-2"
    >
      <section
        class="p-3 rounded-sm bg-white"
      >
        <div
          class="flex justify-between items-baseline"
        >
          <h3
            class="text-lg font-bold pb-4"
          >
            Installation details
          </h3>
          <a
            class="text-primary underline hover:decoration-2 focus:decoration-2 focus:outline-none cursor-pointer text-right ml-2"
            href="/chargers/PSL-12345/installation?socket=A"
          >
            View installation details
          </a>
        </div>
        <div
          class="flex flex-col bg-white rounded-sm overflow-hidden overflow-x-auto"
        >
          <div
            class="inline-block min-w-full -my-2 py-2 align-middle"
          >
            <table
              class="min-w-full"
              tabindex="0"
            >
              <caption
                class="sr-only"
              >
                Installation details table
              </caption>
              <tbody
                class="divide-y"
              >
                <tr
                  class="border-b-neutral/20"
                >
                  <td
                    class="whitespace-normal px-3 py-4 pl-0 pr-2 pt-2 pb-2 whitespace-pre"
                  >
                    Install date
                  </td>
                  <td
                    class="whitespace-normal px-3 py-4 pr-0 pl-2 pt-2 pb-2 text-neutral text-right"
                  >
                    7 July 2023
                  </td>
                </tr>
                <tr
                  class="border-b-neutral/20"
                >
                  <td
                    class="whitespace-normal px-3 py-4 pl-0 pr-2 pt-2 pb-2 whitespace-pre"
                  >
                    Installed by
                  </td>
                  <td
                    class="whitespace-normal px-3 py-4 pr-0 pl-2 pt-2 pb-2 text-neutral text-right"
                  >
                    Joe Spark
                  </td>
                </tr>
                <tr
                  class="border-b-neutral/20"
                >
                  <td
                    class="whitespace-normal px-3 py-4 pl-0 pr-2 pt-2 pb-2 whitespace-pre"
                  >
                    Company
                  </td>
                  <td
                    class="whitespace-normal px-3 py-4 pr-0 pl-2 pt-2 pb-2 text-neutral text-right"
                  >
                    Electrical Services Ltd.
                  </td>
                </tr>
              </tbody>
            </table>
          </div>
        </div>
      </section>
      <section
        class="p-3 rounded-sm bg-white"
      >
        <h3
          class="text-lg font-bold pb-4"
        >
          Warranty details
        </h3>
        <div
          class="flex flex-col bg-white rounded-sm overflow-hidden overflow-x-auto"
        >
          <div
            class="inline-block min-w-full -my-2 py-2 align-middle"
          >
            <table
              class="min-w-full"
              tabindex="0"
            >
              <caption
                class="sr-only"
              >
                Warranty details table
              </caption>
              <tbody
                class="divide-y"
              >
                <tr
                  class="border-b-neutral/20"
                >
                  <td
                    class="whitespace-normal px-3 py-4 pl-0 pr-2 pt-2 pb-2 whitespace-pre"
                  >
                    Warranty start date
                  </td>
                  <td
                    class="whitespace-normal px-3 py-4 pr-0 pl-2 pt-2 pb-2 text-neutral text-right"
                  >
                    1 August 2024
                  </td>
                </tr>
                <tr
                  class="border-b-neutral/20"
                >
                  <td
                    class="whitespace-normal px-3 py-4 pl-0 pr-2 pt-2 pb-2 whitespace-pre"
                  >
                    Warranty end date
                  </td>
                  <td
                    class="whitespace-normal px-3 py-4 pr-0 pl-2 pt-2 pb-2 text-neutral text-right"
                  >
                    1st August 2027
                  </td>
                </tr>
                <tr
                  class="border-b-neutral/20"
                >
                  <td
                    class="whitespace-normal px-3 py-4 pl-0 pr-2 pt-2 pb-2 whitespace-pre"
                  >
                    Warranty status
                  </td>
                  <td
                    class="whitespace-normal px-3 py-4 pr-0 pl-2 pt-2 pb-2 text-neutral text-right"
                  >
                    <span
                      aria-label="Active"
                      class="font-bold px-2.5 py-1 rounded-xs inline-flex items-center tracking-wide bg-success/10 border border-success text-success"
                      role="status"
                    >
                      Active
                    </span>
                  </td>
                </tr>
              </tbody>
            </table>
          </div>
        </div>
      </section>
      <section
        class="p-3 rounded-sm bg-white"
      >
        <div
          class="flex justify-between items-baseline"
        >
          <h3
            class="text-lg font-bold pb-4"
          >
            Last complete charge
          </h3>
          <a
            class="text-primary underline hover:decoration-2 focus:decoration-2 focus:outline-none cursor-pointer text-right ml-2"
            href="/chargers/PSL-12345/recent-charges?socket=A"
          >
            Recent charges
          </a>
        </div>
        <div
          class="flex flex-col bg-white rounded-sm overflow-hidden overflow-x-auto"
        >
          <div
            class="inline-block min-w-full -my-2 py-2 align-middle"
          >
            <table
              class="min-w-full"
              tabindex="0"
            >
              <caption
                class="sr-only"
              >
                Last complete charge table
              </caption>
              <tbody
                class="divide-y"
              >
                <tr
                  class="border-b-neutral/20"
                >
                  <td
                    class="whitespace-normal px-3 py-4 pl-0 pr-2 pt-2 pb-2 whitespace-pre"
                  >
                    Started at
                  </td>
                  <td
                    class="whitespace-normal px-3 py-4 pr-0 pl-2 pt-2 pb-2 text-neutral text-right"
                  >
                    30 June 2024 at 19:00:00
                  </td>
                </tr>
                <tr
                  class="border-b-neutral/20"
                >
                  <td
                    class="whitespace-normal px-3 py-4 pl-0 pr-2 pt-2 pb-2 whitespace-pre"
                  >
                    Ended at
                  </td>
                  <td
                    class="whitespace-normal px-3 py-4 pr-0 pl-2 pt-2 pb-2 text-neutral text-right"
                  >
                    30 June 2024 at 21:00:00
                  </td>
                </tr>
                <tr
                  class="border-b-neutral/20"
                >
                  <td
                    class="whitespace-normal px-3 py-4 pl-0 pr-2 pt-2 pb-2 whitespace-pre"
                  >
                    kWh delivered
                  </td>
                  <td
                    class="whitespace-normal px-3 py-4 pr-0 pl-2 pt-2 pb-2 text-neutral text-right"
                  >
                    567.89 kWh
                  </td>
                </tr>
              </tbody>
            </table>
          </div>
        </div>
      </section>
    </div>
    <div
      class="pb-2"
    />
    <section
      class="p-3 rounded-sm bg-white"
    >
      <div
        class="flex justify-between items-baseline"
      >
        <h3
          class="text-lg font-bold pb-4"
        >
          Modes
        </h3>
        <a
          class="text-primary underline hover:decoration-2 focus:decoration-2 focus:outline-none cursor-pointer text-right ml-2"
          href="/chargers/PSL-12345/mode-history"
        >
          View history
        </a>
      </div>
      <div
        class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 print:grid-cols-3 gap-2"
      >
        <div
          class="pb-2 break-words"
        >
          <div
            class="flex"
          >
            <div
              data-headlessui-state=""
            >
              <div
                class="mr-2"
              >
                <label
                  class="text-lg font-normal block mb-1.5"
                  for="toggle-smart-mode"
                  id="toggle-smart-mode-label"
                >
                  Smart mode
                </label>
              </div>
              <button
                aria-checked="true"
                aria-label="toggle-smart-mode"
                class="bg-primary group relative inline-flex h-6 w-11 shrink-0 cursor-pointer rounded-full border-2 border-transparent transition-colors duration-200 ease-in-out focus:outline-hidden focus:ring-2 focus:ring-primary/50 focus:ring-offset-2 data-[disabled]:bg-neutral/40 data-[disabled]:cursor-not-allowed"
                data-checked=""
                data-headlessui-state="checked"
                id="toggle-smart-mode"
                role="switch"
                tabindex="0"
                type="button"
              >
                <span
                  aria-hidden="true"
                  class="translate-x-5 pointer-events-none inline-block h-5 w-5 transform rounded-full bg-white shadow-sm ring-0 transition duration-200 ease-in-out group-data-[disabled]:bg-neutral/20 group-data-[disabled]:cursor-default"
                />
              </button>
              <span
                hidden=""
                style="position: fixed; top: 1px; left: 1px; width: 1px; height: 0px; padding: 0px; margin: -1px; overflow: hidden; clip: rect(0px, 0px, 0px, 0px); white-space: nowrap; border-width: 0px; display: none;"
              />
            </div>
          </div>
        </div>
        <div
          class="pb-2 break-words"
        >
          <div
            class="flex"
          >
            <div
              data-headlessui-state=""
            >
              <div
                class="mr-2"
              >
                <label
                  class="text-lg font-normal block mb-1.5"
                  for="toggle-off-mode"
                  id="toggle-off-mode-label"
                >
                  Off mode
                </label>
              </div>
              <button
                aria-checked="false"
                aria-label="toggle-off-mode"
                class="bg-neutral/20 group relative inline-flex h-6 w-11 shrink-0 cursor-pointer rounded-full border-2 border-transparent transition-colors duration-200 ease-in-out focus:outline-hidden focus:ring-2 focus:ring-primary/50 focus:ring-offset-2 data-[disabled]:bg-neutral/40 data-[disabled]:cursor-not-allowed"
                data-headlessui-state=""
                id="toggle-off-mode"
                role="switch"
                tabindex="0"
                type="button"
              >
                <span
                  aria-hidden="true"
                  class="translate-x-0 pointer-events-none inline-block h-5 w-5 transform rounded-full bg-white shadow-sm ring-0 transition duration-200 ease-in-out group-data-[disabled]:bg-neutral/20 group-data-[disabled]:cursor-default"
                />
              </button>
              <span
                hidden=""
                style="position: fixed; top: 1px; left: 1px; width: 1px; height: 0px; padding: 0px; margin: -1px; overflow: hidden; clip: rect(0px, 0px, 0px, 0px); white-space: nowrap; border-width: 0px; display: none;"
              />
            </div>
          </div>
        </div>
        <div
          class="pb-2 break-words"
        >
          <div
            class="flex"
          >
            <div
              data-headlessui-state=""
            >
              <div
                class="mr-2"
              >
                <label
                  class="text-lg font-normal block mb-1.5"
                  for="toggle-charge-now"
                  id="toggle-charge-now-label"
                >
                  Charge now
                </label>
              </div>
              <button
                aria-checked="false"
                aria-label="toggle-charge-now"
                class="bg-neutral/20 group relative inline-flex h-6 w-11 shrink-0 cursor-pointer rounded-full border-2 border-transparent transition-colors duration-200 ease-in-out focus:outline-hidden focus:ring-2 focus:ring-primary/50 focus:ring-offset-2 data-[disabled]:bg-neutral/40 data-[disabled]:cursor-not-allowed"
                data-headlessui-state=""
                id="toggle-charge-now"
                role="switch"
                tabindex="0"
                type="button"
              >
                <span
                  aria-hidden="true"
                  class="translate-x-0 pointer-events-none inline-block h-5 w-5 transform rounded-full bg-white shadow-sm ring-0 transition duration-200 ease-in-out group-data-[disabled]:bg-neutral/20 group-data-[disabled]:cursor-default"
                />
              </button>
              <span
                hidden=""
                style="position: fixed; top: 1px; left: 1px; width: 1px; height: 0px; padding: 0px; margin: -1px; overflow: hidden; clip: rect(0px, 0px, 0px, 0px); white-space: nowrap; border-width: 0px; display: none;"
              />
            </div>
          </div>
        </div>
        <div
          class="pb-2 break-words"
        >
          <h5
            class="text-lg"
          >
            Flex
          </h5>
          <span
            class="text-xl font-semibold"
          >
            <a
              class="text-primary underline hover:decoration-2 focus:decoration-2 focus:outline-none text-xl font-semibold hover:underline cursor-pointer"
              href="/chargers/PSL-12345/flex-details"
            >
              Enrolled
            </a>
          </span>
        </div>
        <div
          class="flex"
        >
          <div
            class="pb-2 break-words"
          >
            <h5
              class="text-lg"
            >
              Delegated control
            </h5>
            <div
              class="flex justify-between items-center"
            >
              <span
                class="text-xl font-semibold"
              >
                Active since 30 June 2024 at 21:00:00 (3rd Party)
              </span>
              <button
                aria-label="Remove delegated control"
                class="border-2 border-solid rounded-sm font-bold text-center disabled:no-underline transition ease-in-out duration-200 cursor-pointer disabled:cursor-not-allowed border-transparent text-primary hover:underline hover:text-primary active:font-bold active:text-primary disabled:text-neutral/40 disabled:hover:no-underline disabled:hover:border-transparent p-1.5"
                type="button"
              >
                <svg
                  class="h-4 w-4 stroke-1 stroke-current fill-current"
                  viewBox="0 0 32 32"
                  xmlns="http://www.w3.org/2000/svg"
                >
                  <title>
                    Cross
                  </title>
                  <g>
                    <path
                      d="M16,.07C7.22,.07,.07,7.22,.07,16s7.15,15.93,15.93,15.93,15.93-7.15,15.93-15.93S24.78,.07,16,.07Zm0,29.86c-7.68,0-13.93-6.25-13.93-13.93S8.32,2.07,16,2.07s13.93,6.25,13.93,13.93-6.25,13.93-13.93,13.93Z"
                    />
                    <path
                      d="M23.38,8.62c-.39-.39-1.02-.39-1.41,0l-5.96,5.96-5.96-5.96c-.39-.39-1.02-.39-1.41,0s-.39,1.02,0,1.41l5.96,5.96-5.96,5.96c-.39,.39-.39,1.02,0,1.41,.2,.2,.45,.29,.71,.29s.51-.1,.71-.29l5.96-5.96,5.96,5.96c.2,.2,.45,.29,.71,.29s.51-.1,.71-.29c.39-.39,.39-1.02,0-1.41l-5.96-5.96,5.96-5.96c.39-.39,.39-1.02,0-1.41Z"
                    />
                  </g>
                </svg>
              </button>
            </div>
          </div>
        </div>
      </div>
    </section>
    <div
      class="pb-2"
    />
    <section
      class="p-3 rounded-sm bg-white"
    >
      <div
        class="flex justify-between items-baseline"
      >
        <h3
          class="text-lg font-bold pb-4"
        >
          Schedules
        </h3>
        <a
          class="text-primary underline hover:decoration-2 focus:decoration-2 focus:outline-none cursor-pointer text-right ml-2"
          href="/chargers/PSL-12345/schedules"
        >
          View schedules
        </a>
      </div>
      <div
        class="rounded-md p-3 bg-info/10 border border-info"
      >
        <div
          class="flex"
        >
          <div
            class="shrink-0"
          >
            <span
              class="text-info"
            >
              <svg
                class="fill-current w-6 h-6"
                viewBox="0 0 32 32"
                xmlns="http://www.w3.org/2000/svg"
              >
                <g>
                  <path
                    d="M16.1,29.43A13.43,13.43,0,1,1,29.53,16,13.45,13.45,0,0,1,16.1,29.43Zm0-24.86A11.43,11.43,0,1,0,27.53,16,11.45,11.45,0,0,0,16.1,4.57Z"
                  />
                  <path
                    d="M16,24.12a1,1,0,0,1-1-1V14.94a1,1,0,1,1,2,0v8.18A1,1,0,0,1,16,24.12Z"
                  />
                  <path
                    d="M16.1,10.89a1,1,0,0,1-1-1v-.2a1,1,0,0,1,2,0v.2A1,1,0,0,1,16.1,10.89Z"
                  />
                </g>
              </svg>
            </span>
          </div>
          <div
            class="flex flex-col justify-center ml-4"
          >
            <p
              class="text-md font-normal sr-only break-words"
            >
              Alert
            </p>
            <div
              class="text-info"
            >
              <p
                class="text-md font-normal break-words"
              >
                This charger is currently under delegated control. The below schedules are indicative only and subject to change.
              </p>
            </div>
          </div>
        </div>
      </div>
      <div
        class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-2 gap-2"
      >
        <div
          class="flex flex-col bg-white rounded-sm overflow-hidden overflow-x-auto"
        >
          <div
            class="inline-block min-w-full -my-2 py-2 align-middle"
          >
            <table
              class="min-w-full"
              tabindex="0"
            >
              <caption
                class="sr-only"
              >
                Table of charge schedules and modes
              </caption>
              <thead
                class="border-b border-b-neutral/20"
              >
                <tr
                  class="border-b-neutral/20"
                >
                  <th
                    class="text-left p-3"
                    scope="col"
                  >
                    Start
                  </th>
                  <th
                    class="text-left p-3"
                    scope="col"
                  >
                    Duration
                  </th>
                  <th
                    class="text-left text-center p-3"
                    scope="col"
                  >
                    Active
                  </th>
                </tr>
              </thead>
              <tbody
                class="divide-y border-b border-b-neutral/20"
              >
                <tr
                  class="border-b-neutral/20"
                >
                  <td
                    class="whitespace-normal px-3 py-4"
                  >
                    <p
                      class="text-md font-normal break-words"
                    >
                      Monday
                    </p>
                    <p
                      class="text-md font-normal break-words"
                    >
                      10:00:00
                    </p>
                  </td>
                  <td
                    class="whitespace-normal px-3 py-4"
                  >
                    <p
                      class="text-md font-normal break-words"
                    >
                      4 hours 
                    </p>
                  </td>
                  <td
                    class="whitespace-normal px-3 py-4"
                  >
                    <svg
                      class="fill-current h-6 w-6 mx-auto text-success"
                      viewBox="0 0 32 32"
                      xmlns="http://www.w3.org/2000/svg"
                    >
                      <title>
                        Yes
                      </title>
                      <g>
                        <path
                          d="M16,31.85C7.26,31.85,.15,24.74,.15,16S7.26,.15,16,.15s15.85,7.11,15.85,15.85-7.11,15.85-15.85,15.85Zm0-29.7c-7.64,0-13.85,6.21-13.85,13.85s6.21,13.85,13.85,13.85,13.85-6.21,13.85-13.85S23.64,2.15,16,2.15Z"
                        />
                        <path
                          d="M13.12,22.93c-.27,0-.52-.11-.71-.29l-5.16-5.16c-.39-.39-.39-1.02,0-1.41s1.02-.39,1.41,0l4.45,4.45,9.77-9.77c.39-.39,1.02-.39,1.41,0s.39,1.02,0,1.41l-10.47,10.47c-.19,.19-.44,.29-.71,.29Z"
                        />
                      </g>
                    </svg>
                  </td>
                </tr>
                <tr
                  class="border-b-neutral/20"
                >
                  <td
                    class="whitespace-normal px-3 py-4"
                  >
                    <p
                      class="text-md font-normal break-words"
                    >
                      Tuesday
                    </p>
                    <p
                      class="text-md font-normal break-words"
                    >
                      20:35:00
                    </p>
                  </td>
                  <td
                    class="whitespace-normal px-3 py-4"
                  >
                    <p
                      class="text-md font-normal break-words"
                    >
                      3 hours 15 minutes
                    </p>
                  </td>
                  <td
                    class="whitespace-normal px-3 py-4"
                  >
                    <svg
                      class="fill-current h-6 w-6 mx-auto text-success"
                      viewBox="0 0 32 32"
                      xmlns="http://www.w3.org/2000/svg"
                    >
                      <title>
                        Yes
                      </title>
                      <g>
                        <path
                          d="M16,31.85C7.26,31.85,.15,24.74,.15,16S7.26,.15,16,.15s15.85,7.11,15.85,15.85-7.11,15.85-15.85,15.85Zm0-29.7c-7.64,0-13.85,6.21-13.85,13.85s6.21,13.85,13.85,13.85,13.85-6.21,13.85-13.85S23.64,2.15,16,2.15Z"
                        />
                        <path
                          d="M13.12,22.93c-.27,0-.52-.11-.71-.29l-5.16-5.16c-.39-.39-.39-1.02,0-1.41s1.02-.39,1.41,0l4.45,4.45,9.77-9.77c.39-.39,1.02-.39,1.41,0s.39,1.02,0,1.41l-10.47,10.47c-.19,.19-.44,.29-.71,.29Z"
                        />
                      </g>
                    </svg>
                  </td>
                </tr>
                <tr
                  class="border-b-neutral/20"
                >
                  <td
                    class="whitespace-normal px-3 py-4"
                  >
                    <p
                      class="text-md font-normal break-words"
                    >
                      Wednesday
                    </p>
                    <p
                      class="text-md font-normal break-words"
                    >
                      00:00:00
                    </p>
                  </td>
                  <td
                    class="whitespace-normal px-3 py-4"
                  >
                    <p
                      class="text-md font-normal break-words"
                    >
                      5 hours 
                    </p>
                  </td>
                  <td
                    class="whitespace-normal px-3 py-4"
                  >
                    <svg
                      class="fill-current h-6 w-6 mx-auto text-success"
                      viewBox="0 0 32 32"
                      xmlns="http://www.w3.org/2000/svg"
                    >
                      <title>
                        Yes
                      </title>
                      <g>
                        <path
                          d="M16,31.85C7.26,31.85,.15,24.74,.15,16S7.26,.15,16,.15s15.85,7.11,15.85,15.85-7.11,15.85-15.85,15.85Zm0-29.7c-7.64,0-13.85,6.21-13.85,13.85s6.21,13.85,13.85,13.85,13.85-6.21,13.85-13.85S23.64,2.15,16,2.15Z"
                        />
                        <path
                          d="M13.12,22.93c-.27,0-.52-.11-.71-.29l-5.16-5.16c-.39-.39-.39-1.02,0-1.41s1.02-.39,1.41,0l4.45,4.45,9.77-9.77c.39-.39,1.02-.39,1.41,0s.39,1.02,0,1.41l-10.47,10.47c-.19,.19-.44,.29-.71,.29Z"
                        />
                      </g>
                    </svg>
                  </td>
                </tr>
                <tr
                  class="border-b-neutral/20"
                >
                  <td
                    class="whitespace-normal px-3 py-4"
                  >
                    <p
                      class="text-md font-normal break-words"
                    >
                      Thursday
                    </p>
                    <p
                      class="text-md font-normal break-words"
                    >
                      16:10:00
                    </p>
                  </td>
                  <td
                    class="whitespace-normal px-3 py-4"
                  >
                    <p
                      class="text-md font-normal break-words"
                    >
                      1 hour 
                    </p>
                  </td>
                  <td
                    class="whitespace-normal px-3 py-4"
                  >
                    <svg
                      class="fill-current h-6 w-6 mx-auto text-success"
                      viewBox="0 0 32 32"
                      xmlns="http://www.w3.org/2000/svg"
                    >
                      <title>
                        Yes
                      </title>
                      <g>
                        <path
                          d="M16,31.85C7.26,31.85,.15,24.74,.15,16S7.26,.15,16,.15s15.85,7.11,15.85,15.85-7.11,15.85-15.85,15.85Zm0-29.7c-7.64,0-13.85,6.21-13.85,13.85s6.21,13.85,13.85,13.85,13.85-6.21,13.85-13.85S23.64,2.15,16,2.15Z"
                        />
                        <path
                          d="M13.12,22.93c-.27,0-.52-.11-.71-.29l-5.16-5.16c-.39-.39-.39-1.02,0-1.41s1.02-.39,1.41,0l4.45,4.45,9.77-9.77c.39-.39,1.02-.39,1.41,0s.39,1.02,0,1.41l-10.47,10.47c-.19,.19-.44,.29-.71,.29Z"
                        />
                      </g>
                    </svg>
                  </td>
                </tr>
                <tr
                  class="border-b-neutral/20"
                >
                  <td
                    class="whitespace-normal px-3 py-4"
                  >
                    <p
                      class="text-md font-normal break-words"
                    >
                      Friday
                    </p>
                    <p
                      class="text-md font-normal break-words"
                    >
                      00:00:00
                    </p>
                  </td>
                  <td
                    class="whitespace-normal px-3 py-4"
                  >
                    <p
                      class="text-md font-normal break-words"
                    >
                      5 hours 
                    </p>
                  </td>
                  <td
                    class="whitespace-normal px-3 py-4"
                  >
                    <svg
                      class="fill-current h-6 w-6 mx-auto text-success"
                      viewBox="0 0 32 32"
                      xmlns="http://www.w3.org/2000/svg"
                    >
                      <title>
                        Yes
                      </title>
                      <g>
                        <path
                          d="M16,31.85C7.26,31.85,.15,24.74,.15,16S7.26,.15,16,.15s15.85,7.11,15.85,15.85-7.11,15.85-15.85,15.85Zm0-29.7c-7.64,0-13.85,6.21-13.85,13.85s6.21,13.85,13.85,13.85,13.85-6.21,13.85-13.85S23.64,2.15,16,2.15Z"
                        />
                        <path
                          d="M13.12,22.93c-.27,0-.52-.11-.71-.29l-5.16-5.16c-.39-.39-.39-1.02,0-1.41s1.02-.39,1.41,0l4.45,4.45,9.77-9.77c.39-.39,1.02-.39,1.41,0s.39,1.02,0,1.41l-10.47,10.47c-.19,.19-.44,.29-.71,.29Z"
                        />
                      </g>
                    </svg>
                  </td>
                </tr>
                <tr
                  class="border-b-neutral/20"
                >
                  <td
                    class="whitespace-normal px-3 py-4"
                  >
                    <p
                      class="text-md font-normal break-words"
                    >
                      Saturday
                    </p>
                    <p
                      class="text-md font-normal break-words"
                    >
                      00:00:00
                    </p>
                  </td>
                  <td
                    class="whitespace-normal px-3 py-4"
                  >
                    <p
                      class="text-md font-normal break-words"
                    >
                      5 hours 
                    </p>
                  </td>
                  <td
                    class="whitespace-normal px-3 py-4"
                  >
                    <svg
                      class="fill-current h-6 w-6 mx-auto text-error"
                      viewBox="0 0 32 32"
                      xmlns="http://www.w3.org/2000/svg"
                    >
                      <title>
                        No
                      </title>
                      <g>
                        <path
                          d="M16,.07C7.22,.07,.07,7.22,.07,16s7.15,15.93,15.93,15.93,15.93-7.15,15.93-15.93S24.78,.07,16,.07Zm0,29.86c-7.68,0-13.93-6.25-13.93-13.93S8.32,2.07,16,2.07s13.93,6.25,13.93,13.93-6.25,13.93-13.93,13.93Z"
                        />
                        <path
                          d="M23.38,8.62c-.39-.39-1.02-.39-1.41,0l-5.96,5.96-5.96-5.96c-.39-.39-1.02-.39-1.41,0s-.39,1.02,0,1.41l5.96,5.96-5.96,5.96c-.39,.39-.39,1.02,0,1.41,.2,.2,.45,.29,.71,.29s.51-.1,.71-.29l5.96-5.96,5.96,5.96c.2,.2,.45,.29,.71,.29s.51-.1,.71-.29c.39-.39,.39-1.02,0-1.41l-5.96-5.96,5.96-5.96c.39-.39,.39-1.02,0-1.41Z"
                        />
                      </g>
                    </svg>
                  </td>
                </tr>
                <tr
                  class="border-b-neutral/20"
                >
                  <td
                    class="whitespace-normal px-3 py-4"
                  >
                    <p
                      class="text-md font-normal break-words"
                    >
                      Sunday
                    </p>
                    <p
                      class="text-md font-normal break-words"
                    >
                      00:00:00
                    </p>
                  </td>
                  <td
                    class="whitespace-normal px-3 py-4"
                  >
                    <p
                      class="text-md font-normal break-words"
                    >
                      5 hours 
                    </p>
                  </td>
                  <td
                    class="whitespace-normal px-3 py-4"
                  >
                    <svg
                      class="fill-current h-6 w-6 mx-auto text-error"
                      viewBox="0 0 32 32"
                      xmlns="http://www.w3.org/2000/svg"
                    >
                      <title>
                        No
                      </title>
                      <g>
                        <path
                          d="M16,.07C7.22,.07,.07,7.22,.07,16s7.15,15.93,15.93,15.93,15.93-7.15,15.93-15.93S24.78,.07,16,.07Zm0,29.86c-7.68,0-13.93-6.25-13.93-13.93S8.32,2.07,16,2.07s13.93,6.25,13.93,13.93-6.25,13.93-13.93,13.93Z"
                        />
                        <path
                          d="M23.38,8.62c-.39-.39-1.02-.39-1.41,0l-5.96,5.96-5.96-5.96c-.39-.39-1.02-.39-1.41,0s-.39,1.02,0,1.41l5.96,5.96-5.96,5.96c-.39,.39-.39,1.02,0,1.41,.2,.2,.45,.29,.71,.29s.51-.1,.71-.29l5.96-5.96,5.96,5.96c.2,.2,.45,.29,.71,.29s.51-.1,.71-.29c.39-.39,.39-1.02,0-1.41l-5.96-5.96,5.96-5.96c.39-.39,.39-1.02,0-1.41Z"
                        />
                      </g>
                    </svg>
                  </td>
                </tr>
              </tbody>
            </table>
          </div>
        </div>
        <div
          class="recharts-responsive-container"
          style="width: 100%; height: 320px; min-width: 0;"
        >
          <div
            style="width: 0px; height: 0px; overflow: visible;"
          />
        </div>
      </div>
    </section>
    <div
      class="pb-2"
    />
    <section
      class="p-3 rounded-sm bg-white"
    >
      <h3
        class="text-lg font-bold pb-4"
      >
        Other configuration values
      </h3>
      <div
        class="flex flex-col bg-white rounded-sm overflow-hidden overflow-x-auto"
      >
        <div
          class="inline-block min-w-full -my-2 py-2 align-middle"
        >
          <table
            class="min-w-full"
            tabindex="0"
          >
            <caption
              class="sr-only"
            >
              Table of other configuration values
            </caption>
            <tbody
              class="divide-y"
            >
              <tr
                class="border-b-neutral/20"
              >
                <td
                  class="whitespace-normal px-3 py-4 pl-0 pr-2 pt-2 pb-2 whitespace-pre"
                >
                  ChargeCurrentLimitA
                </td>
                <td
                  class="whitespace-normal px-3 py-4 pr-0 pl-2 pt-2 pb-2 text-neutral text-left"
                >
                  32
                </td>
              </tr>
              <tr
                class="border-b-neutral/20"
              >
                <td
                  class="whitespace-normal px-3 py-4 pl-0 pr-2 pt-2 pb-2 whitespace-pre"
                >
                  OffMode
                </td>
                <td
                  class="whitespace-normal px-3 py-4 pr-0 pl-2 pt-2 pb-2 text-neutral text-left"
                >
                  false
                </td>
              </tr>
              <tr
                class="border-b-neutral/20"
              >
                <td
                  class="whitespace-normal px-3 py-4 pl-0 pr-2 pt-2 pb-2 whitespace-pre"
                >
                  OfflineSchedulingEnabled
                </td>
                <td
                  class="whitespace-normal px-3 py-4 pr-0 pl-2 pt-2 pb-2 text-neutral text-left"
                >
                  true
                </td>
              </tr>
              <tr
                class="border-b-neutral/20"
              >
                <td
                  class="whitespace-normal px-3 py-4 pl-0 pr-2 pt-2 pb-2 whitespace-pre"
                >
                  PPDevClampFaultThreshold
                </td>
                <td
                  class="whitespace-normal px-3 py-4 pr-0 pl-2 pt-2 pb-2 text-neutral text-left"
                >
                  2
                </td>
              </tr>
              <tr
                class="border-b-neutral/20"
              >
                <td
                  class="whitespace-normal px-3 py-4 pl-0 pr-2 pt-2 pb-2 whitespace-pre"
                >
                  PowerBalancingCurrentLimitImportA
                </td>
                <td
                  class="whitespace-normal px-3 py-4 pr-0 pl-2 pt-2 pb-2 text-neutral text-left"
                >
                  50
                </td>
              </tr>
              <tr
                class="border-b-neutral/20"
              >
                <td
                  class="whitespace-normal px-3 py-4 pl-0 pr-2 pt-2 pb-2 whitespace-pre"
                >
                  PowerBalancingEnabled
                </td>
                <td
                  class="whitespace-normal px-3 py-4 pr-0 pl-2 pt-2 pb-2 text-neutral text-left"
                >
                  true
                </td>
              </tr>
              <tr
                class="border-b-neutral/20"
              >
                <td
                  class="whitespace-normal px-3 py-4 pl-0 pr-2 pt-2 pb-2 whitespace-pre"
                >
                  PowerBalancingSensor
                </td>
                <td
                  class="whitespace-normal px-3 py-4 pr-0 pl-2 pt-2 pb-2 text-neutral text-left"
                >
                  NONE
                </td>
              </tr>
              <tr
                class="border-b-neutral/20"
              >
                <td
                  class="whitespace-normal px-3 py-4 pl-0 pr-2 pt-2 pb-2 whitespace-pre"
                >
                  PowerBalancingSensorInstalled
                </td>
                <td
                  class="whitespace-normal px-3 py-4 pr-0 pl-2 pt-2 pb-2 text-neutral text-left"
                >
                  true
                </td>
              </tr>
              <tr
                class="border-b-neutral/20"
              >
                <td
                  class="whitespace-normal px-3 py-4 pl-0 pr-2 pt-2 pb-2 whitespace-pre"
                >
                  PowerBalancingSensorPolarityInverted
                </td>
                <td
                  class="whitespace-normal px-3 py-4 pr-0 pl-2 pt-2 pb-2 text-neutral text-left"
                >
                  true
                </td>
              </tr>
              <tr
                class="border-b-neutral/20"
              >
                <td
                  class="whitespace-normal px-3 py-4 pl-0 pr-2 pt-2 pb-2 whitespace-pre"
                >
                  SolarExportMargin
                </td>
                <td
                  class="whitespace-normal px-3 py-4 pr-0 pl-2 pt-2 pb-2 text-neutral text-left"
                >
                  5
                </td>
              </tr>
              <tr
                class="border-b-neutral/20"
              >
                <td
                  class="whitespace-normal px-3 py-4 pl-0 pr-2 pt-2 pb-2 whitespace-pre"
                >
                  SolarMatchingEnabled
                </td>
                <td
                  class="whitespace-normal px-3 py-4 pr-0 pl-2 pt-2 pb-2 text-neutral text-left"
                >
                  true
                </td>
              </tr>
              <tr
                class="border-b-neutral/20"
              >
                <td
                  class="whitespace-normal px-3 py-4 pl-0 pr-2 pt-2 pb-2 whitespace-pre"
                >
                  SolarMaxGridImport
                </td>
                <td
                  class="whitespace-normal px-3 py-4 pr-0 pl-2 pt-2 pb-2 text-neutral text-left"
                >
                  4.1
                </td>
              </tr>
              <tr
                class="border-b-neutral/20"
              >
                <td
                  class="whitespace-normal px-3 py-4 pl-0 pr-2 pt-2 pb-2 whitespace-pre"
                >
                  SolarStartHysteresis
                </td>
                <td
                  class="whitespace-normal px-3 py-4 pr-0 pl-2 pt-2 pb-2 text-neutral text-left"
                >
                  10
                </td>
              </tr>
              <tr
                class="border-b-neutral/20"
              >
                <td
                  class="whitespace-normal px-3 py-4 pl-0 pr-2 pt-2 pb-2 whitespace-pre"
                >
                  SolarStopHysteresis
                </td>
                <td
                  class="whitespace-normal px-3 py-4 pr-0 pl-2 pt-2 pb-2 text-neutral text-left"
                >
                  64
                </td>
              </tr>
              <tr
                class="border-b-neutral/20"
              >
                <td
                  class="whitespace-normal px-3 py-4 pl-0 pr-2 pt-2 pb-2 whitespace-pre"
                >
                  SolarSystemInstalled
                </td>
                <td
                  class="whitespace-normal px-3 py-4 pr-0 pl-2 pt-2 pb-2 text-neutral text-left"
                >
                  true
                </td>
              </tr>
              <tr
                class="border-b-neutral/20"
              >
                <td
                  class="whitespace-normal px-3 py-4 pl-0 pr-2 pt-2 pb-2 whitespace-pre"
                >
                  FirmwareVersion
                </td>
                <td
                  class="whitespace-normal px-3 py-4 pr-0 pl-2 pt-2 pb-2 text-neutral text-left"
                >
                  1.0.0
                </td>
              </tr>
              <tr
                class="border-b-neutral/20"
              >
                <td
                  class="whitespace-normal px-3 py-4 pl-0 pr-2 pt-2 pb-2 whitespace-pre"
                >
                  LinkyScheduleEnabled
                </td>
                <td
                  class="whitespace-normal px-3 py-4 pr-0 pl-2 pt-2 pb-2 text-neutral text-left"
                >
                  false
                </td>
              </tr>
              <tr
                class="border-b-neutral/20"
              >
                <td
                  class="whitespace-normal px-3 py-4 pl-0 pr-2 pt-2 pb-2 whitespace-pre"
                >
                  RcdBreakerSize
                </td>
                <td
                  class="whitespace-normal px-3 py-4 pr-0 pl-2 pt-2 pb-2 text-neutral text-left"
                >
                  10
                </td>
              </tr>
              <tr
                class="border-b-neutral/20"
              >
                <td
                  class="whitespace-normal px-3 py-4 pl-0 pr-2 pt-2 pb-2 whitespace-pre"
                >
                  UnlockConnectorOnEVSideDisconnect
                </td>
                <td
                  class="whitespace-normal px-3 py-4 pr-0 pl-2 pt-2 pb-2 text-neutral text-left"
                >
                  true
                </td>
              </tr>
            </tbody>
          </table>
        </div>
      </div>
    </section>
    <div
      class="pb-2"
    />
    <section
      class="p-3 rounded-sm bg-white"
    >
      <h3
        class="text-lg font-bold pb-4"
      >
        Tags
      </h3>
      <div
        class="flex flex-col bg-white rounded-sm overflow-hidden overflow-x-auto"
      >
        <div
          class="inline-block min-w-full -my-2 py-2 align-middle"
        >
          <table
            class="min-w-full"
            tabindex="0"
          >
            <caption
              class="sr-only"
            >
              Table of tags
            </caption>
            <tbody
              class="divide-y"
            >
              <tr
                class="border-b-neutral/20"
              >
                <td
                  class="whitespace-normal px-3 py-4 pl-0 pr-2 pt-2 pb-2 whitespace-pre"
                >
                  cur tollo claudeo
                </td>
                <td
                  class="whitespace-normal px-3 py-4 pr-0 pl-2 pt-2 pb-2 text-neutral text-left"
                >
                  sponte totus dedico
                </td>
              </tr>
              <tr
                class="border-b-neutral/20"
              >
                <td
                  class="whitespace-normal px-3 py-4 pl-0 pr-2 pt-2 pb-2 whitespace-pre"
                >
                  terreo cinis utilis
                </td>
                <td
                  class="whitespace-normal px-3 py-4 pr-0 pl-2 pt-2 pb-2 text-neutral text-left"
                >
                  tametsi vilitas adduco
                </td>
              </tr>
              <tr
                class="border-b-neutral/20"
              >
                <td
                  class="whitespace-normal px-3 py-4 pl-0 pr-2 pt-2 pb-2 whitespace-pre"
                >
                  sollers depromo vesica
                </td>
                <td
                  class="whitespace-normal px-3 py-4 pr-0 pl-2 pt-2 pb-2 text-neutral text-left"
                >
                  similique candidus ulterius
                </td>
              </tr>
              <tr
                class="border-b-neutral/20"
              >
                <td
                  class="whitespace-normal px-3 py-4 pl-0 pr-2 pt-2 pb-2 whitespace-pre"
                >
                  quod texo dapifer
                </td>
                <td
                  class="whitespace-normal px-3 py-4 pr-0 pl-2 pt-2 pb-2 text-neutral text-left"
                >
                  termes catena hic
                </td>
              </tr>
              <tr
                class="border-b-neutral/20"
              >
                <td
                  class="whitespace-normal px-3 py-4 pl-0 pr-2 pt-2 pb-2 whitespace-pre"
                >
                  tabgo adaugeo thesis
                </td>
                <td
                  class="whitespace-normal px-3 py-4 pr-0 pl-2 pt-2 pb-2 text-neutral text-left"
                >
                  celo excepturi curvo
                </td>
              </tr>
              <tr
                class="border-b-neutral/20"
              >
                <td
                  class="whitespace-normal px-3 py-4 pl-0 pr-2 pt-2 pb-2 whitespace-pre"
                >
                  asperiores currus theatrum
                </td>
                <td
                  class="whitespace-normal px-3 py-4 pr-0 pl-2 pt-2 pb-2 text-neutral text-left"
                >
                  thorax vinum deporto
                </td>
              </tr>
            </tbody>
          </table>
        </div>
      </div>
    </section>
    <div
      class="pb-2"
    />
  </div>
</body>
`;

exports[`Charger page content should match snapshot with commercial attributes 1`] = `
<body>
  <div>
    <div
      class="rounded-md p-3 bg-warning/10 border border-warning"
    >
      <div
        class="flex"
      >
        <div
          class="shrink-0"
        >
          <span
            class="text-error/70"
          >
            <svg
              class="fill-current w-6 h-6"
              viewBox="0 0 32 32"
              xmlns="http://www.w3.org/2000/svg"
            >
              <g>
                <path
                  d="M16.1,29.43A13.43,13.43,0,1,1,29.53,16,13.45,13.45,0,0,1,16.1,29.43Zm0-24.86A11.43,11.43,0,1,0,27.53,16,11.45,11.45,0,0,0,16.1,4.57Z"
                />
                <path
                  d="M16,24.12a1,1,0,0,1-1-1V14.94a1,1,0,1,1,2,0v8.18A1,1,0,0,1,16,24.12Z"
                />
                <path
                  d="M16.1,10.89a1,1,0,0,1-1-1v-.2a1,1,0,0,1,2,0v.2A1,1,0,0,1,16.1,10.89Z"
                />
              </g>
            </svg>
          </span>
        </div>
        <div
          class="flex flex-col justify-center ml-4"
        >
          <p
            class="text-md font-normal sr-only break-words"
          >
            Alert
          </p>
          <div
            class="text-error/70"
          >
            <p
              class="text-md font-normal break-words"
            >
              It is not currently possible to make changes or send commands to this charger. You can search for this charger in MIS 
              <a
                class="text-primary underline hover:decoration-2 focus:decoration-2 focus:outline-none cursor-pointer"
                href="https://admin.pod-point.com/units?search=PSL-12345"
                target="_blank"
              >
                here
              </a>
               or view this charger in Grafana 
              <a
                class="text-primary underline hover:decoration-2 focus:decoration-2 focus:outline-none cursor-pointer"
                href="https://g-e728fe26ac.grafana-workspace.eu-west-1.amazonaws.com/d/fdtbud32bw45ce/5b147138-e9f2-5846-ba34-c2778f5a748f?var-ppid=PSL-12345"
                target="_blank"
              >
                here
              </a>
              .
            </p>
          </div>
        </div>
      </div>
    </div>
    <div
      class="pb-2"
    />
    <section
      class="p-3 rounded-sm bg-white"
    >
      <header>
        <div
          class="flex"
        >
          <div
            class="lg:flex space-x-1 items-center"
          >
            <div
              class="flex"
            >
              <h1
                class="text-xxl font-bold"
              >
                PSL-12345
              </h1>
              <button
                aria-label="Copy PSL-12345"
                class="border-2 border-solid rounded-sm font-bold text-center disabled:no-underline transition ease-in-out duration-200 cursor-pointer disabled:cursor-not-allowed border-transparent text-primary hover:underline hover:text-primary active:font-bold active:text-primary disabled:text-neutral/40 disabled:hover:no-underline disabled:hover:border-transparent p-1.5 ml-1"
                type="button"
              >
                <svg
                  class="fill-current h-4 w-4"
                  viewBox="0 0 24 24"
                  xmlns="http://www.w3.org/2000/svg"
                >
                  <defs>
                    <style>
                      .cls-1 
                    </style>
                  </defs>
                  <path
                    d="M18.73,14.5h1.83c1.35,0,2.44-1.09,2.44-2.44V3.5c0-1.35-1.09-2.44-2.44-2.44h-8.56c-1.35,0-2.44,1.09-2.44,2.44v1.83M3.45,9.61h8.56c1.35,0,2.44,1.09,2.44,2.44v8.56c0,1.35-1.09,2.44-2.44,2.44H3.45c-1.35,0-2.44-1.09-2.44-2.44v-8.56c0-1.35,1.09-2.44,2.44-2.44Z"
                    style="fill: none; stroke: currentColor; stroke-linecap: round; stroke-linejoin: round; stroke-width: 1.5px;"
                  />
                </svg>
              </button>
            </div>
          </div>
          <div
            class="ml-auto"
          >
            <div
              class="relative inline-block text-left"
              data-headlessui-state=""
            >
              <button
                aria-expanded="false"
                aria-haspopup="menu"
                class="bg-black border-black border-2 border-solid rounded-sm px-4 py-1.5 hover:bg-charcoal hover:border-charcoal focus:bg-charcoal focus:border-charcoal active:bg-charcoal active:border-charcoal disabled:bg-neutral/20 disabled:border-neutral/20 disabled:text-neutral font-bold inline-flex items-center text-white cursor-pointer disabled:cursor-not-allowed"
                data-headlessui-state=""
                id="headlessui-menu-button-:test-id-2"
                type="button"
              >
                <span>
                  Commands
                </span>
                <svg
                  class="fill-current stroke-current h-2 w-2 min-w-0 ml-2"
                  viewBox="0 0 24 24"
                  xmlns="http://www.w3.org/2000/svg"
                >
                  <g>
                    <path
                      d="m13.15,18.84c-.57.64-1.55.69-2.19.11-.04-.04-.08-.07-.11-.11L.4,7.23c-.57-.64-.52-1.61.12-2.19.28-.25.65-.4,1.03-.4h20.91c.85,0,1.55.69,1.55,1.55,0,.38-.14.75-.4,1.03l-10.45,11.62Z"
                    />
                  </g>
                </svg>
              </button>
            </div>
          </div>
        </div>
        <p
          class="text-md font-semibold break-words"
        >
          Test-Name
        </p>
        <div
          class="pb-4"
        />
        <p
          class="text-md font-normal mb-1 break-words"
        >
          Group: Test group
        </p>
        <p
          class="text-md font-normal mb-1 break-words"
        >
          Site: Test site
        </p>
        <p
          class="text-md font-normal mb-1 break-words"
        >
          Site contact: Test Contact, <EMAIL>, +***********
        </p>
        <p
          class="text-md font-normal mb-1 break-words"
        >
          Address: volutabrum baiulus et
        </p>
        <div
          class="pb-4"
        />
        <div>
          <div
            class="flex items-center"
          >
            <a
              class="text-primary underline hover:decoration-2 focus:decoration-2 focus:outline-none cursor-pointer block mr-2"
              href="/accounts/a3450f38-217a-4d0c-8eec-b7d63c6fd2e0/details"
            >
              John Doe - <EMAIL>
            </a>
            <button
              aria-label="Unlink user John Doe"
              class="border-2 border-solid rounded-sm font-bold text-center disabled:no-underline transition ease-in-out duration-200 cursor-pointer disabled:cursor-not-allowed border-transparent text-primary hover:underline hover:text-primary active:font-bold active:text-primary disabled:text-neutral/40 disabled:hover:no-underline disabled:hover:border-transparent p-1.5"
              data-testid="unlink-user-a3450f38-217a-4d0c-8eec-b7d63c6fd2e0"
              id="unlink-user-a3450f38-217a-4d0c-8eec-b7d63c6fd2e0"
              name="unlink-user-a3450f38-217a-4d0c-8eec-b7d63c6fd2e0"
              type="button"
            >
              <svg
                class="fill-current h-4 w-4"
                viewBox="0 0 24 24"
                xmlns="http://www.w3.org/2000/svg"
              >
                <g>
                  <path
                    d="m9.94,14.21c3.38,0,6.12-2.75,6.12-6.12S13.32,1.96,9.94,1.96s-6.12,2.75-6.12,6.12,2.75,6.12,6.12,6.12Zm0-10.75c2.55,0,4.62,2.07,4.62,4.62s-2.07,4.62-4.62,4.62-4.62-2.07-4.62-4.62,2.07-4.62,4.62-4.62Z"
                  />
                  <path
                    d="m9.94,14.73C2.66,14.73.21,20.96.18,21.02c-.15.39.05.82.43.97.39.15.82-.04.97-.43.08-.22,2.13-5.33,8.36-5.33s8.77,5.16,8.87,5.38c.13.27.4.43.68.43.11,0,.21-.02.32-.07.37-.17.54-.62.36-.99-.12-.26-2.98-6.25-10.23-6.25Z"
                  />
                  <path
                    d="m21.42,9.31l2.23-2.23c.29-.29.29-.77,0-1.06s-.77-.29-1.06,0l-2.23,2.23-2.23-2.23c-.29-.29-.77-.29-1.06,0s-.29.77,0,1.06l2.23,2.23-2.23,2.23c-.29.29-.29.77,0,1.06.15.15.34.22.53.22s.38-.07.53-.22l2.23-2.23,2.23,2.23c.15.15.34.22.53.22s.38-.07.53-.22c.29-.29.29-.77,0-1.06l-2.23-2.23Z"
                  />
                </g>
              </svg>
            </button>
          </div>
          <div
            class="flex items-center"
          >
            <a
              class="text-primary underline hover:decoration-2 focus:decoration-2 focus:outline-none cursor-pointer block mr-2"
              href="/accounts/77b7c00e-8890-4a76-b4e6-def3b226d47b/details"
            >
              John Doe - <EMAIL>
            </a>
            <button
              aria-label="Unlink user John Doe"
              class="border-2 border-solid rounded-sm font-bold text-center disabled:no-underline transition ease-in-out duration-200 cursor-pointer disabled:cursor-not-allowed border-transparent text-primary hover:underline hover:text-primary active:font-bold active:text-primary disabled:text-neutral/40 disabled:hover:no-underline disabled:hover:border-transparent p-1.5"
              data-testid="unlink-user-77b7c00e-8890-4a76-b4e6-def3b226d47b"
              id="unlink-user-77b7c00e-8890-4a76-b4e6-def3b226d47b"
              name="unlink-user-77b7c00e-8890-4a76-b4e6-def3b226d47b"
              type="button"
            >
              <svg
                class="fill-current h-4 w-4"
                viewBox="0 0 24 24"
                xmlns="http://www.w3.org/2000/svg"
              >
                <g>
                  <path
                    d="m9.94,14.21c3.38,0,6.12-2.75,6.12-6.12S13.32,1.96,9.94,1.96s-6.12,2.75-6.12,6.12,2.75,6.12,6.12,6.12Zm0-10.75c2.55,0,4.62,2.07,4.62,4.62s-2.07,4.62-4.62,4.62-4.62-2.07-4.62-4.62,2.07-4.62,4.62-4.62Z"
                  />
                  <path
                    d="m9.94,14.73C2.66,14.73.21,20.96.18,21.02c-.15.39.05.82.43.97.39.15.82-.04.97-.43.08-.22,2.13-5.33,8.36-5.33s8.77,5.16,8.87,5.38c.13.27.4.43.68.43.11,0,.21-.02.32-.07.37-.17.54-.62.36-.99-.12-.26-2.98-6.25-10.23-6.25Z"
                  />
                  <path
                    d="m21.42,9.31l2.23-2.23c.29-.29.29-.77,0-1.06s-.77-.29-1.06,0l-2.23,2.23-2.23-2.23c-.29-.29-.77-.29-1.06,0s-.29.77,0,1.06l2.23,2.23-2.23,2.23c-.29.29-.29.77,0,1.06.15.15.34.22.53.22s.38-.07.53-.22l2.23-2.23,2.23,2.23c.15.15.34.22.53.22s.38-.07.53-.22c.29-.29.29-.77,0-1.06l-2.23-2.23Z"
                  />
                </g>
              </svg>
            </button>
          </div>
          <div
            class="flex items-center"
          >
            <a
              class="text-primary underline hover:decoration-2 focus:decoration-2 focus:outline-none cursor-pointer block mr-2"
              href="/accounts/5411de0c-d46b-48c3-b6d2-e6bc3a1f7049/details"
            >
              John Doe - <EMAIL>
            </a>
            <button
              aria-label="Unlink user John Doe"
              class="border-2 border-solid rounded-sm font-bold text-center disabled:no-underline transition ease-in-out duration-200 cursor-pointer disabled:cursor-not-allowed border-transparent text-primary hover:underline hover:text-primary active:font-bold active:text-primary disabled:text-neutral/40 disabled:hover:no-underline disabled:hover:border-transparent p-1.5"
              data-testid="unlink-user-5411de0c-d46b-48c3-b6d2-e6bc3a1f7049"
              id="unlink-user-5411de0c-d46b-48c3-b6d2-e6bc3a1f7049"
              name="unlink-user-5411de0c-d46b-48c3-b6d2-e6bc3a1f7049"
              type="button"
            >
              <svg
                class="fill-current h-4 w-4"
                viewBox="0 0 24 24"
                xmlns="http://www.w3.org/2000/svg"
              >
                <g>
                  <path
                    d="m9.94,14.21c3.38,0,6.12-2.75,6.12-6.12S13.32,1.96,9.94,1.96s-6.12,2.75-6.12,6.12,2.75,6.12,6.12,6.12Zm0-10.75c2.55,0,4.62,2.07,4.62,4.62s-2.07,4.62-4.62,4.62-4.62-2.07-4.62-4.62,2.07-4.62,4.62-4.62Z"
                  />
                  <path
                    d="m9.94,14.73C2.66,14.73.21,20.96.18,21.02c-.15.39.05.82.43.97.39.15.82-.04.97-.43.08-.22,2.13-5.33,8.36-5.33s8.77,5.16,8.87,5.38c.13.27.4.43.68.43.11,0,.21-.02.32-.07.37-.17.54-.62.36-.99-.12-.26-2.98-6.25-10.23-6.25Z"
                  />
                  <path
                    d="m21.42,9.31l2.23-2.23c.29-.29.29-.77,0-1.06s-.77-.29-1.06,0l-2.23,2.23-2.23-2.23c-.29-.29-.77-.29-1.06,0s-.29.77,0,1.06l2.23,2.23-2.23,2.23c-.29.29-.29.77,0,1.06.15.15.34.22.53.22s.38-.07.53-.22l2.23-2.23,2.23,2.23c.15.15.34.22.53.22s.38-.07.53-.22c.29-.29.29-.77,0-1.06l-2.23-2.23Z"
                  />
                </g>
              </svg>
            </button>
          </div>
          <div
            class="flex items-center"
          >
            <a
              class="text-primary underline hover:decoration-2 focus:decoration-2 focus:outline-none cursor-pointer block mr-2"
              href="/accounts/3482bea8-17d5-470e-83bd-4e897b8b11ee/details"
            >
              John Doe - <EMAIL>
            </a>
            <button
              aria-label="Unlink user John Doe"
              class="border-2 border-solid rounded-sm font-bold text-center disabled:no-underline transition ease-in-out duration-200 cursor-pointer disabled:cursor-not-allowed border-transparent text-primary hover:underline hover:text-primary active:font-bold active:text-primary disabled:text-neutral/40 disabled:hover:no-underline disabled:hover:border-transparent p-1.5"
              data-testid="unlink-user-3482bea8-17d5-470e-83bd-4e897b8b11ee"
              id="unlink-user-3482bea8-17d5-470e-83bd-4e897b8b11ee"
              name="unlink-user-3482bea8-17d5-470e-83bd-4e897b8b11ee"
              type="button"
            >
              <svg
                class="fill-current h-4 w-4"
                viewBox="0 0 24 24"
                xmlns="http://www.w3.org/2000/svg"
              >
                <g>
                  <path
                    d="m9.94,14.21c3.38,0,6.12-2.75,6.12-6.12S13.32,1.96,9.94,1.96s-6.12,2.75-6.12,6.12,2.75,6.12,6.12,6.12Zm0-10.75c2.55,0,4.62,2.07,4.62,4.62s-2.07,4.62-4.62,4.62-4.62-2.07-4.62-4.62,2.07-4.62,4.62-4.62Z"
                  />
                  <path
                    d="m9.94,14.73C2.66,14.73.21,20.96.18,21.02c-.15.39.05.82.43.97.39.15.82-.04.97-.43.08-.22,2.13-5.33,8.36-5.33s8.77,5.16,8.87,5.38c.13.27.4.43.68.43.11,0,.21-.02.32-.07.37-.17.54-.62.36-.99-.12-.26-2.98-6.25-10.23-6.25Z"
                  />
                  <path
                    d="m21.42,9.31l2.23-2.23c.29-.29.29-.77,0-1.06s-.77-.29-1.06,0l-2.23,2.23-2.23-2.23c-.29-.29-.77-.29-1.06,0s-.29.77,0,1.06l2.23,2.23-2.23,2.23c-.29.29-.29.77,0,1.06.15.15.34.22.53.22s.38-.07.53-.22l2.23-2.23,2.23,2.23c.15.15.34.22.53.22s.38-.07.53-.22c.29-.29.29-.77,0-1.06l-2.23-2.23Z"
                  />
                </g>
              </svg>
            </button>
          </div>
          <div
            class="flex items-center"
          >
            <a
              class="text-primary underline hover:decoration-2 focus:decoration-2 focus:outline-none cursor-pointer block mr-2"
              href="/accounts/609cf19a-c8c9-43f8-9042-59b5527e5f73/details"
            >
              John Doe - <EMAIL>
            </a>
            <button
              aria-label="Unlink user John Doe"
              class="border-2 border-solid rounded-sm font-bold text-center disabled:no-underline transition ease-in-out duration-200 cursor-pointer disabled:cursor-not-allowed border-transparent text-primary hover:underline hover:text-primary active:font-bold active:text-primary disabled:text-neutral/40 disabled:hover:no-underline disabled:hover:border-transparent p-1.5"
              data-testid="unlink-user-609cf19a-c8c9-43f8-9042-59b5527e5f73"
              id="unlink-user-609cf19a-c8c9-43f8-9042-59b5527e5f73"
              name="unlink-user-609cf19a-c8c9-43f8-9042-59b5527e5f73"
              type="button"
            >
              <svg
                class="fill-current h-4 w-4"
                viewBox="0 0 24 24"
                xmlns="http://www.w3.org/2000/svg"
              >
                <g>
                  <path
                    d="m9.94,14.21c3.38,0,6.12-2.75,6.12-6.12S13.32,1.96,9.94,1.96s-6.12,2.75-6.12,6.12,2.75,6.12,6.12,6.12Zm0-10.75c2.55,0,4.62,2.07,4.62,4.62s-2.07,4.62-4.62,4.62-4.62-2.07-4.62-4.62,2.07-4.62,4.62-4.62Z"
                  />
                  <path
                    d="m9.94,14.73C2.66,14.73.21,20.96.18,21.02c-.15.39.05.82.43.97.39.15.82-.04.97-.43.08-.22,2.13-5.33,8.36-5.33s8.77,5.16,8.87,5.38c.13.27.4.43.68.43.11,0,.21-.02.32-.07.37-.17.54-.62.36-.99-.12-.26-2.98-6.25-10.23-6.25Z"
                  />
                  <path
                    d="m21.42,9.31l2.23-2.23c.29-.29.29-.77,0-1.06s-.77-.29-1.06,0l-2.23,2.23-2.23-2.23c-.29-.29-.77-.29-1.06,0s-.29.77,0,1.06l2.23,2.23-2.23,2.23c-.29.29-.29.77,0,1.06.15.15.34.22.53.22s.38-.07.53-.22l2.23-2.23,2.23,2.23c.15.15.34.22.53.22s.38-.07.53-.22c.29-.29.29-.77,0-1.06l-2.23-2.23Z"
                  />
                </g>
              </svg>
            </button>
          </div>
          <div
            class="flex items-center"
          >
            <a
              class="text-primary underline hover:decoration-2 focus:decoration-2 focus:outline-none cursor-pointer block mr-2"
              href="/accounts/910b444d-f474-46c3-b368-c43b170dbda9/details"
            >
              John Doe - <EMAIL>
            </a>
            <button
              aria-label="Unlink user John Doe"
              class="border-2 border-solid rounded-sm font-bold text-center disabled:no-underline transition ease-in-out duration-200 cursor-pointer disabled:cursor-not-allowed border-transparent text-primary hover:underline hover:text-primary active:font-bold active:text-primary disabled:text-neutral/40 disabled:hover:no-underline disabled:hover:border-transparent p-1.5"
              data-testid="unlink-user-910b444d-f474-46c3-b368-c43b170dbda9"
              id="unlink-user-910b444d-f474-46c3-b368-c43b170dbda9"
              name="unlink-user-910b444d-f474-46c3-b368-c43b170dbda9"
              type="button"
            >
              <svg
                class="fill-current h-4 w-4"
                viewBox="0 0 24 24"
                xmlns="http://www.w3.org/2000/svg"
              >
                <g>
                  <path
                    d="m9.94,14.21c3.38,0,6.12-2.75,6.12-6.12S13.32,1.96,9.94,1.96s-6.12,2.75-6.12,6.12,2.75,6.12,6.12,6.12Zm0-10.75c2.55,0,4.62,2.07,4.62,4.62s-2.07,4.62-4.62,4.62-4.62-2.07-4.62-4.62,2.07-4.62,4.62-4.62Z"
                  />
                  <path
                    d="m9.94,14.73C2.66,14.73.21,20.96.18,21.02c-.15.39.05.82.43.97.39.15.82-.04.97-.43.08-.22,2.13-5.33,8.36-5.33s8.77,5.16,8.87,5.38c.13.27.4.43.68.43.11,0,.21-.02.32-.07.37-.17.54-.62.36-.99-.12-.26-2.98-6.25-10.23-6.25Z"
                  />
                  <path
                    d="m21.42,9.31l2.23-2.23c.29-.29.29-.77,0-1.06s-.77-.29-1.06,0l-2.23,2.23-2.23-2.23c-.29-.29-.77-.29-1.06,0s-.29.77,0,1.06l2.23,2.23-2.23,2.23c-.29.29-.29.77,0,1.06.15.15.34.22.53.22s.38-.07.53-.22l2.23-2.23,2.23,2.23c.15.15.34.22.53.22s.38-.07.53-.22c.29-.29.29-.77,0-1.06l-2.23-2.23Z"
                  />
                </g>
              </svg>
            </button>
          </div>
          <div
            class="pb-2"
          />
          <div
            class="flex max-md:flex-col max-md:space-y-2 md:space-x-6"
          >
            <div
              class="flex items-center"
            >
              <svg
                class="fill-current w-6 h-6"
                viewBox="0 0 24 24"
                xmlns="http://www.w3.org/2000/svg"
              >
                <title>
                  Domestic location
                </title>
                <g>
                  <path
                    d="m22.07,6.92L14.05.84c-1.21-.92-2.89-.92-4.1,0L1.93,6.92c-.84.64-1.34,1.65-1.34,2.7v10.84c0,1.87,1.52,3.39,3.39,3.39h5.04c.41,0,.75-.34.75-.75s-.34-.75-.75-.75H3.98c-1.04,0-1.89-.85-1.89-1.89v-10.84c0-.59.28-1.15.75-1.5L10.86,2.03c.67-.51,1.61-.51,2.28,0l8.02,6.08c.47.36.74.92.74,1.5v10.84c0,1.04-.85,1.89-1.89,1.89h-5.53c-.96,0-1.74-.78-1.74-1.74v-2.63c1.85-.35,3.25-1.98,3.25-3.93v-3.25c0-.41-.34-.75-.75-.75h-.81v-1.88c0-.41-.34-.75-.75-.75s-.75.34-.75.75v1.88h-1.77v-1.88c0-.41-.34-.75-.75-.75s-.75.34-.75.75v1.88h-.93c-.41,0-.75.34-.75.75v3.25c0,1.95,1.4,3.57,3.25,3.93v2.63c0,1.79,1.45,3.24,3.24,3.24h5.53c1.87,0,3.39-1.52,3.39-3.39v-10.84c0-1.05-.5-2.06-1.34-2.7Zm-12.58,7.13v-2.5h5.01v2.5c0,1.38-1.12,2.5-2.5,2.5s-2.5-1.12-2.5-2.5Z"
                  />
                </g>
              </svg>
              <span
                class="ml-2"
              >
                Domestic
              </span>
            </div>
          </div>
        </div>
      </header>
    </section>
    <div
      class="pb-2"
    />
    <div
      class="flex flex-row flex-wrap gap-x-4 gap-y-2"
    >
      <span
        aria-label="Fault vendorErrorCode4 - Invalid Date"
        class="font-bold px-2.5 py-1 rounded-xs inline-flex items-center tracking-wide bg-warning/10 border border-error/70 text-error/70"
        role="status"
      >
        Fault vendorErrorCode4 - Invalid Date
      </span>
      <span
        aria-label="Fault vendorErrorCode3 - Invalid Date"
        class="font-bold px-2.5 py-1 rounded-xs inline-flex items-center tracking-wide bg-warning/10 border border-error/70 text-error/70"
        role="status"
      >
        Fault vendorErrorCode3 - Invalid Date
      </span>
      <span
        aria-label="Fault vendorErrorCode2 - Invalid Date"
        class="font-bold px-2.5 py-1 rounded-xs inline-flex items-center tracking-wide bg-warning/10 border border-error/70 text-error/70"
        role="status"
      >
        Fault vendorErrorCode2 - Invalid Date
      </span>
      <span
        aria-label="Fault vendorErrorCode1 - Invalid Date"
        class="font-bold px-2.5 py-1 rounded-xs inline-flex items-center tracking-wide bg-warning/10 border border-error/70 text-error/70"
        role="status"
      >
        Fault vendorErrorCode1 - Invalid Date
      </span>
    </div>
    <div
      class="pb-2"
    />
    <div
      class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 print:grid-cols-3 gap-2"
    >
      <section
        class="p-3 rounded-sm bg-white md:col-span-2 lg:col-span-1"
      >
        <div
          class="flex justify-between items-baseline"
        >
          <h3
            class="text-lg font-bold pb-4"
          >
            Charger information
          </h3>
          <a
            class="text-primary underline hover:decoration-2 focus:decoration-2 focus:outline-none cursor-pointer text-right ml-2"
            href="/chargers/PSL-12345/logs/diagnostic?socket=A"
          >
            View diagnostic logs
          </a>
        </div>
        <div
          class="flex flex-col bg-white rounded-sm overflow-hidden overflow-x-auto"
        >
          <div
            class="inline-block min-w-full -my-2 py-2 align-middle"
          >
            <table
              class="min-w-full"
              tabindex="0"
            >
              <caption
                class="sr-only"
              >
                Charger information table
              </caption>
              <tbody
                class="divide-y"
              >
                <tr
                  class="border-b-neutral/20"
                >
                  <td
                    class="whitespace-normal px-3 py-4 pl-0 pr-2 pt-2 pb-2 whitespace-pre"
                  >
                    Model SKU
                  </td>
                  <td
                    class="whitespace-normal px-3 py-4 pr-0 pl-2 pt-2 pb-2 text-neutral text-right"
                  >
                    T7-S-07-AMC-BLK
                  </td>
                </tr>
                <tr
                  class="border-b-neutral/20"
                >
                  <td
                    class="whitespace-normal px-3 py-4 pl-0 pr-2 pt-2 pb-2 whitespace-pre"
                  >
                    Arch version
                  </td>
                  <td
                    class="whitespace-normal px-3 py-4 pr-0 pl-2 pt-2 pb-2 text-neutral text-right"
                  >
                    -
                  </td>
                </tr>
                <tr
                  class="border-b-neutral/20"
                >
                  <td
                    class="whitespace-normal px-3 py-4 pl-0 pr-2 pt-2 pb-2 whitespace-pre"
                  >
                    Firmware
                  </td>
                  <td
                    class="whitespace-normal px-3 py-4 pr-0 pl-2 pt-2 pb-2 text-neutral text-right"
                  >
                    A50P-X.Y.Z-0000P
                  </td>
                </tr>
                <tr
                  class="border-b-neutral/20"
                >
                  <td
                    class="whitespace-normal px-3 py-4 pl-0 pr-2 pt-2 pb-2 whitespace-pre"
                  >
                    Manufactured
                  </td>
                  <td
                    class="whitespace-normal px-3 py-4 pr-0 pl-2 pt-2 pb-2 text-neutral text-right"
                  >
                    1 June 2023
                  </td>
                </tr>
                <tr
                  class="border-b-neutral/20"
                >
                  <td
                    class="whitespace-normal px-3 py-4 pl-0 pr-2 pt-2 pb-2 whitespace-pre"
                  >
                    Operator
                  </td>
                  <td
                    class="whitespace-normal px-3 py-4 pr-0 pl-2 pt-2 pb-2 text-neutral text-right"
                  >
                    Pod Point
                  </td>
                </tr>
                <tr
                  class="border-b-neutral/20"
                >
                  <td
                    class="whitespace-normal px-3 py-4 pl-0 pr-2 pt-2 pb-2 whitespace-pre"
                  >
                    MAC address
                  </td>
                  <td
                    class="whitespace-normal px-3 py-4 pr-0 pl-2 pt-2 pb-2 text-neutral text-right"
                  >
                    -
                  </td>
                </tr>
                <tr
                  class="border-b-neutral/20"
                >
                  <td
                    class="whitespace-normal px-3 py-4 pl-0 pr-2 pt-2 pb-2 whitespace-pre"
                  >
                    PCBA serial number
                  </td>
                  <td
                    class="whitespace-normal px-3 py-4 pr-0 pl-2 pt-2 pb-2 text-neutral text-right"
                  >
                    -
                  </td>
                </tr>
              </tbody>
            </table>
          </div>
        </div>
        <div
          class="flex flex-row-reverse"
        >
          <a
            class="text-primary underline hover:decoration-2 focus:decoration-2 focus:outline-none cursor-pointer text-right ml-2"
            href="/chargers/PSL-12345/pcbs/history?socket=A"
          >
            History
          </a>
        </div>
      </section>
      <section
        class="p-3 rounded-sm bg-white"
      >
        <h3
          class="text-lg font-bold pb-4"
        >
          Charger connectivity
        </h3>
        <div
          class="flex flex-col bg-white rounded-sm overflow-hidden overflow-x-auto"
        >
          <div
            class="inline-block min-w-full -my-2 py-2 align-middle"
          >
            <table
              class="min-w-full"
              tabindex="0"
            >
              <caption
                class="sr-only"
              >
                Charger connectivity table
              </caption>
              <tbody
                class="divide-y"
              >
                <tr
                  class="border-b-neutral/20"
                >
                  <td
                    class="whitespace-normal px-3 py-4 pl-0 pr-2 pt-2 pb-2 whitespace-pre"
                  >
                    Status
                  </td>
                  <td
                    class="whitespace-normal px-3 py-4 pr-0 pl-2 pt-2 pb-2 text-neutral text-right"
                  >
                    <span
                      aria-label="connectivityStatus"
                      class="font-bold px-2.5 rounded-xs inline-flex items-center tracking-wide bg-success/10 border border-success text-success py-px text-xs"
                      role="status"
                    >
                      connectivityStatus
                    </span>
                  </td>
                </tr>
                <tr
                  class="border-b-neutral/20"
                >
                  <td
                    class="whitespace-normal px-3 py-4 pl-0 pr-2 pt-2 pb-2 whitespace-pre"
                  >
                    Charging status
                  </td>
                  <td
                    class="whitespace-normal px-3 py-4 pr-0 pl-2 pt-2 pb-2 text-neutral text-right"
                  >
                    chargingState
                  </td>
                </tr>
                <tr
                  class="border-b-neutral/20"
                >
                  <td
                    class="whitespace-normal px-3 py-4 pl-0 pr-2 pt-2 pb-2 whitespace-pre"
                  >
                    Last message
                  </td>
                  <td
                    class="whitespace-normal px-3 py-4 pr-0 pl-2 pt-2 pb-2 text-neutral text-right"
                  >
                    30 June 2024 at 22:00:00
                  </td>
                </tr>
                <tr
                  class="border-b-neutral/20"
                >
                  <td
                    class="whitespace-normal px-3 py-4 pl-0 pr-2 pt-2 pb-2 whitespace-pre"
                  >
                    Connection quality
(scale 1-5)
                  </td>
                  <td
                    class="whitespace-normal px-3 py-4 pr-0 pl-2 pt-2 pb-2 text-neutral text-right"
                  >
                    0
                  </td>
                </tr>
                <tr
                  class="border-b-neutral/20"
                >
                  <td
                    class="whitespace-normal px-3 py-4 pl-0 pr-2 pt-2 pb-2 whitespace-pre"
                  >
                    Router serial number
                  </td>
                  <td
                    class="whitespace-normal px-3 py-4 pr-0 pl-2 pt-2 pb-2 text-neutral text-right"
                  >
                    1102538429
                  </td>
                </tr>
                <tr
                  class="border-b-neutral/20"
                >
                  <td
                    class="whitespace-normal px-3 py-4 pl-0 pr-2 pt-2 pb-2 whitespace-pre"
                  >
                    Router MAC address
                  </td>
                  <td
                    class="whitespace-normal px-3 py-4 pr-0 pl-2 pt-2 pb-2 text-neutral text-right"
                  >
                    001E4223CF13
                  </td>
                </tr>
                <tr
                  class="border-b-neutral/20"
                >
                  <td
                    class="whitespace-normal px-3 py-4 pl-0 pr-2 pt-2 pb-2 whitespace-pre"
                  >
                    Router SIM number
                  </td>
                  <td
                    class="whitespace-normal px-3 py-4 pr-0 pl-2 pt-2 pb-2 text-neutral text-right"
                  >
                    123456789123000043
                  </td>
                </tr>
                <tr
                  class="border-b-neutral/20"
                >
                  <td
                    class="whitespace-normal px-3 py-4 pl-0 pr-2 pt-2 pb-2 whitespace-pre"
                  >
                    Router model
                  </td>
                  <td
                    class="whitespace-normal px-3 py-4 pr-0 pl-2 pt-2 pb-2 text-neutral text-right"
                  >
                    RUT241
                  </td>
                </tr>
              </tbody>
            </table>
          </div>
        </div>
      </section>
      <section
        class="p-3 rounded-sm bg-white"
      >
        <div
          class="flex justify-between items-baseline"
        >
          <h3
            class="text-lg font-bold pb-4"
          >
            Charger access point (AP)
          </h3>
          <a
            class="text-primary underline hover:decoration-2 focus:decoration-2 focus:outline-none cursor-pointer text-right ml-2"
            href="/chargers/PSL-12345?redact=true"
          >
            Hide password
          </a>
        </div>
        <div
          class="flex flex-col bg-white rounded-sm overflow-hidden overflow-x-auto"
        >
          <div
            class="inline-block min-w-full -my-2 py-2 align-middle"
          >
            <table
              class="min-w-full"
              tabindex="0"
            >
              <caption
                class="sr-only"
              >
                Charger access point
              </caption>
              <tbody
                class="divide-y"
              >
                <tr
                  class="border-b-neutral/20"
                >
                  <td
                    class="whitespace-normal px-3 py-4 pl-0 pr-2 pt-2 pb-2 whitespace-pre"
                  >
                    SSID
                  </td>
                  <td
                    class="whitespace-normal px-3 py-4 pr-0 pl-2 pt-2 pb-2 text-neutral text-right"
                  >
                    PP-123456
                  </td>
                </tr>
                <tr
                  class="border-b-neutral/20"
                >
                  <td
                    class="whitespace-normal px-3 py-4 pl-0 pr-2 pt-2 pb-2 whitespace-pre"
                  >
                    Password
                  </td>
                  <td
                    class="whitespace-normal px-3 py-4 pr-0 pl-2 pt-2 pb-2 text-neutral text-right"
                  >
                    password123
                  </td>
                </tr>
              </tbody>
            </table>
          </div>
        </div>
        <div
          class="border-2 border-dotted border-neutral/20 mt-2 h-32 p-3"
        >
          <svg
            class="w-full h-full"
            height="256"
            viewBox="0 0 25 25"
            width="256"
            xmlns="http://www.w3.org/2000/svg"
          >
            <path
              d="       M 7 0 l 1 0 0 1 -1 0 Z   M 10 0 l 1 0 0 1 -1 0 Z M 11 0 l 1 0 0 1 -1 0 Z   M 14 0 l 1 0 0 1 -1 0 Z M 15 0 l 1 0 0 1 -1 0 Z  M 17 0 l 1 0 0 1 -1 0 Z         M 1 1 l 1 0 0 1 -1 0 Z M 2 1 l 1 0 0 1 -1 0 Z M 3 1 l 1 0 0 1 -1 0 Z M 4 1 l 1 0 0 1 -1 0 Z M 5 1 l 1 0 0 1 -1 0 Z  M 7 1 l 1 0 0 1 -1 0 Z   M 10 1 l 1 0 0 1 -1 0 Z M 11 1 l 1 0 0 1 -1 0 Z  M 13 1 l 1 0 0 1 -1 0 Z  M 15 1 l 1 0 0 1 -1 0 Z M 16 1 l 1 0 0 1 -1 0 Z M 17 1 l 1 0 0 1 -1 0 Z  M 19 1 l 1 0 0 1 -1 0 Z M 20 1 l 1 0 0 1 -1 0 Z M 21 1 l 1 0 0 1 -1 0 Z M 22 1 l 1 0 0 1 -1 0 Z M 23 1 l 1 0 0 1 -1 0 Z   M 1 2 l 1 0 0 1 -1 0 Z    M 5 2 l 1 0 0 1 -1 0 Z  M 7 2 l 1 0 0 1 -1 0 Z M 8 2 l 1 0 0 1 -1 0 Z      M 14 2 l 1 0 0 1 -1 0 Z  M 16 2 l 1 0 0 1 -1 0 Z M 17 2 l 1 0 0 1 -1 0 Z  M 19 2 l 1 0 0 1 -1 0 Z    M 23 2 l 1 0 0 1 -1 0 Z   M 1 3 l 1 0 0 1 -1 0 Z    M 5 3 l 1 0 0 1 -1 0 Z  M 7 3 l 1 0 0 1 -1 0 Z M 8 3 l 1 0 0 1 -1 0 Z  M 10 3 l 1 0 0 1 -1 0 Z M 11 3 l 1 0 0 1 -1 0 Z   M 14 3 l 1 0 0 1 -1 0 Z M 15 3 l 1 0 0 1 -1 0 Z  M 17 3 l 1 0 0 1 -1 0 Z  M 19 3 l 1 0 0 1 -1 0 Z    M 23 3 l 1 0 0 1 -1 0 Z   M 1 4 l 1 0 0 1 -1 0 Z    M 5 4 l 1 0 0 1 -1 0 Z  M 7 4 l 1 0 0 1 -1 0 Z   M 10 4 l 1 0 0 1 -1 0 Z M 11 4 l 1 0 0 1 -1 0 Z   M 14 4 l 1 0 0 1 -1 0 Z M 15 4 l 1 0 0 1 -1 0 Z  M 17 4 l 1 0 0 1 -1 0 Z  M 19 4 l 1 0 0 1 -1 0 Z    M 23 4 l 1 0 0 1 -1 0 Z   M 1 5 l 1 0 0 1 -1 0 Z M 2 5 l 1 0 0 1 -1 0 Z M 3 5 l 1 0 0 1 -1 0 Z M 4 5 l 1 0 0 1 -1 0 Z M 5 5 l 1 0 0 1 -1 0 Z  M 7 5 l 1 0 0 1 -1 0 Z   M 10 5 l 1 0 0 1 -1 0 Z M 11 5 l 1 0 0 1 -1 0 Z M 12 5 l 1 0 0 1 -1 0 Z   M 15 5 l 1 0 0 1 -1 0 Z  M 17 5 l 1 0 0 1 -1 0 Z  M 19 5 l 1 0 0 1 -1 0 Z M 20 5 l 1 0 0 1 -1 0 Z M 21 5 l 1 0 0 1 -1 0 Z M 22 5 l 1 0 0 1 -1 0 Z M 23 5 l 1 0 0 1 -1 0 Z         M 7 6 l 1 0 0 1 -1 0 Z  M 9 6 l 1 0 0 1 -1 0 Z  M 11 6 l 1 0 0 1 -1 0 Z  M 13 6 l 1 0 0 1 -1 0 Z  M 15 6 l 1 0 0 1 -1 0 Z  M 17 6 l 1 0 0 1 -1 0 Z        M 0 7 l 1 0 0 1 -1 0 Z M 1 7 l 1 0 0 1 -1 0 Z M 2 7 l 1 0 0 1 -1 0 Z M 3 7 l 1 0 0 1 -1 0 Z M 4 7 l 1 0 0 1 -1 0 Z M 5 7 l 1 0 0 1 -1 0 Z M 6 7 l 1 0 0 1 -1 0 Z M 7 7 l 1 0 0 1 -1 0 Z  M 9 7 l 1 0 0 1 -1 0 Z  M 11 7 l 1 0 0 1 -1 0 Z  M 13 7 l 1 0 0 1 -1 0 Z M 14 7 l 1 0 0 1 -1 0 Z  M 16 7 l 1 0 0 1 -1 0 Z M 17 7 l 1 0 0 1 -1 0 Z M 18 7 l 1 0 0 1 -1 0 Z M 19 7 l 1 0 0 1 -1 0 Z M 20 7 l 1 0 0 1 -1 0 Z M 21 7 l 1 0 0 1 -1 0 Z M 22 7 l 1 0 0 1 -1 0 Z M 23 7 l 1 0 0 1 -1 0 Z M 24 7 l 1 0 0 1 -1 0 Z    M 3 8 l 1 0 0 1 -1 0 Z M 4 8 l 1 0 0 1 -1 0 Z   M 7 8 l 1 0 0 1 -1 0 Z      M 13 8 l 1 0 0 1 -1 0 Z   M 16 8 l 1 0 0 1 -1 0 Z     M 21 8 l 1 0 0 1 -1 0 Z M 22 8 l 1 0 0 1 -1 0 Z    M 1 9 l 1 0 0 1 -1 0 Z   M 4 9 l 1 0 0 1 -1 0 Z M 5 9 l 1 0 0 1 -1 0 Z M 6 9 l 1 0 0 1 -1 0 Z    M 10 9 l 1 0 0 1 -1 0 Z    M 14 9 l 1 0 0 1 -1 0 Z  M 16 9 l 1 0 0 1 -1 0 Z M 17 9 l 1 0 0 1 -1 0 Z  M 19 9 l 1 0 0 1 -1 0 Z M 20 9 l 1 0 0 1 -1 0 Z   M 23 9 l 1 0 0 1 -1 0 Z M 24 9 l 1 0 0 1 -1 0 Z M 0 10 l 1 0 0 1 -1 0 Z  M 2 10 l 1 0 0 1 -1 0 Z   M 5 10 l 1 0 0 1 -1 0 Z    M 9 10 l 1 0 0 1 -1 0 Z M 10 10 l 1 0 0 1 -1 0 Z    M 14 10 l 1 0 0 1 -1 0 Z   M 17 10 l 1 0 0 1 -1 0 Z M 18 10 l 1 0 0 1 -1 0 Z M 19 10 l 1 0 0 1 -1 0 Z    M 23 10 l 1 0 0 1 -1 0 Z  M 0 11 l 1 0 0 1 -1 0 Z  M 2 11 l 1 0 0 1 -1 0 Z   M 5 11 l 1 0 0 1 -1 0 Z M 6 11 l 1 0 0 1 -1 0 Z M 7 11 l 1 0 0 1 -1 0 Z M 8 11 l 1 0 0 1 -1 0 Z  M 10 11 l 1 0 0 1 -1 0 Z  M 12 11 l 1 0 0 1 -1 0 Z M 13 11 l 1 0 0 1 -1 0 Z M 14 11 l 1 0 0 1 -1 0 Z  M 16 11 l 1 0 0 1 -1 0 Z M 17 11 l 1 0 0 1 -1 0 Z M 18 11 l 1 0 0 1 -1 0 Z  M 20 11 l 1 0 0 1 -1 0 Z M 21 11 l 1 0 0 1 -1 0 Z M 22 11 l 1 0 0 1 -1 0 Z   M 0 12 l 1 0 0 1 -1 0 Z M 1 12 l 1 0 0 1 -1 0 Z  M 3 12 l 1 0 0 1 -1 0 Z M 4 12 l 1 0 0 1 -1 0 Z      M 10 12 l 1 0 0 1 -1 0 Z  M 12 12 l 1 0 0 1 -1 0 Z M 13 12 l 1 0 0 1 -1 0 Z M 14 12 l 1 0 0 1 -1 0 Z  M 16 12 l 1 0 0 1 -1 0 Z M 17 12 l 1 0 0 1 -1 0 Z  M 19 12 l 1 0 0 1 -1 0 Z M 20 12 l 1 0 0 1 -1 0 Z  M 22 12 l 1 0 0 1 -1 0 Z   M 0 13 l 1 0 0 1 -1 0 Z  M 2 13 l 1 0 0 1 -1 0 Z   M 5 13 l 1 0 0 1 -1 0 Z M 6 13 l 1 0 0 1 -1 0 Z M 7 13 l 1 0 0 1 -1 0 Z   M 10 13 l 1 0 0 1 -1 0 Z   M 13 13 l 1 0 0 1 -1 0 Z   M 16 13 l 1 0 0 1 -1 0 Z  M 18 13 l 1 0 0 1 -1 0 Z M 19 13 l 1 0 0 1 -1 0 Z M 20 13 l 1 0 0 1 -1 0 Z    M 24 13 l 1 0 0 1 -1 0 Z      M 5 14 l 1 0 0 1 -1 0 Z  M 7 14 l 1 0 0 1 -1 0 Z  M 9 14 l 1 0 0 1 -1 0 Z M 10 14 l 1 0 0 1 -1 0 Z  M 12 14 l 1 0 0 1 -1 0 Z    M 16 14 l 1 0 0 1 -1 0 Z     M 21 14 l 1 0 0 1 -1 0 Z  M 23 14 l 1 0 0 1 -1 0 Z  M 0 15 l 1 0 0 1 -1 0 Z M 1 15 l 1 0 0 1 -1 0 Z   M 4 15 l 1 0 0 1 -1 0 Z M 5 15 l 1 0 0 1 -1 0 Z M 6 15 l 1 0 0 1 -1 0 Z M 7 15 l 1 0 0 1 -1 0 Z  M 9 15 l 1 0 0 1 -1 0 Z    M 13 15 l 1 0 0 1 -1 0 Z M 14 15 l 1 0 0 1 -1 0 Z M 15 15 l 1 0 0 1 -1 0 Z M 16 15 l 1 0 0 1 -1 0 Z M 17 15 l 1 0 0 1 -1 0 Z M 18 15 l 1 0 0 1 -1 0 Z  M 20 15 l 1 0 0 1 -1 0 Z M 21 15 l 1 0 0 1 -1 0 Z M 22 15 l 1 0 0 1 -1 0 Z M 23 15 l 1 0 0 1 -1 0 Z M 24 15 l 1 0 0 1 -1 0 Z   M 2 16 l 1 0 0 1 -1 0 Z   M 5 16 l 1 0 0 1 -1 0 Z  M 7 16 l 1 0 0 1 -1 0 Z  M 9 16 l 1 0 0 1 -1 0 Z  M 11 16 l 1 0 0 1 -1 0 Z  M 13 16 l 1 0 0 1 -1 0 Z M 14 16 l 1 0 0 1 -1 0 Z       M 21 16 l 1 0 0 1 -1 0 Z M 22 16 l 1 0 0 1 -1 0 Z   M 0 17 l 1 0 0 1 -1 0 Z M 1 17 l 1 0 0 1 -1 0 Z M 2 17 l 1 0 0 1 -1 0 Z M 3 17 l 1 0 0 1 -1 0 Z M 4 17 l 1 0 0 1 -1 0 Z M 5 17 l 1 0 0 1 -1 0 Z M 6 17 l 1 0 0 1 -1 0 Z M 7 17 l 1 0 0 1 -1 0 Z      M 13 17 l 1 0 0 1 -1 0 Z M 14 17 l 1 0 0 1 -1 0 Z   M 17 17 l 1 0 0 1 -1 0 Z M 18 17 l 1 0 0 1 -1 0 Z M 19 17 l 1 0 0 1 -1 0 Z  M 21 17 l 1 0 0 1 -1 0 Z M 22 17 l 1 0 0 1 -1 0 Z  M 24 17 l 1 0 0 1 -1 0 Z        M 7 18 l 1 0 0 1 -1 0 Z M 8 18 l 1 0 0 1 -1 0 Z M 9 18 l 1 0 0 1 -1 0 Z   M 12 18 l 1 0 0 1 -1 0 Z   M 15 18 l 1 0 0 1 -1 0 Z  M 17 18 l 1 0 0 1 -1 0 Z  M 19 18 l 1 0 0 1 -1 0 Z  M 21 18 l 1 0 0 1 -1 0 Z  M 23 18 l 1 0 0 1 -1 0 Z   M 1 19 l 1 0 0 1 -1 0 Z M 2 19 l 1 0 0 1 -1 0 Z M 3 19 l 1 0 0 1 -1 0 Z M 4 19 l 1 0 0 1 -1 0 Z M 5 19 l 1 0 0 1 -1 0 Z  M 7 19 l 1 0 0 1 -1 0 Z      M 13 19 l 1 0 0 1 -1 0 Z  M 15 19 l 1 0 0 1 -1 0 Z  M 17 19 l 1 0 0 1 -1 0 Z M 18 19 l 1 0 0 1 -1 0 Z M 19 19 l 1 0 0 1 -1 0 Z  M 21 19 l 1 0 0 1 -1 0 Z M 22 19 l 1 0 0 1 -1 0 Z    M 1 20 l 1 0 0 1 -1 0 Z    M 5 20 l 1 0 0 1 -1 0 Z  M 7 20 l 1 0 0 1 -1 0 Z M 8 20 l 1 0 0 1 -1 0 Z  M 10 20 l 1 0 0 1 -1 0 Z  M 12 20 l 1 0 0 1 -1 0 Z M 13 20 l 1 0 0 1 -1 0 Z M 14 20 l 1 0 0 1 -1 0 Z       M 21 20 l 1 0 0 1 -1 0 Z M 22 20 l 1 0 0 1 -1 0 Z M 23 20 l 1 0 0 1 -1 0 Z M 24 20 l 1 0 0 1 -1 0 Z  M 1 21 l 1 0 0 1 -1 0 Z    M 5 21 l 1 0 0 1 -1 0 Z  M 7 21 l 1 0 0 1 -1 0 Z M 8 21 l 1 0 0 1 -1 0 Z M 9 21 l 1 0 0 1 -1 0 Z    M 13 21 l 1 0 0 1 -1 0 Z   M 16 21 l 1 0 0 1 -1 0 Z  M 18 21 l 1 0 0 1 -1 0 Z   M 21 21 l 1 0 0 1 -1 0 Z M 22 21 l 1 0 0 1 -1 0 Z  M 24 21 l 1 0 0 1 -1 0 Z  M 1 22 l 1 0 0 1 -1 0 Z    M 5 22 l 1 0 0 1 -1 0 Z  M 7 22 l 1 0 0 1 -1 0 Z  M 9 22 l 1 0 0 1 -1 0 Z   M 12 22 l 1 0 0 1 -1 0 Z M 13 22 l 1 0 0 1 -1 0 Z  M 15 22 l 1 0 0 1 -1 0 Z M 16 22 l 1 0 0 1 -1 0 Z M 17 22 l 1 0 0 1 -1 0 Z     M 22 22 l 1 0 0 1 -1 0 Z    M 1 23 l 1 0 0 1 -1 0 Z M 2 23 l 1 0 0 1 -1 0 Z M 3 23 l 1 0 0 1 -1 0 Z M 4 23 l 1 0 0 1 -1 0 Z M 5 23 l 1 0 0 1 -1 0 Z  M 7 23 l 1 0 0 1 -1 0 Z   M 10 23 l 1 0 0 1 -1 0 Z  M 12 23 l 1 0 0 1 -1 0 Z M 13 23 l 1 0 0 1 -1 0 Z  M 15 23 l 1 0 0 1 -1 0 Z  M 17 23 l 1 0 0 1 -1 0 Z M 18 23 l 1 0 0 1 -1 0 Z   M 21 23 l 1 0 0 1 -1 0 Z M 22 23 l 1 0 0 1 -1 0 Z M 23 23 l 1 0 0 1 -1 0 Z M 24 23 l 1 0 0 1 -1 0 Z        M 7 24 l 1 0 0 1 -1 0 Z  M 9 24 l 1 0 0 1 -1 0 Z M 10 24 l 1 0 0 1 -1 0 Z  M 12 24 l 1 0 0 1 -1 0 Z M 13 24 l 1 0 0 1 -1 0 Z M 14 24 l 1 0 0 1 -1 0 Z       M 21 24 l 1 0 0 1 -1 0 Z M 22 24 l 1 0 0 1 -1 0 Z M 23 24 l 1 0 0 1 -1 0 Z "
              fill="#FFFFFF"
            />
            <path
              d="M 0 0 l 1 0 0 1 -1 0 Z M 1 0 l 1 0 0 1 -1 0 Z M 2 0 l 1 0 0 1 -1 0 Z M 3 0 l 1 0 0 1 -1 0 Z M 4 0 l 1 0 0 1 -1 0 Z M 5 0 l 1 0 0 1 -1 0 Z M 6 0 l 1 0 0 1 -1 0 Z  M 8 0 l 1 0 0 1 -1 0 Z M 9 0 l 1 0 0 1 -1 0 Z   M 12 0 l 1 0 0 1 -1 0 Z M 13 0 l 1 0 0 1 -1 0 Z   M 16 0 l 1 0 0 1 -1 0 Z  M 18 0 l 1 0 0 1 -1 0 Z M 19 0 l 1 0 0 1 -1 0 Z M 20 0 l 1 0 0 1 -1 0 Z M 21 0 l 1 0 0 1 -1 0 Z M 22 0 l 1 0 0 1 -1 0 Z M 23 0 l 1 0 0 1 -1 0 Z M 24 0 l 1 0 0 1 -1 0 Z M 0 1 l 1 0 0 1 -1 0 Z      M 6 1 l 1 0 0 1 -1 0 Z  M 8 1 l 1 0 0 1 -1 0 Z M 9 1 l 1 0 0 1 -1 0 Z   M 12 1 l 1 0 0 1 -1 0 Z  M 14 1 l 1 0 0 1 -1 0 Z    M 18 1 l 1 0 0 1 -1 0 Z      M 24 1 l 1 0 0 1 -1 0 Z M 0 2 l 1 0 0 1 -1 0 Z  M 2 2 l 1 0 0 1 -1 0 Z M 3 2 l 1 0 0 1 -1 0 Z M 4 2 l 1 0 0 1 -1 0 Z  M 6 2 l 1 0 0 1 -1 0 Z   M 9 2 l 1 0 0 1 -1 0 Z M 10 2 l 1 0 0 1 -1 0 Z M 11 2 l 1 0 0 1 -1 0 Z M 12 2 l 1 0 0 1 -1 0 Z M 13 2 l 1 0 0 1 -1 0 Z  M 15 2 l 1 0 0 1 -1 0 Z   M 18 2 l 1 0 0 1 -1 0 Z  M 20 2 l 1 0 0 1 -1 0 Z M 21 2 l 1 0 0 1 -1 0 Z M 22 2 l 1 0 0 1 -1 0 Z  M 24 2 l 1 0 0 1 -1 0 Z M 0 3 l 1 0 0 1 -1 0 Z  M 2 3 l 1 0 0 1 -1 0 Z M 3 3 l 1 0 0 1 -1 0 Z M 4 3 l 1 0 0 1 -1 0 Z  M 6 3 l 1 0 0 1 -1 0 Z   M 9 3 l 1 0 0 1 -1 0 Z   M 12 3 l 1 0 0 1 -1 0 Z M 13 3 l 1 0 0 1 -1 0 Z   M 16 3 l 1 0 0 1 -1 0 Z  M 18 3 l 1 0 0 1 -1 0 Z  M 20 3 l 1 0 0 1 -1 0 Z M 21 3 l 1 0 0 1 -1 0 Z M 22 3 l 1 0 0 1 -1 0 Z  M 24 3 l 1 0 0 1 -1 0 Z M 0 4 l 1 0 0 1 -1 0 Z  M 2 4 l 1 0 0 1 -1 0 Z M 3 4 l 1 0 0 1 -1 0 Z M 4 4 l 1 0 0 1 -1 0 Z  M 6 4 l 1 0 0 1 -1 0 Z  M 8 4 l 1 0 0 1 -1 0 Z M 9 4 l 1 0 0 1 -1 0 Z   M 12 4 l 1 0 0 1 -1 0 Z M 13 4 l 1 0 0 1 -1 0 Z   M 16 4 l 1 0 0 1 -1 0 Z  M 18 4 l 1 0 0 1 -1 0 Z  M 20 4 l 1 0 0 1 -1 0 Z M 21 4 l 1 0 0 1 -1 0 Z M 22 4 l 1 0 0 1 -1 0 Z  M 24 4 l 1 0 0 1 -1 0 Z M 0 5 l 1 0 0 1 -1 0 Z      M 6 5 l 1 0 0 1 -1 0 Z  M 8 5 l 1 0 0 1 -1 0 Z M 9 5 l 1 0 0 1 -1 0 Z    M 13 5 l 1 0 0 1 -1 0 Z M 14 5 l 1 0 0 1 -1 0 Z  M 16 5 l 1 0 0 1 -1 0 Z  M 18 5 l 1 0 0 1 -1 0 Z      M 24 5 l 1 0 0 1 -1 0 Z M 0 6 l 1 0 0 1 -1 0 Z M 1 6 l 1 0 0 1 -1 0 Z M 2 6 l 1 0 0 1 -1 0 Z M 3 6 l 1 0 0 1 -1 0 Z M 4 6 l 1 0 0 1 -1 0 Z M 5 6 l 1 0 0 1 -1 0 Z M 6 6 l 1 0 0 1 -1 0 Z  M 8 6 l 1 0 0 1 -1 0 Z  M 10 6 l 1 0 0 1 -1 0 Z  M 12 6 l 1 0 0 1 -1 0 Z  M 14 6 l 1 0 0 1 -1 0 Z  M 16 6 l 1 0 0 1 -1 0 Z  M 18 6 l 1 0 0 1 -1 0 Z M 19 6 l 1 0 0 1 -1 0 Z M 20 6 l 1 0 0 1 -1 0 Z M 21 6 l 1 0 0 1 -1 0 Z M 22 6 l 1 0 0 1 -1 0 Z M 23 6 l 1 0 0 1 -1 0 Z M 24 6 l 1 0 0 1 -1 0 Z         M 8 7 l 1 0 0 1 -1 0 Z  M 10 7 l 1 0 0 1 -1 0 Z  M 12 7 l 1 0 0 1 -1 0 Z   M 15 7 l 1 0 0 1 -1 0 Z          M 0 8 l 1 0 0 1 -1 0 Z M 1 8 l 1 0 0 1 -1 0 Z M 2 8 l 1 0 0 1 -1 0 Z   M 5 8 l 1 0 0 1 -1 0 Z M 6 8 l 1 0 0 1 -1 0 Z  M 8 8 l 1 0 0 1 -1 0 Z M 9 8 l 1 0 0 1 -1 0 Z M 10 8 l 1 0 0 1 -1 0 Z M 11 8 l 1 0 0 1 -1 0 Z M 12 8 l 1 0 0 1 -1 0 Z  M 14 8 l 1 0 0 1 -1 0 Z M 15 8 l 1 0 0 1 -1 0 Z  M 17 8 l 1 0 0 1 -1 0 Z M 18 8 l 1 0 0 1 -1 0 Z M 19 8 l 1 0 0 1 -1 0 Z M 20 8 l 1 0 0 1 -1 0 Z   M 23 8 l 1 0 0 1 -1 0 Z M 24 8 l 1 0 0 1 -1 0 Z M 0 9 l 1 0 0 1 -1 0 Z  M 2 9 l 1 0 0 1 -1 0 Z M 3 9 l 1 0 0 1 -1 0 Z    M 7 9 l 1 0 0 1 -1 0 Z M 8 9 l 1 0 0 1 -1 0 Z M 9 9 l 1 0 0 1 -1 0 Z  M 11 9 l 1 0 0 1 -1 0 Z M 12 9 l 1 0 0 1 -1 0 Z M 13 9 l 1 0 0 1 -1 0 Z  M 15 9 l 1 0 0 1 -1 0 Z   M 18 9 l 1 0 0 1 -1 0 Z   M 21 9 l 1 0 0 1 -1 0 Z M 22 9 l 1 0 0 1 -1 0 Z    M 1 10 l 1 0 0 1 -1 0 Z  M 3 10 l 1 0 0 1 -1 0 Z M 4 10 l 1 0 0 1 -1 0 Z  M 6 10 l 1 0 0 1 -1 0 Z M 7 10 l 1 0 0 1 -1 0 Z M 8 10 l 1 0 0 1 -1 0 Z   M 11 10 l 1 0 0 1 -1 0 Z M 12 10 l 1 0 0 1 -1 0 Z M 13 10 l 1 0 0 1 -1 0 Z  M 15 10 l 1 0 0 1 -1 0 Z M 16 10 l 1 0 0 1 -1 0 Z    M 20 10 l 1 0 0 1 -1 0 Z M 21 10 l 1 0 0 1 -1 0 Z M 22 10 l 1 0 0 1 -1 0 Z  M 24 10 l 1 0 0 1 -1 0 Z  M 1 11 l 1 0 0 1 -1 0 Z  M 3 11 l 1 0 0 1 -1 0 Z M 4 11 l 1 0 0 1 -1 0 Z     M 9 11 l 1 0 0 1 -1 0 Z  M 11 11 l 1 0 0 1 -1 0 Z    M 15 11 l 1 0 0 1 -1 0 Z    M 19 11 l 1 0 0 1 -1 0 Z    M 23 11 l 1 0 0 1 -1 0 Z M 24 11 l 1 0 0 1 -1 0 Z   M 2 12 l 1 0 0 1 -1 0 Z   M 5 12 l 1 0 0 1 -1 0 Z M 6 12 l 1 0 0 1 -1 0 Z M 7 12 l 1 0 0 1 -1 0 Z M 8 12 l 1 0 0 1 -1 0 Z M 9 12 l 1 0 0 1 -1 0 Z  M 11 12 l 1 0 0 1 -1 0 Z    M 15 12 l 1 0 0 1 -1 0 Z   M 18 12 l 1 0 0 1 -1 0 Z   M 21 12 l 1 0 0 1 -1 0 Z  M 23 12 l 1 0 0 1 -1 0 Z M 24 12 l 1 0 0 1 -1 0 Z  M 1 13 l 1 0 0 1 -1 0 Z  M 3 13 l 1 0 0 1 -1 0 Z M 4 13 l 1 0 0 1 -1 0 Z    M 8 13 l 1 0 0 1 -1 0 Z M 9 13 l 1 0 0 1 -1 0 Z  M 11 13 l 1 0 0 1 -1 0 Z M 12 13 l 1 0 0 1 -1 0 Z  M 14 13 l 1 0 0 1 -1 0 Z M 15 13 l 1 0 0 1 -1 0 Z  M 17 13 l 1 0 0 1 -1 0 Z    M 21 13 l 1 0 0 1 -1 0 Z M 22 13 l 1 0 0 1 -1 0 Z M 23 13 l 1 0 0 1 -1 0 Z  M 0 14 l 1 0 0 1 -1 0 Z M 1 14 l 1 0 0 1 -1 0 Z M 2 14 l 1 0 0 1 -1 0 Z M 3 14 l 1 0 0 1 -1 0 Z M 4 14 l 1 0 0 1 -1 0 Z  M 6 14 l 1 0 0 1 -1 0 Z  M 8 14 l 1 0 0 1 -1 0 Z   M 11 14 l 1 0 0 1 -1 0 Z  M 13 14 l 1 0 0 1 -1 0 Z M 14 14 l 1 0 0 1 -1 0 Z M 15 14 l 1 0 0 1 -1 0 Z  M 17 14 l 1 0 0 1 -1 0 Z M 18 14 l 1 0 0 1 -1 0 Z M 19 14 l 1 0 0 1 -1 0 Z M 20 14 l 1 0 0 1 -1 0 Z  M 22 14 l 1 0 0 1 -1 0 Z  M 24 14 l 1 0 0 1 -1 0 Z   M 2 15 l 1 0 0 1 -1 0 Z M 3 15 l 1 0 0 1 -1 0 Z     M 8 15 l 1 0 0 1 -1 0 Z  M 10 15 l 1 0 0 1 -1 0 Z M 11 15 l 1 0 0 1 -1 0 Z M 12 15 l 1 0 0 1 -1 0 Z       M 19 15 l 1 0 0 1 -1 0 Z      M 0 16 l 1 0 0 1 -1 0 Z M 1 16 l 1 0 0 1 -1 0 Z  M 3 16 l 1 0 0 1 -1 0 Z M 4 16 l 1 0 0 1 -1 0 Z  M 6 16 l 1 0 0 1 -1 0 Z  M 8 16 l 1 0 0 1 -1 0 Z  M 10 16 l 1 0 0 1 -1 0 Z  M 12 16 l 1 0 0 1 -1 0 Z   M 15 16 l 1 0 0 1 -1 0 Z M 16 16 l 1 0 0 1 -1 0 Z M 17 16 l 1 0 0 1 -1 0 Z M 18 16 l 1 0 0 1 -1 0 Z M 19 16 l 1 0 0 1 -1 0 Z M 20 16 l 1 0 0 1 -1 0 Z   M 23 16 l 1 0 0 1 -1 0 Z M 24 16 l 1 0 0 1 -1 0 Z         M 8 17 l 1 0 0 1 -1 0 Z M 9 17 l 1 0 0 1 -1 0 Z M 10 17 l 1 0 0 1 -1 0 Z M 11 17 l 1 0 0 1 -1 0 Z M 12 17 l 1 0 0 1 -1 0 Z   M 15 17 l 1 0 0 1 -1 0 Z M 16 17 l 1 0 0 1 -1 0 Z    M 20 17 l 1 0 0 1 -1 0 Z   M 23 17 l 1 0 0 1 -1 0 Z  M 0 18 l 1 0 0 1 -1 0 Z M 1 18 l 1 0 0 1 -1 0 Z M 2 18 l 1 0 0 1 -1 0 Z M 3 18 l 1 0 0 1 -1 0 Z M 4 18 l 1 0 0 1 -1 0 Z M 5 18 l 1 0 0 1 -1 0 Z M 6 18 l 1 0 0 1 -1 0 Z    M 10 18 l 1 0 0 1 -1 0 Z M 11 18 l 1 0 0 1 -1 0 Z  M 13 18 l 1 0 0 1 -1 0 Z M 14 18 l 1 0 0 1 -1 0 Z  M 16 18 l 1 0 0 1 -1 0 Z  M 18 18 l 1 0 0 1 -1 0 Z  M 20 18 l 1 0 0 1 -1 0 Z  M 22 18 l 1 0 0 1 -1 0 Z  M 24 18 l 1 0 0 1 -1 0 Z M 0 19 l 1 0 0 1 -1 0 Z      M 6 19 l 1 0 0 1 -1 0 Z  M 8 19 l 1 0 0 1 -1 0 Z M 9 19 l 1 0 0 1 -1 0 Z M 10 19 l 1 0 0 1 -1 0 Z M 11 19 l 1 0 0 1 -1 0 Z M 12 19 l 1 0 0 1 -1 0 Z  M 14 19 l 1 0 0 1 -1 0 Z  M 16 19 l 1 0 0 1 -1 0 Z    M 20 19 l 1 0 0 1 -1 0 Z   M 23 19 l 1 0 0 1 -1 0 Z M 24 19 l 1 0 0 1 -1 0 Z M 0 20 l 1 0 0 1 -1 0 Z  M 2 20 l 1 0 0 1 -1 0 Z M 3 20 l 1 0 0 1 -1 0 Z M 4 20 l 1 0 0 1 -1 0 Z  M 6 20 l 1 0 0 1 -1 0 Z   M 9 20 l 1 0 0 1 -1 0 Z  M 11 20 l 1 0 0 1 -1 0 Z    M 15 20 l 1 0 0 1 -1 0 Z M 16 20 l 1 0 0 1 -1 0 Z M 17 20 l 1 0 0 1 -1 0 Z M 18 20 l 1 0 0 1 -1 0 Z M 19 20 l 1 0 0 1 -1 0 Z M 20 20 l 1 0 0 1 -1 0 Z     M 0 21 l 1 0 0 1 -1 0 Z  M 2 21 l 1 0 0 1 -1 0 Z M 3 21 l 1 0 0 1 -1 0 Z M 4 21 l 1 0 0 1 -1 0 Z  M 6 21 l 1 0 0 1 -1 0 Z    M 10 21 l 1 0 0 1 -1 0 Z M 11 21 l 1 0 0 1 -1 0 Z M 12 21 l 1 0 0 1 -1 0 Z  M 14 21 l 1 0 0 1 -1 0 Z M 15 21 l 1 0 0 1 -1 0 Z  M 17 21 l 1 0 0 1 -1 0 Z  M 19 21 l 1 0 0 1 -1 0 Z M 20 21 l 1 0 0 1 -1 0 Z   M 23 21 l 1 0 0 1 -1 0 Z  M 0 22 l 1 0 0 1 -1 0 Z  M 2 22 l 1 0 0 1 -1 0 Z M 3 22 l 1 0 0 1 -1 0 Z M 4 22 l 1 0 0 1 -1 0 Z  M 6 22 l 1 0 0 1 -1 0 Z  M 8 22 l 1 0 0 1 -1 0 Z  M 10 22 l 1 0 0 1 -1 0 Z M 11 22 l 1 0 0 1 -1 0 Z   M 14 22 l 1 0 0 1 -1 0 Z    M 18 22 l 1 0 0 1 -1 0 Z M 19 22 l 1 0 0 1 -1 0 Z M 20 22 l 1 0 0 1 -1 0 Z M 21 22 l 1 0 0 1 -1 0 Z  M 23 22 l 1 0 0 1 -1 0 Z M 24 22 l 1 0 0 1 -1 0 Z M 0 23 l 1 0 0 1 -1 0 Z      M 6 23 l 1 0 0 1 -1 0 Z  M 8 23 l 1 0 0 1 -1 0 Z M 9 23 l 1 0 0 1 -1 0 Z  M 11 23 l 1 0 0 1 -1 0 Z   M 14 23 l 1 0 0 1 -1 0 Z  M 16 23 l 1 0 0 1 -1 0 Z   M 19 23 l 1 0 0 1 -1 0 Z M 20 23 l 1 0 0 1 -1 0 Z     M 0 24 l 1 0 0 1 -1 0 Z M 1 24 l 1 0 0 1 -1 0 Z M 2 24 l 1 0 0 1 -1 0 Z M 3 24 l 1 0 0 1 -1 0 Z M 4 24 l 1 0 0 1 -1 0 Z M 5 24 l 1 0 0 1 -1 0 Z M 6 24 l 1 0 0 1 -1 0 Z  M 8 24 l 1 0 0 1 -1 0 Z   M 11 24 l 1 0 0 1 -1 0 Z    M 15 24 l 1 0 0 1 -1 0 Z M 16 24 l 1 0 0 1 -1 0 Z M 17 24 l 1 0 0 1 -1 0 Z M 18 24 l 1 0 0 1 -1 0 Z M 19 24 l 1 0 0 1 -1 0 Z M 20 24 l 1 0 0 1 -1 0 Z    M 24 24 l 1 0 0 1 -1 0 Z"
              fill="#000000"
            />
          </svg>
        </div>
      </section>
    </div>
    <div
      class="pb-2"
    />
    <div
      class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 print:grid-cols-3 gap-2"
    >
      <section
        class="p-3 rounded-sm bg-white"
      >
        <div
          class="flex justify-between items-baseline"
        >
          <h3
            class="text-lg font-bold pb-4"
          >
            Installation details
          </h3>
          <a
            class="text-primary underline hover:decoration-2 focus:decoration-2 focus:outline-none cursor-pointer text-right ml-2"
            href="/chargers/PSL-12345/installation?socket=A"
          >
            View installation details
          </a>
        </div>
        <div
          class="flex flex-col bg-white rounded-sm overflow-hidden overflow-x-auto"
        >
          <div
            class="inline-block min-w-full -my-2 py-2 align-middle"
          >
            <table
              class="min-w-full"
              tabindex="0"
            >
              <caption
                class="sr-only"
              >
                Installation details table
              </caption>
              <tbody
                class="divide-y"
              >
                <tr
                  class="border-b-neutral/20"
                >
                  <td
                    class="whitespace-normal px-3 py-4 pl-0 pr-2 pt-2 pb-2 whitespace-pre"
                  >
                    Install date
                  </td>
                  <td
                    class="whitespace-normal px-3 py-4 pr-0 pl-2 pt-2 pb-2 text-neutral text-right"
                  >
                    7 July 2023
                  </td>
                </tr>
                <tr
                  class="border-b-neutral/20"
                >
                  <td
                    class="whitespace-normal px-3 py-4 pl-0 pr-2 pt-2 pb-2 whitespace-pre"
                  >
                    Installed by
                  </td>
                  <td
                    class="whitespace-normal px-3 py-4 pr-0 pl-2 pt-2 pb-2 text-neutral text-right"
                  >
                    Joe Spark
                  </td>
                </tr>
                <tr
                  class="border-b-neutral/20"
                >
                  <td
                    class="whitespace-normal px-3 py-4 pl-0 pr-2 pt-2 pb-2 whitespace-pre"
                  >
                    Company
                  </td>
                  <td
                    class="whitespace-normal px-3 py-4 pr-0 pl-2 pt-2 pb-2 text-neutral text-right"
                  >
                    Electrical Services Ltd.
                  </td>
                </tr>
              </tbody>
            </table>
          </div>
        </div>
      </section>
      <section
        class="p-3 rounded-sm bg-white"
      >
        <h3
          class="text-lg font-bold pb-4"
        >
          Warranty details
        </h3>
        <div
          class="flex flex-col bg-white rounded-sm overflow-hidden overflow-x-auto"
        >
          <div
            class="inline-block min-w-full -my-2 py-2 align-middle"
          >
            <table
              class="min-w-full"
              tabindex="0"
            >
              <caption
                class="sr-only"
              >
                Warranty details table
              </caption>
              <tbody
                class="divide-y"
              >
                <tr
                  class="border-b-neutral/20"
                >
                  <td
                    class="whitespace-normal px-3 py-4 pl-0 pr-2 pt-2 pb-2 whitespace-pre"
                  >
                    Warranty start date
                  </td>
                  <td
                    class="whitespace-normal px-3 py-4 pr-0 pl-2 pt-2 pb-2 text-neutral text-right"
                  >
                    1 August 2024
                  </td>
                </tr>
                <tr
                  class="border-b-neutral/20"
                >
                  <td
                    class="whitespace-normal px-3 py-4 pl-0 pr-2 pt-2 pb-2 whitespace-pre"
                  >
                    Warranty end date
                  </td>
                  <td
                    class="whitespace-normal px-3 py-4 pr-0 pl-2 pt-2 pb-2 text-neutral text-right"
                  >
                    1st August 2027
                  </td>
                </tr>
                <tr
                  class="border-b-neutral/20"
                >
                  <td
                    class="whitespace-normal px-3 py-4 pl-0 pr-2 pt-2 pb-2 whitespace-pre"
                  >
                    Warranty status
                  </td>
                  <td
                    class="whitespace-normal px-3 py-4 pr-0 pl-2 pt-2 pb-2 text-neutral text-right"
                  >
                    <span
                      aria-label="Active"
                      class="font-bold px-2.5 py-1 rounded-xs inline-flex items-center tracking-wide bg-success/10 border border-success text-success"
                      role="status"
                    >
                      Active
                    </span>
                  </td>
                </tr>
              </tbody>
            </table>
          </div>
        </div>
      </section>
      <section
        class="p-3 rounded-sm bg-white"
      >
        <div
          class="flex justify-between items-baseline"
        >
          <h3
            class="text-lg font-bold pb-4"
          >
            Last complete charge
          </h3>
          <a
            class="text-primary underline hover:decoration-2 focus:decoration-2 focus:outline-none cursor-pointer text-right ml-2"
            href="/chargers/PSL-12345/recent-charges?socket=A"
          >
            Recent charges
          </a>
        </div>
        <div
          class="flex flex-col bg-white rounded-sm overflow-hidden overflow-x-auto"
        >
          <div
            class="inline-block min-w-full -my-2 py-2 align-middle"
          >
            <table
              class="min-w-full"
              tabindex="0"
            >
              <caption
                class="sr-only"
              >
                Last complete charge table
              </caption>
              <tbody
                class="divide-y"
              >
                <tr
                  class="border-b-neutral/20"
                >
                  <td
                    class="whitespace-normal px-3 py-4 pl-0 pr-2 pt-2 pb-2 whitespace-pre"
                  >
                    Started at
                  </td>
                  <td
                    class="whitespace-normal px-3 py-4 pr-0 pl-2 pt-2 pb-2 text-neutral text-right"
                  >
                    30 June 2024 at 19:00:00
                  </td>
                </tr>
                <tr
                  class="border-b-neutral/20"
                >
                  <td
                    class="whitespace-normal px-3 py-4 pl-0 pr-2 pt-2 pb-2 whitespace-pre"
                  >
                    Ended at
                  </td>
                  <td
                    class="whitespace-normal px-3 py-4 pr-0 pl-2 pt-2 pb-2 text-neutral text-right"
                  >
                    30 June 2024 at 21:00:00
                  </td>
                </tr>
                <tr
                  class="border-b-neutral/20"
                >
                  <td
                    class="whitespace-normal px-3 py-4 pl-0 pr-2 pt-2 pb-2 whitespace-pre"
                  >
                    kWh delivered
                  </td>
                  <td
                    class="whitespace-normal px-3 py-4 pr-0 pl-2 pt-2 pb-2 text-neutral text-right"
                  >
                    567.89 kWh
                  </td>
                </tr>
              </tbody>
            </table>
          </div>
        </div>
      </section>
    </div>
    <div
      class="pb-2"
    />
    <section
      class="p-3 rounded-sm bg-white"
    >
      <div
        class="flex justify-between items-baseline"
      >
        <h3
          class="text-lg font-bold pb-4"
        >
          Modes
        </h3>
        <a
          class="text-primary underline hover:decoration-2 focus:decoration-2 focus:outline-none cursor-pointer text-right ml-2"
          href="/chargers/PSL-12345/mode-history"
        >
          View history
        </a>
      </div>
      <div
        class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 print:grid-cols-3 gap-2"
      >
        <div
          class="pb-2 break-words"
        >
          <div
            class="flex"
          >
            <div
              data-headlessui-state=""
            >
              <div
                class="mr-2"
              >
                <label
                  class="text-lg font-normal block mb-1.5"
                  for="toggle-smart-mode"
                  id="toggle-smart-mode-label"
                >
                  Smart mode
                </label>
              </div>
              <button
                aria-checked="true"
                aria-label="toggle-smart-mode"
                class="bg-primary group relative inline-flex h-6 w-11 shrink-0 cursor-pointer rounded-full border-2 border-transparent transition-colors duration-200 ease-in-out focus:outline-hidden focus:ring-2 focus:ring-primary/50 focus:ring-offset-2 data-[disabled]:bg-neutral/40 data-[disabled]:cursor-not-allowed"
                data-checked=""
                data-headlessui-state="checked"
                id="toggle-smart-mode"
                role="switch"
                tabindex="0"
                type="button"
              >
                <span
                  aria-hidden="true"
                  class="translate-x-5 pointer-events-none inline-block h-5 w-5 transform rounded-full bg-white shadow-sm ring-0 transition duration-200 ease-in-out group-data-[disabled]:bg-neutral/20 group-data-[disabled]:cursor-default"
                />
              </button>
              <span
                hidden=""
                style="position: fixed; top: 1px; left: 1px; width: 1px; height: 0px; padding: 0px; margin: -1px; overflow: hidden; clip: rect(0px, 0px, 0px, 0px); white-space: nowrap; border-width: 0px; display: none;"
              />
            </div>
          </div>
        </div>
        <div
          class="pb-2 break-words"
        >
          <div
            class="flex"
          >
            <div
              data-headlessui-state=""
            >
              <div
                class="mr-2"
              >
                <label
                  class="text-lg font-normal block mb-1.5"
                  for="toggle-off-mode"
                  id="toggle-off-mode-label"
                >
                  Off mode
                </label>
              </div>
              <button
                aria-checked="false"
                aria-label="toggle-off-mode"
                class="bg-neutral/20 group relative inline-flex h-6 w-11 shrink-0 cursor-pointer rounded-full border-2 border-transparent transition-colors duration-200 ease-in-out focus:outline-hidden focus:ring-2 focus:ring-primary/50 focus:ring-offset-2 data-[disabled]:bg-neutral/40 data-[disabled]:cursor-not-allowed"
                data-headlessui-state=""
                id="toggle-off-mode"
                role="switch"
                tabindex="0"
                type="button"
              >
                <span
                  aria-hidden="true"
                  class="translate-x-0 pointer-events-none inline-block h-5 w-5 transform rounded-full bg-white shadow-sm ring-0 transition duration-200 ease-in-out group-data-[disabled]:bg-neutral/20 group-data-[disabled]:cursor-default"
                />
              </button>
              <span
                hidden=""
                style="position: fixed; top: 1px; left: 1px; width: 1px; height: 0px; padding: 0px; margin: -1px; overflow: hidden; clip: rect(0px, 0px, 0px, 0px); white-space: nowrap; border-width: 0px; display: none;"
              />
            </div>
          </div>
        </div>
        <div
          class="pb-2 break-words"
        >
          <div
            class="flex"
          >
            <div
              data-headlessui-state=""
            >
              <div
                class="mr-2"
              >
                <label
                  class="text-lg font-normal block mb-1.5"
                  for="toggle-charge-now"
                  id="toggle-charge-now-label"
                >
                  Charge now
                </label>
              </div>
              <button
                aria-checked="false"
                aria-label="toggle-charge-now"
                class="bg-neutral/20 group relative inline-flex h-6 w-11 shrink-0 cursor-pointer rounded-full border-2 border-transparent transition-colors duration-200 ease-in-out focus:outline-hidden focus:ring-2 focus:ring-primary/50 focus:ring-offset-2 data-[disabled]:bg-neutral/40 data-[disabled]:cursor-not-allowed"
                data-headlessui-state=""
                id="toggle-charge-now"
                role="switch"
                tabindex="0"
                type="button"
              >
                <span
                  aria-hidden="true"
                  class="translate-x-0 pointer-events-none inline-block h-5 w-5 transform rounded-full bg-white shadow-sm ring-0 transition duration-200 ease-in-out group-data-[disabled]:bg-neutral/20 group-data-[disabled]:cursor-default"
                />
              </button>
              <span
                hidden=""
                style="position: fixed; top: 1px; left: 1px; width: 1px; height: 0px; padding: 0px; margin: -1px; overflow: hidden; clip: rect(0px, 0px, 0px, 0px); white-space: nowrap; border-width: 0px; display: none;"
              />
            </div>
          </div>
        </div>
        <div
          class="pb-2 break-words"
        >
          <h5
            class="text-lg"
          >
            Flex
          </h5>
          <span
            class="text-xl font-semibold"
          >
            <a
              class="text-primary underline hover:decoration-2 focus:decoration-2 focus:outline-none text-xl font-semibold hover:underline cursor-pointer"
              href="/chargers/PSL-12345/flex-details"
            >
              Enrolled
            </a>
          </span>
        </div>
        <div
          class="flex"
        >
          <div
            class="pb-2 break-words"
          >
            <h5
              class="text-lg"
            >
              Delegated control
            </h5>
            <div
              class="flex justify-between items-center"
            >
              <span
                class="text-xl font-semibold"
              >
                Active since 30 June 2024 at 21:00:00 (3rd Party)
              </span>
              <button
                aria-label="Remove delegated control"
                class="border-2 border-solid rounded-sm font-bold text-center disabled:no-underline transition ease-in-out duration-200 cursor-pointer disabled:cursor-not-allowed border-transparent text-primary hover:underline hover:text-primary active:font-bold active:text-primary disabled:text-neutral/40 disabled:hover:no-underline disabled:hover:border-transparent p-1.5"
                type="button"
              >
                <svg
                  class="h-4 w-4 stroke-1 stroke-current fill-current"
                  viewBox="0 0 32 32"
                  xmlns="http://www.w3.org/2000/svg"
                >
                  <title>
                    Cross
                  </title>
                  <g>
                    <path
                      d="M16,.07C7.22,.07,.07,7.22,.07,16s7.15,15.93,15.93,15.93,15.93-7.15,15.93-15.93S24.78,.07,16,.07Zm0,29.86c-7.68,0-13.93-6.25-13.93-13.93S8.32,2.07,16,2.07s13.93,6.25,13.93,13.93-6.25,13.93-13.93,13.93Z"
                    />
                    <path
                      d="M23.38,8.62c-.39-.39-1.02-.39-1.41,0l-5.96,5.96-5.96-5.96c-.39-.39-1.02-.39-1.41,0s-.39,1.02,0,1.41l5.96,5.96-5.96,5.96c-.39,.39-.39,1.02,0,1.41,.2,.2,.45,.29,.71,.29s.51-.1,.71-.29l5.96-5.96,5.96,5.96c.2,.2,.45,.29,.71,.29s.51-.1,.71-.29c.39-.39,.39-1.02,0-1.41l-5.96-5.96,5.96-5.96c.39-.39,.39-1.02,0-1.41Z"
                    />
                  </g>
                </svg>
              </button>
            </div>
          </div>
        </div>
      </div>
    </section>
    <div
      class="pb-2"
    />
    <section
      class="p-3 rounded-sm bg-white"
    >
      <div
        class="flex justify-between items-baseline"
      >
        <h3
          class="text-lg font-bold pb-4"
        >
          Schedules
        </h3>
        <a
          class="text-primary underline hover:decoration-2 focus:decoration-2 focus:outline-none cursor-pointer text-right ml-2"
          href="/chargers/PSL-12345/schedules"
        >
          View schedules
        </a>
      </div>
      <div
        class="rounded-md p-3 bg-info/10 border border-info"
      >
        <div
          class="flex"
        >
          <div
            class="shrink-0"
          >
            <span
              class="text-info"
            >
              <svg
                class="fill-current w-6 h-6"
                viewBox="0 0 32 32"
                xmlns="http://www.w3.org/2000/svg"
              >
                <g>
                  <path
                    d="M16.1,29.43A13.43,13.43,0,1,1,29.53,16,13.45,13.45,0,0,1,16.1,29.43Zm0-24.86A11.43,11.43,0,1,0,27.53,16,11.45,11.45,0,0,0,16.1,4.57Z"
                  />
                  <path
                    d="M16,24.12a1,1,0,0,1-1-1V14.94a1,1,0,1,1,2,0v8.18A1,1,0,0,1,16,24.12Z"
                  />
                  <path
                    d="M16.1,10.89a1,1,0,0,1-1-1v-.2a1,1,0,0,1,2,0v.2A1,1,0,0,1,16.1,10.89Z"
                  />
                </g>
              </svg>
            </span>
          </div>
          <div
            class="flex flex-col justify-center ml-4"
          >
            <p
              class="text-md font-normal sr-only break-words"
            >
              Alert
            </p>
            <div
              class="text-info"
            >
              <p
                class="text-md font-normal break-words"
              >
                This charger is currently under delegated control. The below schedules are indicative only and subject to change.
              </p>
            </div>
          </div>
        </div>
      </div>
      <div
        class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-2 gap-2"
      >
        <div
          class="flex flex-col bg-white rounded-sm overflow-hidden overflow-x-auto"
        >
          <div
            class="inline-block min-w-full -my-2 py-2 align-middle"
          >
            <table
              class="min-w-full"
              tabindex="0"
            >
              <caption
                class="sr-only"
              >
                Table of charge schedules and modes
              </caption>
              <thead
                class="border-b border-b-neutral/20"
              >
                <tr
                  class="border-b-neutral/20"
                >
                  <th
                    class="text-left p-3"
                    scope="col"
                  >
                    Start
                  </th>
                  <th
                    class="text-left p-3"
                    scope="col"
                  >
                    Duration
                  </th>
                  <th
                    class="text-left text-center p-3"
                    scope="col"
                  >
                    Active
                  </th>
                </tr>
              </thead>
              <tbody
                class="divide-y border-b border-b-neutral/20"
              >
                <tr
                  class="border-b-neutral/20"
                >
                  <td
                    class="whitespace-normal px-3 py-4"
                  >
                    <p
                      class="text-md font-normal break-words"
                    >
                      Monday
                    </p>
                    <p
                      class="text-md font-normal break-words"
                    >
                      10:00:00
                    </p>
                  </td>
                  <td
                    class="whitespace-normal px-3 py-4"
                  >
                    <p
                      class="text-md font-normal break-words"
                    >
                      4 hours 
                    </p>
                  </td>
                  <td
                    class="whitespace-normal px-3 py-4"
                  >
                    <svg
                      class="fill-current h-6 w-6 mx-auto text-success"
                      viewBox="0 0 32 32"
                      xmlns="http://www.w3.org/2000/svg"
                    >
                      <title>
                        Yes
                      </title>
                      <g>
                        <path
                          d="M16,31.85C7.26,31.85,.15,24.74,.15,16S7.26,.15,16,.15s15.85,7.11,15.85,15.85-7.11,15.85-15.85,15.85Zm0-29.7c-7.64,0-13.85,6.21-13.85,13.85s6.21,13.85,13.85,13.85,13.85-6.21,13.85-13.85S23.64,2.15,16,2.15Z"
                        />
                        <path
                          d="M13.12,22.93c-.27,0-.52-.11-.71-.29l-5.16-5.16c-.39-.39-.39-1.02,0-1.41s1.02-.39,1.41,0l4.45,4.45,9.77-9.77c.39-.39,1.02-.39,1.41,0s.39,1.02,0,1.41l-10.47,10.47c-.19,.19-.44,.29-.71,.29Z"
                        />
                      </g>
                    </svg>
                  </td>
                </tr>
                <tr
                  class="border-b-neutral/20"
                >
                  <td
                    class="whitespace-normal px-3 py-4"
                  >
                    <p
                      class="text-md font-normal break-words"
                    >
                      Tuesday
                    </p>
                    <p
                      class="text-md font-normal break-words"
                    >
                      20:35:00
                    </p>
                  </td>
                  <td
                    class="whitespace-normal px-3 py-4"
                  >
                    <p
                      class="text-md font-normal break-words"
                    >
                      3 hours 15 minutes
                    </p>
                  </td>
                  <td
                    class="whitespace-normal px-3 py-4"
                  >
                    <svg
                      class="fill-current h-6 w-6 mx-auto text-success"
                      viewBox="0 0 32 32"
                      xmlns="http://www.w3.org/2000/svg"
                    >
                      <title>
                        Yes
                      </title>
                      <g>
                        <path
                          d="M16,31.85C7.26,31.85,.15,24.74,.15,16S7.26,.15,16,.15s15.85,7.11,15.85,15.85-7.11,15.85-15.85,15.85Zm0-29.7c-7.64,0-13.85,6.21-13.85,13.85s6.21,13.85,13.85,13.85,13.85-6.21,13.85-13.85S23.64,2.15,16,2.15Z"
                        />
                        <path
                          d="M13.12,22.93c-.27,0-.52-.11-.71-.29l-5.16-5.16c-.39-.39-.39-1.02,0-1.41s1.02-.39,1.41,0l4.45,4.45,9.77-9.77c.39-.39,1.02-.39,1.41,0s.39,1.02,0,1.41l-10.47,10.47c-.19,.19-.44,.29-.71,.29Z"
                        />
                      </g>
                    </svg>
                  </td>
                </tr>
                <tr
                  class="border-b-neutral/20"
                >
                  <td
                    class="whitespace-normal px-3 py-4"
                  >
                    <p
                      class="text-md font-normal break-words"
                    >
                      Wednesday
                    </p>
                    <p
                      class="text-md font-normal break-words"
                    >
                      00:00:00
                    </p>
                  </td>
                  <td
                    class="whitespace-normal px-3 py-4"
                  >
                    <p
                      class="text-md font-normal break-words"
                    >
                      5 hours 
                    </p>
                  </td>
                  <td
                    class="whitespace-normal px-3 py-4"
                  >
                    <svg
                      class="fill-current h-6 w-6 mx-auto text-success"
                      viewBox="0 0 32 32"
                      xmlns="http://www.w3.org/2000/svg"
                    >
                      <title>
                        Yes
                      </title>
                      <g>
                        <path
                          d="M16,31.85C7.26,31.85,.15,24.74,.15,16S7.26,.15,16,.15s15.85,7.11,15.85,15.85-7.11,15.85-15.85,15.85Zm0-29.7c-7.64,0-13.85,6.21-13.85,13.85s6.21,13.85,13.85,13.85,13.85-6.21,13.85-13.85S23.64,2.15,16,2.15Z"
                        />
                        <path
                          d="M13.12,22.93c-.27,0-.52-.11-.71-.29l-5.16-5.16c-.39-.39-.39-1.02,0-1.41s1.02-.39,1.41,0l4.45,4.45,9.77-9.77c.39-.39,1.02-.39,1.41,0s.39,1.02,0,1.41l-10.47,10.47c-.19,.19-.44,.29-.71,.29Z"
                        />
                      </g>
                    </svg>
                  </td>
                </tr>
                <tr
                  class="border-b-neutral/20"
                >
                  <td
                    class="whitespace-normal px-3 py-4"
                  >
                    <p
                      class="text-md font-normal break-words"
                    >
                      Thursday
                    </p>
                    <p
                      class="text-md font-normal break-words"
                    >
                      16:10:00
                    </p>
                  </td>
                  <td
                    class="whitespace-normal px-3 py-4"
                  >
                    <p
                      class="text-md font-normal break-words"
                    >
                      1 hour 
                    </p>
                  </td>
                  <td
                    class="whitespace-normal px-3 py-4"
                  >
                    <svg
                      class="fill-current h-6 w-6 mx-auto text-success"
                      viewBox="0 0 32 32"
                      xmlns="http://www.w3.org/2000/svg"
                    >
                      <title>
                        Yes
                      </title>
                      <g>
                        <path
                          d="M16,31.85C7.26,31.85,.15,24.74,.15,16S7.26,.15,16,.15s15.85,7.11,15.85,15.85-7.11,15.85-15.85,15.85Zm0-29.7c-7.64,0-13.85,6.21-13.85,13.85s6.21,13.85,13.85,13.85,13.85-6.21,13.85-13.85S23.64,2.15,16,2.15Z"
                        />
                        <path
                          d="M13.12,22.93c-.27,0-.52-.11-.71-.29l-5.16-5.16c-.39-.39-.39-1.02,0-1.41s1.02-.39,1.41,0l4.45,4.45,9.77-9.77c.39-.39,1.02-.39,1.41,0s.39,1.02,0,1.41l-10.47,10.47c-.19,.19-.44,.29-.71,.29Z"
                        />
                      </g>
                    </svg>
                  </td>
                </tr>
                <tr
                  class="border-b-neutral/20"
                >
                  <td
                    class="whitespace-normal px-3 py-4"
                  >
                    <p
                      class="text-md font-normal break-words"
                    >
                      Friday
                    </p>
                    <p
                      class="text-md font-normal break-words"
                    >
                      00:00:00
                    </p>
                  </td>
                  <td
                    class="whitespace-normal px-3 py-4"
                  >
                    <p
                      class="text-md font-normal break-words"
                    >
                      5 hours 
                    </p>
                  </td>
                  <td
                    class="whitespace-normal px-3 py-4"
                  >
                    <svg
                      class="fill-current h-6 w-6 mx-auto text-success"
                      viewBox="0 0 32 32"
                      xmlns="http://www.w3.org/2000/svg"
                    >
                      <title>
                        Yes
                      </title>
                      <g>
                        <path
                          d="M16,31.85C7.26,31.85,.15,24.74,.15,16S7.26,.15,16,.15s15.85,7.11,15.85,15.85-7.11,15.85-15.85,15.85Zm0-29.7c-7.64,0-13.85,6.21-13.85,13.85s6.21,13.85,13.85,13.85,13.85-6.21,13.85-13.85S23.64,2.15,16,2.15Z"
                        />
                        <path
                          d="M13.12,22.93c-.27,0-.52-.11-.71-.29l-5.16-5.16c-.39-.39-.39-1.02,0-1.41s1.02-.39,1.41,0l4.45,4.45,9.77-9.77c.39-.39,1.02-.39,1.41,0s.39,1.02,0,1.41l-10.47,10.47c-.19,.19-.44,.29-.71,.29Z"
                        />
                      </g>
                    </svg>
                  </td>
                </tr>
                <tr
                  class="border-b-neutral/20"
                >
                  <td
                    class="whitespace-normal px-3 py-4"
                  >
                    <p
                      class="text-md font-normal break-words"
                    >
                      Saturday
                    </p>
                    <p
                      class="text-md font-normal break-words"
                    >
                      00:00:00
                    </p>
                  </td>
                  <td
                    class="whitespace-normal px-3 py-4"
                  >
                    <p
                      class="text-md font-normal break-words"
                    >
                      5 hours 
                    </p>
                  </td>
                  <td
                    class="whitespace-normal px-3 py-4"
                  >
                    <svg
                      class="fill-current h-6 w-6 mx-auto text-error"
                      viewBox="0 0 32 32"
                      xmlns="http://www.w3.org/2000/svg"
                    >
                      <title>
                        No
                      </title>
                      <g>
                        <path
                          d="M16,.07C7.22,.07,.07,7.22,.07,16s7.15,15.93,15.93,15.93,15.93-7.15,15.93-15.93S24.78,.07,16,.07Zm0,29.86c-7.68,0-13.93-6.25-13.93-13.93S8.32,2.07,16,2.07s13.93,6.25,13.93,13.93-6.25,13.93-13.93,13.93Z"
                        />
                        <path
                          d="M23.38,8.62c-.39-.39-1.02-.39-1.41,0l-5.96,5.96-5.96-5.96c-.39-.39-1.02-.39-1.41,0s-.39,1.02,0,1.41l5.96,5.96-5.96,5.96c-.39,.39-.39,1.02,0,1.41,.2,.2,.45,.29,.71,.29s.51-.1,.71-.29l5.96-5.96,5.96,5.96c.2,.2,.45,.29,.71,.29s.51-.1,.71-.29c.39-.39,.39-1.02,0-1.41l-5.96-5.96,5.96-5.96c.39-.39,.39-1.02,0-1.41Z"
                        />
                      </g>
                    </svg>
                  </td>
                </tr>
                <tr
                  class="border-b-neutral/20"
                >
                  <td
                    class="whitespace-normal px-3 py-4"
                  >
                    <p
                      class="text-md font-normal break-words"
                    >
                      Sunday
                    </p>
                    <p
                      class="text-md font-normal break-words"
                    >
                      00:00:00
                    </p>
                  </td>
                  <td
                    class="whitespace-normal px-3 py-4"
                  >
                    <p
                      class="text-md font-normal break-words"
                    >
                      5 hours 
                    </p>
                  </td>
                  <td
                    class="whitespace-normal px-3 py-4"
                  >
                    <svg
                      class="fill-current h-6 w-6 mx-auto text-error"
                      viewBox="0 0 32 32"
                      xmlns="http://www.w3.org/2000/svg"
                    >
                      <title>
                        No
                      </title>
                      <g>
                        <path
                          d="M16,.07C7.22,.07,.07,7.22,.07,16s7.15,15.93,15.93,15.93,15.93-7.15,15.93-15.93S24.78,.07,16,.07Zm0,29.86c-7.68,0-13.93-6.25-13.93-13.93S8.32,2.07,16,2.07s13.93,6.25,13.93,13.93-6.25,13.93-13.93,13.93Z"
                        />
                        <path
                          d="M23.38,8.62c-.39-.39-1.02-.39-1.41,0l-5.96,5.96-5.96-5.96c-.39-.39-1.02-.39-1.41,0s-.39,1.02,0,1.41l5.96,5.96-5.96,5.96c-.39,.39-.39,1.02,0,1.41,.2,.2,.45,.29,.71,.29s.51-.1,.71-.29l5.96-5.96,5.96,5.96c.2,.2,.45,.29,.71,.29s.51-.1,.71-.29c.39-.39,.39-1.02,0-1.41l-5.96-5.96,5.96-5.96c.39-.39,.39-1.02,0-1.41Z"
                        />
                      </g>
                    </svg>
                  </td>
                </tr>
              </tbody>
            </table>
          </div>
        </div>
        <div
          class="recharts-responsive-container"
          style="width: 100%; height: 320px; min-width: 0;"
        >
          <div
            style="width: 0px; height: 0px; overflow: visible;"
          />
        </div>
      </div>
    </section>
    <div
      class="pb-2"
    />
    <section
      class="p-3 rounded-sm bg-white"
    >
      <h3
        class="text-lg font-bold pb-4"
      >
        Other configuration values
      </h3>
      <div
        class="flex flex-col bg-white rounded-sm overflow-hidden overflow-x-auto"
      >
        <div
          class="inline-block min-w-full -my-2 py-2 align-middle"
        >
          <table
            class="min-w-full"
            tabindex="0"
          >
            <caption
              class="sr-only"
            >
              Table of other configuration values
            </caption>
            <tbody
              class="divide-y"
            >
              <tr
                class="border-b-neutral/20"
              >
                <td
                  class="whitespace-normal px-3 py-4 pl-0 pr-2 pt-2 pb-2 whitespace-pre"
                >
                  ChargeCurrentLimitA
                </td>
                <td
                  class="whitespace-normal px-3 py-4 pr-0 pl-2 pt-2 pb-2 text-neutral text-left"
                >
                  32
                </td>
              </tr>
              <tr
                class="border-b-neutral/20"
              >
                <td
                  class="whitespace-normal px-3 py-4 pl-0 pr-2 pt-2 pb-2 whitespace-pre"
                >
                  OffMode
                </td>
                <td
                  class="whitespace-normal px-3 py-4 pr-0 pl-2 pt-2 pb-2 text-neutral text-left"
                >
                  false
                </td>
              </tr>
              <tr
                class="border-b-neutral/20"
              >
                <td
                  class="whitespace-normal px-3 py-4 pl-0 pr-2 pt-2 pb-2 whitespace-pre"
                >
                  OfflineSchedulingEnabled
                </td>
                <td
                  class="whitespace-normal px-3 py-4 pr-0 pl-2 pt-2 pb-2 text-neutral text-left"
                >
                  true
                </td>
              </tr>
              <tr
                class="border-b-neutral/20"
              >
                <td
                  class="whitespace-normal px-3 py-4 pl-0 pr-2 pt-2 pb-2 whitespace-pre"
                >
                  PPDevClampFaultThreshold
                </td>
                <td
                  class="whitespace-normal px-3 py-4 pr-0 pl-2 pt-2 pb-2 text-neutral text-left"
                >
                  2
                </td>
              </tr>
              <tr
                class="border-b-neutral/20"
              >
                <td
                  class="whitespace-normal px-3 py-4 pl-0 pr-2 pt-2 pb-2 whitespace-pre"
                >
                  PowerBalancingCurrentLimitImportA
                </td>
                <td
                  class="whitespace-normal px-3 py-4 pr-0 pl-2 pt-2 pb-2 text-neutral text-left"
                >
                  50
                </td>
              </tr>
              <tr
                class="border-b-neutral/20"
              >
                <td
                  class="whitespace-normal px-3 py-4 pl-0 pr-2 pt-2 pb-2 whitespace-pre"
                >
                  PowerBalancingEnabled
                </td>
                <td
                  class="whitespace-normal px-3 py-4 pr-0 pl-2 pt-2 pb-2 text-neutral text-left"
                >
                  true
                </td>
              </tr>
              <tr
                class="border-b-neutral/20"
              >
                <td
                  class="whitespace-normal px-3 py-4 pl-0 pr-2 pt-2 pb-2 whitespace-pre"
                >
                  PowerBalancingSensor
                </td>
                <td
                  class="whitespace-normal px-3 py-4 pr-0 pl-2 pt-2 pb-2 text-neutral text-left"
                >
                  NONE
                </td>
              </tr>
              <tr
                class="border-b-neutral/20"
              >
                <td
                  class="whitespace-normal px-3 py-4 pl-0 pr-2 pt-2 pb-2 whitespace-pre"
                >
                  PowerBalancingSensorInstalled
                </td>
                <td
                  class="whitespace-normal px-3 py-4 pr-0 pl-2 pt-2 pb-2 text-neutral text-left"
                >
                  true
                </td>
              </tr>
              <tr
                class="border-b-neutral/20"
              >
                <td
                  class="whitespace-normal px-3 py-4 pl-0 pr-2 pt-2 pb-2 whitespace-pre"
                >
                  PowerBalancingSensorPolarityInverted
                </td>
                <td
                  class="whitespace-normal px-3 py-4 pr-0 pl-2 pt-2 pb-2 text-neutral text-left"
                >
                  true
                </td>
              </tr>
              <tr
                class="border-b-neutral/20"
              >
                <td
                  class="whitespace-normal px-3 py-4 pl-0 pr-2 pt-2 pb-2 whitespace-pre"
                >
                  SolarExportMargin
                </td>
                <td
                  class="whitespace-normal px-3 py-4 pr-0 pl-2 pt-2 pb-2 text-neutral text-left"
                >
                  5
                </td>
              </tr>
              <tr
                class="border-b-neutral/20"
              >
                <td
                  class="whitespace-normal px-3 py-4 pl-0 pr-2 pt-2 pb-2 whitespace-pre"
                >
                  SolarMatchingEnabled
                </td>
                <td
                  class="whitespace-normal px-3 py-4 pr-0 pl-2 pt-2 pb-2 text-neutral text-left"
                >
                  true
                </td>
              </tr>
              <tr
                class="border-b-neutral/20"
              >
                <td
                  class="whitespace-normal px-3 py-4 pl-0 pr-2 pt-2 pb-2 whitespace-pre"
                >
                  SolarMaxGridImport
                </td>
                <td
                  class="whitespace-normal px-3 py-4 pr-0 pl-2 pt-2 pb-2 text-neutral text-left"
                >
                  4.1
                </td>
              </tr>
              <tr
                class="border-b-neutral/20"
              >
                <td
                  class="whitespace-normal px-3 py-4 pl-0 pr-2 pt-2 pb-2 whitespace-pre"
                >
                  SolarStartHysteresis
                </td>
                <td
                  class="whitespace-normal px-3 py-4 pr-0 pl-2 pt-2 pb-2 text-neutral text-left"
                >
                  10
                </td>
              </tr>
              <tr
                class="border-b-neutral/20"
              >
                <td
                  class="whitespace-normal px-3 py-4 pl-0 pr-2 pt-2 pb-2 whitespace-pre"
                >
                  SolarStopHysteresis
                </td>
                <td
                  class="whitespace-normal px-3 py-4 pr-0 pl-2 pt-2 pb-2 text-neutral text-left"
                >
                  64
                </td>
              </tr>
              <tr
                class="border-b-neutral/20"
              >
                <td
                  class="whitespace-normal px-3 py-4 pl-0 pr-2 pt-2 pb-2 whitespace-pre"
                >
                  SolarSystemInstalled
                </td>
                <td
                  class="whitespace-normal px-3 py-4 pr-0 pl-2 pt-2 pb-2 text-neutral text-left"
                >
                  true
                </td>
              </tr>
              <tr
                class="border-b-neutral/20"
              >
                <td
                  class="whitespace-normal px-3 py-4 pl-0 pr-2 pt-2 pb-2 whitespace-pre"
                >
                  FirmwareVersion
                </td>
                <td
                  class="whitespace-normal px-3 py-4 pr-0 pl-2 pt-2 pb-2 text-neutral text-left"
                >
                  1.0.0
                </td>
              </tr>
              <tr
                class="border-b-neutral/20"
              >
                <td
                  class="whitespace-normal px-3 py-4 pl-0 pr-2 pt-2 pb-2 whitespace-pre"
                >
                  LinkyScheduleEnabled
                </td>
                <td
                  class="whitespace-normal px-3 py-4 pr-0 pl-2 pt-2 pb-2 text-neutral text-left"
                >
                  false
                </td>
              </tr>
              <tr
                class="border-b-neutral/20"
              >
                <td
                  class="whitespace-normal px-3 py-4 pl-0 pr-2 pt-2 pb-2 whitespace-pre"
                >
                  RcdBreakerSize
                </td>
                <td
                  class="whitespace-normal px-3 py-4 pr-0 pl-2 pt-2 pb-2 text-neutral text-left"
                >
                  10
                </td>
              </tr>
              <tr
                class="border-b-neutral/20"
              >
                <td
                  class="whitespace-normal px-3 py-4 pl-0 pr-2 pt-2 pb-2 whitespace-pre"
                >
                  UnlockConnectorOnEVSideDisconnect
                </td>
                <td
                  class="whitespace-normal px-3 py-4 pr-0 pl-2 pt-2 pb-2 text-neutral text-left"
                >
                  true
                </td>
              </tr>
            </tbody>
          </table>
        </div>
      </div>
    </section>
    <div
      class="pb-2"
    />
    <section
      class="p-3 rounded-sm bg-white"
    >
      <h3
        class="text-lg font-bold pb-4"
      >
        Tags
      </h3>
      <div
        class="flex flex-col bg-white rounded-sm overflow-hidden overflow-x-auto"
      >
        <div
          class="inline-block min-w-full -my-2 py-2 align-middle"
        >
          <table
            class="min-w-full"
            tabindex="0"
          >
            <caption
              class="sr-only"
            >
              Table of tags
            </caption>
            <tbody
              class="divide-y"
            >
              <tr
                class="border-b-neutral/20"
              >
                <td
                  class="whitespace-normal px-3 py-4 pl-0 pr-2 pt-2 pb-2 whitespace-pre"
                >
                  cur tollo claudeo
                </td>
                <td
                  class="whitespace-normal px-3 py-4 pr-0 pl-2 pt-2 pb-2 text-neutral text-left"
                >
                  sponte totus dedico
                </td>
              </tr>
              <tr
                class="border-b-neutral/20"
              >
                <td
                  class="whitespace-normal px-3 py-4 pl-0 pr-2 pt-2 pb-2 whitespace-pre"
                >
                  terreo cinis utilis
                </td>
                <td
                  class="whitespace-normal px-3 py-4 pr-0 pl-2 pt-2 pb-2 text-neutral text-left"
                >
                  tametsi vilitas adduco
                </td>
              </tr>
              <tr
                class="border-b-neutral/20"
              >
                <td
                  class="whitespace-normal px-3 py-4 pl-0 pr-2 pt-2 pb-2 whitespace-pre"
                >
                  sollers depromo vesica
                </td>
                <td
                  class="whitespace-normal px-3 py-4 pr-0 pl-2 pt-2 pb-2 text-neutral text-left"
                >
                  similique candidus ulterius
                </td>
              </tr>
              <tr
                class="border-b-neutral/20"
              >
                <td
                  class="whitespace-normal px-3 py-4 pl-0 pr-2 pt-2 pb-2 whitespace-pre"
                >
                  quod texo dapifer
                </td>
                <td
                  class="whitespace-normal px-3 py-4 pr-0 pl-2 pt-2 pb-2 text-neutral text-left"
                >
                  termes catena hic
                </td>
              </tr>
              <tr
                class="border-b-neutral/20"
              >
                <td
                  class="whitespace-normal px-3 py-4 pl-0 pr-2 pt-2 pb-2 whitespace-pre"
                >
                  tabgo adaugeo thesis
                </td>
                <td
                  class="whitespace-normal px-3 py-4 pr-0 pl-2 pt-2 pb-2 text-neutral text-left"
                >
                  celo excepturi curvo
                </td>
              </tr>
              <tr
                class="border-b-neutral/20"
              >
                <td
                  class="whitespace-normal px-3 py-4 pl-0 pr-2 pt-2 pb-2 whitespace-pre"
                >
                  asperiores currus theatrum
                </td>
                <td
                  class="whitespace-normal px-3 py-4 pr-0 pl-2 pt-2 pb-2 text-neutral text-left"
                >
                  thorax vinum deporto
                </td>
              </tr>
            </tbody>
          </table>
        </div>
      </div>
    </section>
    <div
      class="pb-2"
    />
  </div>
</body>
`;

exports[`Charger page content should match snapshot with multiple sockets 1`] = `
<body>
  <div>
    <div
      class="rounded-md p-3 bg-warning/10 border border-warning"
    >
      <div
        class="flex"
      >
        <div
          class="shrink-0"
        >
          <span
            class="text-error/70"
          >
            <svg
              class="fill-current w-6 h-6"
              viewBox="0 0 32 32"
              xmlns="http://www.w3.org/2000/svg"
            >
              <g>
                <path
                  d="M16.1,29.43A13.43,13.43,0,1,1,29.53,16,13.45,13.45,0,0,1,16.1,29.43Zm0-24.86A11.43,11.43,0,1,0,27.53,16,11.45,11.45,0,0,0,16.1,4.57Z"
                />
                <path
                  d="M16,24.12a1,1,0,0,1-1-1V14.94a1,1,0,1,1,2,0v8.18A1,1,0,0,1,16,24.12Z"
                />
                <path
                  d="M16.1,10.89a1,1,0,0,1-1-1v-.2a1,1,0,0,1,2,0v.2A1,1,0,0,1,16.1,10.89Z"
                />
              </g>
            </svg>
          </span>
        </div>
        <div
          class="flex flex-col justify-center ml-4"
        >
          <p
            class="text-md font-normal sr-only break-words"
          >
            Alert
          </p>
          <div
            class="text-error/70"
          >
            <p
              class="text-md font-normal break-words"
            >
              It is not currently possible to make changes or send commands to this charger. You can search for this charger in MIS 
              <a
                class="text-primary underline hover:decoration-2 focus:decoration-2 focus:outline-none cursor-pointer"
                href="https://admin.pod-point.com/units?search=PSL-12345"
                target="_blank"
              >
                here
              </a>
               or view this charger in Grafana 
              <a
                class="text-primary underline hover:decoration-2 focus:decoration-2 focus:outline-none cursor-pointer"
                href="https://g-e728fe26ac.grafana-workspace.eu-west-1.amazonaws.com/d/fdtbud32bw45ce/5b147138-e9f2-5846-ba34-c2778f5a748f?var-ppid=PSL-12345"
                target="_blank"
              >
                here
              </a>
              .
            </p>
          </div>
        </div>
      </div>
    </div>
    <div
      class="pb-2"
    />
    <section
      class="p-3 rounded-sm bg-white"
    >
      <header>
        <div
          class="flex"
        >
          <div
            class="lg:flex items-center"
          >
            <div
              class="flex"
            >
              <h1
                class="text-xxl font-bold"
              >
                PSL-12345
              </h1>
              <button
                aria-label="Copy PSL-12345"
                class="border-2 border-solid rounded-sm font-bold text-center disabled:no-underline transition ease-in-out duration-200 cursor-pointer disabled:cursor-not-allowed border-transparent text-primary hover:underline hover:text-primary active:font-bold active:text-primary disabled:text-neutral/40 disabled:hover:no-underline disabled:hover:border-transparent p-1.5 ml-1"
                type="button"
              >
                <svg
                  class="fill-current h-4 w-4"
                  viewBox="0 0 24 24"
                  xmlns="http://www.w3.org/2000/svg"
                >
                  <defs>
                    <style>
                      .cls-1 
                    </style>
                  </defs>
                  <path
                    d="M18.73,14.5h1.83c1.35,0,2.44-1.09,2.44-2.44V3.5c0-1.35-1.09-2.44-2.44-2.44h-8.56c-1.35,0-2.44,1.09-2.44,2.44v1.83M3.45,9.61h8.56c1.35,0,2.44,1.09,2.44,2.44v8.56c0,1.35-1.09,2.44-2.44,2.44H3.45c-1.35,0-2.44-1.09-2.44-2.44v-8.56c0-1.35,1.09-2.44,2.44-2.44Z"
                    style="fill: none; stroke: currentColor; stroke-linecap: round; stroke-linejoin: round; stroke-width: 1.5px;"
                  />
                </svg>
              </button>
            </div>
            <div
              class="lg:ml-6 my-4 lg:my-0"
            >
              <div
                class="flex space-x-4"
              >
                <button
                  class="px-2 font-semibold border-b outline-hidden focus-visible:ring-2 focus-visible:ring-info text-primary border-b-primary hover:text-primary cursor-default"
                  disabled=""
                >
                  Socket A
                </button>
                <button
                  class="px-2 font-semibold text-neutral hover:text-black border-b border-b-transparent outline-hidden focus-visible:ring-2 focus-visible:ring-info"
                >
                  Socket B
                </button>
                <button
                  class="px-2 font-semibold text-neutral hover:text-black border-b border-b-transparent outline-hidden focus-visible:ring-2 focus-visible:ring-info"
                >
                  Socket C
                </button>
              </div>
            </div>
          </div>
          <div
            class="ml-auto"
          >
            <div
              class="relative inline-block text-left"
              data-headlessui-state=""
            >
              <button
                aria-expanded="false"
                aria-haspopup="menu"
                class="bg-black border-black border-2 border-solid rounded-sm px-4 py-1.5 hover:bg-charcoal hover:border-charcoal focus:bg-charcoal focus:border-charcoal active:bg-charcoal active:border-charcoal disabled:bg-neutral/20 disabled:border-neutral/20 disabled:text-neutral font-bold inline-flex items-center text-white cursor-pointer disabled:cursor-not-allowed"
                data-headlessui-state=""
                id="headlessui-menu-button-:test-id-2"
                type="button"
              >
                <span>
                  Commands
                </span>
                <svg
                  class="fill-current stroke-current h-2 w-2 min-w-0 ml-2"
                  viewBox="0 0 24 24"
                  xmlns="http://www.w3.org/2000/svg"
                >
                  <g>
                    <path
                      d="m13.15,18.84c-.57.64-1.55.69-2.19.11-.04-.04-.08-.07-.11-.11L.4,7.23c-.57-.64-.52-1.61.12-2.19.28-.25.65-.4,1.03-.4h20.91c.85,0,1.55.69,1.55,1.55,0,.38-.14.75-.4,1.03l-10.45,11.62Z"
                    />
                  </g>
                </svg>
              </button>
            </div>
          </div>
        </div>
        <p
          class="text-md font-semibold break-words"
        >
          Test-Name
        </p>
        <div>
          <div
            class="flex items-center"
          >
            <a
              class="text-primary underline hover:decoration-2 focus:decoration-2 focus:outline-none cursor-pointer block mr-2"
              href="/accounts/a3450f38-217a-4d0c-8eec-b7d63c6fd2e0/details"
            >
              John Doe - <EMAIL>
            </a>
            <button
              aria-label="Unlink user John Doe"
              class="border-2 border-solid rounded-sm font-bold text-center disabled:no-underline transition ease-in-out duration-200 cursor-pointer disabled:cursor-not-allowed border-transparent text-primary hover:underline hover:text-primary active:font-bold active:text-primary disabled:text-neutral/40 disabled:hover:no-underline disabled:hover:border-transparent p-1.5"
              data-testid="unlink-user-a3450f38-217a-4d0c-8eec-b7d63c6fd2e0"
              id="unlink-user-a3450f38-217a-4d0c-8eec-b7d63c6fd2e0"
              name="unlink-user-a3450f38-217a-4d0c-8eec-b7d63c6fd2e0"
              type="button"
            >
              <svg
                class="fill-current h-4 w-4"
                viewBox="0 0 24 24"
                xmlns="http://www.w3.org/2000/svg"
              >
                <g>
                  <path
                    d="m9.94,14.21c3.38,0,6.12-2.75,6.12-6.12S13.32,1.96,9.94,1.96s-6.12,2.75-6.12,6.12,2.75,6.12,6.12,6.12Zm0-10.75c2.55,0,4.62,2.07,4.62,4.62s-2.07,4.62-4.62,4.62-4.62-2.07-4.62-4.62,2.07-4.62,4.62-4.62Z"
                  />
                  <path
                    d="m9.94,14.73C2.66,14.73.21,20.96.18,21.02c-.15.39.05.82.43.97.39.15.82-.04.97-.43.08-.22,2.13-5.33,8.36-5.33s8.77,5.16,8.87,5.38c.13.27.4.43.68.43.11,0,.21-.02.32-.07.37-.17.54-.62.36-.99-.12-.26-2.98-6.25-10.23-6.25Z"
                  />
                  <path
                    d="m21.42,9.31l2.23-2.23c.29-.29.29-.77,0-1.06s-.77-.29-1.06,0l-2.23,2.23-2.23-2.23c-.29-.29-.77-.29-1.06,0s-.29.77,0,1.06l2.23,2.23-2.23,2.23c-.29.29-.29.77,0,1.06.15.15.34.22.53.22s.38-.07.53-.22l2.23-2.23,2.23,2.23c.15.15.34.22.53.22s.38-.07.53-.22c.29-.29.29-.77,0-1.06l-2.23-2.23Z"
                  />
                </g>
              </svg>
            </button>
          </div>
          <div
            class="flex items-center"
          >
            <a
              class="text-primary underline hover:decoration-2 focus:decoration-2 focus:outline-none cursor-pointer block mr-2"
              href="/accounts/77b7c00e-8890-4a76-b4e6-def3b226d47b/details"
            >
              John Doe - <EMAIL>
            </a>
            <button
              aria-label="Unlink user John Doe"
              class="border-2 border-solid rounded-sm font-bold text-center disabled:no-underline transition ease-in-out duration-200 cursor-pointer disabled:cursor-not-allowed border-transparent text-primary hover:underline hover:text-primary active:font-bold active:text-primary disabled:text-neutral/40 disabled:hover:no-underline disabled:hover:border-transparent p-1.5"
              data-testid="unlink-user-77b7c00e-8890-4a76-b4e6-def3b226d47b"
              id="unlink-user-77b7c00e-8890-4a76-b4e6-def3b226d47b"
              name="unlink-user-77b7c00e-8890-4a76-b4e6-def3b226d47b"
              type="button"
            >
              <svg
                class="fill-current h-4 w-4"
                viewBox="0 0 24 24"
                xmlns="http://www.w3.org/2000/svg"
              >
                <g>
                  <path
                    d="m9.94,14.21c3.38,0,6.12-2.75,6.12-6.12S13.32,1.96,9.94,1.96s-6.12,2.75-6.12,6.12,2.75,6.12,6.12,6.12Zm0-10.75c2.55,0,4.62,2.07,4.62,4.62s-2.07,4.62-4.62,4.62-4.62-2.07-4.62-4.62,2.07-4.62,4.62-4.62Z"
                  />
                  <path
                    d="m9.94,14.73C2.66,14.73.21,20.96.18,21.02c-.15.39.05.82.43.97.39.15.82-.04.97-.43.08-.22,2.13-5.33,8.36-5.33s8.77,5.16,8.87,5.38c.13.27.4.43.68.43.11,0,.21-.02.32-.07.37-.17.54-.62.36-.99-.12-.26-2.98-6.25-10.23-6.25Z"
                  />
                  <path
                    d="m21.42,9.31l2.23-2.23c.29-.29.29-.77,0-1.06s-.77-.29-1.06,0l-2.23,2.23-2.23-2.23c-.29-.29-.77-.29-1.06,0s-.29.77,0,1.06l2.23,2.23-2.23,2.23c-.29.29-.29.77,0,1.06.15.15.34.22.53.22s.38-.07.53-.22l2.23-2.23,2.23,2.23c.15.15.34.22.53.22s.38-.07.53-.22c.29-.29.29-.77,0-1.06l-2.23-2.23Z"
                  />
                </g>
              </svg>
            </button>
          </div>
          <div
            class="flex items-center"
          >
            <a
              class="text-primary underline hover:decoration-2 focus:decoration-2 focus:outline-none cursor-pointer block mr-2"
              href="/accounts/5411de0c-d46b-48c3-b6d2-e6bc3a1f7049/details"
            >
              John Doe - <EMAIL>
            </a>
            <button
              aria-label="Unlink user John Doe"
              class="border-2 border-solid rounded-sm font-bold text-center disabled:no-underline transition ease-in-out duration-200 cursor-pointer disabled:cursor-not-allowed border-transparent text-primary hover:underline hover:text-primary active:font-bold active:text-primary disabled:text-neutral/40 disabled:hover:no-underline disabled:hover:border-transparent p-1.5"
              data-testid="unlink-user-5411de0c-d46b-48c3-b6d2-e6bc3a1f7049"
              id="unlink-user-5411de0c-d46b-48c3-b6d2-e6bc3a1f7049"
              name="unlink-user-5411de0c-d46b-48c3-b6d2-e6bc3a1f7049"
              type="button"
            >
              <svg
                class="fill-current h-4 w-4"
                viewBox="0 0 24 24"
                xmlns="http://www.w3.org/2000/svg"
              >
                <g>
                  <path
                    d="m9.94,14.21c3.38,0,6.12-2.75,6.12-6.12S13.32,1.96,9.94,1.96s-6.12,2.75-6.12,6.12,2.75,6.12,6.12,6.12Zm0-10.75c2.55,0,4.62,2.07,4.62,4.62s-2.07,4.62-4.62,4.62-4.62-2.07-4.62-4.62,2.07-4.62,4.62-4.62Z"
                  />
                  <path
                    d="m9.94,14.73C2.66,14.73.21,20.96.18,21.02c-.15.39.05.82.43.97.39.15.82-.04.97-.43.08-.22,2.13-5.33,8.36-5.33s8.77,5.16,8.87,5.38c.13.27.4.43.68.43.11,0,.21-.02.32-.07.37-.17.54-.62.36-.99-.12-.26-2.98-6.25-10.23-6.25Z"
                  />
                  <path
                    d="m21.42,9.31l2.23-2.23c.29-.29.29-.77,0-1.06s-.77-.29-1.06,0l-2.23,2.23-2.23-2.23c-.29-.29-.77-.29-1.06,0s-.29.77,0,1.06l2.23,2.23-2.23,2.23c-.29.29-.29.77,0,1.06.15.15.34.22.53.22s.38-.07.53-.22l2.23-2.23,2.23,2.23c.15.15.34.22.53.22s.38-.07.53-.22c.29-.29.29-.77,0-1.06l-2.23-2.23Z"
                  />
                </g>
              </svg>
            </button>
          </div>
          <div
            class="flex items-center"
          >
            <a
              class="text-primary underline hover:decoration-2 focus:decoration-2 focus:outline-none cursor-pointer block mr-2"
              href="/accounts/3482bea8-17d5-470e-83bd-4e897b8b11ee/details"
            >
              John Doe - <EMAIL>
            </a>
            <button
              aria-label="Unlink user John Doe"
              class="border-2 border-solid rounded-sm font-bold text-center disabled:no-underline transition ease-in-out duration-200 cursor-pointer disabled:cursor-not-allowed border-transparent text-primary hover:underline hover:text-primary active:font-bold active:text-primary disabled:text-neutral/40 disabled:hover:no-underline disabled:hover:border-transparent p-1.5"
              data-testid="unlink-user-3482bea8-17d5-470e-83bd-4e897b8b11ee"
              id="unlink-user-3482bea8-17d5-470e-83bd-4e897b8b11ee"
              name="unlink-user-3482bea8-17d5-470e-83bd-4e897b8b11ee"
              type="button"
            >
              <svg
                class="fill-current h-4 w-4"
                viewBox="0 0 24 24"
                xmlns="http://www.w3.org/2000/svg"
              >
                <g>
                  <path
                    d="m9.94,14.21c3.38,0,6.12-2.75,6.12-6.12S13.32,1.96,9.94,1.96s-6.12,2.75-6.12,6.12,2.75,6.12,6.12,6.12Zm0-10.75c2.55,0,4.62,2.07,4.62,4.62s-2.07,4.62-4.62,4.62-4.62-2.07-4.62-4.62,2.07-4.62,4.62-4.62Z"
                  />
                  <path
                    d="m9.94,14.73C2.66,14.73.21,20.96.18,21.02c-.15.39.05.82.43.97.39.15.82-.04.97-.43.08-.22,2.13-5.33,8.36-5.33s8.77,5.16,8.87,5.38c.13.27.4.43.68.43.11,0,.21-.02.32-.07.37-.17.54-.62.36-.99-.12-.26-2.98-6.25-10.23-6.25Z"
                  />
                  <path
                    d="m21.42,9.31l2.23-2.23c.29-.29.29-.77,0-1.06s-.77-.29-1.06,0l-2.23,2.23-2.23-2.23c-.29-.29-.77-.29-1.06,0s-.29.77,0,1.06l2.23,2.23-2.23,2.23c-.29.29-.29.77,0,1.06.15.15.34.22.53.22s.38-.07.53-.22l2.23-2.23,2.23,2.23c.15.15.34.22.53.22s.38-.07.53-.22c.29-.29.29-.77,0-1.06l-2.23-2.23Z"
                  />
                </g>
              </svg>
            </button>
          </div>
          <div
            class="flex items-center"
          >
            <a
              class="text-primary underline hover:decoration-2 focus:decoration-2 focus:outline-none cursor-pointer block mr-2"
              href="/accounts/609cf19a-c8c9-43f8-9042-59b5527e5f73/details"
            >
              John Doe - <EMAIL>
            </a>
            <button
              aria-label="Unlink user John Doe"
              class="border-2 border-solid rounded-sm font-bold text-center disabled:no-underline transition ease-in-out duration-200 cursor-pointer disabled:cursor-not-allowed border-transparent text-primary hover:underline hover:text-primary active:font-bold active:text-primary disabled:text-neutral/40 disabled:hover:no-underline disabled:hover:border-transparent p-1.5"
              data-testid="unlink-user-609cf19a-c8c9-43f8-9042-59b5527e5f73"
              id="unlink-user-609cf19a-c8c9-43f8-9042-59b5527e5f73"
              name="unlink-user-609cf19a-c8c9-43f8-9042-59b5527e5f73"
              type="button"
            >
              <svg
                class="fill-current h-4 w-4"
                viewBox="0 0 24 24"
                xmlns="http://www.w3.org/2000/svg"
              >
                <g>
                  <path
                    d="m9.94,14.21c3.38,0,6.12-2.75,6.12-6.12S13.32,1.96,9.94,1.96s-6.12,2.75-6.12,6.12,2.75,6.12,6.12,6.12Zm0-10.75c2.55,0,4.62,2.07,4.62,4.62s-2.07,4.62-4.62,4.62-4.62-2.07-4.62-4.62,2.07-4.62,4.62-4.62Z"
                  />
                  <path
                    d="m9.94,14.73C2.66,14.73.21,20.96.18,21.02c-.15.39.05.82.43.97.39.15.82-.04.97-.43.08-.22,2.13-5.33,8.36-5.33s8.77,5.16,8.87,5.38c.13.27.4.43.68.43.11,0,.21-.02.32-.07.37-.17.54-.62.36-.99-.12-.26-2.98-6.25-10.23-6.25Z"
                  />
                  <path
                    d="m21.42,9.31l2.23-2.23c.29-.29.29-.77,0-1.06s-.77-.29-1.06,0l-2.23,2.23-2.23-2.23c-.29-.29-.77-.29-1.06,0s-.29.77,0,1.06l2.23,2.23-2.23,2.23c-.29.29-.29.77,0,1.06.15.15.34.22.53.22s.38-.07.53-.22l2.23-2.23,2.23,2.23c.15.15.34.22.53.22s.38-.07.53-.22c.29-.29.29-.77,0-1.06l-2.23-2.23Z"
                  />
                </g>
              </svg>
            </button>
          </div>
          <div
            class="flex items-center"
          >
            <a
              class="text-primary underline hover:decoration-2 focus:decoration-2 focus:outline-none cursor-pointer block mr-2"
              href="/accounts/910b444d-f474-46c3-b368-c43b170dbda9/details"
            >
              John Doe - <EMAIL>
            </a>
            <button
              aria-label="Unlink user John Doe"
              class="border-2 border-solid rounded-sm font-bold text-center disabled:no-underline transition ease-in-out duration-200 cursor-pointer disabled:cursor-not-allowed border-transparent text-primary hover:underline hover:text-primary active:font-bold active:text-primary disabled:text-neutral/40 disabled:hover:no-underline disabled:hover:border-transparent p-1.5"
              data-testid="unlink-user-910b444d-f474-46c3-b368-c43b170dbda9"
              id="unlink-user-910b444d-f474-46c3-b368-c43b170dbda9"
              name="unlink-user-910b444d-f474-46c3-b368-c43b170dbda9"
              type="button"
            >
              <svg
                class="fill-current h-4 w-4"
                viewBox="0 0 24 24"
                xmlns="http://www.w3.org/2000/svg"
              >
                <g>
                  <path
                    d="m9.94,14.21c3.38,0,6.12-2.75,6.12-6.12S13.32,1.96,9.94,1.96s-6.12,2.75-6.12,6.12,2.75,6.12,6.12,6.12Zm0-10.75c2.55,0,4.62,2.07,4.62,4.62s-2.07,4.62-4.62,4.62-4.62-2.07-4.62-4.62,2.07-4.62,4.62-4.62Z"
                  />
                  <path
                    d="m9.94,14.73C2.66,14.73.21,20.96.18,21.02c-.15.39.05.82.43.97.39.15.82-.04.97-.43.08-.22,2.13-5.33,8.36-5.33s8.77,5.16,8.87,5.38c.13.27.4.43.68.43.11,0,.21-.02.32-.07.37-.17.54-.62.36-.99-.12-.26-2.98-6.25-10.23-6.25Z"
                  />
                  <path
                    d="m21.42,9.31l2.23-2.23c.29-.29.29-.77,0-1.06s-.77-.29-1.06,0l-2.23,2.23-2.23-2.23c-.29-.29-.77-.29-1.06,0s-.29.77,0,1.06l2.23,2.23-2.23,2.23c-.29.29-.29.77,0,1.06.15.15.34.22.53.22s.38-.07.53-.22l2.23-2.23,2.23,2.23c.15.15.34.22.53.22s.38-.07.53-.22c.29-.29.29-.77,0-1.06l-2.23-2.23Z"
                  />
                </g>
              </svg>
            </button>
          </div>
          <div
            class="pb-2"
          />
          <div
            class="flex max-md:flex-col max-md:space-y-2 md:space-x-6"
          >
            <div
              class="flex items-center"
            >
              <svg
                class="fill-current w-6 h-6"
                viewBox="0 0 24 24"
                xmlns="http://www.w3.org/2000/svg"
              >
                <title>
                  Domestic location
                </title>
                <g>
                  <path
                    d="m22.07,6.92L14.05.84c-1.21-.92-2.89-.92-4.1,0L1.93,6.92c-.84.64-1.34,1.65-1.34,2.7v10.84c0,1.87,1.52,3.39,3.39,3.39h5.04c.41,0,.75-.34.75-.75s-.34-.75-.75-.75H3.98c-1.04,0-1.89-.85-1.89-1.89v-10.84c0-.59.28-1.15.75-1.5L10.86,2.03c.67-.51,1.61-.51,2.28,0l8.02,6.08c.47.36.74.92.74,1.5v10.84c0,1.04-.85,1.89-1.89,1.89h-5.53c-.96,0-1.74-.78-1.74-1.74v-2.63c1.85-.35,3.25-1.98,3.25-3.93v-3.25c0-.41-.34-.75-.75-.75h-.81v-1.88c0-.41-.34-.75-.75-.75s-.75.34-.75.75v1.88h-1.77v-1.88c0-.41-.34-.75-.75-.75s-.75.34-.75.75v1.88h-.93c-.41,0-.75.34-.75.75v3.25c0,1.95,1.4,3.57,3.25,3.93v2.63c0,1.79,1.45,3.24,3.24,3.24h5.53c1.87,0,3.39-1.52,3.39-3.39v-10.84c0-1.05-.5-2.06-1.34-2.7Zm-12.58,7.13v-2.5h5.01v2.5c0,1.38-1.12,2.5-2.5,2.5s-2.5-1.12-2.5-2.5Z"
                  />
                </g>
              </svg>
              <span
                class="ml-2"
              >
                Domestic
              </span>
            </div>
          </div>
        </div>
      </header>
    </section>
    <div
      class="pb-2"
    />
    <div
      class="flex flex-row flex-wrap gap-x-4 gap-y-2"
    >
      <span
        aria-label="Fault vendorErrorCode4 - Invalid Date"
        class="font-bold px-2.5 py-1 rounded-xs inline-flex items-center tracking-wide bg-warning/10 border border-error/70 text-error/70"
        role="status"
      >
        Fault vendorErrorCode4 - Invalid Date
      </span>
      <span
        aria-label="Fault vendorErrorCode3 - Invalid Date"
        class="font-bold px-2.5 py-1 rounded-xs inline-flex items-center tracking-wide bg-warning/10 border border-error/70 text-error/70"
        role="status"
      >
        Fault vendorErrorCode3 - Invalid Date
      </span>
      <span
        aria-label="Fault vendorErrorCode2 - Invalid Date"
        class="font-bold px-2.5 py-1 rounded-xs inline-flex items-center tracking-wide bg-warning/10 border border-error/70 text-error/70"
        role="status"
      >
        Fault vendorErrorCode2 - Invalid Date
      </span>
      <span
        aria-label="Fault vendorErrorCode1 - Invalid Date"
        class="font-bold px-2.5 py-1 rounded-xs inline-flex items-center tracking-wide bg-warning/10 border border-error/70 text-error/70"
        role="status"
      >
        Fault vendorErrorCode1 - Invalid Date
      </span>
    </div>
    <div
      class="pb-2"
    />
    <div
      class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 print:grid-cols-3 gap-2"
    >
      <section
        class="p-3 rounded-sm bg-white md:col-span-2 lg:col-span-1"
      >
        <div
          class="flex justify-between items-baseline"
        >
          <h3
            class="text-lg font-bold pb-4"
          >
            Charger information
          </h3>
          <a
            class="text-primary underline hover:decoration-2 focus:decoration-2 focus:outline-none cursor-pointer text-right ml-2"
            href="/chargers/PSL-12345/logs/diagnostic?socket=A"
          >
            View diagnostic logs
          </a>
        </div>
        <div
          class="flex flex-col bg-white rounded-sm overflow-hidden overflow-x-auto"
        >
          <div
            class="inline-block min-w-full -my-2 py-2 align-middle"
          >
            <table
              class="min-w-full"
              tabindex="0"
            >
              <caption
                class="sr-only"
              >
                Charger information table
              </caption>
              <tbody
                class="divide-y"
              >
                <tr
                  class="border-b-neutral/20"
                >
                  <td
                    class="whitespace-normal px-3 py-4 pl-0 pr-2 pt-2 pb-2 whitespace-pre"
                  >
                    Model SKU
                  </td>
                  <td
                    class="whitespace-normal px-3 py-4 pr-0 pl-2 pt-2 pb-2 text-neutral text-right"
                  >
                    T7-S-07-AMC-BLK
                  </td>
                </tr>
                <tr
                  class="border-b-neutral/20"
                >
                  <td
                    class="whitespace-normal px-3 py-4 pl-0 pr-2 pt-2 pb-2 whitespace-pre"
                  >
                    Arch version
                  </td>
                  <td
                    class="whitespace-normal px-3 py-4 pr-0 pl-2 pt-2 pb-2 text-neutral text-right"
                  >
                    -
                  </td>
                </tr>
                <tr
                  class="border-b-neutral/20"
                >
                  <td
                    class="whitespace-normal px-3 py-4 pl-0 pr-2 pt-2 pb-2 whitespace-pre"
                  >
                    Firmware
                  </td>
                  <td
                    class="whitespace-normal px-3 py-4 pr-0 pl-2 pt-2 pb-2 text-neutral text-right"
                  >
                    A50P-X.Y.Z-0000P
                  </td>
                </tr>
                <tr
                  class="border-b-neutral/20"
                >
                  <td
                    class="whitespace-normal px-3 py-4 pl-0 pr-2 pt-2 pb-2 whitespace-pre"
                  >
                    Manufactured
                  </td>
                  <td
                    class="whitespace-normal px-3 py-4 pr-0 pl-2 pt-2 pb-2 text-neutral text-right"
                  >
                    1 June 2023
                  </td>
                </tr>
                <tr
                  class="border-b-neutral/20"
                >
                  <td
                    class="whitespace-normal px-3 py-4 pl-0 pr-2 pt-2 pb-2 whitespace-pre"
                  >
                    Operator
                  </td>
                  <td
                    class="whitespace-normal px-3 py-4 pr-0 pl-2 pt-2 pb-2 text-neutral text-right"
                  >
                    Pod Point
                  </td>
                </tr>
                <tr
                  class="border-b-neutral/20"
                >
                  <td
                    class="whitespace-normal px-3 py-4 pl-0 pr-2 pt-2 pb-2 whitespace-pre"
                  >
                    MAC address
                  </td>
                  <td
                    class="whitespace-normal px-3 py-4 pr-0 pl-2 pt-2 pb-2 text-neutral text-right"
                  >
                    -
                  </td>
                </tr>
                <tr
                  class="border-b-neutral/20"
                >
                  <td
                    class="whitespace-normal px-3 py-4 pl-0 pr-2 pt-2 pb-2 whitespace-pre"
                  >
                    PCBA serial number
                  </td>
                  <td
                    class="whitespace-normal px-3 py-4 pr-0 pl-2 pt-2 pb-2 text-neutral text-right"
                  >
                    -
                  </td>
                </tr>
              </tbody>
            </table>
          </div>
        </div>
        <div
          class="flex flex-row-reverse"
        >
          <a
            class="text-primary underline hover:decoration-2 focus:decoration-2 focus:outline-none cursor-pointer text-right ml-2"
            href="/chargers/PSL-12345/pcbs/history?socket=A"
          >
            History
          </a>
        </div>
      </section>
      <section
        class="p-3 rounded-sm bg-white"
      >
        <h3
          class="text-lg font-bold pb-4"
        >
          Charger connectivity
        </h3>
        <div
          class="flex flex-col bg-white rounded-sm overflow-hidden overflow-x-auto"
        >
          <div
            class="inline-block min-w-full -my-2 py-2 align-middle"
          >
            <table
              class="min-w-full"
              tabindex="0"
            >
              <caption
                class="sr-only"
              >
                Charger connectivity table
              </caption>
              <tbody
                class="divide-y"
              >
                <tr
                  class="border-b-neutral/20"
                >
                  <td
                    class="whitespace-normal px-3 py-4 pl-0 pr-2 pt-2 pb-2 whitespace-pre"
                  >
                    Status
                  </td>
                  <td
                    class="whitespace-normal px-3 py-4 pr-0 pl-2 pt-2 pb-2 text-neutral text-right"
                  >
                    <span
                      aria-label="connectivityStatus"
                      class="font-bold px-2.5 rounded-xs inline-flex items-center tracking-wide bg-success/10 border border-success text-success py-px text-xs"
                      role="status"
                    >
                      connectivityStatus
                    </span>
                  </td>
                </tr>
                <tr
                  class="border-b-neutral/20"
                >
                  <td
                    class="whitespace-normal px-3 py-4 pl-0 pr-2 pt-2 pb-2 whitespace-pre"
                  >
                    Charging status
                  </td>
                  <td
                    class="whitespace-normal px-3 py-4 pr-0 pl-2 pt-2 pb-2 text-neutral text-right"
                  >
                    chargingState
                  </td>
                </tr>
                <tr
                  class="border-b-neutral/20"
                >
                  <td
                    class="whitespace-normal px-3 py-4 pl-0 pr-2 pt-2 pb-2 whitespace-pre"
                  >
                    Last message
                  </td>
                  <td
                    class="whitespace-normal px-3 py-4 pr-0 pl-2 pt-2 pb-2 text-neutral text-right"
                  >
                    30 June 2024 at 22:00:00
                  </td>
                </tr>
                <tr
                  class="border-b-neutral/20"
                >
                  <td
                    class="whitespace-normal px-3 py-4 pl-0 pr-2 pt-2 pb-2 whitespace-pre"
                  >
                    Connection quality
(scale 1-5)
                  </td>
                  <td
                    class="whitespace-normal px-3 py-4 pr-0 pl-2 pt-2 pb-2 text-neutral text-right"
                  >
                    0
                  </td>
                </tr>
                <tr
                  class="border-b-neutral/20"
                >
                  <td
                    class="whitespace-normal px-3 py-4 pl-0 pr-2 pt-2 pb-2 whitespace-pre"
                  >
                    Router serial number
                  </td>
                  <td
                    class="whitespace-normal px-3 py-4 pr-0 pl-2 pt-2 pb-2 text-neutral text-right"
                  >
                    1102538429
                  </td>
                </tr>
                <tr
                  class="border-b-neutral/20"
                >
                  <td
                    class="whitespace-normal px-3 py-4 pl-0 pr-2 pt-2 pb-2 whitespace-pre"
                  >
                    Router MAC address
                  </td>
                  <td
                    class="whitespace-normal px-3 py-4 pr-0 pl-2 pt-2 pb-2 text-neutral text-right"
                  >
                    001E4223CF13
                  </td>
                </tr>
                <tr
                  class="border-b-neutral/20"
                >
                  <td
                    class="whitespace-normal px-3 py-4 pl-0 pr-2 pt-2 pb-2 whitespace-pre"
                  >
                    Router SIM number
                  </td>
                  <td
                    class="whitespace-normal px-3 py-4 pr-0 pl-2 pt-2 pb-2 text-neutral text-right"
                  >
                    123456789123000043
                  </td>
                </tr>
                <tr
                  class="border-b-neutral/20"
                >
                  <td
                    class="whitespace-normal px-3 py-4 pl-0 pr-2 pt-2 pb-2 whitespace-pre"
                  >
                    Router model
                  </td>
                  <td
                    class="whitespace-normal px-3 py-4 pr-0 pl-2 pt-2 pb-2 text-neutral text-right"
                  >
                    RUT241
                  </td>
                </tr>
              </tbody>
            </table>
          </div>
        </div>
      </section>
      <section
        class="p-3 rounded-sm bg-white"
      >
        <div
          class="flex justify-between items-baseline"
        >
          <h3
            class="text-lg font-bold pb-4"
          >
            Charger access point (AP)
          </h3>
          <a
            class="text-primary underline hover:decoration-2 focus:decoration-2 focus:outline-none cursor-pointer text-right ml-2"
            href="/chargers/PSL-12345?redact=true&socket=A"
          >
            Hide password
          </a>
        </div>
        <div
          class="flex flex-col bg-white rounded-sm overflow-hidden overflow-x-auto"
        >
          <div
            class="inline-block min-w-full -my-2 py-2 align-middle"
          >
            <table
              class="min-w-full"
              tabindex="0"
            >
              <caption
                class="sr-only"
              >
                Charger access point
              </caption>
              <tbody
                class="divide-y"
              >
                <tr
                  class="border-b-neutral/20"
                >
                  <td
                    class="whitespace-normal px-3 py-4 pl-0 pr-2 pt-2 pb-2 whitespace-pre"
                  >
                    SSID
                  </td>
                  <td
                    class="whitespace-normal px-3 py-4 pr-0 pl-2 pt-2 pb-2 text-neutral text-right"
                  >
                    PP-123456
                  </td>
                </tr>
                <tr
                  class="border-b-neutral/20"
                >
                  <td
                    class="whitespace-normal px-3 py-4 pl-0 pr-2 pt-2 pb-2 whitespace-pre"
                  >
                    Password
                  </td>
                  <td
                    class="whitespace-normal px-3 py-4 pr-0 pl-2 pt-2 pb-2 text-neutral text-right"
                  >
                    password123
                  </td>
                </tr>
              </tbody>
            </table>
          </div>
        </div>
        <div
          class="border-2 border-dotted border-neutral/20 mt-2 h-32 p-3"
        >
          <svg
            class="w-full h-full"
            height="256"
            viewBox="0 0 25 25"
            width="256"
            xmlns="http://www.w3.org/2000/svg"
          >
            <path
              d="       M 7 0 l 1 0 0 1 -1 0 Z   M 10 0 l 1 0 0 1 -1 0 Z M 11 0 l 1 0 0 1 -1 0 Z   M 14 0 l 1 0 0 1 -1 0 Z M 15 0 l 1 0 0 1 -1 0 Z  M 17 0 l 1 0 0 1 -1 0 Z         M 1 1 l 1 0 0 1 -1 0 Z M 2 1 l 1 0 0 1 -1 0 Z M 3 1 l 1 0 0 1 -1 0 Z M 4 1 l 1 0 0 1 -1 0 Z M 5 1 l 1 0 0 1 -1 0 Z  M 7 1 l 1 0 0 1 -1 0 Z   M 10 1 l 1 0 0 1 -1 0 Z M 11 1 l 1 0 0 1 -1 0 Z  M 13 1 l 1 0 0 1 -1 0 Z  M 15 1 l 1 0 0 1 -1 0 Z M 16 1 l 1 0 0 1 -1 0 Z M 17 1 l 1 0 0 1 -1 0 Z  M 19 1 l 1 0 0 1 -1 0 Z M 20 1 l 1 0 0 1 -1 0 Z M 21 1 l 1 0 0 1 -1 0 Z M 22 1 l 1 0 0 1 -1 0 Z M 23 1 l 1 0 0 1 -1 0 Z   M 1 2 l 1 0 0 1 -1 0 Z    M 5 2 l 1 0 0 1 -1 0 Z  M 7 2 l 1 0 0 1 -1 0 Z M 8 2 l 1 0 0 1 -1 0 Z      M 14 2 l 1 0 0 1 -1 0 Z  M 16 2 l 1 0 0 1 -1 0 Z M 17 2 l 1 0 0 1 -1 0 Z  M 19 2 l 1 0 0 1 -1 0 Z    M 23 2 l 1 0 0 1 -1 0 Z   M 1 3 l 1 0 0 1 -1 0 Z    M 5 3 l 1 0 0 1 -1 0 Z  M 7 3 l 1 0 0 1 -1 0 Z M 8 3 l 1 0 0 1 -1 0 Z  M 10 3 l 1 0 0 1 -1 0 Z M 11 3 l 1 0 0 1 -1 0 Z   M 14 3 l 1 0 0 1 -1 0 Z M 15 3 l 1 0 0 1 -1 0 Z  M 17 3 l 1 0 0 1 -1 0 Z  M 19 3 l 1 0 0 1 -1 0 Z    M 23 3 l 1 0 0 1 -1 0 Z   M 1 4 l 1 0 0 1 -1 0 Z    M 5 4 l 1 0 0 1 -1 0 Z  M 7 4 l 1 0 0 1 -1 0 Z   M 10 4 l 1 0 0 1 -1 0 Z M 11 4 l 1 0 0 1 -1 0 Z   M 14 4 l 1 0 0 1 -1 0 Z M 15 4 l 1 0 0 1 -1 0 Z  M 17 4 l 1 0 0 1 -1 0 Z  M 19 4 l 1 0 0 1 -1 0 Z    M 23 4 l 1 0 0 1 -1 0 Z   M 1 5 l 1 0 0 1 -1 0 Z M 2 5 l 1 0 0 1 -1 0 Z M 3 5 l 1 0 0 1 -1 0 Z M 4 5 l 1 0 0 1 -1 0 Z M 5 5 l 1 0 0 1 -1 0 Z  M 7 5 l 1 0 0 1 -1 0 Z   M 10 5 l 1 0 0 1 -1 0 Z M 11 5 l 1 0 0 1 -1 0 Z M 12 5 l 1 0 0 1 -1 0 Z   M 15 5 l 1 0 0 1 -1 0 Z  M 17 5 l 1 0 0 1 -1 0 Z  M 19 5 l 1 0 0 1 -1 0 Z M 20 5 l 1 0 0 1 -1 0 Z M 21 5 l 1 0 0 1 -1 0 Z M 22 5 l 1 0 0 1 -1 0 Z M 23 5 l 1 0 0 1 -1 0 Z         M 7 6 l 1 0 0 1 -1 0 Z  M 9 6 l 1 0 0 1 -1 0 Z  M 11 6 l 1 0 0 1 -1 0 Z  M 13 6 l 1 0 0 1 -1 0 Z  M 15 6 l 1 0 0 1 -1 0 Z  M 17 6 l 1 0 0 1 -1 0 Z        M 0 7 l 1 0 0 1 -1 0 Z M 1 7 l 1 0 0 1 -1 0 Z M 2 7 l 1 0 0 1 -1 0 Z M 3 7 l 1 0 0 1 -1 0 Z M 4 7 l 1 0 0 1 -1 0 Z M 5 7 l 1 0 0 1 -1 0 Z M 6 7 l 1 0 0 1 -1 0 Z M 7 7 l 1 0 0 1 -1 0 Z  M 9 7 l 1 0 0 1 -1 0 Z  M 11 7 l 1 0 0 1 -1 0 Z  M 13 7 l 1 0 0 1 -1 0 Z M 14 7 l 1 0 0 1 -1 0 Z  M 16 7 l 1 0 0 1 -1 0 Z M 17 7 l 1 0 0 1 -1 0 Z M 18 7 l 1 0 0 1 -1 0 Z M 19 7 l 1 0 0 1 -1 0 Z M 20 7 l 1 0 0 1 -1 0 Z M 21 7 l 1 0 0 1 -1 0 Z M 22 7 l 1 0 0 1 -1 0 Z M 23 7 l 1 0 0 1 -1 0 Z M 24 7 l 1 0 0 1 -1 0 Z    M 3 8 l 1 0 0 1 -1 0 Z M 4 8 l 1 0 0 1 -1 0 Z   M 7 8 l 1 0 0 1 -1 0 Z      M 13 8 l 1 0 0 1 -1 0 Z   M 16 8 l 1 0 0 1 -1 0 Z     M 21 8 l 1 0 0 1 -1 0 Z M 22 8 l 1 0 0 1 -1 0 Z    M 1 9 l 1 0 0 1 -1 0 Z   M 4 9 l 1 0 0 1 -1 0 Z M 5 9 l 1 0 0 1 -1 0 Z M 6 9 l 1 0 0 1 -1 0 Z    M 10 9 l 1 0 0 1 -1 0 Z    M 14 9 l 1 0 0 1 -1 0 Z  M 16 9 l 1 0 0 1 -1 0 Z M 17 9 l 1 0 0 1 -1 0 Z  M 19 9 l 1 0 0 1 -1 0 Z M 20 9 l 1 0 0 1 -1 0 Z   M 23 9 l 1 0 0 1 -1 0 Z M 24 9 l 1 0 0 1 -1 0 Z M 0 10 l 1 0 0 1 -1 0 Z  M 2 10 l 1 0 0 1 -1 0 Z   M 5 10 l 1 0 0 1 -1 0 Z    M 9 10 l 1 0 0 1 -1 0 Z M 10 10 l 1 0 0 1 -1 0 Z    M 14 10 l 1 0 0 1 -1 0 Z   M 17 10 l 1 0 0 1 -1 0 Z M 18 10 l 1 0 0 1 -1 0 Z M 19 10 l 1 0 0 1 -1 0 Z    M 23 10 l 1 0 0 1 -1 0 Z  M 0 11 l 1 0 0 1 -1 0 Z  M 2 11 l 1 0 0 1 -1 0 Z   M 5 11 l 1 0 0 1 -1 0 Z M 6 11 l 1 0 0 1 -1 0 Z M 7 11 l 1 0 0 1 -1 0 Z M 8 11 l 1 0 0 1 -1 0 Z  M 10 11 l 1 0 0 1 -1 0 Z  M 12 11 l 1 0 0 1 -1 0 Z M 13 11 l 1 0 0 1 -1 0 Z M 14 11 l 1 0 0 1 -1 0 Z  M 16 11 l 1 0 0 1 -1 0 Z M 17 11 l 1 0 0 1 -1 0 Z M 18 11 l 1 0 0 1 -1 0 Z  M 20 11 l 1 0 0 1 -1 0 Z M 21 11 l 1 0 0 1 -1 0 Z M 22 11 l 1 0 0 1 -1 0 Z   M 0 12 l 1 0 0 1 -1 0 Z M 1 12 l 1 0 0 1 -1 0 Z  M 3 12 l 1 0 0 1 -1 0 Z M 4 12 l 1 0 0 1 -1 0 Z      M 10 12 l 1 0 0 1 -1 0 Z  M 12 12 l 1 0 0 1 -1 0 Z M 13 12 l 1 0 0 1 -1 0 Z M 14 12 l 1 0 0 1 -1 0 Z  M 16 12 l 1 0 0 1 -1 0 Z M 17 12 l 1 0 0 1 -1 0 Z  M 19 12 l 1 0 0 1 -1 0 Z M 20 12 l 1 0 0 1 -1 0 Z  M 22 12 l 1 0 0 1 -1 0 Z   M 0 13 l 1 0 0 1 -1 0 Z  M 2 13 l 1 0 0 1 -1 0 Z   M 5 13 l 1 0 0 1 -1 0 Z M 6 13 l 1 0 0 1 -1 0 Z M 7 13 l 1 0 0 1 -1 0 Z   M 10 13 l 1 0 0 1 -1 0 Z   M 13 13 l 1 0 0 1 -1 0 Z   M 16 13 l 1 0 0 1 -1 0 Z  M 18 13 l 1 0 0 1 -1 0 Z M 19 13 l 1 0 0 1 -1 0 Z M 20 13 l 1 0 0 1 -1 0 Z    M 24 13 l 1 0 0 1 -1 0 Z      M 5 14 l 1 0 0 1 -1 0 Z  M 7 14 l 1 0 0 1 -1 0 Z  M 9 14 l 1 0 0 1 -1 0 Z M 10 14 l 1 0 0 1 -1 0 Z  M 12 14 l 1 0 0 1 -1 0 Z    M 16 14 l 1 0 0 1 -1 0 Z     M 21 14 l 1 0 0 1 -1 0 Z  M 23 14 l 1 0 0 1 -1 0 Z  M 0 15 l 1 0 0 1 -1 0 Z M 1 15 l 1 0 0 1 -1 0 Z   M 4 15 l 1 0 0 1 -1 0 Z M 5 15 l 1 0 0 1 -1 0 Z M 6 15 l 1 0 0 1 -1 0 Z M 7 15 l 1 0 0 1 -1 0 Z  M 9 15 l 1 0 0 1 -1 0 Z    M 13 15 l 1 0 0 1 -1 0 Z M 14 15 l 1 0 0 1 -1 0 Z M 15 15 l 1 0 0 1 -1 0 Z M 16 15 l 1 0 0 1 -1 0 Z M 17 15 l 1 0 0 1 -1 0 Z M 18 15 l 1 0 0 1 -1 0 Z  M 20 15 l 1 0 0 1 -1 0 Z M 21 15 l 1 0 0 1 -1 0 Z M 22 15 l 1 0 0 1 -1 0 Z M 23 15 l 1 0 0 1 -1 0 Z M 24 15 l 1 0 0 1 -1 0 Z   M 2 16 l 1 0 0 1 -1 0 Z   M 5 16 l 1 0 0 1 -1 0 Z  M 7 16 l 1 0 0 1 -1 0 Z  M 9 16 l 1 0 0 1 -1 0 Z  M 11 16 l 1 0 0 1 -1 0 Z  M 13 16 l 1 0 0 1 -1 0 Z M 14 16 l 1 0 0 1 -1 0 Z       M 21 16 l 1 0 0 1 -1 0 Z M 22 16 l 1 0 0 1 -1 0 Z   M 0 17 l 1 0 0 1 -1 0 Z M 1 17 l 1 0 0 1 -1 0 Z M 2 17 l 1 0 0 1 -1 0 Z M 3 17 l 1 0 0 1 -1 0 Z M 4 17 l 1 0 0 1 -1 0 Z M 5 17 l 1 0 0 1 -1 0 Z M 6 17 l 1 0 0 1 -1 0 Z M 7 17 l 1 0 0 1 -1 0 Z      M 13 17 l 1 0 0 1 -1 0 Z M 14 17 l 1 0 0 1 -1 0 Z   M 17 17 l 1 0 0 1 -1 0 Z M 18 17 l 1 0 0 1 -1 0 Z M 19 17 l 1 0 0 1 -1 0 Z  M 21 17 l 1 0 0 1 -1 0 Z M 22 17 l 1 0 0 1 -1 0 Z  M 24 17 l 1 0 0 1 -1 0 Z        M 7 18 l 1 0 0 1 -1 0 Z M 8 18 l 1 0 0 1 -1 0 Z M 9 18 l 1 0 0 1 -1 0 Z   M 12 18 l 1 0 0 1 -1 0 Z   M 15 18 l 1 0 0 1 -1 0 Z  M 17 18 l 1 0 0 1 -1 0 Z  M 19 18 l 1 0 0 1 -1 0 Z  M 21 18 l 1 0 0 1 -1 0 Z  M 23 18 l 1 0 0 1 -1 0 Z   M 1 19 l 1 0 0 1 -1 0 Z M 2 19 l 1 0 0 1 -1 0 Z M 3 19 l 1 0 0 1 -1 0 Z M 4 19 l 1 0 0 1 -1 0 Z M 5 19 l 1 0 0 1 -1 0 Z  M 7 19 l 1 0 0 1 -1 0 Z      M 13 19 l 1 0 0 1 -1 0 Z  M 15 19 l 1 0 0 1 -1 0 Z  M 17 19 l 1 0 0 1 -1 0 Z M 18 19 l 1 0 0 1 -1 0 Z M 19 19 l 1 0 0 1 -1 0 Z  M 21 19 l 1 0 0 1 -1 0 Z M 22 19 l 1 0 0 1 -1 0 Z    M 1 20 l 1 0 0 1 -1 0 Z    M 5 20 l 1 0 0 1 -1 0 Z  M 7 20 l 1 0 0 1 -1 0 Z M 8 20 l 1 0 0 1 -1 0 Z  M 10 20 l 1 0 0 1 -1 0 Z  M 12 20 l 1 0 0 1 -1 0 Z M 13 20 l 1 0 0 1 -1 0 Z M 14 20 l 1 0 0 1 -1 0 Z       M 21 20 l 1 0 0 1 -1 0 Z M 22 20 l 1 0 0 1 -1 0 Z M 23 20 l 1 0 0 1 -1 0 Z M 24 20 l 1 0 0 1 -1 0 Z  M 1 21 l 1 0 0 1 -1 0 Z    M 5 21 l 1 0 0 1 -1 0 Z  M 7 21 l 1 0 0 1 -1 0 Z M 8 21 l 1 0 0 1 -1 0 Z M 9 21 l 1 0 0 1 -1 0 Z    M 13 21 l 1 0 0 1 -1 0 Z   M 16 21 l 1 0 0 1 -1 0 Z  M 18 21 l 1 0 0 1 -1 0 Z   M 21 21 l 1 0 0 1 -1 0 Z M 22 21 l 1 0 0 1 -1 0 Z  M 24 21 l 1 0 0 1 -1 0 Z  M 1 22 l 1 0 0 1 -1 0 Z    M 5 22 l 1 0 0 1 -1 0 Z  M 7 22 l 1 0 0 1 -1 0 Z  M 9 22 l 1 0 0 1 -1 0 Z   M 12 22 l 1 0 0 1 -1 0 Z M 13 22 l 1 0 0 1 -1 0 Z  M 15 22 l 1 0 0 1 -1 0 Z M 16 22 l 1 0 0 1 -1 0 Z M 17 22 l 1 0 0 1 -1 0 Z     M 22 22 l 1 0 0 1 -1 0 Z    M 1 23 l 1 0 0 1 -1 0 Z M 2 23 l 1 0 0 1 -1 0 Z M 3 23 l 1 0 0 1 -1 0 Z M 4 23 l 1 0 0 1 -1 0 Z M 5 23 l 1 0 0 1 -1 0 Z  M 7 23 l 1 0 0 1 -1 0 Z   M 10 23 l 1 0 0 1 -1 0 Z  M 12 23 l 1 0 0 1 -1 0 Z M 13 23 l 1 0 0 1 -1 0 Z  M 15 23 l 1 0 0 1 -1 0 Z  M 17 23 l 1 0 0 1 -1 0 Z M 18 23 l 1 0 0 1 -1 0 Z   M 21 23 l 1 0 0 1 -1 0 Z M 22 23 l 1 0 0 1 -1 0 Z M 23 23 l 1 0 0 1 -1 0 Z M 24 23 l 1 0 0 1 -1 0 Z        M 7 24 l 1 0 0 1 -1 0 Z  M 9 24 l 1 0 0 1 -1 0 Z M 10 24 l 1 0 0 1 -1 0 Z  M 12 24 l 1 0 0 1 -1 0 Z M 13 24 l 1 0 0 1 -1 0 Z M 14 24 l 1 0 0 1 -1 0 Z       M 21 24 l 1 0 0 1 -1 0 Z M 22 24 l 1 0 0 1 -1 0 Z M 23 24 l 1 0 0 1 -1 0 Z "
              fill="#FFFFFF"
            />
            <path
              d="M 0 0 l 1 0 0 1 -1 0 Z M 1 0 l 1 0 0 1 -1 0 Z M 2 0 l 1 0 0 1 -1 0 Z M 3 0 l 1 0 0 1 -1 0 Z M 4 0 l 1 0 0 1 -1 0 Z M 5 0 l 1 0 0 1 -1 0 Z M 6 0 l 1 0 0 1 -1 0 Z  M 8 0 l 1 0 0 1 -1 0 Z M 9 0 l 1 0 0 1 -1 0 Z   M 12 0 l 1 0 0 1 -1 0 Z M 13 0 l 1 0 0 1 -1 0 Z   M 16 0 l 1 0 0 1 -1 0 Z  M 18 0 l 1 0 0 1 -1 0 Z M 19 0 l 1 0 0 1 -1 0 Z M 20 0 l 1 0 0 1 -1 0 Z M 21 0 l 1 0 0 1 -1 0 Z M 22 0 l 1 0 0 1 -1 0 Z M 23 0 l 1 0 0 1 -1 0 Z M 24 0 l 1 0 0 1 -1 0 Z M 0 1 l 1 0 0 1 -1 0 Z      M 6 1 l 1 0 0 1 -1 0 Z  M 8 1 l 1 0 0 1 -1 0 Z M 9 1 l 1 0 0 1 -1 0 Z   M 12 1 l 1 0 0 1 -1 0 Z  M 14 1 l 1 0 0 1 -1 0 Z    M 18 1 l 1 0 0 1 -1 0 Z      M 24 1 l 1 0 0 1 -1 0 Z M 0 2 l 1 0 0 1 -1 0 Z  M 2 2 l 1 0 0 1 -1 0 Z M 3 2 l 1 0 0 1 -1 0 Z M 4 2 l 1 0 0 1 -1 0 Z  M 6 2 l 1 0 0 1 -1 0 Z   M 9 2 l 1 0 0 1 -1 0 Z M 10 2 l 1 0 0 1 -1 0 Z M 11 2 l 1 0 0 1 -1 0 Z M 12 2 l 1 0 0 1 -1 0 Z M 13 2 l 1 0 0 1 -1 0 Z  M 15 2 l 1 0 0 1 -1 0 Z   M 18 2 l 1 0 0 1 -1 0 Z  M 20 2 l 1 0 0 1 -1 0 Z M 21 2 l 1 0 0 1 -1 0 Z M 22 2 l 1 0 0 1 -1 0 Z  M 24 2 l 1 0 0 1 -1 0 Z M 0 3 l 1 0 0 1 -1 0 Z  M 2 3 l 1 0 0 1 -1 0 Z M 3 3 l 1 0 0 1 -1 0 Z M 4 3 l 1 0 0 1 -1 0 Z  M 6 3 l 1 0 0 1 -1 0 Z   M 9 3 l 1 0 0 1 -1 0 Z   M 12 3 l 1 0 0 1 -1 0 Z M 13 3 l 1 0 0 1 -1 0 Z   M 16 3 l 1 0 0 1 -1 0 Z  M 18 3 l 1 0 0 1 -1 0 Z  M 20 3 l 1 0 0 1 -1 0 Z M 21 3 l 1 0 0 1 -1 0 Z M 22 3 l 1 0 0 1 -1 0 Z  M 24 3 l 1 0 0 1 -1 0 Z M 0 4 l 1 0 0 1 -1 0 Z  M 2 4 l 1 0 0 1 -1 0 Z M 3 4 l 1 0 0 1 -1 0 Z M 4 4 l 1 0 0 1 -1 0 Z  M 6 4 l 1 0 0 1 -1 0 Z  M 8 4 l 1 0 0 1 -1 0 Z M 9 4 l 1 0 0 1 -1 0 Z   M 12 4 l 1 0 0 1 -1 0 Z M 13 4 l 1 0 0 1 -1 0 Z   M 16 4 l 1 0 0 1 -1 0 Z  M 18 4 l 1 0 0 1 -1 0 Z  M 20 4 l 1 0 0 1 -1 0 Z M 21 4 l 1 0 0 1 -1 0 Z M 22 4 l 1 0 0 1 -1 0 Z  M 24 4 l 1 0 0 1 -1 0 Z M 0 5 l 1 0 0 1 -1 0 Z      M 6 5 l 1 0 0 1 -1 0 Z  M 8 5 l 1 0 0 1 -1 0 Z M 9 5 l 1 0 0 1 -1 0 Z    M 13 5 l 1 0 0 1 -1 0 Z M 14 5 l 1 0 0 1 -1 0 Z  M 16 5 l 1 0 0 1 -1 0 Z  M 18 5 l 1 0 0 1 -1 0 Z      M 24 5 l 1 0 0 1 -1 0 Z M 0 6 l 1 0 0 1 -1 0 Z M 1 6 l 1 0 0 1 -1 0 Z M 2 6 l 1 0 0 1 -1 0 Z M 3 6 l 1 0 0 1 -1 0 Z M 4 6 l 1 0 0 1 -1 0 Z M 5 6 l 1 0 0 1 -1 0 Z M 6 6 l 1 0 0 1 -1 0 Z  M 8 6 l 1 0 0 1 -1 0 Z  M 10 6 l 1 0 0 1 -1 0 Z  M 12 6 l 1 0 0 1 -1 0 Z  M 14 6 l 1 0 0 1 -1 0 Z  M 16 6 l 1 0 0 1 -1 0 Z  M 18 6 l 1 0 0 1 -1 0 Z M 19 6 l 1 0 0 1 -1 0 Z M 20 6 l 1 0 0 1 -1 0 Z M 21 6 l 1 0 0 1 -1 0 Z M 22 6 l 1 0 0 1 -1 0 Z M 23 6 l 1 0 0 1 -1 0 Z M 24 6 l 1 0 0 1 -1 0 Z         M 8 7 l 1 0 0 1 -1 0 Z  M 10 7 l 1 0 0 1 -1 0 Z  M 12 7 l 1 0 0 1 -1 0 Z   M 15 7 l 1 0 0 1 -1 0 Z          M 0 8 l 1 0 0 1 -1 0 Z M 1 8 l 1 0 0 1 -1 0 Z M 2 8 l 1 0 0 1 -1 0 Z   M 5 8 l 1 0 0 1 -1 0 Z M 6 8 l 1 0 0 1 -1 0 Z  M 8 8 l 1 0 0 1 -1 0 Z M 9 8 l 1 0 0 1 -1 0 Z M 10 8 l 1 0 0 1 -1 0 Z M 11 8 l 1 0 0 1 -1 0 Z M 12 8 l 1 0 0 1 -1 0 Z  M 14 8 l 1 0 0 1 -1 0 Z M 15 8 l 1 0 0 1 -1 0 Z  M 17 8 l 1 0 0 1 -1 0 Z M 18 8 l 1 0 0 1 -1 0 Z M 19 8 l 1 0 0 1 -1 0 Z M 20 8 l 1 0 0 1 -1 0 Z   M 23 8 l 1 0 0 1 -1 0 Z M 24 8 l 1 0 0 1 -1 0 Z M 0 9 l 1 0 0 1 -1 0 Z  M 2 9 l 1 0 0 1 -1 0 Z M 3 9 l 1 0 0 1 -1 0 Z    M 7 9 l 1 0 0 1 -1 0 Z M 8 9 l 1 0 0 1 -1 0 Z M 9 9 l 1 0 0 1 -1 0 Z  M 11 9 l 1 0 0 1 -1 0 Z M 12 9 l 1 0 0 1 -1 0 Z M 13 9 l 1 0 0 1 -1 0 Z  M 15 9 l 1 0 0 1 -1 0 Z   M 18 9 l 1 0 0 1 -1 0 Z   M 21 9 l 1 0 0 1 -1 0 Z M 22 9 l 1 0 0 1 -1 0 Z    M 1 10 l 1 0 0 1 -1 0 Z  M 3 10 l 1 0 0 1 -1 0 Z M 4 10 l 1 0 0 1 -1 0 Z  M 6 10 l 1 0 0 1 -1 0 Z M 7 10 l 1 0 0 1 -1 0 Z M 8 10 l 1 0 0 1 -1 0 Z   M 11 10 l 1 0 0 1 -1 0 Z M 12 10 l 1 0 0 1 -1 0 Z M 13 10 l 1 0 0 1 -1 0 Z  M 15 10 l 1 0 0 1 -1 0 Z M 16 10 l 1 0 0 1 -1 0 Z    M 20 10 l 1 0 0 1 -1 0 Z M 21 10 l 1 0 0 1 -1 0 Z M 22 10 l 1 0 0 1 -1 0 Z  M 24 10 l 1 0 0 1 -1 0 Z  M 1 11 l 1 0 0 1 -1 0 Z  M 3 11 l 1 0 0 1 -1 0 Z M 4 11 l 1 0 0 1 -1 0 Z     M 9 11 l 1 0 0 1 -1 0 Z  M 11 11 l 1 0 0 1 -1 0 Z    M 15 11 l 1 0 0 1 -1 0 Z    M 19 11 l 1 0 0 1 -1 0 Z    M 23 11 l 1 0 0 1 -1 0 Z M 24 11 l 1 0 0 1 -1 0 Z   M 2 12 l 1 0 0 1 -1 0 Z   M 5 12 l 1 0 0 1 -1 0 Z M 6 12 l 1 0 0 1 -1 0 Z M 7 12 l 1 0 0 1 -1 0 Z M 8 12 l 1 0 0 1 -1 0 Z M 9 12 l 1 0 0 1 -1 0 Z  M 11 12 l 1 0 0 1 -1 0 Z    M 15 12 l 1 0 0 1 -1 0 Z   M 18 12 l 1 0 0 1 -1 0 Z   M 21 12 l 1 0 0 1 -1 0 Z  M 23 12 l 1 0 0 1 -1 0 Z M 24 12 l 1 0 0 1 -1 0 Z  M 1 13 l 1 0 0 1 -1 0 Z  M 3 13 l 1 0 0 1 -1 0 Z M 4 13 l 1 0 0 1 -1 0 Z    M 8 13 l 1 0 0 1 -1 0 Z M 9 13 l 1 0 0 1 -1 0 Z  M 11 13 l 1 0 0 1 -1 0 Z M 12 13 l 1 0 0 1 -1 0 Z  M 14 13 l 1 0 0 1 -1 0 Z M 15 13 l 1 0 0 1 -1 0 Z  M 17 13 l 1 0 0 1 -1 0 Z    M 21 13 l 1 0 0 1 -1 0 Z M 22 13 l 1 0 0 1 -1 0 Z M 23 13 l 1 0 0 1 -1 0 Z  M 0 14 l 1 0 0 1 -1 0 Z M 1 14 l 1 0 0 1 -1 0 Z M 2 14 l 1 0 0 1 -1 0 Z M 3 14 l 1 0 0 1 -1 0 Z M 4 14 l 1 0 0 1 -1 0 Z  M 6 14 l 1 0 0 1 -1 0 Z  M 8 14 l 1 0 0 1 -1 0 Z   M 11 14 l 1 0 0 1 -1 0 Z  M 13 14 l 1 0 0 1 -1 0 Z M 14 14 l 1 0 0 1 -1 0 Z M 15 14 l 1 0 0 1 -1 0 Z  M 17 14 l 1 0 0 1 -1 0 Z M 18 14 l 1 0 0 1 -1 0 Z M 19 14 l 1 0 0 1 -1 0 Z M 20 14 l 1 0 0 1 -1 0 Z  M 22 14 l 1 0 0 1 -1 0 Z  M 24 14 l 1 0 0 1 -1 0 Z   M 2 15 l 1 0 0 1 -1 0 Z M 3 15 l 1 0 0 1 -1 0 Z     M 8 15 l 1 0 0 1 -1 0 Z  M 10 15 l 1 0 0 1 -1 0 Z M 11 15 l 1 0 0 1 -1 0 Z M 12 15 l 1 0 0 1 -1 0 Z       M 19 15 l 1 0 0 1 -1 0 Z      M 0 16 l 1 0 0 1 -1 0 Z M 1 16 l 1 0 0 1 -1 0 Z  M 3 16 l 1 0 0 1 -1 0 Z M 4 16 l 1 0 0 1 -1 0 Z  M 6 16 l 1 0 0 1 -1 0 Z  M 8 16 l 1 0 0 1 -1 0 Z  M 10 16 l 1 0 0 1 -1 0 Z  M 12 16 l 1 0 0 1 -1 0 Z   M 15 16 l 1 0 0 1 -1 0 Z M 16 16 l 1 0 0 1 -1 0 Z M 17 16 l 1 0 0 1 -1 0 Z M 18 16 l 1 0 0 1 -1 0 Z M 19 16 l 1 0 0 1 -1 0 Z M 20 16 l 1 0 0 1 -1 0 Z   M 23 16 l 1 0 0 1 -1 0 Z M 24 16 l 1 0 0 1 -1 0 Z         M 8 17 l 1 0 0 1 -1 0 Z M 9 17 l 1 0 0 1 -1 0 Z M 10 17 l 1 0 0 1 -1 0 Z M 11 17 l 1 0 0 1 -1 0 Z M 12 17 l 1 0 0 1 -1 0 Z   M 15 17 l 1 0 0 1 -1 0 Z M 16 17 l 1 0 0 1 -1 0 Z    M 20 17 l 1 0 0 1 -1 0 Z   M 23 17 l 1 0 0 1 -1 0 Z  M 0 18 l 1 0 0 1 -1 0 Z M 1 18 l 1 0 0 1 -1 0 Z M 2 18 l 1 0 0 1 -1 0 Z M 3 18 l 1 0 0 1 -1 0 Z M 4 18 l 1 0 0 1 -1 0 Z M 5 18 l 1 0 0 1 -1 0 Z M 6 18 l 1 0 0 1 -1 0 Z    M 10 18 l 1 0 0 1 -1 0 Z M 11 18 l 1 0 0 1 -1 0 Z  M 13 18 l 1 0 0 1 -1 0 Z M 14 18 l 1 0 0 1 -1 0 Z  M 16 18 l 1 0 0 1 -1 0 Z  M 18 18 l 1 0 0 1 -1 0 Z  M 20 18 l 1 0 0 1 -1 0 Z  M 22 18 l 1 0 0 1 -1 0 Z  M 24 18 l 1 0 0 1 -1 0 Z M 0 19 l 1 0 0 1 -1 0 Z      M 6 19 l 1 0 0 1 -1 0 Z  M 8 19 l 1 0 0 1 -1 0 Z M 9 19 l 1 0 0 1 -1 0 Z M 10 19 l 1 0 0 1 -1 0 Z M 11 19 l 1 0 0 1 -1 0 Z M 12 19 l 1 0 0 1 -1 0 Z  M 14 19 l 1 0 0 1 -1 0 Z  M 16 19 l 1 0 0 1 -1 0 Z    M 20 19 l 1 0 0 1 -1 0 Z   M 23 19 l 1 0 0 1 -1 0 Z M 24 19 l 1 0 0 1 -1 0 Z M 0 20 l 1 0 0 1 -1 0 Z  M 2 20 l 1 0 0 1 -1 0 Z M 3 20 l 1 0 0 1 -1 0 Z M 4 20 l 1 0 0 1 -1 0 Z  M 6 20 l 1 0 0 1 -1 0 Z   M 9 20 l 1 0 0 1 -1 0 Z  M 11 20 l 1 0 0 1 -1 0 Z    M 15 20 l 1 0 0 1 -1 0 Z M 16 20 l 1 0 0 1 -1 0 Z M 17 20 l 1 0 0 1 -1 0 Z M 18 20 l 1 0 0 1 -1 0 Z M 19 20 l 1 0 0 1 -1 0 Z M 20 20 l 1 0 0 1 -1 0 Z     M 0 21 l 1 0 0 1 -1 0 Z  M 2 21 l 1 0 0 1 -1 0 Z M 3 21 l 1 0 0 1 -1 0 Z M 4 21 l 1 0 0 1 -1 0 Z  M 6 21 l 1 0 0 1 -1 0 Z    M 10 21 l 1 0 0 1 -1 0 Z M 11 21 l 1 0 0 1 -1 0 Z M 12 21 l 1 0 0 1 -1 0 Z  M 14 21 l 1 0 0 1 -1 0 Z M 15 21 l 1 0 0 1 -1 0 Z  M 17 21 l 1 0 0 1 -1 0 Z  M 19 21 l 1 0 0 1 -1 0 Z M 20 21 l 1 0 0 1 -1 0 Z   M 23 21 l 1 0 0 1 -1 0 Z  M 0 22 l 1 0 0 1 -1 0 Z  M 2 22 l 1 0 0 1 -1 0 Z M 3 22 l 1 0 0 1 -1 0 Z M 4 22 l 1 0 0 1 -1 0 Z  M 6 22 l 1 0 0 1 -1 0 Z  M 8 22 l 1 0 0 1 -1 0 Z  M 10 22 l 1 0 0 1 -1 0 Z M 11 22 l 1 0 0 1 -1 0 Z   M 14 22 l 1 0 0 1 -1 0 Z    M 18 22 l 1 0 0 1 -1 0 Z M 19 22 l 1 0 0 1 -1 0 Z M 20 22 l 1 0 0 1 -1 0 Z M 21 22 l 1 0 0 1 -1 0 Z  M 23 22 l 1 0 0 1 -1 0 Z M 24 22 l 1 0 0 1 -1 0 Z M 0 23 l 1 0 0 1 -1 0 Z      M 6 23 l 1 0 0 1 -1 0 Z  M 8 23 l 1 0 0 1 -1 0 Z M 9 23 l 1 0 0 1 -1 0 Z  M 11 23 l 1 0 0 1 -1 0 Z   M 14 23 l 1 0 0 1 -1 0 Z  M 16 23 l 1 0 0 1 -1 0 Z   M 19 23 l 1 0 0 1 -1 0 Z M 20 23 l 1 0 0 1 -1 0 Z     M 0 24 l 1 0 0 1 -1 0 Z M 1 24 l 1 0 0 1 -1 0 Z M 2 24 l 1 0 0 1 -1 0 Z M 3 24 l 1 0 0 1 -1 0 Z M 4 24 l 1 0 0 1 -1 0 Z M 5 24 l 1 0 0 1 -1 0 Z M 6 24 l 1 0 0 1 -1 0 Z  M 8 24 l 1 0 0 1 -1 0 Z   M 11 24 l 1 0 0 1 -1 0 Z    M 15 24 l 1 0 0 1 -1 0 Z M 16 24 l 1 0 0 1 -1 0 Z M 17 24 l 1 0 0 1 -1 0 Z M 18 24 l 1 0 0 1 -1 0 Z M 19 24 l 1 0 0 1 -1 0 Z M 20 24 l 1 0 0 1 -1 0 Z    M 24 24 l 1 0 0 1 -1 0 Z"
              fill="#000000"
            />
          </svg>
        </div>
      </section>
    </div>
    <div
      class="pb-2"
    />
    <div
      class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 print:grid-cols-3 gap-2"
    >
      <section
        class="p-3 rounded-sm bg-white"
      >
        <div
          class="flex justify-between items-baseline"
        >
          <h3
            class="text-lg font-bold pb-4"
          >
            Installation details
          </h3>
          <a
            class="text-primary underline hover:decoration-2 focus:decoration-2 focus:outline-none cursor-pointer text-right ml-2"
            href="/chargers/PSL-12345/installation?socket=A"
          >
            View installation details
          </a>
        </div>
        <div
          class="flex flex-col bg-white rounded-sm overflow-hidden overflow-x-auto"
        >
          <div
            class="inline-block min-w-full -my-2 py-2 align-middle"
          >
            <table
              class="min-w-full"
              tabindex="0"
            >
              <caption
                class="sr-only"
              >
                Installation details table
              </caption>
              <tbody
                class="divide-y"
              >
                <tr
                  class="border-b-neutral/20"
                >
                  <td
                    class="whitespace-normal px-3 py-4 pl-0 pr-2 pt-2 pb-2 whitespace-pre"
                  >
                    Install date
                  </td>
                  <td
                    class="whitespace-normal px-3 py-4 pr-0 pl-2 pt-2 pb-2 text-neutral text-right"
                  >
                    7 July 2023
                  </td>
                </tr>
                <tr
                  class="border-b-neutral/20"
                >
                  <td
                    class="whitespace-normal px-3 py-4 pl-0 pr-2 pt-2 pb-2 whitespace-pre"
                  >
                    Installed by
                  </td>
                  <td
                    class="whitespace-normal px-3 py-4 pr-0 pl-2 pt-2 pb-2 text-neutral text-right"
                  >
                    Joe Spark
                  </td>
                </tr>
                <tr
                  class="border-b-neutral/20"
                >
                  <td
                    class="whitespace-normal px-3 py-4 pl-0 pr-2 pt-2 pb-2 whitespace-pre"
                  >
                    Company
                  </td>
                  <td
                    class="whitespace-normal px-3 py-4 pr-0 pl-2 pt-2 pb-2 text-neutral text-right"
                  >
                    Electrical Services Ltd.
                  </td>
                </tr>
              </tbody>
            </table>
          </div>
        </div>
      </section>
      <section
        class="p-3 rounded-sm bg-white"
      >
        <h3
          class="text-lg font-bold pb-4"
        >
          Warranty details
        </h3>
        <div
          class="flex flex-col bg-white rounded-sm overflow-hidden overflow-x-auto"
        >
          <div
            class="inline-block min-w-full -my-2 py-2 align-middle"
          >
            <table
              class="min-w-full"
              tabindex="0"
            >
              <caption
                class="sr-only"
              >
                Warranty details table
              </caption>
              <tbody
                class="divide-y"
              >
                <tr
                  class="border-b-neutral/20"
                >
                  <td
                    class="whitespace-normal px-3 py-4 pl-0 pr-2 pt-2 pb-2 whitespace-pre"
                  >
                    Warranty start date
                  </td>
                  <td
                    class="whitespace-normal px-3 py-4 pr-0 pl-2 pt-2 pb-2 text-neutral text-right"
                  >
                    1 August 2024
                  </td>
                </tr>
                <tr
                  class="border-b-neutral/20"
                >
                  <td
                    class="whitespace-normal px-3 py-4 pl-0 pr-2 pt-2 pb-2 whitespace-pre"
                  >
                    Warranty end date
                  </td>
                  <td
                    class="whitespace-normal px-3 py-4 pr-0 pl-2 pt-2 pb-2 text-neutral text-right"
                  >
                    1st August 2027
                  </td>
                </tr>
                <tr
                  class="border-b-neutral/20"
                >
                  <td
                    class="whitespace-normal px-3 py-4 pl-0 pr-2 pt-2 pb-2 whitespace-pre"
                  >
                    Warranty status
                  </td>
                  <td
                    class="whitespace-normal px-3 py-4 pr-0 pl-2 pt-2 pb-2 text-neutral text-right"
                  >
                    <span
                      aria-label="Active"
                      class="font-bold px-2.5 py-1 rounded-xs inline-flex items-center tracking-wide bg-success/10 border border-success text-success"
                      role="status"
                    >
                      Active
                    </span>
                  </td>
                </tr>
              </tbody>
            </table>
          </div>
        </div>
      </section>
      <section
        class="p-3 rounded-sm bg-white"
      >
        <div
          class="flex justify-between items-baseline"
        >
          <h3
            class="text-lg font-bold pb-4"
          >
            Last complete charge
          </h3>
          <a
            class="text-primary underline hover:decoration-2 focus:decoration-2 focus:outline-none cursor-pointer text-right ml-2"
            href="/chargers/PSL-12345/recent-charges?socket=A"
          >
            Recent charges
          </a>
        </div>
        <div
          class="flex flex-col bg-white rounded-sm overflow-hidden overflow-x-auto"
        >
          <div
            class="inline-block min-w-full -my-2 py-2 align-middle"
          >
            <table
              class="min-w-full"
              tabindex="0"
            >
              <caption
                class="sr-only"
              >
                Last complete charge table
              </caption>
              <tbody
                class="divide-y"
              >
                <tr
                  class="border-b-neutral/20"
                >
                  <td
                    class="whitespace-normal px-3 py-4 pl-0 pr-2 pt-2 pb-2 whitespace-pre"
                  >
                    Started at
                  </td>
                  <td
                    class="whitespace-normal px-3 py-4 pr-0 pl-2 pt-2 pb-2 text-neutral text-right"
                  >
                    30 June 2024 at 19:00:00
                  </td>
                </tr>
                <tr
                  class="border-b-neutral/20"
                >
                  <td
                    class="whitespace-normal px-3 py-4 pl-0 pr-2 pt-2 pb-2 whitespace-pre"
                  >
                    Ended at
                  </td>
                  <td
                    class="whitespace-normal px-3 py-4 pr-0 pl-2 pt-2 pb-2 text-neutral text-right"
                  >
                    30 June 2024 at 21:00:00
                  </td>
                </tr>
                <tr
                  class="border-b-neutral/20"
                >
                  <td
                    class="whitespace-normal px-3 py-4 pl-0 pr-2 pt-2 pb-2 whitespace-pre"
                  >
                    kWh delivered
                  </td>
                  <td
                    class="whitespace-normal px-3 py-4 pr-0 pl-2 pt-2 pb-2 text-neutral text-right"
                  >
                    567.89 kWh
                  </td>
                </tr>
              </tbody>
            </table>
          </div>
        </div>
      </section>
    </div>
    <div
      class="pb-2"
    />
    <section
      class="p-3 rounded-sm bg-white"
    >
      <div
        class="flex justify-between items-baseline"
      >
        <h3
          class="text-lg font-bold pb-4"
        >
          Modes
        </h3>
        <a
          class="text-primary underline hover:decoration-2 focus:decoration-2 focus:outline-none cursor-pointer text-right ml-2"
          href="/chargers/PSL-12345/mode-history"
        >
          View history
        </a>
      </div>
      <div
        class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 print:grid-cols-3 gap-2"
      >
        <div
          class="pb-2 break-words"
        >
          <div
            class="flex"
          >
            <div
              data-headlessui-state=""
            >
              <div
                class="mr-2"
              >
                <label
                  class="text-lg font-normal block mb-1.5"
                  for="toggle-smart-mode"
                  id="toggle-smart-mode-label"
                >
                  Smart mode
                </label>
              </div>
              <button
                aria-checked="true"
                aria-label="toggle-smart-mode"
                class="bg-primary group relative inline-flex h-6 w-11 shrink-0 cursor-pointer rounded-full border-2 border-transparent transition-colors duration-200 ease-in-out focus:outline-hidden focus:ring-2 focus:ring-primary/50 focus:ring-offset-2 data-[disabled]:bg-neutral/40 data-[disabled]:cursor-not-allowed"
                data-checked=""
                data-headlessui-state="checked"
                id="toggle-smart-mode"
                role="switch"
                tabindex="0"
                type="button"
              >
                <span
                  aria-hidden="true"
                  class="translate-x-5 pointer-events-none inline-block h-5 w-5 transform rounded-full bg-white shadow-sm ring-0 transition duration-200 ease-in-out group-data-[disabled]:bg-neutral/20 group-data-[disabled]:cursor-default"
                />
              </button>
              <span
                hidden=""
                style="position: fixed; top: 1px; left: 1px; width: 1px; height: 0px; padding: 0px; margin: -1px; overflow: hidden; clip: rect(0px, 0px, 0px, 0px); white-space: nowrap; border-width: 0px; display: none;"
              />
            </div>
          </div>
        </div>
        <div
          class="pb-2 break-words"
        >
          <div
            class="flex"
          >
            <div
              data-headlessui-state=""
            >
              <div
                class="mr-2"
              >
                <label
                  class="text-lg font-normal block mb-1.5"
                  for="toggle-off-mode"
                  id="toggle-off-mode-label"
                >
                  Off mode
                </label>
              </div>
              <button
                aria-checked="false"
                aria-label="toggle-off-mode"
                class="bg-neutral/20 group relative inline-flex h-6 w-11 shrink-0 cursor-pointer rounded-full border-2 border-transparent transition-colors duration-200 ease-in-out focus:outline-hidden focus:ring-2 focus:ring-primary/50 focus:ring-offset-2 data-[disabled]:bg-neutral/40 data-[disabled]:cursor-not-allowed"
                data-headlessui-state=""
                id="toggle-off-mode"
                role="switch"
                tabindex="0"
                type="button"
              >
                <span
                  aria-hidden="true"
                  class="translate-x-0 pointer-events-none inline-block h-5 w-5 transform rounded-full bg-white shadow-sm ring-0 transition duration-200 ease-in-out group-data-[disabled]:bg-neutral/20 group-data-[disabled]:cursor-default"
                />
              </button>
              <span
                hidden=""
                style="position: fixed; top: 1px; left: 1px; width: 1px; height: 0px; padding: 0px; margin: -1px; overflow: hidden; clip: rect(0px, 0px, 0px, 0px); white-space: nowrap; border-width: 0px; display: none;"
              />
            </div>
          </div>
        </div>
        <div
          class="pb-2 break-words"
        >
          <div
            class="flex"
          >
            <div
              data-headlessui-state=""
            >
              <div
                class="mr-2"
              >
                <label
                  class="text-lg font-normal block mb-1.5"
                  for="toggle-charge-now"
                  id="toggle-charge-now-label"
                >
                  Charge now
                </label>
              </div>
              <button
                aria-checked="false"
                aria-label="toggle-charge-now"
                class="bg-neutral/20 group relative inline-flex h-6 w-11 shrink-0 cursor-pointer rounded-full border-2 border-transparent transition-colors duration-200 ease-in-out focus:outline-hidden focus:ring-2 focus:ring-primary/50 focus:ring-offset-2 data-[disabled]:bg-neutral/40 data-[disabled]:cursor-not-allowed"
                data-headlessui-state=""
                id="toggle-charge-now"
                role="switch"
                tabindex="0"
                type="button"
              >
                <span
                  aria-hidden="true"
                  class="translate-x-0 pointer-events-none inline-block h-5 w-5 transform rounded-full bg-white shadow-sm ring-0 transition duration-200 ease-in-out group-data-[disabled]:bg-neutral/20 group-data-[disabled]:cursor-default"
                />
              </button>
              <span
                hidden=""
                style="position: fixed; top: 1px; left: 1px; width: 1px; height: 0px; padding: 0px; margin: -1px; overflow: hidden; clip: rect(0px, 0px, 0px, 0px); white-space: nowrap; border-width: 0px; display: none;"
              />
            </div>
          </div>
        </div>
        <div
          class="pb-2 break-words"
        >
          <h5
            class="text-lg"
          >
            Flex
          </h5>
          <span
            class="text-xl font-semibold"
          >
            <a
              class="text-primary underline hover:decoration-2 focus:decoration-2 focus:outline-none text-xl font-semibold hover:underline cursor-pointer"
              href="/chargers/PSL-12345/flex-details"
            >
              Enrolled
            </a>
          </span>
        </div>
        <div
          class="flex"
        >
          <div
            class="pb-2 break-words"
          >
            <h5
              class="text-lg"
            >
              Delegated control
            </h5>
            <div
              class="flex justify-between items-center"
            >
              <span
                class="text-xl font-semibold"
              >
                Active since 30 June 2024 at 21:00:00 (3rd Party)
              </span>
              <button
                aria-label="Remove delegated control"
                class="border-2 border-solid rounded-sm font-bold text-center disabled:no-underline transition ease-in-out duration-200 cursor-pointer disabled:cursor-not-allowed border-transparent text-primary hover:underline hover:text-primary active:font-bold active:text-primary disabled:text-neutral/40 disabled:hover:no-underline disabled:hover:border-transparent p-1.5"
                type="button"
              >
                <svg
                  class="h-4 w-4 stroke-1 stroke-current fill-current"
                  viewBox="0 0 32 32"
                  xmlns="http://www.w3.org/2000/svg"
                >
                  <title>
                    Cross
                  </title>
                  <g>
                    <path
                      d="M16,.07C7.22,.07,.07,7.22,.07,16s7.15,15.93,15.93,15.93,15.93-7.15,15.93-15.93S24.78,.07,16,.07Zm0,29.86c-7.68,0-13.93-6.25-13.93-13.93S8.32,2.07,16,2.07s13.93,6.25,13.93,13.93-6.25,13.93-13.93,13.93Z"
                    />
                    <path
                      d="M23.38,8.62c-.39-.39-1.02-.39-1.41,0l-5.96,5.96-5.96-5.96c-.39-.39-1.02-.39-1.41,0s-.39,1.02,0,1.41l5.96,5.96-5.96,5.96c-.39,.39-.39,1.02,0,1.41,.2,.2,.45,.29,.71,.29s.51-.1,.71-.29l5.96-5.96,5.96,5.96c.2,.2,.45,.29,.71,.29s.51-.1,.71-.29c.39-.39,.39-1.02,0-1.41l-5.96-5.96,5.96-5.96c.39-.39,.39-1.02,0-1.41Z"
                    />
                  </g>
                </svg>
              </button>
            </div>
          </div>
        </div>
      </div>
    </section>
    <div
      class="pb-2"
    />
    <section
      class="p-3 rounded-sm bg-white"
    >
      <div
        class="flex justify-between items-baseline"
      >
        <h3
          class="text-lg font-bold pb-4"
        >
          Schedules
        </h3>
        <a
          class="text-primary underline hover:decoration-2 focus:decoration-2 focus:outline-none cursor-pointer text-right ml-2"
          href="/chargers/PSL-12345/schedules"
        >
          View schedules
        </a>
      </div>
      <div
        class="rounded-md p-3 bg-info/10 border border-info"
      >
        <div
          class="flex"
        >
          <div
            class="shrink-0"
          >
            <span
              class="text-info"
            >
              <svg
                class="fill-current w-6 h-6"
                viewBox="0 0 32 32"
                xmlns="http://www.w3.org/2000/svg"
              >
                <g>
                  <path
                    d="M16.1,29.43A13.43,13.43,0,1,1,29.53,16,13.45,13.45,0,0,1,16.1,29.43Zm0-24.86A11.43,11.43,0,1,0,27.53,16,11.45,11.45,0,0,0,16.1,4.57Z"
                  />
                  <path
                    d="M16,24.12a1,1,0,0,1-1-1V14.94a1,1,0,1,1,2,0v8.18A1,1,0,0,1,16,24.12Z"
                  />
                  <path
                    d="M16.1,10.89a1,1,0,0,1-1-1v-.2a1,1,0,0,1,2,0v.2A1,1,0,0,1,16.1,10.89Z"
                  />
                </g>
              </svg>
            </span>
          </div>
          <div
            class="flex flex-col justify-center ml-4"
          >
            <p
              class="text-md font-normal sr-only break-words"
            >
              Alert
            </p>
            <div
              class="text-info"
            >
              <p
                class="text-md font-normal break-words"
              >
                This charger is currently under delegated control. The below schedules are indicative only and subject to change.
              </p>
            </div>
          </div>
        </div>
      </div>
      <div
        class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-2 gap-2"
      >
        <div
          class="flex flex-col bg-white rounded-sm overflow-hidden overflow-x-auto"
        >
          <div
            class="inline-block min-w-full -my-2 py-2 align-middle"
          >
            <table
              class="min-w-full"
              tabindex="0"
            >
              <caption
                class="sr-only"
              >
                Table of charge schedules and modes
              </caption>
              <thead
                class="border-b border-b-neutral/20"
              >
                <tr
                  class="border-b-neutral/20"
                >
                  <th
                    class="text-left p-3"
                    scope="col"
                  >
                    Start
                  </th>
                  <th
                    class="text-left p-3"
                    scope="col"
                  >
                    Duration
                  </th>
                  <th
                    class="text-left text-center p-3"
                    scope="col"
                  >
                    Active
                  </th>
                </tr>
              </thead>
              <tbody
                class="divide-y border-b border-b-neutral/20"
              >
                <tr
                  class="border-b-neutral/20"
                >
                  <td
                    class="whitespace-normal px-3 py-4"
                  >
                    <p
                      class="text-md font-normal break-words"
                    >
                      Monday
                    </p>
                    <p
                      class="text-md font-normal break-words"
                    >
                      10:00:00
                    </p>
                  </td>
                  <td
                    class="whitespace-normal px-3 py-4"
                  >
                    <p
                      class="text-md font-normal break-words"
                    >
                      4 hours 
                    </p>
                  </td>
                  <td
                    class="whitespace-normal px-3 py-4"
                  >
                    <svg
                      class="fill-current h-6 w-6 mx-auto text-success"
                      viewBox="0 0 32 32"
                      xmlns="http://www.w3.org/2000/svg"
                    >
                      <title>
                        Yes
                      </title>
                      <g>
                        <path
                          d="M16,31.85C7.26,31.85,.15,24.74,.15,16S7.26,.15,16,.15s15.85,7.11,15.85,15.85-7.11,15.85-15.85,15.85Zm0-29.7c-7.64,0-13.85,6.21-13.85,13.85s6.21,13.85,13.85,13.85,13.85-6.21,13.85-13.85S23.64,2.15,16,2.15Z"
                        />
                        <path
                          d="M13.12,22.93c-.27,0-.52-.11-.71-.29l-5.16-5.16c-.39-.39-.39-1.02,0-1.41s1.02-.39,1.41,0l4.45,4.45,9.77-9.77c.39-.39,1.02-.39,1.41,0s.39,1.02,0,1.41l-10.47,10.47c-.19,.19-.44,.29-.71,.29Z"
                        />
                      </g>
                    </svg>
                  </td>
                </tr>
                <tr
                  class="border-b-neutral/20"
                >
                  <td
                    class="whitespace-normal px-3 py-4"
                  >
                    <p
                      class="text-md font-normal break-words"
                    >
                      Tuesday
                    </p>
                    <p
                      class="text-md font-normal break-words"
                    >
                      20:35:00
                    </p>
                  </td>
                  <td
                    class="whitespace-normal px-3 py-4"
                  >
                    <p
                      class="text-md font-normal break-words"
                    >
                      3 hours 15 minutes
                    </p>
                  </td>
                  <td
                    class="whitespace-normal px-3 py-4"
                  >
                    <svg
                      class="fill-current h-6 w-6 mx-auto text-success"
                      viewBox="0 0 32 32"
                      xmlns="http://www.w3.org/2000/svg"
                    >
                      <title>
                        Yes
                      </title>
                      <g>
                        <path
                          d="M16,31.85C7.26,31.85,.15,24.74,.15,16S7.26,.15,16,.15s15.85,7.11,15.85,15.85-7.11,15.85-15.85,15.85Zm0-29.7c-7.64,0-13.85,6.21-13.85,13.85s6.21,13.85,13.85,13.85,13.85-6.21,13.85-13.85S23.64,2.15,16,2.15Z"
                        />
                        <path
                          d="M13.12,22.93c-.27,0-.52-.11-.71-.29l-5.16-5.16c-.39-.39-.39-1.02,0-1.41s1.02-.39,1.41,0l4.45,4.45,9.77-9.77c.39-.39,1.02-.39,1.41,0s.39,1.02,0,1.41l-10.47,10.47c-.19,.19-.44,.29-.71,.29Z"
                        />
                      </g>
                    </svg>
                  </td>
                </tr>
                <tr
                  class="border-b-neutral/20"
                >
                  <td
                    class="whitespace-normal px-3 py-4"
                  >
                    <p
                      class="text-md font-normal break-words"
                    >
                      Wednesday
                    </p>
                    <p
                      class="text-md font-normal break-words"
                    >
                      00:00:00
                    </p>
                  </td>
                  <td
                    class="whitespace-normal px-3 py-4"
                  >
                    <p
                      class="text-md font-normal break-words"
                    >
                      5 hours 
                    </p>
                  </td>
                  <td
                    class="whitespace-normal px-3 py-4"
                  >
                    <svg
                      class="fill-current h-6 w-6 mx-auto text-success"
                      viewBox="0 0 32 32"
                      xmlns="http://www.w3.org/2000/svg"
                    >
                      <title>
                        Yes
                      </title>
                      <g>
                        <path
                          d="M16,31.85C7.26,31.85,.15,24.74,.15,16S7.26,.15,16,.15s15.85,7.11,15.85,15.85-7.11,15.85-15.85,15.85Zm0-29.7c-7.64,0-13.85,6.21-13.85,13.85s6.21,13.85,13.85,13.85,13.85-6.21,13.85-13.85S23.64,2.15,16,2.15Z"
                        />
                        <path
                          d="M13.12,22.93c-.27,0-.52-.11-.71-.29l-5.16-5.16c-.39-.39-.39-1.02,0-1.41s1.02-.39,1.41,0l4.45,4.45,9.77-9.77c.39-.39,1.02-.39,1.41,0s.39,1.02,0,1.41l-10.47,10.47c-.19,.19-.44,.29-.71,.29Z"
                        />
                      </g>
                    </svg>
                  </td>
                </tr>
                <tr
                  class="border-b-neutral/20"
                >
                  <td
                    class="whitespace-normal px-3 py-4"
                  >
                    <p
                      class="text-md font-normal break-words"
                    >
                      Thursday
                    </p>
                    <p
                      class="text-md font-normal break-words"
                    >
                      16:10:00
                    </p>
                  </td>
                  <td
                    class="whitespace-normal px-3 py-4"
                  >
                    <p
                      class="text-md font-normal break-words"
                    >
                      1 hour 
                    </p>
                  </td>
                  <td
                    class="whitespace-normal px-3 py-4"
                  >
                    <svg
                      class="fill-current h-6 w-6 mx-auto text-success"
                      viewBox="0 0 32 32"
                      xmlns="http://www.w3.org/2000/svg"
                    >
                      <title>
                        Yes
                      </title>
                      <g>
                        <path
                          d="M16,31.85C7.26,31.85,.15,24.74,.15,16S7.26,.15,16,.15s15.85,7.11,15.85,15.85-7.11,15.85-15.85,15.85Zm0-29.7c-7.64,0-13.85,6.21-13.85,13.85s6.21,13.85,13.85,13.85,13.85-6.21,13.85-13.85S23.64,2.15,16,2.15Z"
                        />
                        <path
                          d="M13.12,22.93c-.27,0-.52-.11-.71-.29l-5.16-5.16c-.39-.39-.39-1.02,0-1.41s1.02-.39,1.41,0l4.45,4.45,9.77-9.77c.39-.39,1.02-.39,1.41,0s.39,1.02,0,1.41l-10.47,10.47c-.19,.19-.44,.29-.71,.29Z"
                        />
                      </g>
                    </svg>
                  </td>
                </tr>
                <tr
                  class="border-b-neutral/20"
                >
                  <td
                    class="whitespace-normal px-3 py-4"
                  >
                    <p
                      class="text-md font-normal break-words"
                    >
                      Friday
                    </p>
                    <p
                      class="text-md font-normal break-words"
                    >
                      00:00:00
                    </p>
                  </td>
                  <td
                    class="whitespace-normal px-3 py-4"
                  >
                    <p
                      class="text-md font-normal break-words"
                    >
                      5 hours 
                    </p>
                  </td>
                  <td
                    class="whitespace-normal px-3 py-4"
                  >
                    <svg
                      class="fill-current h-6 w-6 mx-auto text-success"
                      viewBox="0 0 32 32"
                      xmlns="http://www.w3.org/2000/svg"
                    >
                      <title>
                        Yes
                      </title>
                      <g>
                        <path
                          d="M16,31.85C7.26,31.85,.15,24.74,.15,16S7.26,.15,16,.15s15.85,7.11,15.85,15.85-7.11,15.85-15.85,15.85Zm0-29.7c-7.64,0-13.85,6.21-13.85,13.85s6.21,13.85,13.85,13.85,13.85-6.21,13.85-13.85S23.64,2.15,16,2.15Z"
                        />
                        <path
                          d="M13.12,22.93c-.27,0-.52-.11-.71-.29l-5.16-5.16c-.39-.39-.39-1.02,0-1.41s1.02-.39,1.41,0l4.45,4.45,9.77-9.77c.39-.39,1.02-.39,1.41,0s.39,1.02,0,1.41l-10.47,10.47c-.19,.19-.44,.29-.71,.29Z"
                        />
                      </g>
                    </svg>
                  </td>
                </tr>
                <tr
                  class="border-b-neutral/20"
                >
                  <td
                    class="whitespace-normal px-3 py-4"
                  >
                    <p
                      class="text-md font-normal break-words"
                    >
                      Saturday
                    </p>
                    <p
                      class="text-md font-normal break-words"
                    >
                      00:00:00
                    </p>
                  </td>
                  <td
                    class="whitespace-normal px-3 py-4"
                  >
                    <p
                      class="text-md font-normal break-words"
                    >
                      5 hours 
                    </p>
                  </td>
                  <td
                    class="whitespace-normal px-3 py-4"
                  >
                    <svg
                      class="fill-current h-6 w-6 mx-auto text-error"
                      viewBox="0 0 32 32"
                      xmlns="http://www.w3.org/2000/svg"
                    >
                      <title>
                        No
                      </title>
                      <g>
                        <path
                          d="M16,.07C7.22,.07,.07,7.22,.07,16s7.15,15.93,15.93,15.93,15.93-7.15,15.93-15.93S24.78,.07,16,.07Zm0,29.86c-7.68,0-13.93-6.25-13.93-13.93S8.32,2.07,16,2.07s13.93,6.25,13.93,13.93-6.25,13.93-13.93,13.93Z"
                        />
                        <path
                          d="M23.38,8.62c-.39-.39-1.02-.39-1.41,0l-5.96,5.96-5.96-5.96c-.39-.39-1.02-.39-1.41,0s-.39,1.02,0,1.41l5.96,5.96-5.96,5.96c-.39,.39-.39,1.02,0,1.41,.2,.2,.45,.29,.71,.29s.51-.1,.71-.29l5.96-5.96,5.96,5.96c.2,.2,.45,.29,.71,.29s.51-.1,.71-.29c.39-.39,.39-1.02,0-1.41l-5.96-5.96,5.96-5.96c.39-.39,.39-1.02,0-1.41Z"
                        />
                      </g>
                    </svg>
                  </td>
                </tr>
                <tr
                  class="border-b-neutral/20"
                >
                  <td
                    class="whitespace-normal px-3 py-4"
                  >
                    <p
                      class="text-md font-normal break-words"
                    >
                      Sunday
                    </p>
                    <p
                      class="text-md font-normal break-words"
                    >
                      00:00:00
                    </p>
                  </td>
                  <td
                    class="whitespace-normal px-3 py-4"
                  >
                    <p
                      class="text-md font-normal break-words"
                    >
                      5 hours 
                    </p>
                  </td>
                  <td
                    class="whitespace-normal px-3 py-4"
                  >
                    <svg
                      class="fill-current h-6 w-6 mx-auto text-error"
                      viewBox="0 0 32 32"
                      xmlns="http://www.w3.org/2000/svg"
                    >
                      <title>
                        No
                      </title>
                      <g>
                        <path
                          d="M16,.07C7.22,.07,.07,7.22,.07,16s7.15,15.93,15.93,15.93,15.93-7.15,15.93-15.93S24.78,.07,16,.07Zm0,29.86c-7.68,0-13.93-6.25-13.93-13.93S8.32,2.07,16,2.07s13.93,6.25,13.93,13.93-6.25,13.93-13.93,13.93Z"
                        />
                        <path
                          d="M23.38,8.62c-.39-.39-1.02-.39-1.41,0l-5.96,5.96-5.96-5.96c-.39-.39-1.02-.39-1.41,0s-.39,1.02,0,1.41l5.96,5.96-5.96,5.96c-.39,.39-.39,1.02,0,1.41,.2,.2,.45,.29,.71,.29s.51-.1,.71-.29l5.96-5.96,5.96,5.96c.2,.2,.45,.29,.71,.29s.51-.1,.71-.29c.39-.39,.39-1.02,0-1.41l-5.96-5.96,5.96-5.96c.39-.39,.39-1.02,0-1.41Z"
                        />
                      </g>
                    </svg>
                  </td>
                </tr>
              </tbody>
            </table>
          </div>
        </div>
        <div
          class="recharts-responsive-container"
          style="width: 100%; height: 320px; min-width: 0;"
        >
          <div
            style="width: 0px; height: 0px; overflow: visible;"
          />
        </div>
      </div>
    </section>
    <div
      class="pb-2"
    />
    <section
      class="p-3 rounded-sm bg-white"
    >
      <h3
        class="text-lg font-bold pb-4"
      >
        Other configuration values
      </h3>
      <div
        class="flex flex-col bg-white rounded-sm overflow-hidden overflow-x-auto"
      >
        <div
          class="inline-block min-w-full -my-2 py-2 align-middle"
        >
          <table
            class="min-w-full"
            tabindex="0"
          >
            <caption
              class="sr-only"
            >
              Table of other configuration values
            </caption>
            <tbody
              class="divide-y"
            >
              <tr
                class="border-b-neutral/20"
              >
                <td
                  class="whitespace-normal px-3 py-4 pl-0 pr-2 pt-2 pb-2 whitespace-pre"
                >
                  ChargeCurrentLimitA
                </td>
                <td
                  class="whitespace-normal px-3 py-4 pr-0 pl-2 pt-2 pb-2 text-neutral text-left"
                >
                  32
                </td>
              </tr>
              <tr
                class="border-b-neutral/20"
              >
                <td
                  class="whitespace-normal px-3 py-4 pl-0 pr-2 pt-2 pb-2 whitespace-pre"
                >
                  OffMode
                </td>
                <td
                  class="whitespace-normal px-3 py-4 pr-0 pl-2 pt-2 pb-2 text-neutral text-left"
                >
                  false
                </td>
              </tr>
              <tr
                class="border-b-neutral/20"
              >
                <td
                  class="whitespace-normal px-3 py-4 pl-0 pr-2 pt-2 pb-2 whitespace-pre"
                >
                  OfflineSchedulingEnabled
                </td>
                <td
                  class="whitespace-normal px-3 py-4 pr-0 pl-2 pt-2 pb-2 text-neutral text-left"
                >
                  true
                </td>
              </tr>
              <tr
                class="border-b-neutral/20"
              >
                <td
                  class="whitespace-normal px-3 py-4 pl-0 pr-2 pt-2 pb-2 whitespace-pre"
                >
                  PPDevClampFaultThreshold
                </td>
                <td
                  class="whitespace-normal px-3 py-4 pr-0 pl-2 pt-2 pb-2 text-neutral text-left"
                >
                  2
                </td>
              </tr>
              <tr
                class="border-b-neutral/20"
              >
                <td
                  class="whitespace-normal px-3 py-4 pl-0 pr-2 pt-2 pb-2 whitespace-pre"
                >
                  PowerBalancingCurrentLimitImportA
                </td>
                <td
                  class="whitespace-normal px-3 py-4 pr-0 pl-2 pt-2 pb-2 text-neutral text-left"
                >
                  50
                </td>
              </tr>
              <tr
                class="border-b-neutral/20"
              >
                <td
                  class="whitespace-normal px-3 py-4 pl-0 pr-2 pt-2 pb-2 whitespace-pre"
                >
                  PowerBalancingEnabled
                </td>
                <td
                  class="whitespace-normal px-3 py-4 pr-0 pl-2 pt-2 pb-2 text-neutral text-left"
                >
                  true
                </td>
              </tr>
              <tr
                class="border-b-neutral/20"
              >
                <td
                  class="whitespace-normal px-3 py-4 pl-0 pr-2 pt-2 pb-2 whitespace-pre"
                >
                  PowerBalancingSensor
                </td>
                <td
                  class="whitespace-normal px-3 py-4 pr-0 pl-2 pt-2 pb-2 text-neutral text-left"
                >
                  NONE
                </td>
              </tr>
              <tr
                class="border-b-neutral/20"
              >
                <td
                  class="whitespace-normal px-3 py-4 pl-0 pr-2 pt-2 pb-2 whitespace-pre"
                >
                  PowerBalancingSensorInstalled
                </td>
                <td
                  class="whitespace-normal px-3 py-4 pr-0 pl-2 pt-2 pb-2 text-neutral text-left"
                >
                  true
                </td>
              </tr>
              <tr
                class="border-b-neutral/20"
              >
                <td
                  class="whitespace-normal px-3 py-4 pl-0 pr-2 pt-2 pb-2 whitespace-pre"
                >
                  PowerBalancingSensorPolarityInverted
                </td>
                <td
                  class="whitespace-normal px-3 py-4 pr-0 pl-2 pt-2 pb-2 text-neutral text-left"
                >
                  true
                </td>
              </tr>
              <tr
                class="border-b-neutral/20"
              >
                <td
                  class="whitespace-normal px-3 py-4 pl-0 pr-2 pt-2 pb-2 whitespace-pre"
                >
                  SolarExportMargin
                </td>
                <td
                  class="whitespace-normal px-3 py-4 pr-0 pl-2 pt-2 pb-2 text-neutral text-left"
                >
                  5
                </td>
              </tr>
              <tr
                class="border-b-neutral/20"
              >
                <td
                  class="whitespace-normal px-3 py-4 pl-0 pr-2 pt-2 pb-2 whitespace-pre"
                >
                  SolarMatchingEnabled
                </td>
                <td
                  class="whitespace-normal px-3 py-4 pr-0 pl-2 pt-2 pb-2 text-neutral text-left"
                >
                  true
                </td>
              </tr>
              <tr
                class="border-b-neutral/20"
              >
                <td
                  class="whitespace-normal px-3 py-4 pl-0 pr-2 pt-2 pb-2 whitespace-pre"
                >
                  SolarMaxGridImport
                </td>
                <td
                  class="whitespace-normal px-3 py-4 pr-0 pl-2 pt-2 pb-2 text-neutral text-left"
                >
                  4.1
                </td>
              </tr>
              <tr
                class="border-b-neutral/20"
              >
                <td
                  class="whitespace-normal px-3 py-4 pl-0 pr-2 pt-2 pb-2 whitespace-pre"
                >
                  SolarStartHysteresis
                </td>
                <td
                  class="whitespace-normal px-3 py-4 pr-0 pl-2 pt-2 pb-2 text-neutral text-left"
                >
                  10
                </td>
              </tr>
              <tr
                class="border-b-neutral/20"
              >
                <td
                  class="whitespace-normal px-3 py-4 pl-0 pr-2 pt-2 pb-2 whitespace-pre"
                >
                  SolarStopHysteresis
                </td>
                <td
                  class="whitespace-normal px-3 py-4 pr-0 pl-2 pt-2 pb-2 text-neutral text-left"
                >
                  64
                </td>
              </tr>
              <tr
                class="border-b-neutral/20"
              >
                <td
                  class="whitespace-normal px-3 py-4 pl-0 pr-2 pt-2 pb-2 whitespace-pre"
                >
                  SolarSystemInstalled
                </td>
                <td
                  class="whitespace-normal px-3 py-4 pr-0 pl-2 pt-2 pb-2 text-neutral text-left"
                >
                  true
                </td>
              </tr>
              <tr
                class="border-b-neutral/20"
              >
                <td
                  class="whitespace-normal px-3 py-4 pl-0 pr-2 pt-2 pb-2 whitespace-pre"
                >
                  FirmwareVersion
                </td>
                <td
                  class="whitespace-normal px-3 py-4 pr-0 pl-2 pt-2 pb-2 text-neutral text-left"
                >
                  1.0.0
                </td>
              </tr>
              <tr
                class="border-b-neutral/20"
              >
                <td
                  class="whitespace-normal px-3 py-4 pl-0 pr-2 pt-2 pb-2 whitespace-pre"
                >
                  LinkyScheduleEnabled
                </td>
                <td
                  class="whitespace-normal px-3 py-4 pr-0 pl-2 pt-2 pb-2 text-neutral text-left"
                >
                  false
                </td>
              </tr>
              <tr
                class="border-b-neutral/20"
              >
                <td
                  class="whitespace-normal px-3 py-4 pl-0 pr-2 pt-2 pb-2 whitespace-pre"
                >
                  RcdBreakerSize
                </td>
                <td
                  class="whitespace-normal px-3 py-4 pr-0 pl-2 pt-2 pb-2 text-neutral text-left"
                >
                  10
                </td>
              </tr>
              <tr
                class="border-b-neutral/20"
              >
                <td
                  class="whitespace-normal px-3 py-4 pl-0 pr-2 pt-2 pb-2 whitespace-pre"
                >
                  UnlockConnectorOnEVSideDisconnect
                </td>
                <td
                  class="whitespace-normal px-3 py-4 pr-0 pl-2 pt-2 pb-2 text-neutral text-left"
                >
                  true
                </td>
              </tr>
            </tbody>
          </table>
        </div>
      </div>
    </section>
    <div
      class="pb-2"
    />
    <section
      class="p-3 rounded-sm bg-white"
    >
      <h3
        class="text-lg font-bold pb-4"
      >
        Tags
      </h3>
      <div
        class="flex flex-col bg-white rounded-sm overflow-hidden overflow-x-auto"
      >
        <div
          class="inline-block min-w-full -my-2 py-2 align-middle"
        >
          <table
            class="min-w-full"
            tabindex="0"
          >
            <caption
              class="sr-only"
            >
              Table of tags
            </caption>
            <tbody
              class="divide-y"
            >
              <tr
                class="border-b-neutral/20"
              >
                <td
                  class="whitespace-normal px-3 py-4 pl-0 pr-2 pt-2 pb-2 whitespace-pre"
                >
                  cur tollo claudeo
                </td>
                <td
                  class="whitespace-normal px-3 py-4 pr-0 pl-2 pt-2 pb-2 text-neutral text-left"
                >
                  sponte totus dedico
                </td>
              </tr>
              <tr
                class="border-b-neutral/20"
              >
                <td
                  class="whitespace-normal px-3 py-4 pl-0 pr-2 pt-2 pb-2 whitespace-pre"
                >
                  terreo cinis utilis
                </td>
                <td
                  class="whitespace-normal px-3 py-4 pr-0 pl-2 pt-2 pb-2 text-neutral text-left"
                >
                  tametsi vilitas adduco
                </td>
              </tr>
              <tr
                class="border-b-neutral/20"
              >
                <td
                  class="whitespace-normal px-3 py-4 pl-0 pr-2 pt-2 pb-2 whitespace-pre"
                >
                  sollers depromo vesica
                </td>
                <td
                  class="whitespace-normal px-3 py-4 pr-0 pl-2 pt-2 pb-2 text-neutral text-left"
                >
                  similique candidus ulterius
                </td>
              </tr>
              <tr
                class="border-b-neutral/20"
              >
                <td
                  class="whitespace-normal px-3 py-4 pl-0 pr-2 pt-2 pb-2 whitespace-pre"
                >
                  quod texo dapifer
                </td>
                <td
                  class="whitespace-normal px-3 py-4 pr-0 pl-2 pt-2 pb-2 text-neutral text-left"
                >
                  termes catena hic
                </td>
              </tr>
              <tr
                class="border-b-neutral/20"
              >
                <td
                  class="whitespace-normal px-3 py-4 pl-0 pr-2 pt-2 pb-2 whitespace-pre"
                >
                  tabgo adaugeo thesis
                </td>
                <td
                  class="whitespace-normal px-3 py-4 pr-0 pl-2 pt-2 pb-2 text-neutral text-left"
                >
                  celo excepturi curvo
                </td>
              </tr>
              <tr
                class="border-b-neutral/20"
              >
                <td
                  class="whitespace-normal px-3 py-4 pl-0 pr-2 pt-2 pb-2 whitespace-pre"
                >
                  asperiores currus theatrum
                </td>
                <td
                  class="whitespace-normal px-3 py-4 pr-0 pl-2 pt-2 pb-2 text-neutral text-left"
                >
                  thorax vinum deporto
                </td>
              </tr>
            </tbody>
          </table>
        </div>
      </div>
    </section>
    <div
      class="pb-2"
    />
  </div>
</body>
`;
