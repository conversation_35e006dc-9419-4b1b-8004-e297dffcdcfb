import { PageContent as AccountsPageContent } from './page-content';
import { renderWithProviders } from '../test-utils';

const mockRouterPush = jest.fn();
jest.mock('next/navigation', () => ({
  useRouter: () => ({
    push: (url: string) => mockRouterPush(url),
  }),
}));

describe('Accounts page content', () => {
  it('should render correctly', async () => {
    const { baseElement } = renderWithProviders(<AccountsPageContent />);
    expect(baseElement).toBeInTheDocument();
  });

  it('should match snapshot', async () => {
    const { baseElement } = renderWithProviders(<AccountsPageContent />);
    expect(baseElement).toMatchSnapshot();
  });

  // NOT IMPLEMENTED YET
  it.todo(
    'should find by account email and return the corresponding accounts page'
  );
});
