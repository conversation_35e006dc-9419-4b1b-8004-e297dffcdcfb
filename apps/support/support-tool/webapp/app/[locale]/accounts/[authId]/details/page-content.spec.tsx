import { AccountDetails } from '../../actions/get-details';
import { PageContent as AccountsDetailsPageContent } from './page-content';
import { appRequestHandler } from '@experience/commercial/next/app-request-utils';
import { renderWithProviders } from '../../../test-utils';
import { screen, waitFor } from '@testing-library/react';

jest.mock('@experience/commercial/next/app-request-utils');

const mockHandler = jest.mocked(appRequestHandler);

const mockDetails: AccountDetails = {
  authId: 'abc123',
  balance: { amount: 1099, currency: 'GBP' },
  email: '',
  isEmailVerified: false,
  accountCreationTimestamp: *************,
  lastSignInTimestamp: *************,
  firstName: 'Jane',
  lastName: 'Doe',
  organisations: [],
  status: 'active',
  locale: 'en',
};

describe('Accounts page content', () => {
  beforeEach(() => {
    mockHandler.mockResolvedValueOnce([
      {
        id: '919276fd-bbad-438f-ad48-a2d2c22d0812',
        phoneNumber: '+*************',
        enrollmentTime: 'Fri, 22 Sep 2017 01:49:58 GMT',
      },
    ]);

    mockHandler.mockResolvedValueOnce([
      {
        ppid: 'PSL-12345',
        model: 'Solo 3S',
      },
    ]);
  });

  it('should render correctly', async () => {
    const { baseElement } = renderWithProviders(
      <AccountsDetailsPageContent details={mockDetails} />
    );
    await waitFor(() => {
      expect(baseElement).toBeInTheDocument();
    });
  });

  it('should match snapshot', async () => {
    const { baseElement } = renderWithProviders(
      <AccountsDetailsPageContent details={mockDetails} />
    );
    await waitFor(() => {
      expect(baseElement).toMatchSnapshot();
    });
  });

  it('should render back link that takes user back to accounts page', () => {
    renderWithProviders(<AccountsDetailsPageContent details={mockDetails} />);

    expect(screen.getByRole('link', { name: 'Back' })).toHaveAttribute(
      'href',
      '/accounts'
    );
  });

  describe('account locale', () => {
    it.each([
      ['en', '🇬🇧 United Kingdom'],
      ['es', '🇪🇸 Spain'],
      ['fr', '🇫🇷 France'],
      ['ie', '🇮🇪 Republic of Ireland'],
      ['no', '🇳🇴 Norway'],
    ])(
      'should be rendered with correct display name for region %s',
      async (locale, expected) => {
        const props = { details: { ...mockDetails, locale } };

        renderWithProviders(<AccountsDetailsPageContent {...props} />);

        expect(await screen.findByText(expected)).toBeInTheDocument();
      }
    );
  });

  describe('account balance', () => {
    it.each([
      ['GBP', 1099, '£10.99'],
      ['EUR', 51_232, '€512.32'],
      ['NOK', 4000, 'NOK 40.00'],
    ])(
      'should be rendered with the correct currency for region %s',
      async (currency, amount, expected) => {
        const props = {
          details: { ...mockDetails, balance: { currency, amount } },
        };

        renderWithProviders(<AccountsDetailsPageContent {...props} />);

        expect(await screen.findByText(expected)).toBeInTheDocument();
      }
    );
  });
});
