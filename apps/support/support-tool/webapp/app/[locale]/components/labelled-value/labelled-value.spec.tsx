import { LabelledValue } from './labelled-value';
import { render, screen } from '@testing-library/react';

describe('LabelledValue', () => {
  it('should render correctly', () => {
    const { baseElement } = render(
      <LabelledValue label="Favourite colour" value="Orange" />
    );
    expect(baseElement).toBeInTheDocument();
    expect(screen.getByText('Favourite colour')).toBeInTheDocument();
    expect(screen.getByText('Orange')).toBeInTheDocument();
  });

  it('should match snapshot with string value', () => {
    const { baseElement } = render(
      <LabelledValue label="Favourite colour" value="Orange" />
    );
    expect(baseElement).toMatchSnapshot();
  });

  it('should match snapshot with component value', () => {
    const { baseElement } = render(
      <LabelledValue
        label="Favourite colour"
        value={<span className="bg-warning">uh oh</span>}
      />
    );
    expect(baseElement).toMatchSnapshot();
  });
});
