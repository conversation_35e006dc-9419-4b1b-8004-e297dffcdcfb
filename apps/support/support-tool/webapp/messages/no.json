{"Layout": {"navigationHomeLink": "<PERSON><PERSON><PERSON>", "navigationAccountsLink": "<PERSON><PERSON><PERSON>", "navigationChargersLink": "<PERSON>der", "navigationSubscriptionsLink": "Abonnementer"}, "Home": {"heading": "<PERSON><PERSON><PERSON>ø<PERSON>", "accountsCardHeading": "<PERSON><PERSON><PERSON>", "accountsCardDescription": "Vis og administrer kontoinnstillinger", "accountsCardLink": "<PERSON><PERSON><PERSON> etter konto", "chargersCardHeading": "<PERSON>der", "chargersCardDescription": "Se aktiviteten, og administrer laderinnstillinger", "chargersCardLink": "<PERSON><PERSON><PERSON> etter ladere", "subscriptionsCardHeading": "Abonnementer", "subscriptionsCardDescription": "Vis og administrere Pod Drive abonnementer", "subscriptionsCardLink": "<PERSON><PERSON><PERSON> etter abonnementer", "title": "<PERSON><PERSON><PERSON>ø<PERSON>"}, "Accounts": {"AccountStatusBadgeComponent": {"activeLabel": "Aktiv", "disabledLabel": "Deaktivert", "defaultLabel": "<PERSON><PERSON><PERSON><PERSON>"}, "EmailVerificationBadgeComponent": {"verifiedLabel": "Bekreftet", "unverifiedLabel": "<PERSON>kke verifi<PERSON>t"}, "SearchPage": {"heading": "<PERSON><PERSON><PERSON>", "searchCardHeading": "<PERSON><PERSON><PERSON> etter en konto:", "searchCardEmailLabel": "E-post", "searchCardSubmitButton": "Gå", "noSearchResults": "Ingen kontoer samsvarer med ditt gjeldende søk.", "searchResultsLink": "<PERSON><PERSON><PERSON>", "title": "<PERSON><PERSON><PERSON> verktøy - kontoer", "emailPlaceholder": "<EMAIL>"}, "DetailsPage": {"accountDetailsPageHeading": "Kontoinformasjon", "actionMenuEditName": "<PERSON><PERSON> navn", "actionMenuEditEmailAddress": "Rediger e-postadresse", "actionMenuEditRegion": "Rediger region", "actionMenuSendPasswordRecovery": "Send gjenoppretting av passord", "actionMenuDeactivateAccount": "<PERSON><PERSON><PERSON><PERSON> konto", "actionMenuReactivateAccount": "<PERSON><PERSON><PERSON><PERSON> konto", "actionMenuResendEmailVerification": "Send e-postbekreftelse på nytt", "cancelButton": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "saveButton": "Lagre", "saveChangesButton": "<PERSON><PERSON><PERSON> endringer", "noDisplayName": "<PERSON><PERSON><PERSON> an<PERSON>t", "backLinkLabel": "Tilbake", "accountCreated": "Opprettet: {createdAt}", "accountLastSignIn": "Sist innlogget: {signedIn}", "accountStatusLabel": "Konto status", "emailVerificationLabel": "Bekreft e-post adresse", "regionLabel": "<PERSON><PERSON><PERSON>", "accountBalanceLabel": "<PERSON><PERSON> saldo", "chargersTabTitle": "<PERSON>der", "factorsTabTitle": "Autentisering (2FA)", "activityTableTitle": "Aktivitet", "activityTableDateHeading": "Da<PERSON>", "activityTableTimeHeading": "Tid", "activityTableEventHeading": "<PERSON><PERSON><PERSON><PERSON>", "activityTableDetailsHeading": "<PERSON><PERSON><PERSON>", "activityTableStatusHeading": "Status:", "success": "Vellykket", "failed": "Mislyktes", "factorsTableTitle": "<PERSON>ak<PERSON><PERSON>", "factorsTableCountryCode": "Landets kode", "factorsTableLastThreeDigits": "Telefonnummer siste tre tall", "factorsTableEnrollmentTime": "Registrert", "chargersTableTitle": "<PERSON>der", "chargersTablePpidHeading": "PPID", "chargersTableActionsHeading": "<PERSON><PERSON>", "unlinkChargerButton": "Frakoblet lader", "accountPaymentsTableTitle": "<PERSON><PERSON> for {authId}", "deactivatedAccountModalTitle": "<PERSON><PERSON><PERSON><PERSON> bruker", "deactivatedAccountModalParagraph": "Kunden vil bli logget ut av alle tjenestene og vil ikke kunne logge på igjen.", "deactivatedAccountModalConfirmButton": "<PERSON><PERSON><PERSON><PERSON> konto", "deactivatedAccountSuccessMessage": "Kundekonto deaktivert – det kan ta noen sekunder før denne endringen er brukt", "deactivatedAccountFailedMessage": "<PERSON><PERSON> ikke deaktivere konto", "deactivatedAccountRefundAlertTitle": "Tilbakebetaling kreves", "deactivatedAccountRefundAlertParagraph": "Pass på å refundere kunden som deres konto er for øyeblikket {balanceString} i kreditt.", "deactivatedAccountPaymentAlertTitle": "<PERSON><PERSON> kreves", "deactivatedAccountPaymentAlertParagraph": "<PERSON>nden må fylle opp og nullstille sin saldo på {balanceString} før du avslutter kontoen.", "editNameSuccessMessage": "Brukeropplysningene har oppdatert dette kan ta noen sekunder å oppdatere", "editNameFailedMessage": "<PERSON>nne ikke oppdatere brukerde<PERSON>jer", "editNameModalTitle": "<PERSON><PERSON> navn", "editNameModalFirstNameLabel": "Fornavn", "editNameModalLastNameLabel": "Etternavn", "editRegionSuccessMessage": "Brukerdetaljer ble oppdatert", "editRegionFailedMessage": "<PERSON>nne ikke oppdatere brukerde<PERSON>jer", "editRegionFormRegionLabel": "<PERSON><PERSON><PERSON>", "editRegionFormRegionSelectPlaceholder": "Velg en region", "editRegionModalTitle": "Rediger region", "reactivateAccountSuccessMessage": "Kundekonto er reaktivert – det kan ta noen sekunder før denne endringen blir brukt", "reactivateAccountFailedMessage": "<PERSON><PERSON><PERSON> ble ikke reaktivere", "reactivateAccountModalTitle": "Aktivere bruker", "reactivateAccountModalParagraph": "Kundekontoen vil bli aktivert på nytt, slik at de kan logge inn.", "reactivateAccountModalConfirmButton": "<PERSON><PERSON><PERSON><PERSON> konto", "resendVerificationEmailSuccessMessage": "E-post bekreftelse sendt på nytt", "resendVerificationEmailErrorMessage": "Kunne ikke sende e-postbekreftelse på nytt", "resendVerificationEmailModalTitle": "Send e-postbekreftelse", "resendVerificationEmailModalConfirmButton": "Send e-postbekreftelse", "sendPasswordResetEmailSuccessMessage": "E-post for å tilbakestille passord er sendt", "sendPasswordResetEmailErrorMessage": "Kunne ikke sende epost for å tilbakestille passord", "sendPasswordResetEmailModalTitle": "Send tilbakestilling av passord", "sendPasswordResetEmailModalParagraph": "En tilbakestilling av passord epost sendes til {email}", "sendPasswordResetEmailModalConfirmButton": "Send tilbakestilling av passord", "sendUpdateEmailSuccessMessage": "Opp<PERSON>r e-postadresse fore<PERSON><PERSON><PERSON><PERSON> sendt", "sendUpdateEmailDuplicateErrorMessage": "Det finnes allerede en konto med denne e-postadressen", "sendUpdateEmailErrorMessage": "<PERSON><PERSON> ikke sende oppdateringsforespørselen for e-post", "sendUpdateEmailErrorModalTitle": "Rediger e-postadresse", "sendUpdateEmailErrorModalParagraph": "En e-post vil bli sendt til den nye e-postadressen til kunden for å bekrefte endringen før kontoen er oppdatert.", "sendUpdateEmailErrorFormEmailLabel": "Epost adresse", "title": "St<PERSON>tte verktøy - Kontodetaljer", "accountActionTitle": "<PERSON><PERSON>"}}, "Chargers": {"ChargeModeHistoryPage": {"chargeNow": "Lade nÃ¥", "expired": "<PERSON><PERSON><PERSON><PERSON>", "heading": "Ladningsmodus historikk - {ppid}", "manualMode": "<PERSON><PERSON> modus", "manualStop": "Kansellert av bruker eller erstattet", "overrideType": "Overstyr type", "requested": "Forespurt", "start": "Begynn", "stop": "Stopp", "stopReason": "Stopp begrunnelse", "tableCaption": "<PERSON>bell med historikk for ladningsmodus", "title": "<PERSON><PERSON><PERSON> verktøy - Faste modus historikk"}, "Components": {"AdminsTable": {"caption": "<PERSON><PERSON> over administratorer", "emailHeader": "E-post", "expandLabel": "<PERSON><PERSON><PERSON>", "header": "Administratorer", "noAdmins": "Denne gruppen laderen tilhører har ingen administratorer konfigurert.", "lastLoginHeader": "Siste pålogging"}, "Alerts": {"chargeScheduleBannerMessage": "Denne laderen er for øyeblikket i manuell modus. Planene nedenfor trer ikke i kraft.", "delegatedControlBannerMessage": "Denne avgiften er under delegert kontroll. Disse tidsplanene nedenfor indikerer bare dette og er gjenstand for endring.", "grafanaLinkMessage": " eller se denne laderen i <PERSON>ana ", "externalOperatorBannerMessage": "<PERSON><PERSON> laderen drives ikke av Pod Point. Kunder må kontakte operatør for støtte. Du kan søke etter denne laderen i MIS ", "here": "her", "supportedArchitectureBannerMessage": "Du kan søke etter denne laderen i MIS ", "unSupportedArchitectureBannerMessage": "Det er for øyeblikket ikke mulig å gjøre endringer eller sende kommandoer til denne laderen. Du kan søke etter denne laderen i MIS "}, "Badges": {"defaultFault": "Feil - {firstOccurrence}", "faultVendorCode": "Feil {vendorErrorCode} - {firstOccurrence}", "faultDescription": "Feil {podPointFault}: {description} - {firstOccurrence}", "faultErrorCode": "Feil {errorCode} - {firstOccurrence}", "outOfRangeFault": "<PERSON><PERSON>r<PERSON><PERSON> utenfor rekkevidde", "reversePolarityFault": "<PERSON><PERSON><PERSON>dt <PERSON>itet, hø<PERSON> nø<PERSON>, l<PERSON><PERSON> jord", "fuseSaverFault": "Angripende feil / CLS Signifikant differanse mellom bil- og clampstrøm (ingen solar)", "renaultSettingFault": "Feil ved endring av feilinnstilling (Norge)", "arrayFault": "<PERSON><PERSON>", "highVoltageFault": "<PERSON><PERSON><PERSON> spenning", "lowVoltageFault": "Lav spenning", "pilotLowFault": "Lav Pilot", "a12VFault": "A-12V feil", "cableOverTempStopFault": "CableOverTempStop", "overtemperatureFault": "Overtemperatur", "overcurrentFault": "Overstrøm", "hardwareDamageFault": "Maskinvare skadeforbindelse Relésveis", "mennekesFault": "<PERSON><PERSON><PERSON>", "rCDACFault": "RCD AC", "groundFailureFault": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "6mAFault": "6mA deteksjonsbenker (RCD DC)", "unlockSocketFault": "<PERSON><PERSON><PERSON> opp Socket Feil", "upstreamPowerFault": "Oppstrøms strøm / brownout faktet", "linuxUnavailableFault": "Intern feil mellom STM og Linux (linux utilgjengelig)", "queueOverflowFault": "Intern feil mellom STM og Linux (kø overflyt)", "invalidModeFault": "Kjøretøyet er i en ugyldig modus for lading (rapportert av IEC-stakken)", "exceededImportLimitUnder125Fault": "Overskred importgrensen (mellom 100-125 %) i 60 sekunder", "exceededImportLimitOver125Fault": "Overskredet importgrensen med 125%", "threePhaseEarthPENFault": "3-f<PERSON><PERSON>EN-feil (jord)", "threePhaseNeutralPENFault": "3-fase PEN-feil (nøytral)", "threePhaseMissingPhaseFault": "Fase manglende 3 faseenhet", "linkyConnectionFault": "<PERSON><PERSON> tapt forbin<PERSON><PERSON>", "linkyDaughterBoardFault": " <PERSON><PERSON> datteren mangler eller er feil", "voltagePolarityFault": "Levende og nøytralt bytte", "6mASelfTestFault": "<PERSON><PERSON> i egenprøving", "lockNotDetectedFault": "<PERSON><PERSON><PERSON> i<PERSON>", "midFault": "Manglende eller skadet MID-måler", "rfidNotDetectedFault": "RFID ikke oppdaget", "rfidErrorFault": "RFID tilstede men feil", "veryHighVoltageFault": "<PERSON><PERSON><PERSON><PERSON> høy spenning, >270V, sannsynlig feil i PEN", "firmwareAvailable": "Firmware oppdatering tilgjengelig", "firmwareUpdateNotRequested": "Firmwareoppdatering er ikke forespurt", "firmwareUpdateNotAccepted": "Firmwareoppdatering er ikke godkjent", "firmwareUpdateInProgress": "<PERSON><PERSON><PERSON> op<PERSON> pågår", "firmwareUpdateFailed": "Firmware oppdatering mislyktes"}, "Cards": {"warrantyDetailsTitle": "<PERSON><PERSON><PERSON>", "warrantyDetailsTableTitle": "<PERSON><PERSON><PERSON>", "warrantyDetailsTableStartDateKey": "<PERSON><PERSON><PERSON>", "warrantyDetailsTableEndDateKey": "Sluttdato for garanti", "warrantyDetailsTableWarrantyStatusKey": "Garanti status", "warrantyDetailsTableWarrantyStatusActive": "Aktiv", "warrantyDetailsTableWarrantyStatusInactive": "Inaktiv", "chargerAccessPointTitle": "Charger access point (AP)", "chargerAccessPointHidePassword": "Sk<PERSON><PERSON> passord", "chargerAccessPointShowPassword": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> passord", "chargerAccessPointTableTitle": "Laderen tilgangspunkt", "chargerAccessPointTableSSIDKey": "SSID", "chargerAccessPointTablePasswordKey": "Passord", "chargerConnectivityQuality0": "0", "chargerConnectivityQuality1": "1 (Poor, RSSI {signalStrength})", "chargerConnectivityQuality2": "2 (<PERSON>, RSSI {signalStrength})", "chargerConnectivityQuality3": "3 (<PERSON><PERSON>, <PERSON><PERSON> {signalStrength})", "chargerConnectivityQuality4": "4 (veld<PERSON> bra, RSSI {signalStrength})", "chargerConnectivityQuality5": "5 (<PERSON><PERSON><PERSON><PERSON>, RSSI {signalStrength})", "chargerConnectivityTableChargingStatusKey": "Lader status", "chargerConnectivityTableConnectionQuality": "Koblingens kvalitet", "chargerConnectivityTableConnectionQualityScale": "(skala 1-5)", "chargerConnectivityTableLastMessageKey": "Siste melding", "chargerConnectivityTableLastSeenKey": "<PERSON>ste tilkobling startet", "chargerConnectivityTableMacAddress": "Router MAC adresse", "chargerConnectivityTableModel": "Router modell", "chargerConnectivityTableSerialNumber": "<PERSON><PERSON> <PERSON>", "chargerConnectivityTableSimNumber": "Router SIM-nummer", "chargerConnectivityTableStatusKey": "Status:", "chargerConnectivityTableTitle": "<PERSON><PERSON>", "chargerConnectivityTitle": "La<PERSON> tilko<PERSON>", "chargerConnectivityWifiStatusUnknown": "ukjent", "chargerInformationTitle": "Laderen informasjon", "chargerInformationViewLogsLink": "Vis diagnoselogger", "chargerInformationHistoryLink": "Historikk", "chargerInformationTableTitle": "Laderen informasjonstabell", "chargerInformationTableSKUKey": "Modell vare nr.", "chargerInformationTableArchitectureKey": "Arch versjon", "chargerInformationTableFirmwareKey": "<PERSON><PERSON><PERSON>", "chargerInformationTableManufacturedKey": "<PERSON><PERSON><PERSON>", "chargerInformationTableOperatorKey": "Operatør", "chargerInformationTableOperatorEditLabel": "<PERSON><PERSON>ø<PERSON>", "chargerInformationTableMACKey": "MAC adresse", "chargerInformationTablePCBAKey": "PCBA serienummer", "installationDetailsTitle": "Detaljer for installasjon", "installationDetailsViewDetailsLink": "Vis detaljer om installasjonen", "installationDetailsTableTitle": "Installasjonsdetaljer tabell", "installationDetailsTableInstallDateKey": "<PERSON><PERSON> installert", "installationDetailsTableInstalledByKey": "Installert av", "installationDetailsTableCompanyKey": "Firma", "lastCompleteChargeTitle": "Siste fullstendige lading", "lastCompleteChargeRecentCharges": "<PERSON>ste k<PERSON>nader", "lastCompleteChargeTableTitle": "<PERSON><PERSON> fullstendige fyllings<PERSON>bell", "lastCompleteChargeTableStartedAtKey": "Startet kl.", "lastCompleteChargeTableEndedAtKey": "<PERSON>lut<PERSON>t på", "lastCompleteChargeTableKWhKey": "kWh levert", "setOperatorTooltip": "<PERSON><PERSON>ø<PERSON>", "tariffAllDay": "<PERSON><PERSON> dagen", "tariffsDayOfWeek1": "Mandag", "tariffsDayOfWeek2": "Tirsdag", "tariffsDayOfWeek3": "Onsdag", "tariffsDayOfWeek4": "Torsdag", "tariffsDayOfWeek5": "Fred<PERSON>", "tariffsDayOfWeek6": "<PERSON><PERSON><PERSON><PERSON>", "tariffsDayOfWeek7": "<PERSON><PERSON><PERSON>g", "tariffEnergyProvider": "Leverandør av energi", "tariffEffectiveFrom": "Effektiv fra", "tariffsTitle": "Tariffs", "tariffRate": "Sats", "tariffRateOffPeak": "Off-peak rate", "tariffRatePeak": "Maksimal rate", "vehicleBatteryCapacity": "<PERSON><PERSON><PERSON>", "vehicleBatteryPercentage": "Gjeldende batterinivå", "vehicleChargeLimit": "Grense for fyllingsmengde", "vehicleEnodeConnected": "eNode tilkoblet", "vehicleMakeAndModel": "Lag og modell", "vehiclePluggedIn": "<PERSON><PERSON><PERSON> til", "vehicleTableCaption": "Belastes etter tider", "vehicleTitle": "Kjøretøy", "vehicleViewAllVehicles": "<PERSON><PERSON>", "yes": "<PERSON>a", "no": "<PERSON><PERSON>"}, "ChargeModes": {"3rdPartyOperator": "3. part", "active": "Aktiv", "candidate": "Forslag", "chargeModeHeading": "<PERSON><PERSON>", "smartModeLabel": "Smart modus", "smartModeSwitchToSmart": "<PERSON><PERSON> satt for å smarte.", "smartModeConfirmActivateModalTitle": "Aktiver smartmodus", "smartModeConfirmDeactivateModalTitle": "Deaktiver smart modus", "smartModeConfirmActivateModalMessage": "Er du sikker på at du vil aktivere smartmodus?", "smartModeConfirmDeactivateModalMessage": "Er du sikker på at du vil deaktivere smartmodus?", "smartModeModalConfirmButton": "Bekreft", "smartModeModalCancelButton": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "smartModeDelegatedControlModalTitle": "<PERSON><PERSON><PERSON> kontroll", "smartModeDelegatedControlModalContent": "Smart modus kan ikke endres mens laderen er under delegert kontroll", "smartModeDelegatedControlModalConfirmButton": "Ok", "smartModeToastError": "Feil ved innstilling av smartmodus", "chargeNowLabel": "Ladning nå", "chargeNowModalCancelButton": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "chargeNowModalDurationErrorMessage": "{errorMessage}", "chargeNowModalHeading": "Still inn varigheten av ladningen nå", "chargeNowModalHoursLabel": "Timer", "chargeNowModalMinutesLabel": "<PERSON><PERSON>", "chargeNowModalSaveChangesButton": "<PERSON><PERSON><PERSON> endringer", "chargeNowSwitchToActive": "Last inn nå satt til aktiv.", "chargeNowSwitchToInactive": "Belast<PERSON> settes nå til inaktiv.", "chargeNowToastError": "<PERSON>il ved endring av fakturering nå", "delegatedControlLabel": "<PERSON><PERSON><PERSON> kontroll", "delegatedControlModalConfirmButton": "Bekreft", "delegatedControlModalCancelButton": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "delegatedControlModalContent": "Er du sikker på at du vil fjerne lager {ppid} fra delegert kontroll?", "delegatedControlModalTitle": "Fjern fra delegert kontroll", "delegatedControlToastSuccess": "Laderen er ikke lenger under delege<PERSON> kontroll", "delegatedControlToastError": "Feil ved fjerning fra delegert kontroll", "flexLabel": "Flex", "flexValueEnrolled": "Registrert", "flexValueUnenrolled": "Uregistrert", "inactive": "Inaktiv", "offModeLabel": "Av modus", "offModeModalActivateHeading": "Aktiver av-modus", "offModeModalDeactivateHeading": "Deaktiver av-modus", "offModeModalActivateContent": "Er du sikker på at du vil aktivere avslått modus?", "offModeModalDeactivateContent": "Er du sikker på at du vil deaktivere avslå?", "offModeModalConfirmButton": "Bekreft", "offModeModalCancelButton": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "offModeToastActivatedSuccess": "Av-modus er aktivert", "offModeToastDeactivatedSuccess": "Av-modus er deaktivert", "offModeToastError": "Feil ved innstilling av modus", "pending": "Ventende", "since": "siden", "smartModeSwitchToManual": "Modus satt til manual.", "unknown": "<PERSON><PERSON><PERSON><PERSON>"}, "ChargeSchedules": {"chargeSchedulesHeader": "Tidsplaner", "addSchedulesLink": "<PERSON>gg til tidsplaner", "editSchedulesLink": "Rediger tidsplaner", "viewSchedulesLink": "<PERSON>is tidsplaner", "noSchedulesFoundText": "Laderen kan brukes til enhver tid, det er ingen belastningsplaner."}, "ChargeSchedulesTable": {"startHeading": "Begynn", "caption": "<PERSON><PERSON> med tidsplaner og moduser", "durationHeading": "<PERSON><PERSON><PERSON><PERSON>", "activeHeading": "Aktiv", "activeScheduleYes": "<PERSON>a", "activeScheduleNo": "<PERSON><PERSON>", "scheduleDayOfWeek1": "Mandag", "scheduleDayOfWeek2": "Tirsdag", "scheduleDayOfWeek3": "Onsdag", "scheduleDayOfWeek4": "Torsdag", "scheduleDayOfWeek5": "Fred<PERSON>", "scheduleDayOfWeek6": "<PERSON><PERSON><PERSON><PERSON>", "scheduleDayOfWeek7": "<PERSON><PERSON><PERSON>g"}, "ChargeSchedulesChart": {"scheduleDayOfWeek1": "Mon", "scheduleDayOfWeek2": "Tir", "scheduleDayOfWeek3": "Ons", "scheduleDayOfWeek4": "<PERSON>hu", "scheduleDayOfWeek5": "Fre", "scheduleDayOfWeek6": "<PERSON><PERSON><PERSON>", "scheduleDayOfWeek7": "<PERSON>ø<PERSON>"}, "ChargerConfiguration": {"configurationHeading": "Laderen konfigurasjon", "tableCaption": "Konfigurasjonstabell for laderen", "editButtonAriaLabel": "Rediger lader konfigurasjon", "powerBalancingEnabledLabel": "Kraftsaldo aktivert", "powerBalancingEnabledValueYes": "<PERSON>a", "powerBalancingEnabledValueNo": "<PERSON><PERSON>", "powerBalancingSensorLabel": "Strømbalanseringssensor", "powerBalancingSensorArray": "Matrise", "powerBalancingSensorCtClamp": "CT klemme", "powerBalancingSensorLinky": "<PERSON><PERSON>", "powerBalancingSensorNone": "Ingen", "ctMaxAmpsLabel": "<PERSON><PERSON><PERSON><PERSON> tilførsel ved CT klampe (Amps)", "chargerRatingAmpsLabel": "<PERSON><PERSON><PERSON><PERSON> (Amps)", "lastRetrievedLabel": "Sist hentet"}, "ChargerHeader": {"copyButtonAriaLabel": "<PERSON><PERSON><PERSON> {heading}", "copySuccessMessage": "Kopiert \"{heading}\" til utklippstavle"}, "ChargerIcons": {"locationIconTextCommercial": "<PERSON><PERSON><PERSON><PERSON>", "locationIconTitleCommercial": "<PERSON><PERSON><PERSON><PERSON> plassering", "locationIconTextDomestic": "Innenlands", "locationIconTitleDomestic": "Innenlands plassering", "isPublicIconText": "Offentlig:", "isPublicIconValueYes": "<PERSON>a", "isPublicIconValueNo": "<PERSON><PERSON>", "confirmIconText": "Bekreft", "confirmIconValueYes": "<PERSON>a", "confirmIconValueNo": "<PERSON><PERSON>", "hasMidmeterEnabledIconText": "MID meter:", "hasMidmeterEnabledIconValueYes": "<PERSON>a", "hasMidmeterEnabledIconValueNo": "<PERSON><PERSON>", "hasTariffIconText": "Tildelt tabell:", "hasTariffIconValueYes": "<PERSON>a", "hasTariffIconValueNo": "<PERSON><PERSON>", "hasRfidReaderIconText": "RFID leser:", "hasRfidReaderIconValueYes": "<PERSON>a", "hasRfidReaderIconValueNo": "<PERSON><PERSON>", "podDriveSubscriptionIconText": "Pod Drive abonnement", "podDriveSubscriptionIconTitle": "Laderen har et Pod Drive abonnement", "hasSubscriptionIconText": "Subscription:", "hasSubscriptionIconValueYes": "Yes", "hasSubscriptionIconValueNo": "No"}, "ChargersTable": {"caption": "<PERSON><PERSON> over andre ladere plassert på samme sted", "expandLabel": "<PERSON><PERSON><PERSON>", "header": "Lader (nettsted)", "modelHeader": "<PERSON><PERSON>", "nameHeader": "Navn", "noChargers": "<PERSON>ne laderen har ingen andre ladere på samme kontrollsted.", "ppidHeader": "PPID"}, "ChargingIntentsTable": {"caption": "<PERSON>bell over fyllings<PERSON>g<PERSON> etter tider", "noChargingIntents": "Denne laderen har ingen ladeh<PERSON><PERSON>.", "shortFormDayOfWeek1": "Mon", "shortFormDayOfWeek2": "<PERSON><PERSON>", "shortFormDayOfWeek3": "Ons", "shortFormDayOfWeek4": "Tors", "shortFormDayOfWeek5": "Fre", "shortFormDayOfWeek6": "<PERSON><PERSON><PERSON>", "shortFormDayOfWeek7": "<PERSON>ø<PERSON>", "vehicleChargeBy": "Belast av", "batteryToAdd": "Batteri å legge til", "enodeConnectedWarning": "* EK-gruppen som er koblet til en ladningsgren<PERSON>, og som bare faller tilbake til tidsplanen nedenfor hvis de ikke kan koble til."}, "DomainsTable": {"activatedOnHeader": "<PERSON><PERSON> a<PERSON>", "caption": "Liste over domener", "expandLabel": "<PERSON><PERSON><PERSON>", "domainNameHeader": "Domene navn", "header": "<PERSON><PERSON>", "noDomains": "Denne gruppen laderen tilhører ingen domener konfigurert."}, "DriversTable": {"caption": "Liste over <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "emailHeader": "E-post", "expandLabel": "<PERSON><PERSON><PERSON>", "header": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "noDrivers": "Denne gruppen som denne laderen tilhører har ingen sj<PERSON><PERSON><PERSON><PERSON> satt opp.", "statusHeader": "Status:", "tierHeader": "<PERSON><PERSON><PERSON>"}, "LinkedUsers": {"unlinkUserAriaLabel": "Koble fra brukeren {user}"}, "RfidCardsTable": {"activeHeader": "Aktiv", "caption": "Tabell med RFID-kort", "expandLabel": "<PERSON><PERSON><PERSON>", "header": "RFID kort", "noCards": "<PERSON>ne lader har ingen RFID-kort.", "referenceHeader": "Referanse"}, "OtherConfigurationValues": {"header": "<PERSON>", "issueFetching": "Det oppstod et problem med å hente konfigurasjonen.", "tableCaption": "<PERSON><PERSON> med andre konfigurasjonsverdier"}, "Modals": {"cancelButton": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "confirmButton": "Send<PERSON>", "saveChangesButton": "<PERSON><PERSON><PERSON> endringer", "commandModalParagraph": "<PERSON>r du sikker på at du vil sende kommando {command} for å lade {ppid} (kontakt {socket})?", "commandModal": "Socket {socket}", "commandModalSuccess": "{command} ble sendt som kommando", "commandModalSoftResetCommand": "Myk omstart", "commandModalHardResetCommand": "Hard reset", "commandModalSetInServiceCommand": "Sett inn tjeneste", "commandModalSetOutOfServiceCommand": "Sett ut av tjenesten", "commandModalUnlockConnectorCommand": "LÃ¥s opp kobling", "commandModalError": "Feil under sending av {command} kommando", "requestDiagnosticLogsErrorMessage": "Kan ikke be om diagnoselogger", "requestDiagnosticLogsParagraph": "Be om 24 timer med diagnoselogger fra å lade {ppid} {socket}", "requestDiagnosticLogsSocket": "Socket {socket}", "requestDiagnosticLogsStartDateLabel": "Start dato", "requestDiagnosticLogsStartTimeLabel": "Start tidspunkt", "requestDiagnosticLogsTitle": "Be om diagnoselogger", "requestFirmwareUpdateParagraph": "<PERSON>r du sikker på at du vil sende fastvare oppdateringsforespørsel {ppid} {socket}?", "requestFirmwareUpdateSocket": "Socket {socket}", "requestFirmwareUpdateSuccess": "Vellykket forespurt fastvareoppdatering", "requestFirmwareUpdateError": "K<PERSON>te ikke å be om fastvareoppdatering", "requestFirmwareUpdateTitle": "Be om fastvareoppdatering", "retrieveLatestChargerConfigurationParagraph": "Er du sikker på at du vil hente den nyeste konfigurasjonen for laderen {ppid}?", "retrieveLatestChargerConfigurationSuccess": "Den nyeste konfigurasjonen er påkrevd. Det kan ta noen sekunder å oppdatere. Du må kanskje oppdatere nettleseren din.", "retrieveLatestChargerConfigurationError": "Kunne ikke be om nyeste konfigurasjon", "retrieveLatestChargerConfigurationTitle": "Hent den nyeste laderkonfigurasjonen", "setOperatorSuccess": "Oppsettet av operatør. Det kan ta noen sekunder å oppdatere. Du må kanskje oppdatere nettleseren din.", "setOperatorError": "Kan ikke angi operatør", "setOperatorLabel": "Operatør", "setOperatorTitle": "<PERSON><PERSON>ø<PERSON>"}, "CommandsMenu": {"title": "<PERSON><PERSON><PERSON><PERSON>", "softResetCommand": "Myk omstart", "hardResetCommand": "Hard reset", "setInServiceCommand": "Sett inn tjeneste", "setOutOfServiceCommand": "Sett ut av tjenesten", "unlockConnectorCommand": "LÃ¥s opp kobling", "requestDiagnosticLogsCommand": "Be om diagnoselogger", "requestFirmwareUpdateCommand": "Be om fastvareoppdatering", "retrieveLatestConfigurationCommand": "Hent nyeste konfigurasjon"}, "SocketSwitcher": {"socketTitle": "Socket {socket}", "optionAll": "Alle"}, "SolarConfiguration": {"heading": "Soloppsett konfigurasjon", "tableCaption": "Konfigurasjonstabell for solen", "editButtonAriaLabel": "<PERSON><PERSON> solo<PERSON>", "pvSolarSystemInstalledLabel": "solvarmeanlegg med PV installert", "pvSolarSystemInstalledValueYes": "<PERSON>a", "pvSolarSystemInstalledValueNo": "<PERSON><PERSON>", "solarMatchingEnabledLabel": "Solenergi matching aktivert", "solarMatchingEnabledValueYes": "<PERSON>a", "solarMatchingEnabledValueNo": "<PERSON><PERSON>", "maxGridImportLabel": "<PERSON><PERSON> tabell<PERSON> (Amps)"}, "Tags": {"header": "Tagger", "noTags": "Denne lader har ingen tagger.", "tableCaption": "Liste over emneord"}, "TopCardLayout": {"addressPrefix": "<PERSON><PERSON><PERSON>:", "backLinkLabel": "Tilbake", "generateCommissioningCertificateLink": "Genererer igangkjøringssertifikat", "groupPrefix": "Gruppe:", "siteContactPrefix": "Nettstedskontakt:", "sitePrefix": "Nettsted:"}}, "ConfigurationPage": {"backButton": "<PERSON>der", "chargeCurrentLimit": "<PERSON><PERSON><PERSON><PERSON> (Amps)", "chargerConfiguration": "Laderen konfigurasjon", "confirmCharge": "Bekreft belastning", "heading": "Konfigurasjon - {ppid} (lomme {socket})", "linkySchedulesEnabled": "<PERSON><PERSON> tidsplaner aktivert", "offlineSchedulingEnabled": "Offline planlegging aktivert", "penFaultMode": "Pennens feilmodus", "powerBalancingCurrentLimit": "<PERSON><PERSON><PERSON><PERSON> tilførsel ved CT klampe (Amps)", "powerBalancingEnabled": "Kraftsaldo aktivert", "powerBalancingSensor": "Strømbalanseringssensor", "powerBalancingSensorArray": "Matrise", "powerBalancingSensorCtClamp": "CT klemme", "powerBalancingSensorInstalled": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "powerBalancingSensorLinky": "<PERSON><PERSON>", "powerBalancingSensorNone": "Ingen", "powerBalancingSensorPlaceholder": "V<PERSON>g strømbalanseringssensor", "powerBalancingSensorPolarityInverted": "<PERSON><PERSON><PERSON><PERSON>aldo (CT-klamp) invertert polaritet", "ppDevClampFaultThreshold": "CT klamps feil<PERSON>kel (Amps)", "rcdBreakerSize": "EV lager RCBO rating (Amps)", "saveChangesButton": "<PERSON><PERSON><PERSON> endringer", "selectMode": "Velg modus", "selectThreshold": "<PERSON><PERSON>g terskel", "socketTitle": "(Socket {socket})", "solarConfiguration": "Soloppsett konfigurasjon", "solarExportMargin": "Solcelleeksportmargin (Amps)", "solarExportMarginTooltip": "En forskyvning til eksportsettpunktet for bestemmelse av tilgjengelig effekt fra solgenerering. Dette brukes til å sørge for at systemer som inneholder et batteri, ikke gir batteristrøm til å lade EV. Bør være 0 for installasjoner uten et batterisystem installert.", "solarMatchingEnabled": "Solenergi matching aktivert", "solarMatchingEnabledTooltip": "Om soldaten som samsvarer med overskudd er aktivert. Dette er merket med \"Sollade modus\" i mobilappen.", "solarMaxGridImport": "<PERSON><PERSON> tabell<PERSON> (Amps)", "solarMaxGridImportConverted": "Maks tabellimport (kW)", "solarMaxGridImportConvertedTooltip": "En konvertering av maksimal nettimport fra amps til kW slik det vises i mobilappen.", "solarMaxGridImportTooltip": "<PERSON><PERSON><PERSON><PERSON> mengde elektrisitet (i Amps) som kan trekkes fra nettet for å kunne generere sollys og for å lade seg opp. Når satt til 6.0, vil lader lades med 1.4 kW når det ikke genereres solenergi.", "solarStartDelay": "Forsinkelse på solarium (sekunder)", "solarStartDelayTooltip": "Minimum varighet i sekunder på konsekvent høyere generert strøm nødvendig for å øke ladningshastigheten. Brukt til å unngå påfølgende veksling mellom ladningshastigheter.", "solarStopDelay": "Forsinkelse av solstopp (sekunder)", "solarStopDelayTooltip": "Minimum varighet i sekunder på konsekvent lavere generert strøm nødvendig for å redusere ladningshastigheten. Brukt til å unngå påfølgende veksling mellom ladningshastigheter.", "solarSystemInstalled": "solvarmeanlegg med PV installert", "solarSystemInstalledTooltip": "Om eiendommen har et PV solcelleanlegg installert (eller et hvilket som helst annet genereringsanlegg av elektrisitet som kan eksportere til nettet). Dette merket \"Jeg har solcellepaneler\" i mobilappen.", "title": "St<PERSON>tte verktøy - ladningskonfigurasjon", "toastError": "Feil ved oppdatering av konfigurasjon", "toastSuccess": "Vellykket oppdatert konfigurasjon", "unlockConnectorOnEVSideDisconnect": "Skal kabellåsing fra lader når koblet ut fra kjøretøy?"}, "DetailsPage": {"backButton": "<PERSON>der", "cancelButton": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "noDisplayName": "<PERSON><PERSON><PERSON> an<PERSON>t", "saveButton": "Lagre", "saveChangesButton": "<PERSON><PERSON><PERSON> endringer", "socketTitle": "(Socket {socket})", "title": "<PERSON><PERSON>tte verktøy - Lader"}, "DiagnosticsLogsViewerPage": {"actions": "<PERSON><PERSON>", "download": "Nedlasting", "end": "Slutt", "heading": "Diagnostiske logger - {ppid} (lomme {socket})", "openButton": "<PERSON><PERSON><PERSON>", "socketTitle": "(Socket {socket})", "start": "Begynn", "status": "Status:", "tableCaption": "Liste over diagnoselogger", "title": "Diagnostisering loggviser", "topPageTitle": "<PERSON><PERSON><PERSON> verktøy - Diagnostiske logger", "viewerPageTitle": "Støtt verktøy - Diagnostikkloggvisning"}, "FlexDetailsPage": {"flexDeletedAt": "<PERSON><PERSON><PERSON><PERSON>", "flexDetailsHeading": "Flex detaljer - {ppid}", "flexDirection": "Retning", "flexProgrammeEnrollmentDate": "<PERSON><PERSON><PERSON><PERSON> dato", "flexProgrammeStatus": "Status på programmet", "flexProgrammes": "Flex programmer", "flexProgrammesUnEnrollmentDate": "Utrullingsdato", "flexRequestedAt": "Forespurt", "flexScheduledEnd": "<PERSON><PERSON><PERSON> slutt", "flexScheduledStart": "<PERSON>lagt start", "flexStatus": "Status:", "socketTitle": "(Socket {socket})", "title": "<PERSON><PERSON>tte verktøy - Flex-detaljer"}, "InstallationPage": {"heading": "Installasjonsdetaljer - {ppid}", "headingWithSocket": "Installasjonsdetaljer - {ppid} (Socket {socket})", "installationImages": "Installasjons bilder", "installationValues": "Disse er verdiene oppført på det punktet som laderen ble installert. De kan avvike fra nåværende verdier og kan ikke oppdateres.", "installationDate": "Installasjonsdato", "installedBy": "Installert av", "company": "Firma", "outOfService": "Ute av service", "yes": "<PERSON>a", "no": "<PERSON><PERSON>", "householdMaxSupply": "<PERSON><PERSON><PERSON><PERSON> tilførsel ved CT klampe (Amps)", "breakerSize": "EV lager RCBO rating (Amps)", "powerRatingPerPhase": "<PERSON><PERSON><PERSON><PERSON> (Amps)", "powerBalancingSensorInstalled": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "powerBalancingEnabled": "Kraftsaldo aktivert", "powerBalancingSensor": "Strømbalanseringssensor", "powerBalancingSensorArray": "Matrise", "powerBalancingSensorCtClamp": "CT klemme", "powerBalancingSensorLinky": "<PERSON><PERSON>", "powerBalancingSensorNone": "Ingen", "linkySchedulesEnabled": "<PERSON><PERSON> tidsplaner aktivert", "powerGenerationSystemInstalled": "solvarmeanlegg med PV installert", "socketTitle": "(Socket {socket})", "title": "St<PERSON>tte verktøy - Installasjonsdetaljer"}, "PcbHistoryPage": {"all": "Alle", "datetime": "Dato/klokkeslett", "errorCode": "<PERSON>il kode", "failed": "Mislyktes", "heading": "PCB historie - {ppid} (lomme {socket})", "inProgress": "Under arbeid", "serialNumber": "Serienummer", "status": "Status:", "success": "Vellykket", "socketTitle": "(Socket {socket})", "tableCaption": "<PERSON><PERSON> med pcb-historikk", "title": "<PERSON><PERSON><PERSON>rktøy - PCB historie"}, "RecentChargesPage": {"heading": "<PERSON><PERSON><PERSON>førte kostnader - {ppid}", "socketHeading": " (Socket {socket})", "downloadChargesCsvButton": "Last ned CSV", "pluggedInAtHeader": "<PERSON><PERSON><PERSON> til", "startedAtHeader": "Startet kl.", "endedAtHeader": "<PERSON>lut<PERSON>t på", "unpluggedAtHeader": "Frakoblet", "energyTotalHeader": "<PERSON><PERSON><PERSON> levert (kWh)", "gridEnergyTotalHeader": "Grid energy (kWh)", "generationEnergyTotalHeader": "Solenergi (kWh)", "energyCostHeader": "Energi kostet", "revenueGeneratedHeader": "Inntekter", "chargeDurationTotalHeader": "Varsel på lading", "totalDurationHeader": "Total varighet", "doorHeader": "Socket", "confirmedHeader": "Bekreftet", "actionsHeader": "<PERSON><PERSON>", "actionsEditEnergyTooltip": "Rediger energikostnader", "actionsEditEnergyAriaLabel": "Rediger energikostnader {chargeId}", "actionsEditRevenueCostTooltip": "Rediger inntektskostnader", "actionsEditRevenueCostAriaLabel": "Rediger inntektskostnader {chargeId}", "tableCaption": "<PERSON><PERSON> over n<PERSON><PERSON><PERSON><PERSON> kostnader", "tableFilterPlaceholder": "Alle", "noDataFound": "Ingen nylige kostnader funnet", "filtersLabel": "Filter: ", "clearFilters": "<PERSON><PERSON><PERSON>", "pluggedInFilterYear": "Siste 12 måneder", "pluggedInFilterWeek": "Siste 7 dager", "pluggedInFilter30days": "Siste 30 dager", "pluggedInFilter90days": "Siste 90 dager", "pluggedInFilterMonth": "<PERSON><PERSON>", "pluggedInFilterPeviousMonth": "<PERSON><PERSON><PERSON>", "socketTitle": "(Socket {socket})", "title": "<PERSON><PERSON>tte verktøy - Siste kostnader", "editRevenueCostTitle": "Rediger inntekter", "editRevenueCostDescription": "Amenper inntektene på denne g<PERSON>, dette er det beløp driveren betaler til nettstedet for gebyret. Balansen i førerhuset vil ikke bli endret som en del av denne endringen.", "editRevenueCostEnergyTotalText": "<PERSON><PERSON><PERSON> <PERSON>t", "editRevenueCostInputUpdatedCost": "Oppdatert inntekt (Gr.", "editRevenueCostInputCost": "<PERSON>ris per kWh (i blyam)", "editRevenueCostInputCostPlaceholder": "<PERSON><PERSON> derfor per kWh", "editEnergyCostTitle": "Rediger kostnad", "editEnergyCostDescription": "Oppdater energikostnaden for denne belastningen.", "editEnergyCostEnergyTotalText": "<PERSON><PERSON><PERSON> levert (kWh)", "editGridEnergyCostEnergyTotalText": "Grid energy (kWh)", "editEnergyCostInputUpdatedCost": "Oppdatert energikostnad (£)", "editEnergyCostInputCost": "Pence per kWh", "editEnergyCostInputCostPlaceholder": "<PERSON><PERSON> derfor per kWh", "updateCostFormSaveButton": "<PERSON><PERSON><PERSON> endringer", "updateCostFormCancelButton": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "chargeAlreadyExpensedError": "Denne avgiften er allerede kostnadsført og kan ikke oppdateres", "commonInternalServerError": "Noe gikk galt. Vennligst prøv igjen"}, "SchedulesEditPage": {"active": "Aktiv", "activeScheduleYes": "<PERSON>a", "activeScheduleNo": "<PERSON><PERSON>", "chargeScheduleDurationError": "Den minste tidsplanen varer i 15 minutter", "chargeScheduleOverlapError": "En eller flere planer overlapping. <PERSON><PERSON><PERSON><PERSON> at det er minst 15 minutter mellom hver tidsplan", "day": "<PERSON><PERSON>", "duration": "<PERSON><PERSON><PERSON><PERSON>", "errorToast": "Feil ved oppdatering av tidsplan", "heading": "Rediger tidsplaner - {ppid}", "headingReadOnly": "Vis tidsplaner - {ppid}", "hours": "timer", "minutes": "minutter", "saveChangesButton": "<PERSON><PERSON><PERSON> endringer", "start": "Begynn", "successToast": "Oppdatert tidsplan", "title": "<PERSON><PERSON>tte verktøy - Rediger kostnadsplaner", "titleReadOnly": "<PERSON><PERSON><PERSON> verktøy - Vis avgiftsplaner", "Monday": "Mandag", "Tuesday": "Tirsdag", "Wednesday": "Onsdag", "Thursday": "Torsdag", "Friday": "Fred<PERSON>", "Saturday": "<PERSON><PERSON><PERSON><PERSON>", "Sunday": "<PERSON><PERSON><PERSON>g"}, "SearchPage": {"heading": "<PERSON>der", "searchCardHeading": "Naviger til lading:", "searchFormPpidLabel": "PPID eller navn", "searchFormSubmitButton": "Gå", "title": "<PERSON><PERSON>tte verktøy - Lader"}, "VehiclesPage": {"batteryCapacity": "<PERSON><PERSON><PERSON>", "vehicleBatteryPercentage": "Gjeldende batterinivå", "chargeLimit": "Grense for fyllingsmengde", "enodeConnected": "eNode tilkoblet", "heading": "Kj<PERSON>retøy - {ppid}", "lastSeen": "Kjøretøy sist sett på", "lastUpdated": "Ladestatusen sist oppdatert på", "no": "<PERSON><PERSON>", "powerDeliveryState": "Mengde leveransetilstand", "title": "Supportverktøy – Kjøretøy", "yes": "<PERSON>a"}, "CommissioningCertificatePage": {"title": "Kommisjonens sertifikat", "heading": "Sertifikat for ferdigstillelse - {ppid}", "backButton": "Tilbake", "generatedAt": "Generert: {date}", "printButton": "Skriv ut sertifikat", "downloadButton": "Last ned PDF", "certificateTitle": "Kommisjonens sertifikat", "ppidLabel": "PPID nummer(er):", "addressLabel": "<PERSON><PERSON><PERSON>:", "groupLabel": "Gruppe:", "siteLabel": "Nettsted:", "adminEmailLabel": "Admin e-postadresse:", "communicatingStatement": "Enheten(e) som er installert i dette prosjektet kommuniserer for tiden", "appearOnAppStatement": "Enheten(e) som er installert i dette prosjektet skal vises i appen", "authenticatedViaAppStatement": "Enhet(er) installert i dette prosjektet er satt til å autentiseres via appen", "paygTariffStatement": "Enheten(e) som er installert i dette prosjektet, er satt opp en PAYG-tariff", "warrantyStartDateLabel": "<PERSON><PERSON><PERSON><PERSON>to:", "warrantyEndDateLabel": "<PERSON><PERSON><PERSON> slut<PERSON>:", "installedByLabel": "Installert av:", "installerCompanyLabel": "Installeringsprogram Company:", "handoverTitle": " Bekreft ferdigstillelse av overlevering", "companyRepresented": "<PERSON><PERSON><PERSON> representert:", "companyName": "Pod punkt ltd", "contactName": "Salgsoperasjonsteam", "contactNumber": "0207 247 4114", "companyAddress": "222 Gray's Inn Road, London, WC1X 8HB", "handoverDate": "Generert dato:", "nameLabel": "Navn:", "contactNumberLabel": "Kontaktnummer:", "yes": "<PERSON>a", "no": "<PERSON><PERSON>"}}, "Shared": {"UnlinkChargerModal": {"successToast": "Laderen koblet fra konto", "errorToast": "Kan ikke koble fra laderen", "chargerPageConfirmMessage": "Er du sikker på at du vil fjerne denne kontoen fra denne laderen?", "confirmMessage": "Er du sikker på at du vil koble denne laderen fra denne kontoen?", "title": "Frakoblet lader", "confirmMessageParagraph": "Kontoinnehaveren vil ikke lenger kunne se eller administrere laderen i driverappen. De vil ha tilgang til å lade data fra før laderen ble koblet fra.", "confirmButtonText": "Frakoblet lader", "cancelButtonText": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}}, "Subscriptions": {"SearchPage": {"heading": "Abonnementer", "searchCardHeading": "<PERSON><PERSON><PERSON> etter abonnementer:", "searchResultsOrderedAt": "Bestilt", "searchResultsLink": "<PERSON><PERSON><PERSON>", "searchResultsStatus": "Status:", "searchCardInputLabel": "E-post eller PPID", "searchCardInputPlaceholder": "<EMAIL>", "searchCardSubmitButton": "Gå", "noSearchResults": "Ingen abonnementer funnet", "title": "Brukerstøtteverktøy - Abonnementer"}, "StatusPage": {"title": "Støtte verktøy - Abonnementstatus", "heading": "Pod stasjons status", "noPpidAllocated": "Lader ID: N/A"}, "SubscriptionActions": {"payUpfrontFee": "Betal ekspedisjonsgebyr", "completeHomeSurvey": "Spørreundersøkelse fullført", "checkAffordability": "<PERSON><PERSON><PERSON> passert", "setupDirectDebit": "<PERSON>r. de<PERSON> satt opp", "signDocuments": "Juridiske dokumenter signert", "chargingStationInstalled": "Lader stasjon installert"}, "StatusBadges": {"subscriptionStatusActive": "Aktiv", "subscriptionStatusCancelled": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "subscriptionStatusPending": "Ventende", "subscriptionStatusSuspended": "<PERSON><PERSON><PERSON><PERSON>", "subscriptionStatusRejected": "<PERSON><PERSON><PERSON>", "subscriptionStatusEnded": "Avsluttet"}}, "WorkInProgress": {"bannerParagraph": "<PERSON>ne siden fungerer fortsatt på. Noen funksjoner fungerer muligens ikke, eller finnes ikke ennå."}, "Errors": {"ctClampThresholdMinValue": "CT klampens feilterskel må være større enn eller lik 1", "ctClampThresholdMaxValue": "CT klampens feilterskel må være mindre enn eller lik 5", "powerBalancingMaxValue": "Strømbalansert klemmerverdi må være mindre enn eller lik 100", "powerBalancingMinValue": "Strømsaldo klemmeverdi må være større enn eller lik 0", "powerBalancingSensorValue": "Må være en av følgende: A<PERSON>y, CT klamp, Linky, Ingen", "breakerSizeMinValue": "Vurdering av EV-belastning RCBO må være større enn 0", "maxCurrentRatingMinValue": "Maksimal nåværende rangering må være større enn 0", "solarMinGridImportValue": "Tabellimport (amps) må være større enn eller lik 0.0", "solarMaxGridImportValue": "Import av tabell (amps) må være mindre enn eller lik 6,0", "solarStartHysteresisMinValue": "Solcellens starthysterese må være større enn 0", "solarStopHysteresisMinValue": "Solsengehysterese må være større enn 0", "solarExportMarginMinValue": "Soleksportmargin (amps) må være større enn eller lik 0,0", "solarExportMarginMaxValue": "Soleksportmargin (amps) må være mindre enn eller lik 6,0", "penFaultModeValue": "PEN-feilmodus må være enten høy intervall eller lav skala", "currencyPenceError": "'Inndata må være en gyldig valutaverdi i det'", "priceMinValueError": "Prisen må være større enn £{price}", "priceMaxValueError": "Prisen må være mindre enn £{price}", "priceDecimalPlacesError": "<PERSON>risen må være innen tre desimaler", "tagValueWhitespace": "Merkelappverdien kan ikke ha mello<PERSON>rom", "tagValueLowercase": "Tag verdien må inneholde små bokstaver", "tagInvalidKey": "Tag-nøkkelen er ugyldig", "tagInvalidValue": "Tagg-verdien for denne nøk<PERSON>en er ugyldig"}}