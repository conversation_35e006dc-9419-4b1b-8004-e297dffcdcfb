{"Layout": {"navigationHomeLink": "Home", "navigationAccountsLink": "Accounts", "navigationChargersLink": "Chargers", "navigationSubscriptionsLink": "Subscriptions"}, "Home": {"heading": "Support Tool", "accountsCardHeading": "Accounts", "accountsCardDescription": "View and manage account settings", "accountsCardLink": "Search account", "chargersCardHeading": "Chargers", "chargersCardDescription": "View activity and manage charger settings", "chargersCardLink": "Search chargers", "subscriptionsCardHeading": "Subscriptions", "subscriptionsCardDescription": "View and manage Pod Drive subscriptions", "subscriptionsCardLink": "Search subscriptions", "title": "Support Tool"}, "Accounts": {"AccountStatusBadgeComponent": {"activeLabel": "Active", "disabledLabel": "Deactivated", "defaultLabel": "Unknown"}, "EmailVerificationBadgeComponent": {"verifiedLabel": "Verified", "unverifiedLabel": "Not verified"}, "SearchPage": {"heading": "Accounts", "searchCardHeading": "Search for an account:", "searchCardEmailLabel": "Email", "searchCardSubmitButton": "Go", "noSearchResults": "No accounts match your current search.", "searchResultsLink": "Open", "title": "Support Tool - Accounts", "emailPlaceholder": "<EMAIL>"}, "DetailsPage": {"accountDetailsPageHeading": "Account details", "actionMenuEditName": "Edit name", "actionMenuEditEmailAddress": "Edit email address", "actionMenuEditRegion": "Edit region", "actionMenuSendPasswordRecovery": "Send password recovery", "actionMenuDeactivateAccount": "Deactivate account", "actionMenuReactivateAccount": "Reactivate account", "actionMenuResendEmailVerification": "Resend email verification", "cancelButton": "Cancel", "saveButton": "Save", "saveChangesButton": "Save changes", "noDisplayName": "Not set", "backLinkLabel": "Back", "accountCreated": "Created: {createdAt}", "accountLastSignIn": "Last sign in: {signedIn}", "accountStatusLabel": "Account status", "emailVerificationLabel": "Email verification", "regionLabel": "Region", "accountBalanceLabel": "Account balance", "chargersTabTitle": "Chargers", "factorsTabTitle": "Authentication (2FA)", "activityTableTitle": "Activity", "activityTableDateHeading": "Date", "activityTableTimeHeading": "Time", "activityTableEventHeading": "Event", "activityTableDetailsHeading": "Details", "activityTableStatusHeading": "Status", "success": "Success", "failed": "Failed", "factorsTableTitle": "Factors", "factorsTableCountryCode": "Country code", "factorsTableLastThreeDigits": "Phone number last 3 digits", "factorsTableEnrollmentTime": "Enrolled", "chargersTableTitle": "Chargers", "chargersTablePpidHeading": "PPID", "chargersTableActionsHeading": "Actions", "unlinkChargerButton": "Unlink charger", "accountPaymentsTableTitle": "Payments for {authId}", "deactivatedAccountModalTitle": "Deactivate user", "deactivatedAccountModalParagraph": "The customer will be logged out of all services and will be unable to log back in.", "deactivatedAccountModalConfirmButton": "Deactivate account", "deactivatedAccountSuccessMessage": "Customer account deactivated – it may take a few seconds for this change to be applied", "deactivatedAccountFailedMessage": "Failed to deactivate account", "deactivatedAccountRefundAlertTitle": "Refund required", "deactivatedAccountRefundAlertParagraph": "Please ensure to refund the customer as their account is currently {balanceString} in credit.", "deactivatedAccountPaymentAlertTitle": "Payment required", "deactivatedAccountPaymentAlertParagraph": "The customer must top up to clear their account balance of {balanceString} before closing the account.", "editNameSuccessMessage": "User details updated this might take a few seconds to update", "editNameFailedMessage": "Failed to update user details", "editNameModalTitle": "Edit name", "editNameModalFirstNameLabel": "First name", "editNameModalLastNameLabel": "Last name", "editRegionSuccessMessage": "User details updated successfully", "editRegionFailedMessage": "Failed to update user details", "editRegionFormRegionLabel": "Region", "editRegionFormRegionSelectPlaceholder": "Select a region", "editRegionModalTitle": "Edit region", "reactivateAccountSuccessMessage": "Customer account reactivated – it may take a few seconds for this change to be applied", "reactivateAccountFailedMessage": "Failed to reactivate account", "reactivateAccountModalTitle": "Reactivate user", "reactivateAccountModalParagraph": "The customer account will be re-activated, allowing them to log in.", "reactivateAccountModalConfirmButton": "Reactivate account", "resendVerificationEmailSuccessMessage": "Email verification resent", "resendVerificationEmailErrorMessage": "Failed to resend email verification", "resendVerificationEmailModalTitle": "Send email verification", "resendVerificationEmailModalConfirmButton": "Send email verification", "sendPasswordResetEmailSuccessMessage": "Password reset email sent", "sendPasswordResetEmailErrorMessage": "Failed to send password reset email", "sendPasswordResetEmailModalTitle": "Send password reset", "sendPasswordResetEmailModalParagraph": "A reset password email will be sent to {email}", "sendPasswordResetEmailModalConfirmButton": "Send password reset", "sendUpdateEmailSuccessMessage": "Update email address request sent", "sendUpdateEmailDuplicateErrorMessage": "An account with this email address already exists", "sendUpdateEmailErrorMessage": "Failed to send update email request", "sendUpdateEmailErrorModalTitle": "Edit email address", "sendUpdateEmailErrorModalParagraph": "An email will be sent to the new email address for the customer to confirm the change before the account is updated.", "sendUpdateEmailErrorFormEmailLabel": "Email address", "title": "Support Tool - Account Details", "accountActionTitle": "Actions"}}, "Chargers": {"ChargeModeHistoryPage": {"chargeNow": "Charge Now", "expired": "Expired", "heading": "Charge mode history - {ppid}", "manualMode": "Manual Mode", "manualStop": "Cancelled by the user or superseded", "overrideType": "Override Type", "requested": "Requested", "start": "Start", "stop": "Stop", "stopReason": "Stop Reason", "tableCaption": "Table of charge mode history", "title": "Support Tool - Charge mode history"}, "Components": {"AdminsTable": {"caption": "Table of admins", "emailHeader": "Email", "expandLabel": "Expand", "header": "Admins", "noAdmins": "This group this charger belongs to has no admins configured.", "lastLoginHeader": "Last login"}, "Alerts": {"chargeScheduleBannerMessage": "This charger is currently in manual mode. The below schedules will not take effect.", "delegatedControlBannerMessage": "This charger is currently under delegated control. The below schedules are indicative only and subject to change.", "grafanaLinkMessage": " or view this charger in Grafana ", "externalOperatorBannerMessage": "This charger is not operated by Pod Point. Customers need to contact the operator for support. You can search for this charger in MIS ", "here": "here", "supportedArchitectureBannerMessage": "You can search for this charger in MIS ", "unSupportedArchitectureBannerMessage": "It is not currently possible to make changes or send commands to this charger. You can search for this charger in MIS "}, "Badges": {"defaultFault": "Fault - {firstOccurrence}", "faultVendorCode": "Fault {vendorErrorCode} - {firstOccurrence}", "faultDescription": "Fault {podPointFault}: {description} - {firstOccurrence}", "faultErrorCode": "Fault {errorCode} - {firstOccurrence}", "outOfRangeFault": "Proximity out of range", "reversePolarityFault": "Reverse polarity, high neutral, loose earth", "fuseSaverFault": "Fuse Saver Error / CLS Significant difference between car & clamp current (no solar)", "renaultSettingFault": "Renault setting fault (Norway)", "arrayFault": "<PERSON><PERSON><PERSON>", "highVoltageFault": "High Voltage", "lowVoltageFault": "Low Voltage", "pilotLowFault": "Pilot Low", "a12VFault": "A-12V level fault", "cableOverTempStopFault": "CableOverTempStop", "overtemperatureFault": "Overtemperature", "overcurrentFault": "Overcurrent", "hardwareDamageFault": "Hardware Damage Relay weld", "mennekesFault": "<PERSON><PERSON><PERSON>", "rCDACFault": "RCD AC", "groundFailureFault": "GroundFailure", "6mAFault": "6mA detection bender (RCD DC)", "unlockSocketFault": "Unlock Socket Fault", "upstreamPowerFault": "Upstream power / brownout Fault", "linuxUnavailableFault": "Internal Error between STM and Linux (linux unavailable)", "queueOverflowFault": "Internal Error between STM and Linux  (queue overflow)", "invalidModeFault": "The vehicle is in an invalid mode for charging (Reported by IEC stack)", "exceededImportLimitUnder125Fault": "Exceeded import limit (between 100-125%) for 60 seconds", "exceededImportLimitOver125Fault": "Exceeded import limit by 125%", "threePhaseEarthPENFault": "3-phase PEN fault (earth)", "threePhaseNeutralPENFault": "3-phase PEN fault (neutral)", "threePhaseMissingPhaseFault": "Phase missing on 3-phase unit", "linkyConnectionFault": "<PERSON><PERSON> lost connection", "linkyDaughterBoardFault": " Linky daughterboard missing or faulty", "voltagePolarityFault": "Live and neutral swapped", "6mASelfTestFault": "RCD self-test fault", "lockNotDetectedFault": "Lock not detected", "midFault": "Missing or damaged MID meter", "rfidNotDetectedFault": "RFID not detected", "rfidErrorFault": "RFID present but faulty", "veryHighVoltageFault": "Very high voltage, >270V, likely PEN fault", "firmwareAvailable": "Firmware update available", "firmwareUpdateNotRequested": "Firmware update not requested", "firmwareUpdateNotAccepted": "Firmware update not accepted", "firmwareUpdateInProgress": "Firmware update in progress", "firmwareUpdateFailed": "Firmware update failed"}, "Cards": {"warrantyDetailsTitle": "Warranty details", "warrantyDetailsTableTitle": "Warranty details table", "warrantyDetailsTableStartDateKey": "Warranty start date", "warrantyDetailsTableEndDateKey": "Warranty end date", "warrantyDetailsTableWarrantyStatusKey": "Warranty status", "warrantyDetailsTableWarrantyStatusActive": "Active", "warrantyDetailsTableWarrantyStatusInactive": "Inactive", "chargerAccessPointTitle": "Charger access point (AP)", "chargerAccessPointHidePassword": "Hide password", "chargerAccessPointShowPassword": "Reveal password", "chargerAccessPointTableTitle": "Charger access point", "chargerAccessPointTableSSIDKey": "SSID", "chargerAccessPointTablePasswordKey": "Password", "chargerConnectivityQuality0": "0", "chargerConnectivityQuality1": "1 (Poor, RSSI {signalStrength})", "chargerConnectivityQuality2": "2 (<PERSON>, RSSI {signalStrength})", "chargerConnectivityQuality3": "3 (<PERSON>, RSSI {signalStrength})", "chargerConnectivityQuality4": "4 (Very Good, RSSI {signalStrength})", "chargerConnectivityQuality5": "5 (Excellent, RSSI {signalStrength})", "chargerConnectivityTableChargingStatusKey": "Charging status", "chargerConnectivityTableConnectionQuality": "Connection quality", "chargerConnectivityTableConnectionQualityScale": "(scale 1-5)", "chargerConnectivityTableLastMessageKey": "Last message", "chargerConnectivityTableLastSeenKey": "Last connection started", "chargerConnectivityTableMacAddress": "Router MAC address", "chargerConnectivityTableModel": "Router model", "chargerConnectivityTableSerialNumber": "Router serial number", "chargerConnectivityTableSimNumber": "Router SIM number", "chargerConnectivityTableStatusKey": "Status", "chargerConnectivityTableTitle": "Charger connectivity table", "chargerConnectivityTitle": "Charger connectivity", "chargerConnectivityWifiStatusUnknown": "unknown", "chargerInformationTitle": "Charger information", "chargerInformationViewLogsLink": "View diagnostic logs", "chargerInformationHistoryLink": "History", "chargerInformationTableTitle": "Charger information table", "chargerInformationTableSKUKey": "Model SKU", "chargerInformationTableArchitectureKey": "Arch version", "chargerInformationTableFirmwareKey": "Firmware", "chargerInformationTableManufacturedKey": "Manufactured", "chargerInformationTableOperatorKey": "Operator", "chargerInformationTableOperatorEditLabel": "Set operator", "chargerInformationTableMACKey": "MAC address", "chargerInformationTablePCBAKey": "PCBA serial number", "installationDetailsTitle": "Installation details", "installationDetailsViewDetailsLink": "View installation details", "installationDetailsTableTitle": "Installation details table", "installationDetailsTableInstallDateKey": "Install date", "installationDetailsTableInstalledByKey": "Installed by", "installationDetailsTableCompanyKey": "Company", "lastCompleteChargeTitle": "Last complete charge", "lastCompleteChargeRecentCharges": "Recent charges", "lastCompleteChargeTableTitle": "Last complete charge table", "lastCompleteChargeTableStartedAtKey": "Started at", "lastCompleteChargeTableEndedAtKey": "Ended at", "lastCompleteChargeTableKWhKey": "kWh delivered", "setOperatorTooltip": "Set operator", "tariffAllDay": "All day", "tariffsDayOfWeek1": "Monday", "tariffsDayOfWeek2": "Tuesday", "tariffsDayOfWeek3": "Wednesday", "tariffsDayOfWeek4": "Thursday", "tariffsDayOfWeek5": "Friday", "tariffsDayOfWeek6": "Saturday", "tariffsDayOfWeek7": "Sunday", "tariffEnergyProvider": "Energy provider", "tariffEffectiveFrom": "Effective from", "tariffsTitle": "Tariffs", "tariffRate": "Rate", "tariffRateOffPeak": "Off-peak rate", "tariffRatePeak": "Peak rate", "vehicleBatteryCapacity": "Battery capacity", "vehicleBatteryPercentage": "Current battery level", "vehicleChargeLimit": "Charge limit", "vehicleEnodeConnected": "eNode connected", "vehicleMakeAndModel": "Make and model", "vehiclePluggedIn": "Plugged in", "vehicleTableCaption": "Charge by times", "vehicleTitle": "Vehicle", "vehicleViewAllVehicles": "View vehicles", "yes": "Yes", "no": "No"}, "ChargeModes": {"3rdPartyOperator": "3rd Party", "active": "Active", "candidate": "Candidate", "chargeModeHeading": "Modes", "smartModeLabel": "Smart mode", "smartModeSwitchToSmart": "Mode set to smart.", "smartModeConfirmActivateModalTitle": "Activate smart mode", "smartModeConfirmDeactivateModalTitle": "Deactivate smart mode", "smartModeConfirmActivateModalMessage": "Are you sure you want to activate smart mode?", "smartModeConfirmDeactivateModalMessage": "Are you sure you want to deactivate smart mode?", "smartModeModalConfirmButton": "Confirm", "smartModeModalCancelButton": "Cancel", "smartModeDelegatedControlModalTitle": "Delegated control", "smartModeDelegatedControlModalContent": "Smart mode cannot be changed whilst charger is under delegated control", "smartModeDelegatedControlModalConfirmButton": "OK", "smartModeToastError": "Error when setting smart mode", "chargeNowLabel": "Charge now", "chargeNowModalCancelButton": "Cancel", "chargeNowModalDurationErrorMessage": "{errorMessage}", "chargeNowModalHeading": "Set duration of charge now", "chargeNowModalHoursLabel": "Hours", "chargeNowModalMinutesLabel": "Minutes", "chargeNowModalSaveChangesButton": "Save changes", "chargeNowSwitchToActive": "Charge now set to active.", "chargeNowSwitchToInactive": "Charge now set to inactive.", "chargeNowToastError": "Error when setting charge now", "delegatedControlLabel": "Delegated control", "delegatedControlModalConfirmButton": "Confirm", "delegatedControlModalCancelButton": "Cancel", "delegatedControlModalContent": "Are you sure you want to remove charger {ppid} from delegated control?", "delegatedControlModalTitle": "Remove from delegated control", "delegatedControlToastSuccess": "Charger no longer under delegated control", "delegatedControlToastError": "Error when removing from delegated control", "flexLabel": "Flex", "flexValueEnrolled": "Enrolled", "flexValueUnenrolled": "Unenrolled", "inactive": "Inactive", "offModeLabel": "Off mode", "offModeModalActivateHeading": "Activate off mode", "offModeModalDeactivateHeading": "Deactivate off mode", "offModeModalActivateContent": "Are you sure you want to activate off mode?", "offModeModalDeactivateContent": "Are you sure you want to deactivate off mode?", "offModeModalConfirmButton": "Confirm", "offModeModalCancelButton": "Cancel", "offModeToastActivatedSuccess": "Off mode has been activated", "offModeToastDeactivatedSuccess": "Off mode has been deactivated", "offModeToastError": "Error when setting off mode", "pending": "Pending", "since": "since", "smartModeSwitchToManual": "Mode set to manual.", "unknown": "Unknown"}, "ChargeSchedules": {"chargeSchedulesHeader": "Schedules", "addSchedulesLink": "Add schedules", "editSchedulesLink": "Edit schedules", "viewSchedulesLink": "View schedules", "noSchedulesFoundText": "This charger can be used at all times, there are no charge schedules."}, "ChargeSchedulesTable": {"startHeading": "Start", "caption": "Table of charge schedules and modes", "durationHeading": "Duration", "activeHeading": "Active", "activeScheduleYes": "Yes", "activeScheduleNo": "No", "scheduleDayOfWeek1": "Monday", "scheduleDayOfWeek2": "Tuesday", "scheduleDayOfWeek3": "Wednesday", "scheduleDayOfWeek4": "Thursday", "scheduleDayOfWeek5": "Friday", "scheduleDayOfWeek6": "Saturday", "scheduleDayOfWeek7": "Sunday"}, "ChargeSchedulesChart": {"scheduleDayOfWeek1": "Mon", "scheduleDayOfWeek2": "<PERSON><PERSON>", "scheduleDayOfWeek3": "Wed", "scheduleDayOfWeek4": "<PERSON>hu", "scheduleDayOfWeek5": "<PERSON><PERSON>", "scheduleDayOfWeek6": "Sat", "scheduleDayOfWeek7": "Sun"}, "ChargerConfiguration": {"configurationHeading": "Charger configuration", "tableCaption": "Charger configuration table", "editButtonAriaLabel": "Edit charger configuration", "powerBalancingEnabledLabel": "Power balancing enabled", "powerBalancingEnabledValueYes": "Yes", "powerBalancingEnabledValueNo": "No", "powerBalancingSensorLabel": "Power balancing sensor", "powerBalancingSensorArray": "Array", "powerBalancingSensorCtClamp": "CT clamp", "powerBalancingSensorLinky": "<PERSON><PERSON>", "powerBalancingSensorNone": "None", "ctMaxAmpsLabel": "Max supply at CT clamp (Amps)", "chargerRatingAmpsLabel": "Charger rating (Amps)", "lastRetrievedLabel": "Last retrieved"}, "ChargerHeader": {"copyButtonAriaLabel": "Copy {heading}", "copySuccessMessage": "Successfully copied \"{heading}\" to the clipboard"}, "ChargerIcons": {"locationIconTextCommercial": "Commercial", "locationIconTitleCommercial": "Commercial location", "locationIconTextDomestic": "Domestic", "locationIconTitleDomestic": "Domestic location", "isPublicIconText": "Public:", "isPublicIconValueYes": "Yes", "isPublicIconValueNo": "No", "confirmIconText": "Confirm:", "confirmIconValueYes": "Yes", "confirmIconValueNo": "No", "hasMidmeterEnabledIconText": "MID meter:", "hasMidmeterEnabledIconValueYes": "Yes", "hasMidmeterEnabledIconValueNo": "No", "hasTariffIconText": "Tariff assigned:", "hasTariffIconValueYes": "Yes", "hasTariffIconValueNo": "No", "hasRfidReaderIconText": "RFID reader:", "hasRfidReaderIconValueYes": "Yes", "hasRfidReaderIconValueNo": "No", "podDriveSubscriptionIconText": "Pod Drive subscription", "podDriveSubscriptionIconTitle": "Charger has a Pod Drive subscription", "hasSubscriptionIconText": "Subscription:", "hasSubscriptionIconValueYes": "Yes", "hasSubscriptionIconValueNo": "No"}, "ChargersTable": {"caption": "Table of other chargers located at the same site", "expandLabel": "Expand", "header": "Chargers (site)", "modelHeader": "Model", "nameHeader": "Name", "noChargers": "This charger has no other chargers located at the same site.", "ppidHeader": "PPID"}, "ChargingIntentsTable": {"caption": "Table of charge by times", "noChargingIntents": "This charger has no charging intents.", "shortFormDayOfWeek1": "Mon", "shortFormDayOfWeek2": "<PERSON><PERSON>", "shortFormDayOfWeek3": "Wed", "shortFormDayOfWeek4": "<PERSON><PERSON><PERSON>", "shortFormDayOfWeek5": "<PERSON><PERSON>", "shortFormDayOfWeek6": "Sat", "shortFormDayOfWeek7": "Sun", "vehicleChargeBy": "Charge by", "batteryToAdd": "Battery to add", "enodeConnectedWarning": "* Enode connected vehicles charge to the charge limit, and only fallback to the schedule below if they cannot connect."}, "DomainsTable": {"activatedOnHeader": "Date activated", "caption": "Table of domains", "expandLabel": "Expand", "domainNameHeader": "Domain name", "header": "Domains", "noDomains": "This group this charger belongs to has no domains configured."}, "DriversTable": {"caption": "Table of drivers", "emailHeader": "Email", "expandLabel": "Expand", "header": "Drivers", "noDrivers": "This group this charger belongs to has no drivers configured.", "statusHeader": "Status", "tierHeader": "Tier"}, "LinkedUsers": {"unlinkUserAriaLabel": "Unlink user {user}"}, "RfidCardsTable": {"activeHeader": "Active", "caption": "Table of RFID cards", "expandLabel": "Expand", "header": "RFID Cards", "noCards": "This charger has no RFID cards.", "referenceHeader": "Reference"}, "OtherConfigurationValues": {"header": "Other configuration values", "issueFetching": "There was an issue fetching the configuration.", "tableCaption": "Table of other configuration values"}, "Modals": {"cancelButton": "Cancel", "confirmButton": "Send", "saveChangesButton": "Save changes", "commandModalParagraph": "Are you sure you want to send command {command} to charger {ppid} (Socket {socket})?", "commandModal": "Socket {socket}", "commandModalSuccess": "Successfully sent {command} command", "commandModalSoftResetCommand": "Soft reset", "commandModalHardResetCommand": "Hard reset", "commandModalSetInServiceCommand": "Set in service", "commandModalSetOutOfServiceCommand": "Set out of service", "commandModalUnlockConnectorCommand": "Unlock connector", "commandModalError": "Error sending {command} command", "requestDiagnosticLogsErrorMessage": "Failed to request diagnostic logs", "requestDiagnosticLogsParagraph": "Request 24 hours of diagnostic logs from charger {ppid} {socket}", "requestDiagnosticLogsSocket": "Socket {socket}", "requestDiagnosticLogsStartDateLabel": "Start date", "requestDiagnosticLogsStartTimeLabel": "Start time", "requestDiagnosticLogsTitle": "Request diagnostic logs", "requestFirmwareUpdateParagraph": "Are you sure you want to send firmware update request to charger {ppid} {socket}?", "requestFirmwareUpdateSocket": "Socket {socket}", "requestFirmwareUpdateSuccess": "Successfully requested firmware update", "requestFirmwareUpdateError": "Failed to request firmware update", "requestFirmwareUpdateTitle": "Request firmware update", "retrieveLatestChargerConfigurationParagraph": "Are you sure you want to retrieve the latest configuration for charger {ppid}?", "retrieveLatestChargerConfigurationSuccess": "Successfully requested the latest configuration. It may take a few seconds to update. You may need to refresh your browser.", "retrieveLatestChargerConfigurationError": "Failed to request latest configuration", "retrieveLatestChargerConfigurationTitle": "Retrieve latest charger configuration", "setOperatorSuccess": "Successfully set the operator. It may take a few seconds to update. You may need to refresh your browser.", "setOperatorError": "Failed to set operator", "setOperatorLabel": "Operator", "setOperatorTitle": "Set operator"}, "CommandsMenu": {"title": "Commands", "softResetCommand": "Soft reset", "hardResetCommand": "Hard reset", "setInServiceCommand": "Set in service", "setOutOfServiceCommand": "Set out of service", "unlockConnectorCommand": "Unlock connector", "requestDiagnosticLogsCommand": "Request diagnostic logs", "requestFirmwareUpdateCommand": "Request firmware update", "retrieveLatestConfigurationCommand": "Retrieve latest configuration"}, "SocketSwitcher": {"socketTitle": "Socket {socket}", "optionAll": "All"}, "SolarConfiguration": {"heading": "Solar configuration", "tableCaption": "Solar configuration table", "editButtonAriaLabel": "Edit solar configuration", "pvSolarSystemInstalledLabel": "PV solar system installed", "pvSolarSystemInstalledValueYes": "Yes", "pvSolarSystemInstalledValueNo": "No", "solarMatchingEnabledLabel": "Solar matching enabled", "solarMatchingEnabledValueYes": "Yes", "solarMatchingEnabledValueNo": "No", "maxGridImportLabel": "Max grid import (Amps)"}, "Tags": {"header": "Tags", "noTags": "This charger has no tags.", "tableCaption": "Table of tags"}, "TopCardLayout": {"addressPrefix": "Address:", "backLinkLabel": "Back", "generateCommissioningCertificateLink": "Generate commissioning certificate", "groupPrefix": "Group:", "siteContactPrefix": "Site contact:", "sitePrefix": "Site:"}}, "ConfigurationPage": {"backButton": "Chargers", "chargeCurrentLimit": "Charger rating (Amps)", "chargerConfiguration": "Charger configuration", "confirmCharge": "Confirm charge", "heading": "Configuration - {ppid} (Socket {socket})", "linkySchedulesEnabled": "Linky schedules enabled", "offlineSchedulingEnabled": "Offline scheduling enabled", "penFaultMode": "Pen fault mode", "powerBalancingCurrentLimit": "Max supply at CT clamp (Amps)", "powerBalancingEnabled": "Power balancing enabled", "powerBalancingSensor": "Power balancing sensor", "powerBalancingSensorArray": "Array", "powerBalancingSensorCtClamp": "CT clamp", "powerBalancingSensorInstalled": "Power balancing installed", "powerBalancingSensorLinky": "<PERSON><PERSON>", "powerBalancingSensorNone": "None", "powerBalancingSensorPlaceholder": "Select power balancing sensor", "powerBalancingSensorPolarityInverted": "Power balancing (CT clamp) polarity inverted", "ppDevClampFaultThreshold": "CT clamp fault threshold (Amps)", "rcdBreakerSize": "EV charger RCBO rating (Amps)", "saveChangesButton": "Save changes", "selectMode": "Select mode", "selectThreshold": "Select threshold", "socketTitle": "(Socket {socket})", "solarConfiguration": "Solar configuration", "solarExportMargin": "Solar export margin (Amps)", "solarExportMarginTooltip": "An offset to the export set point for determining available power from solar generation. This is used to ensure that systems that incorporate a battery don't provide battery power to charge the EV. Should be 0 for installations without a battery system installed.", "solarMatchingEnabled": "Solar matching enabled", "solarMatchingEnabledTooltip": "Whether the surplus solar matching feature is enabled. This is labelled 'Solar charging mode' in the mobile app.", "solarMaxGridImport": "Max grid import (Amps)", "solarMaxGridImportConverted": "Max grid import (kW)", "solarMaxGridImportConvertedTooltip": "A conversion of the max grid import from amps into kW’s as displayed in the mobile app.", "solarMaxGridImportTooltip": "Maximum amount (in Amps) of electricity to be pulled from the grid in order to top-up solar generation and allow a charging to occur. When set to 6.0, the charger will charge at 1.4kW when no solar energy is being generated.", "solarStartDelay": "Solar start delay (seconds)", "solarStartDelayTooltip": "The minimum duration in seconds of consistently higher generated current required in order to increase the charging rate. Used to avoid successive alternating between charging rates.", "solarStopDelay": "Solar stop delay (seconds)", "solarStopDelayTooltip": "The minimum duration in seconds of consistently lower generated current required in order to decrease the charging rate. Used to avoid successive alternating between charging rates.", "solarSystemInstalled": "PV solar system installed", "solarSystemInstalledTooltip": "Whether the property has a PV Solar system installed (or any other electricity generation system that can export to the grid). This is labelled 'I have solar panels' in the mobile app.", "title": "Support Tool - Charger configuration", "toastError": "Error updating configuration", "toastSuccess": "Successfully updated configuration", "unlockConnectorOnEVSideDisconnect": "Should cable unlock from charger when unplugged from vehicle?"}, "DetailsPage": {"backButton": "Chargers", "cancelButton": "Cancel", "noDisplayName": "Not set", "saveButton": "Save", "saveChangesButton": "Save changes", "socketTitle": "(Socket {socket})", "title": "Support Tool - Charger"}, "DiagnosticsLogsViewerPage": {"actions": "Actions", "download": "Download", "end": "End", "heading": "Diagnostic logs - {ppid} (Socket {socket})", "openButton": "Open log viewer", "socketTitle": "(Socket {socket})", "start": "Start", "status": "Status", "tableCaption": "Table of diagnostic logs", "title": "Diagnostics log viewer", "topPageTitle": "Support Tool - Diagnostic logs", "viewerPageTitle": "Support Tool - Diagnostic Logs Viewer"}, "FlexDetailsPage": {"flexDeletedAt": "Skipped", "flexDetailsHeading": "Flex details - {ppid}", "flexDirection": "Direction", "flexProgrammeEnrollmentDate": "Enrollment date", "flexProgrammeStatus": "Programme status", "flexProgrammes": "Flex programmes", "flexProgrammesUnEnrollmentDate": "Unenrollment date", "flexRequestedAt": "Requested", "flexScheduledEnd": "Scheduled end", "flexScheduledStart": "Scheduled start", "flexStatus": "Status", "socketTitle": "(Socket {socket})", "title": "Support Tool - Flex details"}, "InstallationPage": {"heading": "Installation details - {ppid}", "headingWithSocket": "Installation details - {ppid} (Socket {socket})", "installationImages": "Installation images", "installationValues": "These are the values recorded at the point the charger was installed. They may differ from the current values and cannot be updated.", "installationDate": "Installation date", "installedBy": "Installed by", "company": "Company", "outOfService": "Out of service", "yes": "Yes", "no": "No", "householdMaxSupply": "Max supply at CT clamp (Amps)", "breakerSize": "EV charger RCBO rating (Amps)", "powerRatingPerPhase": "Charger rating (Amps)", "powerBalancingSensorInstalled": "Power balancing installed", "powerBalancingEnabled": "Power balancing enabled", "powerBalancingSensor": "Power balancing sensor", "powerBalancingSensorArray": "Array", "powerBalancingSensorCtClamp": "CT clamp", "powerBalancingSensorLinky": "<PERSON><PERSON>", "powerBalancingSensorNone": "None", "linkySchedulesEnabled": "Linky schedules enabled", "powerGenerationSystemInstalled": "PV solar system installed", "socketTitle": "(Socket {socket})", "title": "Support Tool - Installation details"}, "PcbHistoryPage": {"all": "All", "datetime": "Date/Time", "errorCode": "Error code", "failed": "Failed", "heading": "PCB history - {ppid} (Socket {socket})", "inProgress": "In progress", "serialNumber": "Serial number", "status": "Status", "success": "Success", "socketTitle": "(Socket {socket})", "tableCaption": "Table of pcb history", "title": "Support Tool - PCB history"}, "RecentChargesPage": {"heading": "Recent completed charges - {ppid}", "socketHeading": " (Socket {socket})", "downloadChargesCsvButton": "Download CSV", "pluggedInAtHeader": "Plugged in", "startedAtHeader": "Started at", "endedAtHeader": "Ended at", "unpluggedAtHeader": "Unplugged", "energyTotalHeader": "Energy delivered (kWh)", "gridEnergyTotalHeader": "Grid energy (kWh)", "generationEnergyTotalHeader": "Solar energy (kWh)", "energyCostHeader": "Energy cost", "revenueGeneratedHeader": "Revenue", "chargeDurationTotalHeader": "Charge duration", "totalDurationHeader": "Total duration", "doorHeader": "Socket", "confirmedHeader": "Confirmed", "actionsHeader": "Actions", "actionsEditEnergyTooltip": "Edit energy cost", "actionsEditEnergyAriaLabel": "Edit energy cost {chargeId}", "actionsEditRevenueCostTooltip": "Edit revenue cost", "actionsEditRevenueCostAriaLabel": "Edit revenue cost {chargeId}", "tableCaption": "Table of recent completed charges", "tableFilterPlaceholder": "All", "noDataFound": "No recent charges found", "filtersLabel": "Filters: ", "clearFilters": "Clear", "pluggedInFilterYear": "Last 12 months", "pluggedInFilterWeek": "Last 7 days", "pluggedInFilter30days": "Last 30 days", "pluggedInFilter90days": "Last 90 days", "pluggedInFilterMonth": "This month", "pluggedInFilterPeviousMonth": "Previous month", "socketTitle": "(Socket {socket})", "title": "Support Tool - Recent charges", "editRevenueCostTitle": "Edit revenue", "editRevenueCostDescription": "Amend the revenue for this charge, this is the amount the driver pays to the site for the charge. The driver’s wallet balance will not be amended as part of this change.", "editRevenueCostEnergyTotalText": "Energy delivered", "editRevenueCostInputUpdatedCost": "Updated revenue (£)", "editRevenueCostInputCost": "Price per kWh (in pence)", "editRevenueCostInputCostPlaceholder": "Enter pence per kWh", "editEnergyCostTitle": "Edit cost", "editEnergyCostDescription": "Update the energy cost for this charge.", "editEnergyCostEnergyTotalText": "Energy delivered (kWh)", "editGridEnergyCostEnergyTotalText": "Grid energy (kWh)", "editEnergyCostInputUpdatedCost": "Updated energy cost (£)", "editEnergyCostInputCost": "Pence per kWh", "editEnergyCostInputCostPlaceholder": "Enter pence per kWh", "updateCostFormSaveButton": "Save changes", "updateCostFormCancelButton": "Cancel", "chargeAlreadyExpensedError": "This charge has already been expensed and cannot be updated", "commonInternalServerError": "Something went wrong. Please try again"}, "SchedulesEditPage": {"active": "Active", "activeScheduleYes": "Yes", "activeScheduleNo": "No", "chargeScheduleDurationError": "The minimum schedule duration is 15 minutes", "chargeScheduleOverlapError": "One or more schedules overlapping. Please ensure there is at least 15 minutes between any schedules", "day": "Day", "duration": "Duration", "errorToast": "Error updating schedule", "heading": "Edit schedules - {ppid}", "headingReadOnly": "View schedules - {ppid}", "hours": "hours", "minutes": "minutes", "saveChangesButton": "Save changes", "start": "Start", "successToast": "Successfully updated schedule", "title": "Support Tool - Edit charge schedules", "titleReadOnly": "Support Tool - View charge schedules", "Monday": "Monday", "Tuesday": "Tuesday", "Wednesday": "Wednesday", "Thursday": "Thursday", "Friday": "Friday", "Saturday": "Saturday", "Sunday": "Sunday"}, "SearchPage": {"heading": "Chargers", "searchCardHeading": "Navigate to charger:", "searchFormPpidLabel": "PPID or Name", "searchFormSubmitButton": "Go", "title": "Support Tool - Chargers"}, "VehiclesPage": {"batteryCapacity": "Battery capacity", "vehicleBatteryPercentage": "Current battery level", "chargeLimit": "Charge limit", "enodeConnected": "eNode connected", "heading": "Vehicles - {ppid}", "lastSeen": "Vehicle last seen at", "lastUpdated": "Charge state last updated at", "no": "No", "powerDeliveryState": "Power delivery state", "title": "Support Tool - Vehicle", "yes": "Yes"}, "CommissioningCertificatePage": {"title": "Commissioning Certificate", "heading": "Commissioning Certificate - {ppid}", "backButton": "Back", "generatedAt": "Generated: {date}", "printButton": "Print Certificate", "downloadButton": "Download PDF", "certificateTitle": "Commissioning Certificate", "ppidLabel": "PPID number(s):", "addressLabel": "Address:", "groupLabel": "Group:", "siteLabel": "Site:", "adminEmailLabel": "Admin Email Address:", "communicatingStatement": "The unit(s) installed in this project are currently communicating", "appearOnAppStatement": "The unit(s) installed in this project are set to appear on the app", "authenticatedViaAppStatement": "The unit(s) installed in this project are set to be authenticated via the app", "paygTariffStatement": "The unit(s) installed in this project have been set a PAYG tariff", "warrantyStartDateLabel": "Warranty Start Date:", "warrantyEndDateLabel": "Warranty End Date:", "installedByLabel": "Installed by:", "installerCompanyLabel": "Installer Company:", "handoverTitle": " Handover statement confirming commissioning complete", "companyRepresented": "Company represented:", "companyName": "Pod Point ltd", "contactName": "Sales Operations Team", "contactNumber": "0207 247 4114", "companyAddress": "222 Gray's Inn Road, London, WC1X 8HB", "handoverDate": "Generated Date:", "nameLabel": "Name:", "contactNumberLabel": "Contact Number:", "yes": "Yes", "no": "No"}}, "Shared": {"UnlinkChargerModal": {"successToast": "Charger successfully unlinked from account", "errorToast": "Failed to unlink charger", "chargerPageConfirmMessage": "Are you sure you want to unlink this account from this charger?", "confirmMessage": "Are you sure you want to unlink this charger from this account?", "title": "Unlink charger", "confirmMessageParagraph": "The account holder will no longer be able to see or manage the charger in the driver app. They will have access to charge data from before the charger was unlinked.", "confirmButtonText": "Unlink charger", "cancelButtonText": "Cancel"}}, "Subscriptions": {"SearchPage": {"heading": "Subscriptions", "searchCardHeading": "Search for subscriptions:", "searchResultsOrderedAt": "Ordered", "searchResultsLink": "Open", "searchResultsStatus": "Status", "searchCardInputLabel": "Email or PPID", "searchCardInputPlaceholder": "<EMAIL>", "searchCardSubmitButton": "Go", "noSearchResults": "No subscriptions found", "title": "Support Tool - Subscriptions"}, "StatusPage": {"title": "Support Tool - Subscription status", "heading": "Pod Drive Status", "noPpidAllocated": "Charger ID: N/A"}, "SubscriptionActions": {"payUpfrontFee": "Pay upfront fee", "completeHomeSurvey": "Survey completed", "checkAffordability": "Affordability passed", "setupDirectDebit": "Direct debit set up", "signDocuments": "Legal documents signed", "chargingStationInstalled": "Charging station installed"}, "StatusBadges": {"subscriptionStatusActive": "Active", "subscriptionStatusCancelled": "Cancelled", "subscriptionStatusPending": "Pending", "subscriptionStatusSuspended": "Suspended", "subscriptionStatusRejected": "Rejected", "subscriptionStatusEnded": "Ended"}}, "WorkInProgress": {"bannerParagraph": "This page is still being worked on. Some features may not work, or not exist yet."}, "Errors": {"ctClampThresholdMinValue": "CT clamp fault threshold must be greater than or equal to 1", "ctClampThresholdMaxValue": "CT clamp fault threshold must be less than or equal to 5", "powerBalancingMaxValue": "Power balancing clamp rating must be less than or equal to 100", "powerBalancingMinValue": "Power balancing clamp rating must be greater than or equal to 0", "powerBalancingSensorValue": "Must be one of the following: <PERSON><PERSON><PERSON>, <PERSON> clamp, <PERSON><PERSON>, <PERSON>", "breakerSizeMinValue": "EV charger RCBO rating must be greater than 0", "maxCurrentRatingMinValue": "Max current rating must be greater than 0", "solarMinGridImportValue": "Grid import (amps) must be greater than or equal to 0.0", "solarMaxGridImportValue": "Grid import (amps) must be less than or equal to 6.0", "solarStartHysteresisMinValue": "Solar start hysteresis must be greater than 0", "solarStopHysteresisMinValue": "Solar stop hysteresis must be greater than 0", "solarExportMarginMinValue": "Solar export margin (amps) must be greater than or equal to 0.0", "solarExportMarginMaxValue": "Solar export margin (amps) must be less than or equal to 6.0", "penFaultModeValue": "PEN fault mode must be either High Range or Low Range", "currencyPenceError": "'Input must be a valid currency value in pence'", "priceMinValueError": "Price must be greater than £{price}", "priceMaxValueError": "Price must be less than £{price}", "priceDecimalPlacesError": "Price must be within three decimal places", "tagValueWhitespace": "Tag value must not have spaces", "tagValueLowercase": "Tag value must be lowercase", "tagInvalidKey": "Tag key is invalid", "tagInvalidValue": "Tag value for this key is invalid"}}