{"Layout": {"navigationHomeLink": "<PERSON><PERSON><PERSON>", "navigationAccountsLink": "<PERSON><PERSON><PERSON>", "navigationChargersLink": "Chargeurs", "navigationSubscriptionsLink": "Abonnements"}, "Home": {"heading": "<PERSON>il d'assistance", "accountsCardHeading": "<PERSON><PERSON><PERSON>", "accountsCardDescription": "Voir et gérer les paramètres du compte", "accountsCardLink": "Rechercher un compte", "chargersCardHeading": "Chargeurs", "chargersCardDescription": "Voir l'activité et gérer les paramètres du chargeur", "chargersCardLink": "Rechercher des chargeurs", "subscriptionsCardHeading": "Abonnements", "subscriptionsCardDescription": "Voir et gérer les abonnements à Pod Drive", "subscriptionsCardLink": "Rechercher des abonnements", "title": "<PERSON>il d'assistance"}, "Accounts": {"AccountStatusBadgeComponent": {"activeLabel": "Actif", "disabledLabel": "Désactivé", "defaultLabel": "Inconnu"}, "EmailVerificationBadgeComponent": {"verifiedLabel": "Vérifié", "unverifiedLabel": "Non vérifié"}, "SearchPage": {"heading": "<PERSON><PERSON><PERSON>", "searchCardHeading": "Rechercher un compte :", "searchCardEmailLabel": "E-mail", "searchCardSubmitButton": "<PERSON><PERSON>", "noSearchResults": "Aucun compte ne correspond à votre recherche actuelle.", "searchResultsLink": "Ouvert", "title": "<PERSON><PERSON> d'assistance - <PERSON><PERSON><PERSON>", "emailPlaceholder": "<EMAIL>"}, "DetailsPage": {"accountDetailsPageHeading": "<PERSON>é<PERSON> du compte", "actionMenuEditName": "Modifier le nom", "actionMenuEditEmailAddress": "Modifier l'adresse e-mail", "actionMenuEditRegion": "Modifier la région", "actionMenuSendPasswordRecovery": "Envoyer la récupération du mot de passe", "actionMenuDeactivateAccount": "Désactiver le compte", "actionMenuReactivateAccount": "<PERSON><PERSON><PERSON><PERSON> le compte", "actionMenuResendEmailVerification": "Renvoyer la vérification e-mail", "cancelButton": "<PERSON><PERSON><PERSON><PERSON>", "saveButton": "Enregistrer", "saveChangesButton": "Enregistrer les modifications", "noDisplayName": "Non défini", "backLinkLabel": "Précédent", "accountCreated": "Créé : {createdAt}", "accountLastSignIn": "Dernière connexion : {signedIn}", "accountStatusLabel": "Statut du compte", "emailVerificationLabel": "Vérification de l'adresse e-mail", "regionLabel": "Région", "accountBalanceLabel": "Solde du compte", "chargersTabTitle": "Chargeurs", "factorsTabTitle": "Authentification (2FA)", "activityTableTitle": "Activité", "activityTableDateHeading": "Date", "activityTableTimeHeading": "<PERSON><PERSON>", "activityTableEventHeading": "Événement", "activityTableDetailsHeading": "Détails", "activityTableStatusHeading": "Statut", "success": "Su<PERSON>ès", "failed": "Échec", "factorsTableTitle": "Facteurs", "factorsTableCountryCode": "Indicatif pays", "factorsTableLastThreeDigits": "3 derniers chiffres du numéro de téléphone", "factorsTableEnrollmentTime": "Inscrit", "chargersTableTitle": "Chargeurs", "chargersTablePpidHeading": "PPID", "chargersTableActionsHeading": "Actions", "unlinkChargerButton": "Dissocier le chargeur", "accountPaymentsTableTitle": "Paiements pour {authId}", "deactivatedAccountModalTitle": "Désactiver l'utilisateur", "deactivatedAccountModalParagraph": "Le client sera déconnecté de tous les services et ne pourra pas se reconnecter.", "deactivatedAccountModalConfirmButton": "Désactiver le compte", "deactivatedAccountSuccessMessage": "Le compte client a été désactivé - cela peut prendre quelques secondes pour que ce changement soit appliqué", "deactivatedAccountFailedMessage": "Impossible de désactiver le compte", "deactivatedAccountRefundAlertTitle": "Remboursement requis", "deactivatedAccountRefundAlertParagraph": "<PERSON><PERSON><PERSON>z vous assurer de rembourser le client car son compte est actuellement {balanceString} en crédit.", "deactivatedAccountPaymentAlertTitle": "Paiement requis", "deactivatedAccountPaymentAlertParagraph": "Le client doit recharger pour effacer le solde de son compte {balanceString} avant de fermer le compte.", "editNameSuccessMessage": "Les détails de l'utilisateur sont mis à jour, cela peut prendre quelques secondes", "editNameFailedMessage": "Impossible de mettre à jour les détails de l'utilisateur", "editNameModalTitle": "Modifier le nom", "editNameModalFirstNameLabel": "Prénom", "editNameModalLastNameLabel": "Nom de famille", "editRegionSuccessMessage": "Les détails de l'utilisateur ont été mis à jour avec succès", "editRegionFailedMessage": "Impossible de mettre à jour les détails de l'utilisateur", "editRegionFormRegionLabel": "Région", "editRegionFormRegionSelectPlaceholder": "Sélectionner une région", "editRegionModalTitle": "Modifier la région", "reactivateAccountSuccessMessage": "Le compte client a été réactivé - cela peut prendre quelques secondes pour que cette modification soit appliquée", "reactivateAccountFailedMessage": "Échec de la réactivation du compte", "reactivateAccountModalTitle": "Réactiver l'utilisateur", "reactivateAccountModalParagraph": "Le compte client sera réactivé, ce qui lui permettra de se connecter.", "reactivateAccountModalConfirmButton": "<PERSON><PERSON><PERSON><PERSON> le compte", "resendVerificationEmailSuccessMessage": "E-mail de vérification renvoyé", "resendVerificationEmailErrorMessage": "Impossible de renvoyer la vérification e-mail", "resendVerificationEmailModalTitle": "Envoyer la vérification e-mail", "resendVerificationEmailModalConfirmButton": "Envoyer la vérification e-mail", "sendPasswordResetEmailSuccessMessage": "E-mail de réinitialisation du mot de passe envoyé", "sendPasswordResetEmailErrorMessage": "Échec de l'envoi de l'e-mail de réinitialisation du mot de passe", "sendPasswordResetEmailModalTitle": "Envoyer la réinitialisation du mot de passe", "sendPasswordResetEmailModalParagraph": "Un e-mail de réinitialisation du mot de passe sera envoyé à {email}", "sendPasswordResetEmailModalConfirmButton": "Envoyer la réinitialisation du mot de passe", "sendUpdateEmailSuccessMessage": "De<PERSON><PERSON> de mise à jour d'adresse e-mail envoyée", "sendUpdateEmailDuplicateErrorMessage": "Un compte avec cette adresse e-mail existe déjà", "sendUpdateEmailErrorMessage": "Impossible d'envoyer la demande de mise à jour de l'e-mail", "sendUpdateEmailErrorModalTitle": "Modifier l'adresse e-mail", "sendUpdateEmailErrorModalParagraph": "Un e-mail sera envoyé à la nouvelle adresse e-mail pour que le client confirme le changement avant que le compte ne soit mis à jour.", "sendUpdateEmailErrorFormEmailLabel": "Adresse e-mail", "title": "<PERSON>il d'assistance - <PERSON><PERSON><PERSON> du compte", "accountActionTitle": "Actions"}}, "Chargers": {"ChargeModeHistoryPage": {"chargeNow": "Charger maintenant", "expired": "Expiré", "heading": "Historique du mode de charge - {ppid}", "manualMode": "Mode manuel", "manualStop": "Annulé par l'utilisateur ou remplacé", "overrideType": "Remplacer le type", "requested": "<PERSON><PERSON><PERSON>", "start": "<PERSON><PERSON><PERSON><PERSON>", "stop": "<PERSON><PERSON><PERSON><PERSON>", "stopReason": "<PERSON><PERSON>", "tableCaption": "Historique du mode de charge", "title": "Outil de support - Historique du mode de charge"}, "Components": {"AdminsTable": {"caption": "Tableau des administrateurs", "emailHeader": "E-mail", "expandLabel": "<PERSON><PERSON><PERSON><PERSON>", "header": "Administrateurs", "noAdmins": "Ce groupe auquel appartient cette borne de recharge n'a aucun administrateur configuré.", "lastLoginHeader": "Dernière connexion"}, "Alerts": {"chargeScheduleBannerMessage": "Cette borne est actuellement en mode manuel. Les horaires ci-dessous ne prendront pas effet.", "delegatedControlBannerMessage": "Cette borne est actuellement sous contrôle délégué. Les horaires ci-dessous sont uniquement donnés à titre indicatif et peuvent être modifiés.", "grafanaLinkMessage": " ou afficher cette borne dans l'application Grafana ", "externalOperatorBannerMessage": "Ce chargeur n'est pas géré par Pod Point. Les clients doivent contacter l'opérateur pour obtenir de l'aide. Vous pouvez rechercher ce chargeur dans MIS ", "here": "ici", "supportedArchitectureBannerMessage": "Vous pouvez rechercher ce chargeur dans MIS ", "unSupportedArchitectureBannerMessage": "Il n'est actuellement pas possible d'apporter des changements ou d'envoyer des commandes à ce chargeur. Vous pouvez rechercher ce chargeur dans MIS "}, "Badges": {"defaultFault": "Défaillance - {firstOccurrence}", "faultVendorCode": "Défaillance {vendorErrorCode} - {firstOccurrence}", "faultDescription": "Défaillance {podPointFault} : {description} - {firstOccurrence}", "faultErrorCode": "Défaillance {errorCode} - {firstOccurrence}", "outOfRangeFault": "Proximité hors limites", "reversePolarityFault": "Inverser la polarité, neutre haut, terre libre", "fuseSaverFault": "Erreur d'enregistrement de fusible/CLS différence significative entre le courant de la voiture et de la pince (pas de solaire)", "renaultSettingFault": "Défaut réglage Renault (Norvège)", "arrayFault": "Défaut module", "highVoltageFault": "Tension haute", "lowVoltageFault": "Tension basse", "pilotLowFault": "<PERSON><PERSON> faible", "a12VFault": "Défaut niveau A-12V", "cableOverTempStopFault": "CableOverTempStop", "overtemperatureFault": "Surtempérature", "overcurrentFault": "Surcourant", "hardwareDamageFault": "Dommage matériel Soudure du relais", "mennekesFault": "Défaillance de Mennekes", "rCDACFault": "RCD AC", "groundFailureFault": "Défaillanceterre", "6mAFault": "plieur détection 6mA (RCD DC)", "unlockSocketFault": "Débloquer défaillance prise", "upstreamPowerFault": "Défaut alimentation amont / brownout", "linuxUnavailableFault": "Erreur interne entre STM et Linux (Linux indisponible)", "queueOverflowFault": "Erreur interne entre STM et Linux (débordement de file d'attente)", "invalidModeFault": "Le véhicule est dans un mode invalide pour la charge (rapporté par la pile IEC)", "exceededImportLimitUnder125Fault": "Limite d'importation dépassée (entre 100-125 %) pendant 60 secondes", "exceededImportLimitOver125Fault": "Limite d'importation dépassée de 125 %", "threePhaseEarthPENFault": "Défaut PEN 3-phase (terre)", "threePhaseNeutralPENFault": "Défaut PEN 3 phases (neutre)", "threePhaseMissingPhaseFault": "Phase manquante sur unité 3 phases", "linkyConnectionFault": "Connexion perdue", "linkyDaughterBoardFault": " Carte fille liée manquante ou défectueuse", "voltagePolarityFault": "Vie et neutre échangés", "6mASelfTestFault": "Défaut auto-test RCD", "lockNotDetectedFault": "Verrouillage non détecté", "midFault": "Compteur <PERSON><PERSON> ou endommagé", "rfidNotDetectedFault": "RFID non détecté", "rfidErrorFault": "RFID présent mais dé<PERSON>", "veryHighVoltageFault": "Dé<PERSON>ut très haute tension, >270V, probablement PEN", "firmwareAvailable": "Mise à jour du firmware disponible", "firmwareUpdateNotRequested": "Mise à jour du firmware non demandée", "firmwareUpdateNotAccepted": "Mise à jour du firmware non acceptée", "firmwareUpdateInProgress": "Mise à jour du firmware en cours", "firmwareUpdateFailed": "La mise à jour du firmware a échoué"}, "Cards": {"warrantyDetailsTitle": "<PERSON><PERSON><PERSON> de la garantie", "warrantyDetailsTableTitle": "Tableau des détails de la garantie", "warrantyDetailsTableStartDateKey": "Date de début de garantie", "warrantyDetailsTableEndDateKey": "Date de fin de garantie", "warrantyDetailsTableWarrantyStatusKey": "Statut de garantie", "warrantyDetailsTableWarrantyStatusActive": "Actif", "warrantyDetailsTableWarrantyStatusInactive": "Inactif", "chargerAccessPointTitle": "Point d'accès chargeur (AP)", "chargerAccessPointHidePassword": "<PERSON><PERSON> le mot de passe", "chargerAccessPointShowPassword": "Afficher le mot de passe", "chargerAccessPointTableTitle": "Point d'accès chargeur", "chargerAccessPointTableSSIDKey": "SSID", "chargerAccessPointTablePasswordKey": "Mot de passe", "chargerConnectivityQuality0": "0", "chargerConnectivityQuality1": "1 (<PERSON><PERSON><PERSON>, RSSI {signalStrength})", "chargerConnectivityQuality2": "2 (<PERSON><PERSON><PERSON>, <PERSON><PERSON> {signalStrength})", "chargerConnectivityQuality3": "3 (<PERSON>, <PERSON><PERSON> {signalStrength})", "chargerConnectivityQuality4": "4 (<PERSON><PERSON><PERSON> bon, <PERSON><PERSON> {signalStrength})", "chargerConnectivityQuality5": "5 (Excellent, RSSI {signalStrength})", "chargerConnectivityTableChargingStatusKey": "Statut de charge", "chargerConnectivityTableConnectionQuality": "Qualité de la connexion", "chargerConnectivityTableConnectionQualityScale": "(<PERSON><PERSON><PERSON> 1-5)", "chargerConnectivityTableLastMessageKey": "<PERSON><PERSON> message", "chargerConnectivityTableLastSeenKey": "Dernière connexion démarrée", "chargerConnectivityTableMacAddress": "Adresse MAC du routeur", "chargerConnectivityTableModel": "<PERSON><PERSON><PERSON><PERSON>", "chargerConnectivityTableSerialNumber": "Numéro de série du routeur", "chargerConnectivityTableSimNumber": "Numéro de carte SIM du routeur", "chargerConnectivityTableStatusKey": "Statut", "chargerConnectivityTableTitle": "Tableau de connectivité chargeur", "chargerConnectivityTitle": "Connectivité du chargeur", "chargerConnectivityWifiStatusUnknown": "inconnu", "chargerInformationTitle": "Informations sur le chargeur", "chargerInformationViewLogsLink": "Voir les logs de diagnostic", "chargerInformationHistoryLink": "Historique", "chargerInformationTableTitle": "Tableau d'information du chargeur", "chargerInformationTableSKUKey": "Modèle SKU", "chargerInformationTableArchitectureKey": "Version Arch", "chargerInformationTableFirmwareKey": "Firmware", "chargerInformationTableManufacturedKey": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "chargerInformationTableOperatorKey": "Opérateur", "chargerInformationTableOperatorEditLabel": "Définir l'opérateur", "chargerInformationTableMACKey": "Adresse MAC", "chargerInformationTablePCBAKey": "Numéro de série PCBA", "installationDetailsTitle": "Dé<PERSON> de l'installation", "installationDetailsViewDetailsLink": "Voir les détails de l'installation", "installationDetailsTableTitle": "Tableau des détails de l'installation", "installationDetailsTableInstallDateKey": "Date d'installation", "installationDetailsTableInstalledByKey": "Installé par", "installationDetailsTableCompanyKey": "Entreprise", "lastCompleteChargeTitle": "Dernière charge complète", "lastCompleteChargeRecentCharges": "Dernières charges", "lastCompleteChargeTableTitle": "Tableau dernière charge complète", "lastCompleteChargeTableStartedAtKey": "<PERSON><PERSON><PERSON><PERSON>", "lastCompleteChargeTableEndedAtKey": "<PERSON><PERSON><PERSON><PERSON>", "lastCompleteChargeTableKWhKey": "kWh fourni", "setOperatorTooltip": "Définir l'opérateur", "tariffAllDay": "Toute la journée", "tariffsDayOfWeek1": "<PERSON><PERSON>", "tariffsDayOfWeek2": "<PERSON><PERSON>", "tariffsDayOfWeek3": "<PERSON><PERSON><PERSON><PERSON>", "tariffsDayOfWeek4": "<PERSON><PERSON>", "tariffsDayOfWeek5": "<PERSON><PERSON><PERSON><PERSON>", "tariffsDayOfWeek6": "<PERSON><PERSON>", "tariffsDayOfWeek7": "<PERSON><PERSON><PERSON>", "tariffEnergyProvider": "Fournisseur d'énergie", "tariffEffectiveFrom": "À partir de", "tariffsTitle": "Tariffs", "tariffRate": "<PERSON><PERSON><PERSON>", "tariffRateOffPeak": "Décalage de pic", "tariffRatePeak": "<PERSON><PERSON> crête", "vehicleBatteryCapacity": "Capacité de la batterie", "vehicleBatteryPercentage": "Niveau de batterie actuel", "vehicleChargeLimit": "Limite de charge", "vehicleEnodeConnected": "eNode connecté", "vehicleMakeAndModel": "Fabrication et modèle", "vehiclePluggedIn": "Branché", "vehicleTableCaption": "Charger par fois", "vehicleTitle": "Véhicule", "vehicleViewAllVehicles": "Voir les véhicules", "yes": "O<PERSON>", "no": "Non"}, "ChargeModes": {"3rdPartyOperator": "<PERSON><PERSON> partie", "active": "Actif", "candidate": "Candidat", "chargeModeHeading": "Modes", "smartModeLabel": "Mode intelligent", "smartModeSwitchToSmart": "Mode défini sur Intelligent.", "smartModeConfirmActivateModalTitle": "Activer le mode intelligent", "smartModeConfirmDeactivateModalTitle": "Désactiver le mode intelligent", "smartModeConfirmActivateModalMessage": "Êtes-vous sûr de vouloir activer le mode intelligent?", "smartModeConfirmDeactivateModalMessage": "Êtes-vous sûr de vouloir dés<PERSON>r le mode intelligent?", "smartModeModalConfirmButton": "Valider", "smartModeModalCancelButton": "Annuler", "smartModeDelegatedControlModalTitle": "Contrôle délégué", "smartModeDelegatedControlModalContent": "Le mode intelligent ne peut pas être modifié lorsque la borne est sous contrôle délégué", "smartModeDelegatedControlModalConfirmButton": "Ok", "smartModeToastError": "Erreur lors de la configuration du mode intelligent", "chargeNowLabel": "Charger maintenant", "chargeNowModalCancelButton": "Annuler", "chargeNowModalDurationErrorMessage": "{errorMessage}", "chargeNowModalHeading": "Définir la durée de charge maintenant", "chargeNowModalHoursLabel": "<PERSON><PERSON>", "chargeNowModalMinutesLabel": "Minutes", "chargeNowModalSaveChangesButton": "Enregistrer les modifications", "chargeNowSwitchToActive": "La charge est à présent définie comme active.", "chargeNowSwitchToInactive": "La charge est à présent définie comme inactive.", "chargeNowToastError": "Erreur lors de la configuration de Charger maintenant", "delegatedControlLabel": "Contrôle délégué", "delegatedControlModalConfirmButton": "Valider", "delegatedControlModalCancelButton": "Annuler", "delegatedControlModalContent": "Êtes-vous sûr de vouloir retirer le chargeur {ppid} du contrôle délégué ?", "delegatedControlModalTitle": "Retirer du contrôle délégué", "delegatedControlToastSuccess": "Chargeur n'est plus sous contrôle délégué", "delegatedControlToastError": "Erreur lors de la suppression du contrôle délégué", "flexLabel": "Flex", "flexValueEnrolled": "Inscrit", "flexValueUnenrolled": "Désinscrit", "inactive": "Inactif", "offModeLabel": "Désactiver le mode", "offModeModalActivateHeading": "Activer le mode off", "offModeModalDeactivateHeading": "Désactiver le mode", "offModeModalActivateContent": "Êtes-vous sûr de vouloir dés<PERSON>r le mode ?", "offModeModalDeactivateContent": "Êtes-vous sûr de vouloir dés<PERSON>r le mode ?", "offModeModalConfirmButton": "Valider", "offModeModalCancelButton": "Annuler", "offModeToastActivatedSuccess": "Le mode Off a été activé", "offModeToastDeactivatedSuccess": "Le mode désactivé a été désactivé", "offModeToastError": "Erreur lors de la désactivation du mode", "pending": "En attente", "since": "depuis le", "smartModeSwitchToManual": "Mode défini sur manuel.", "unknown": "Inconnu"}, "ChargeSchedules": {"chargeSchedulesHeader": "Plannings", "addSchedulesLink": "Ajouter des horaires", "editSchedulesLink": "Modifier les horaires", "viewSchedulesLink": "Afficher les horaires", "noSchedulesFoundText": "Ce chargeur peut être utilisé à tout moment, il n'y a pas de planification de charge."}, "ChargeSchedulesTable": {"startHeading": "D<PERSON>but", "caption": "Tableau des horaires et modes de charge", "durationHeading": "<PERSON><PERSON><PERSON>", "activeHeading": "Actif", "activeScheduleYes": "O<PERSON>", "activeScheduleNo": "Non", "scheduleDayOfWeek1": "<PERSON><PERSON>", "scheduleDayOfWeek2": "<PERSON><PERSON>", "scheduleDayOfWeek3": "<PERSON><PERSON><PERSON><PERSON>", "scheduleDayOfWeek4": "<PERSON><PERSON>", "scheduleDayOfWeek5": "<PERSON><PERSON><PERSON><PERSON>", "scheduleDayOfWeek6": "<PERSON><PERSON>", "scheduleDayOfWeek7": "<PERSON><PERSON><PERSON>"}, "ChargeSchedulesChart": {"scheduleDayOfWeek1": "<PERSON>n", "scheduleDayOfWeek2": "Mar", "scheduleDayOfWeek3": "<PERSON><PERSON>", "scheduleDayOfWeek4": "<PERSON><PERSON>", "scheduleDayOfWeek5": "Ven", "scheduleDayOfWeek6": "Sam", "scheduleDayOfWeek7": "<PERSON><PERSON>"}, "ChargerConfiguration": {"configurationHeading": "Configuration du chargeur", "tableCaption": "Tableau de configuration du chargeur", "editButtonAriaLabel": "Modifier la configuration du chargeur", "powerBalancingEnabledLabel": "Équilibrage de puissance activé", "powerBalancingEnabledValueYes": "O<PERSON>", "powerBalancingEnabledValueNo": "Non", "powerBalancingSensorLabel": "Capteur d'équilibrage de puissance", "powerBalancingSensorArray": "<PERSON><PERSON>", "powerBalancingSensorCtClamp": "Pince CT", "powerBalancingSensorLinky": "<PERSON><PERSON>", "powerBalancingSensorNone": "Aucun", "ctMaxAmpsLabel": "Alimentation max au tore de mesure (A)", "chargerRatingAmpsLabel": "Intensité du chargeur (A)", "lastRetrievedLabel": "Dernière récupération"}, "ChargerHeader": {"copyButtonAriaLabel": "<PERSON><PERSON><PERSON> {heading}", "copySuccessMessage": "« {heading} » copié avec succès dans le presse-papiers"}, "ChargerIcons": {"locationIconTextCommercial": "Commercial", "locationIconTitleCommercial": "Emplacement commercial", "locationIconTextDomestic": "<PERSON><PERSON><PERSON>", "locationIconTitleDomestic": "Emplacement domicile", "isPublicIconText": "Public :", "isPublicIconValueYes": "O<PERSON>", "isPublicIconValueNo": "Non", "confirmIconText": "Confirmer :", "confirmIconValueYes": "O<PERSON>", "confirmIconValueNo": "Non", "hasMidmeterEnabledIconText": "MID meter:", "hasMidmeterEnabledIconValueYes": "O<PERSON>", "hasMidmeterEnabledIconValueNo": "Non", "hasTariffIconText": "<PERSON><PERSON><PERSON> attribué :", "hasTariffIconValueYes": "O<PERSON>", "hasTariffIconValueNo": "Non", "hasRfidReaderIconText": "Lecteur RFID :", "hasRfidReaderIconValueYes": "O<PERSON>", "hasRfidReaderIconValueNo": "Non", "podDriveSubscriptionIconText": "Abonnement au Pod Drive", "podDriveSubscriptionIconTitle": "Le chargeur a un abonnement à Pod Drive", "hasSubscriptionIconText": "Subscription:", "hasSubscriptionIconValueYes": "O<PERSON>", "hasSubscriptionIconValueNo": "Non"}, "ChargersTable": {"caption": "Tableau des autres bornes de recharge situées sur le même site", "expandLabel": "<PERSON><PERSON><PERSON><PERSON>", "header": "Bornes de recharge (site)", "modelHeader": "<PERSON><PERSON><PERSON><PERSON>", "nameHeader": "Nom", "noChargers": "Cette borne de recharge n'a pas d'autres bornes de recharges situées sur le même site.", "ppidHeader": "PPID"}, "ChargingIntentsTable": {"caption": "Tableau de charge par fois", "noChargingIntents": "Ce chargeur n'a aucune intention de rechargement.", "shortFormDayOfWeek1": "<PERSON>n", "shortFormDayOfWeek2": "<PERSON><PERSON>", "shortFormDayOfWeek3": "<PERSON><PERSON>", "shortFormDayOfWeek4": "<PERSON><PERSON>", "shortFormDayOfWeek5": "Ven", "shortFormDayOfWeek6": "Sam", "shortFormDayOfWeek7": "<PERSON><PERSON>", "vehicleChargeBy": "Charger par", "batteryToAdd": "Batterie à ajouter", "enodeConnectedWarning": "* La charge des véhicules connectés à un Enode jusqu'à la limite de charge et ne peut revenir à l'horaire ci-dessous que s'ils ne peuvent pas se connecter."}, "DomainsTable": {"activatedOnHeader": "Date d'activation", "caption": "Tableau des domaines", "expandLabel": "<PERSON><PERSON><PERSON><PERSON>", "domainNameHeader": "Nom de domaine", "header": "Domaines", "noDomains": "Ce groupe auquel appartient cette borne de recharge n'a aucun domaine configuré."}, "DriversTable": {"caption": "Tableau des pilotes", "emailHeader": "E-mail", "expandLabel": "<PERSON><PERSON><PERSON><PERSON>", "header": "Pilotes", "noDrivers": "Ce groupe auquel appartient cette borne de recharge n'a aucun pilote configuré.", "statusHeader": "Statut", "tierHeader": "Palier"}, "LinkedUsers": {"unlinkUserAriaLabel": "Dissocier l'utilisateur {user}"}, "RfidCardsTable": {"activeHeader": "Actif", "caption": "Tableau des cartes RFID", "expandLabel": "<PERSON><PERSON><PERSON><PERSON>", "header": "Cartes RFID", "noCards": "Ce chargeur n'a pas de cartes RFID.", "referenceHeader": "Référence"}, "OtherConfigurationValues": {"header": "Autres valeurs de configuration", "issueFetching": "Un problème est survenu lors de la récupération de la configuration.", "tableCaption": "Tableau des autres valeurs de configuration"}, "Modals": {"cancelButton": "<PERSON><PERSON><PERSON><PERSON>", "confirmButton": "Envoyer", "saveChangesButton": "Enregistrer les modifications", "commandModalParagraph": "Êtes-vous sûr de vouloir envoyer la commande {command} au chargeur {ppid} (Prise {socket} ) ?", "commandModal": "Prise {socket}", "commandModalSuccess": "Commande {command} envoy<PERSON> avec succès", "commandModalSoftResetCommand": "Soft reset", "commandModalHardResetCommand": "Hard reset", "commandModalSetInServiceCommand": "Définir en service", "commandModalSetOutOfServiceCommand": "Établir hors service", "commandModalUnlockConnectorCommand": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> le connecteur", "commandModalError": "Erreur lors de l'envoi de la commande {command}", "requestDiagnosticLogsErrorMessage": "Impossible de demander les logs de diagnostic", "requestDiagnosticLogsParagraph": "Demander 24 heures de logs de diagnostic au chargeur {ppid} {socket}", "requestDiagnosticLogsSocket": "Prise {socket}", "requestDiagnosticLogsStartDateLabel": "Date de début", "requestDiagnosticLogsStartTimeLabel": "<PERSON><PERSON> d<PERSON>", "requestDiagnosticLogsTitle": "Demander des logs de diagnostic", "requestFirmwareUpdateParagraph": "Êtes-vous sûr de vouloir envoyer une demande de mise à jour du firmware au chargeur {ppid} {socket} ?", "requestFirmwareUpdateSocket": "Prise {socket}", "requestFirmwareUpdateSuccess": "Mise à jour du firmware demandée avec succès", "requestFirmwareUpdateError": "Impossible de demander la mise à jour du firmware", "requestFirmwareUpdateTitle": "De<PERSON>er la mise à jour du firmware", "retrieveLatestChargerConfigurationParagraph": "Êtes-vous sûr de vouloir obtenir la dernière configuration pour le chargeur {ppid} ?", "retrieveLatestChargerConfigurationSuccess": "La dernière configuration a été demandée avec succès. La mise à jour peut prendre quelques secondes. V<PERSON> devrez peut-être actualiser votre navigateur.", "retrieveLatestChargerConfigurationError": "Impossible de demander la dernière configuration", "retrieveLatestChargerConfigurationTitle": "Récupérer la dernière configuration du chargeur", "setOperatorSuccess": "Le paramétrage de l'opérateur a réussi. La mise à jour peut prendre quelques secondes. V<PERSON> devrez peut-être actualiser votre navigateur.", "setOperatorError": "Impossible de définir l'opérateur", "setOperatorLabel": "Opérateur", "setOperatorTitle": "Définir l'opérateur"}, "CommandsMenu": {"title": "Commandes", "softResetCommand": "Soft reset", "hardResetCommand": "Hard reset", "setInServiceCommand": "Définir en service", "setOutOfServiceCommand": "Définir hors service", "unlockConnectorCommand": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> le connecteur", "requestDiagnosticLogsCommand": "Demander des logs de diagnostic", "requestFirmwareUpdateCommand": "De<PERSON>er la mise à jour du firmware", "retrieveLatestConfigurationCommand": "Obtenir la dernière configuration"}, "SocketSwitcher": {"socketTitle": "Prise {socket}", "optionAll": "Tous"}, "SolarConfiguration": {"heading": "Configuration solaire", "tableCaption": "Tableau de configuration solaire", "editButtonAriaLabel": "Modifier la configuration solaire", "pvSolarSystemInstalledLabel": "Système solaire P<PERSON> installé", "pvSolarSystemInstalledValueYes": "O<PERSON>", "pvSolarSystemInstalledValueNo": "Non", "solarMatchingEnabledLabel": "Correspondance solaire activée", "solarMatchingEnabledValueYes": "O<PERSON>", "solarMatchingEnabledValueNo": "Non", "maxGridImportLabel": "Importation max du réseau (A)"}, "Tags": {"header": "Tags", "noTags": "Ce chargeur n'a pas de tags.", "tableCaption": "Tableau des tags"}, "TopCardLayout": {"addressPrefix": "<PERSON><PERSON><PERSON> :", "backLinkLabel": "Précédent", "generateCommissioningCertificateLink": "Générer un certificat de mise en service", "groupPrefix": "Groupe :", "siteContactPrefix": "Contact du site :", "sitePrefix": "Site :"}}, "ConfigurationPage": {"backButton": "Chargeurs", "chargeCurrentLimit": "Intensité du chargeur (A)", "chargerConfiguration": "Configuration du chargeur", "confirmCharge": "Confirmer la charge", "heading": "Configuration - {ppid} (Prise {socket})", "linkySchedulesEnabled": "Programmes liés activés", "offlineSchedulingEnabled": "Planification hors ligne activée", "penFaultMode": "Défaut mode pen", "powerBalancingCurrentLimit": "Alimentation max au tore de mesure (A)", "powerBalancingEnabled": "Équilibrage de puissance activé", "powerBalancingSensor": "Capteur d'équilibrage de puissance", "powerBalancingSensorArray": "<PERSON><PERSON>", "powerBalancingSensorCtClamp": "Pince CT", "powerBalancingSensorInstalled": "Équilibrage de puissance installé", "powerBalancingSensorLinky": "<PERSON><PERSON>", "powerBalancingSensorNone": "Aucun", "powerBalancingSensorPlaceholder": "Sé<PERSON><PERSON>ner le capteur d'équilibrage de puissance", "powerBalancingSensorPolarityInverted": "Polarité inversée de l'équilibrage de puissance (le tore de mesure)", "ppDevClampFaultThreshold": "<PERSON><PERSON> de défaut le tore de mesure (A)", "rcdBreakerSize": "Intensité RCBO chargeur VE (A)", "saveChangesButton": "Enregistrer les modifications", "selectMode": "Sélectionner le mode", "selectThreshold": "Sé<PERSON><PERSON><PERSON> le seuil", "socketTitle": "(Prise {socket})", "solarConfiguration": "Configuration solaire", "solarExportMargin": "Marge d'exportation solaire (A)", "solarExportMarginTooltip": "Un décalage par rapport au point d'exportation pour déterminer l'énergie disponible à partir de la production solaire. Ce processus est utilisé pour s'assurer que les systèmes à batterie ne fournissent pas de puissance pour charger le VE. Doit être de 0 pour les installations sans système de batterie installé.", "solarMatchingEnabled": "Correspondance solaire activée", "solarMatchingEnabledTooltip": "Si la fonction de correspondance solaire excédentaire est activée. Cette option est étiquetée « Mode de chargement solaire » dans l'application mobile.", "solarMaxGridImport": "Importation max du réseau (A)", "solarMaxGridImportConverted": "Importation max du réseau (kW)", "solarMaxGridImportConvertedTooltip": "Une conversion de l'importation max du réseau des ampères en kW comme affiché dans l'application mobile.", "solarMaxGridImportTooltip": "Quantité maximale (en ampères) d'électricité à extraire du réseau afin de recharger la production solaire et de permettre une charge. Lorsqu'il est fixé à 6,0, le chargeur se charge à 1,4 kW lorsqu'il ne produit pas d'énergie solaire.", "solarStartDelay": "<PERSON><PERSON><PERSON> solair<PERSON> (secondes)", "solarStartDelayTooltip": "La durée minimale en secondes du courant plus élevé généré de façon constante nécessaire pour augmenter le taux de chargement. Utilisé pour éviter une alternance successive entre les vitesses de chargement.", "solarStopDelay": "<PERSON><PERSON><PERSON> (secondes)", "solarStopDelayTooltip": "La durée minimale en secondes du courant inférieur généré de façon constante nécessaire pour diminuer le taux de chargement. Utilisé pour éviter une alternance successive entre les vitesses de chargement.", "solarSystemInstalled": "Système solaire P<PERSON> installé", "solarSystemInstalledTooltip": "Si la propriété a un système solaire PV installé (ou tout autre système de production d'électricité qui peut exporter vers le réseau). Étiqueté « J'ai des panneaux solaires » dans l'application mobile.", "title": "Outil d'assistance - Configuration de la borne de recharge", "toastError": "Erreur lors de la mise à jour de la configuration", "toastSuccess": "Configuration mise à jour avec succès", "unlockConnectorOnEVSideDisconnect": "Le câble doit-il être déverrouillé du chargeur lorsqu'il est débranché du véhicule ?"}, "DetailsPage": {"backButton": "Chargeurs", "cancelButton": "<PERSON><PERSON><PERSON><PERSON>", "noDisplayName": "Non défini", "saveButton": "Enregistrer", "saveChangesButton": "Enregistrer les modifications", "socketTitle": "(Prise {socket})", "title": "<PERSON>il d'assistance - <PERSON><PERSON> de recharge"}, "DiagnosticsLogsViewerPage": {"actions": "Actions", "download": "Télécharger", "end": "Fin", "heading": "Logs diagnostiques - {ppid} (Prise {socket})", "openButton": "<PERSON><PERSON><PERSON><PERSON><PERSON> le lecteur de logs", "socketTitle": "(Prise {socket})", "start": "D<PERSON>but", "status": "Statut", "tableCaption": "Tableau des logs de diagnostic", "title": "Lecteur de logs de diagnostics", "topPageTitle": "Outil d'assistance - Logs de diagnostic", "viewerPageTitle": "<PERSON>il d'assistance - Lecteur de logs de diagnostic"}, "FlexDetailsPage": {"flexDeletedAt": "Ignoré", "flexDetailsHeading": "Détails Flex - {ppid}", "flexDirection": "Orientation", "flexProgrammeEnrollmentDate": "Date d'inscription", "flexProgrammeStatus": "Statut du programme", "flexProgrammes": "Programmes Flex", "flexProgrammesUnEnrollmentDate": "Date de désinscription", "flexRequestedAt": "<PERSON><PERSON><PERSON>", "flexScheduledEnd": "Fin programmée", "flexScheduledStart": "Début programmé", "flexStatus": "Statut", "socketTitle": "(Prise {socket})", "title": "<PERSON>il d'assistance - <PERSON><PERSON><PERSON> F<PERSON>"}, "InstallationPage": {"heading": "<PERSON>é<PERSON> de l'installation - {ppid}", "headingWithSocket": "Détails de l'installation - {ppid} (Prise {socket})", "installationImages": "Images de l'installation", "installationValues": "Ce sont les valeurs enregistrées au moment où le chargeur a été installé. Elles peuvent différer des valeurs actuelles et ne peuvent pas être mises à jour.", "installationDate": "Date d'installation", "installedBy": "Installé par", "company": "Entreprise", "outOfService": "Hors service", "yes": "O<PERSON>", "no": "Non", "householdMaxSupply": "Alimentation max au tore de mesure (A)", "breakerSize": "Intensité RCBO chargeur VE (A)", "powerRatingPerPhase": "Intensité du chargeur (A)", "powerBalancingSensorInstalled": "Équilibrage de puissance installé", "powerBalancingEnabled": "Équilibrage de puissance activé", "powerBalancingSensor": "Capteur d'équilibrage de puissance", "powerBalancingSensorArray": "<PERSON><PERSON>", "powerBalancingSensorCtClamp": "Pince CT", "powerBalancingSensorLinky": "<PERSON><PERSON>", "powerBalancingSensorNone": "Aucun", "linkySchedulesEnabled": "Programmes liés activés", "powerGenerationSystemInstalled": "Système solaire P<PERSON> installé", "socketTitle": "(Prise {socket})", "title": "Outil d'assistance - <PERSON><PERSON><PERSON> de l'installation"}, "PcbHistoryPage": {"all": "Tous", "datetime": "Date/Heure", "errorCode": "Code d'erreur", "failed": "Échec", "heading": "Historique PCB - {ppid} (Prise {socket})", "inProgress": "En cours", "serialNumber": "Numéro de série", "status": "Statut", "success": "Su<PERSON>ès", "socketTitle": "(Prise {socket})", "tableCaption": "Tableau de l'historique de pcb", "title": "Outil d'assistance - Historique PCB"}, "RecentChargesPage": {"heading": "Dernières charges terminées - {ppid}", "socketHeading": " (Prise {socket})", "downloadChargesCsvButton": "Télécharger CSV", "pluggedInAtHeader": "Branché", "startedAtHeader": "<PERSON><PERSON><PERSON><PERSON>", "endedAtHeader": "<PERSON><PERSON><PERSON><PERSON>", "unpluggedAtHeader": "Débranché", "energyTotalHeader": "Énergie fournie (kWh)", "gridEnergyTotalHeader": "Énergie réseau (kWh)", "generationEnergyTotalHeader": "Énergie solaire (kWh)", "energyCostHeader": "Coût énergétique", "revenueGeneratedHeader": "<PERSON>en<PERSON>", "chargeDurationTotalHeader": "Du<PERSON><PERSON> de la charge", "totalDurationHeader": "Durée totale", "doorHeader": "Prise", "confirmedHeader": "<PERSON><PERSON><PERSON><PERSON>", "actionsHeader": "Actions", "actionsEditEnergyTooltip": "Modifier le coût de l'énergie", "actionsEditEnergyAriaLabel": "Modifier le coût de l'énergie {chargeId}", "actionsEditRevenueCostTooltip": "Modifier le coût des revenus", "actionsEditRevenueCostAriaLabel": "Modifier le coût des revenus {chargeId}", "tableCaption": "Tableau des charges récemment complétées", "tableFilterPlaceholder": "Tous", "noDataFound": "Aucune charge récente trouvée", "filtersLabel": "Filtres : ", "clearFilters": "<PERSON><PERSON><PERSON><PERSON>", "pluggedInFilterYear": "Les 12 derniers mois", "pluggedInFilterWeek": "Les 7 derniers jours", "pluggedInFilter30days": "Les 30 derniers jours", "pluggedInFilter90days": "Les 90 derniers jours", "pluggedInFilterMonth": "Ce mois-ci", "pluggedInFilterPeviousMonth": "<PERSON><PERSON>", "socketTitle": "(Prise {socket})", "title": "Outil d'assistance - Charges récentes", "editRevenueCostTitle": "Modifier les revenus", "editRevenueCostDescription": "Modifiez les revenus pour cette charge, c'est le montant que le conducteur paie au site pour la charge. Le solde du portefeuille du conducteur ne sera pas modifié dans le cadre de ce changement.", "editRevenueCostEnergyTotalText": "Énergie fournie", "editRevenueCostInputUpdatedCost": "Revenus mis à jour (£)", "editRevenueCostInputCost": "Prix par kWh (en pence)", "editRevenueCostInputCostPlaceholder": "Entrer les pence par kWh", "editEnergyCostTitle": "Modifier le coût", "editEnergyCostDescription": "Mettre à jour le coût de l'énergie pour cette charge.", "editEnergyCostEnergyTotalText": "Énergie fournie (kWh)", "editGridEnergyCostEnergyTotalText": "Énergie réseau (kWh)", "editEnergyCostInputUpdatedCost": "Coût énergétique mis à jour (£)", "editEnergyCostInputCost": "Pence par kWh", "editEnergyCostInputCostPlaceholder": "Entrer les pence par kWh", "updateCostFormSaveButton": "Enregistrer les modifications", "updateCostFormCancelButton": "Annuler", "chargeAlreadyExpensedError": "Cette charge a déjà été débitée et ne peut pas être mise à jour", "commonInternalServerError": "Une erreur s'est produite. Veuillez réessayer"}, "SchedulesEditPage": {"active": "Actif", "activeScheduleYes": "O<PERSON>", "activeScheduleNo": "Non", "chargeScheduleDurationError": "La durée minimale du planning est de 15 minutes", "chargeScheduleOverlapError": "Un ou plusieurs plannings se chevauchent. Veuillez vous assurer qu'il y a au moins 15 minutes entre chaque planning", "day": "Jour", "duration": "<PERSON><PERSON><PERSON>", "errorToast": "Erreur lors de la mise à jour du planning", "heading": "Modifier les horaires - {ppid}", "headingReadOnly": "Afficher les horaires - {ppid}", "hours": "heures", "minutes": "minutes", "saveChangesButton": "Enregistrer les modifications", "start": "D<PERSON>but", "successToast": "Planning mis à jour avec succès", "title": "Outil d'assistance - Modifier les plannings de charge", "titleReadOnly": "Outil d'assistance - Afficher les horaires de charge", "Monday": "<PERSON><PERSON>", "Tuesday": "<PERSON><PERSON>", "Wednesday": "<PERSON><PERSON><PERSON><PERSON>", "Thursday": "<PERSON><PERSON>", "Friday": "<PERSON><PERSON><PERSON><PERSON>", "Saturday": "<PERSON><PERSON>", "Sunday": "<PERSON><PERSON><PERSON>"}, "SearchPage": {"heading": "Chargeurs", "searchCardHeading": "Naviguer vers le chargeur :", "searchFormPpidLabel": "PPID ou Nom", "searchFormSubmitButton": "<PERSON><PERSON>", "title": "<PERSON>il d'assistance - <PERSON><PERSON> de recharge"}, "VehiclesPage": {"batteryCapacity": "Capacité de la batterie", "vehicleBatteryPercentage": "Niveau de batterie actuel", "chargeLimit": "Limite de charge", "enodeConnected": "eNode connecté", "heading": "Véhicules - {ppid}", "lastSeen": "Véhicule vu pour la dernière fois à", "lastUpdated": "Dernière mise à jour de l'état de charge à", "no": "Non", "powerDeliveryState": "État de livraison du courant", "title": "Outil de soutien - Véhicule", "yes": "O<PERSON>"}, "CommissioningCertificatePage": {"title": "Certificat de mise en service", "heading": "Certificat de mise en service - {ppid}", "backButton": "Précédent", "generatedAt": "Généré: {date}", "printButton": "Imprimer le certificat", "downloadButton": "Télécharger le PDF", "certificateTitle": "Certificat de mise en service", "ppidLabel": "Nombre(s) PPID:", "addressLabel": "<PERSON><PERSON><PERSON> :", "groupLabel": "Groupe :", "siteLabel": "Site :", "adminEmailLabel": "Adresse e-mail de l'administrateur:", "communicatingStatement": "Les unités installées dans ce projet communiquent actuellement", "appearOnAppStatement": "Les unités installées dans ce projet sont configurées pour apparaître sur l'application", "authenticatedViaAppStatement": "Les unités installées dans ce projet sont configurées pour être authentifiées via l'application", "paygTariffStatement": "Les unités installées dans ce projet ont été définies un tarif PAYG", "warrantyStartDateLabel": "Date de début de garantie :", "warrantyEndDateLabel": "Date de fin de garantie :", "installedByLabel": "Installé par:", "installerCompanyLabel": "Société d'installateur :", "handoverTitle": " <PERSON><PERSON><PERSON> de livraison confirmant la mise en service terminée", "companyRepresented": "Entreprise représentée :", "companyName": "Point de Pod ltd", "contactName": "Équipe des Opérations Ventes", "contactNumber": "0207 247 4114", "companyAddress": "222 Gray's Inn Road, Londres, WC1X 8HB", "handoverDate": "Date de génération :", "nameLabel": "Nom:", "contactNumberLabel": "Numéro de contact:", "yes": "O<PERSON>", "no": "Non"}}, "Shared": {"UnlinkChargerModal": {"successToast": "Borne de recharge dissociée du compte avec succès", "errorToast": "Échec de la dissociation de la borne de recharge", "chargerPageConfirmMessage": "Êtes-vous sûr de vouloir dissocier ce compte de cette borne de recharge ?", "confirmMessage": "Êtes-vous sûr de vouloir dissocier cette borne de recharge de ce compte ?", "title": "Dissocier la borne de recharge", "confirmMessageParagraph": "Le titulaire du compte ne sera plus en mesure de voir ou de gérer la borne de recharge dans l'application conducteur. Il aura accès aux données de charge antérieures à la déconnexion de la borne de recharge.", "confirmButtonText": "Dissocier la borne de recharge", "cancelButtonText": "Annuler"}}, "Subscriptions": {"SearchPage": {"heading": "Abonnements", "searchCardHeading": "Rechercher des abonnements:", "searchResultsOrderedAt": "Commandé", "searchResultsLink": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "searchResultsStatus": "Statut", "searchCardInputLabel": "<PERSON><PERSON><PERSON> ou PPID", "searchCardInputPlaceholder": "<EMAIL>", "searchCardSubmitButton": "Accéder", "noSearchResults": "Aucun abonnement trouvé", "title": "Outil de support - Abonnements"}, "StatusPage": {"title": "Outil de support - Statut de l'abonnement", "heading": "Statut du lecteur de pod", "noPpidAllocated": "ID du chargeur : N/A"}, "SubscriptionActions": {"payUpfrontFee": "Payer les frais d'entrée", "completeHomeSurvey": "<PERSON><PERSON><PERSON><PERSON><PERSON> terminée", "checkAffordability": "L'accessibilité est passée", "setupDirectDebit": "Configuration du débit direct", "signDocuments": "Documents légaux signés", "chargingStationInstalled": "Station de chargement installée"}, "StatusBadges": {"subscriptionStatusActive": "Actif", "subscriptionStatusCancelled": "<PERSON><PERSON><PERSON>", "subscriptionStatusPending": "En attente", "subscriptionStatusSuspended": "Suspendu", "subscriptionStatusRejected": "<PERSON><PERSON><PERSON>", "subscriptionStatusEnded": "<PERSON><PERSON><PERSON><PERSON>"}}, "WorkInProgress": {"bannerParagraph": "Cette page est toujours en cours de création. Certaines fonctionnalités peuvent ne pas fonctionner, ou n'existent pas encore."}, "Errors": {"ctClampThresholdMinValue": "<PERSON>uil de défaut le tore de mesure (A) doit être supérieur ou égal à 1", "ctClampThresholdMaxValue": "<PERSON><PERSON> de défaut le tore de mesure (A) doit être inférieur ou égal à 5", "powerBalancingMaxValue": "Les données du tore d'équilibrage de puissance doivent être inférieures ou égales à 100", "powerBalancingMinValue": "Les données du tore d'équilibrage de puissance doivent être supérieures ou égales à 0", "powerBalancingSensorValue": "Doit être l'un des éléments suivants : <PERSON>au, CT clamp, <PERSON><PERSON>, <PERSON><PERSON>n", "breakerSizeMinValue": "L'intensité du disjoncteur de la borne de recharge EV doit être supérieure à 0", "maxCurrentRatingMinValue": "L'intensité maximale doit être supérieure à 0", "solarMinGridImportValue": "L'importation réseau (A) doit être supérieure ou égale à 0,0", "solarMaxGridImportValue": "L'importation réseau (A) doit être inférieure ou égal à 6,0", "solarStartHysteresisMinValue": "L'hystérésis de démarrage solaire doit être supérieure à 0", "solarStopHysteresisMinValue": "L'hystérésis d'arrêt solaire doit être supérieure à 0", "solarExportMarginMinValue": "La marge d'exportation solaire (A) doit être supérieure ou égale à 0,0", "solarExportMarginMaxValue": "La marge d'exportation solaire (A) doit être inférieure ou égale à 6,0", "penFaultModeValue": "Le mode de défaut PEN doit être soit en intensité haute soit en intensité basse", "currencyPenceError": "'L'entrée doit être une valeur de devise valide en pence'", "priceMinValueError": "Le prix doit être supérieur à {price} £", "priceMaxValueError": "Le prix doit être inférieur à {price} £", "priceDecimalPlacesError": "Le prix doit contenir moins de trois décimales", "tagValueWhitespace": "La valeur du tag ne doit pas avoir d'espaces", "tagValueLowercase": "La valeur du tag doit être en minuscules", "tagInvalidKey": "La clé du tag est invalide", "tagInvalidValue": "La valeur du tag pour cette clé n'est pas valide"}}