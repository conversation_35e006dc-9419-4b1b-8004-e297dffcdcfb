-- name: CreatePodadminCharge :exec
INSERT INTO charges (id, location_id, unit_id, door, energy_cost, billing_event_id, starts_at, ends_at, kwh_used, duration,
                            is_closed, billing_account_id, claimed_charge_id)
VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?);

-- name: CreatePodadminChargeDefault :exec
INSERT INTO charges (location_id, unit_id, door, energy_cost, billing_event_id, starts_at, ends_at, kwh_used, duration, is_closed, billing_account_id, claimed_charge_id)
VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?);

-- name: DeletePodadminCharge :exec
DELETE
FROM charges
WHERE id = ?;

-- name: DeletePodadminCharges :exec
DELETE FROM charges
WHERE id IN (sqlc.slice('ids'));

-- name: DeletePodadminChargesInIDRange :exec
DELETE
FROM charges
WHERE id BETWEEN ? AND ?;

-- name: CreatePodadminUnit :exec
INSERT IGNORE INTO pod_units (id, ppid, model_id, status_id, relay_weld_flag)
VALUES (?, ?, 1, 1, 0);

-- name: CreatePodadminUnitDefault :exec
INSERT IGNORE INTO pod_units (ppid, model_id, status_id, relay_weld_flag)
VALUES (?, 1, 1, 0);

-- name: DeletePodadminUnit :exec
DELETE
FROM pod_units
WHERE id = ?;

-- name: DeletePodadminUnitsInIDRange :exec
DELETE
FROM pod_units
WHERE id BETWEEN ? AND ?;

-- name: DeletePodadminUnits :exec
DELETE FROM pod_units
WHERE id IN (sqlc.slice('ids'));

-- name: CreatePodadminGroup :exec
INSERT INTO `groups` (id, uid, type_id, owner_user_id, name, contact_name, phone, business_name, line_1, line_2, postal_town, postcode, country, fee_percentage, created_at, updated_at, deleted_at)
VALUES (?, ?, 1, 6846, 'Pod Point - Testing', 'N A', '+44(0)20 7247 4114', 'Pod Point - Software Team', 'Floor 4, Cityside House', '40 Adler Street, Aldgate East', 'London', 'E1 1EE', 'GB', 0.00, '2010-07-29 13:00:00', '2023-01-10 14:29:36', NULL);

-- name: CreatePodadminGroupDefault :exec
INSERT INTO `groups` (uid, type_id, owner_user_id, name, contact_name, phone, business_name, line_1, line_2, postal_town, postcode, country, fee_percentage, created_at, updated_at, deleted_at)
VALUES (?, 1, 6846, 'Pod Point - Testing', 'N A', '+44(0)20 7247 4114', 'Pod Point - Software Team', 'Floor 4, Cityside House', '40 Adler Street, Aldgate East', 'London', 'E1 1EE', 'GB', 0.00, '2010-07-29 13:00:00', '2023-01-10 14:29:36', NULL);

-- name: DeletePodadminGroup :exec
DELETE
FROM `groups`
WHERE id = ?;

-- name: DeletePodadminGroupsInIDRange :exec
DELETE
FROM `groups`
WHERE id BETWEEN ? AND ?;

-- name: DeletePodadminGroups :exec
DELETE FROM `groups`
WHERE id IN (sqlc.slice('ids'));

-- name: GetLastInsertId :one
SELECT LAST_INSERT_ID();

-- name: CreatePodadminAddress :exec
INSERT INTO pod_addresses (id, business_name, line_1, line_2,
                           postal_town, postcode, country, description, type_id, deleted_at, group_id)
VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?);

-- name: DeletePodadminAddresses :exec
DELETE FROM pod_addresses
WHERE id IN (sqlc.slice('ids'));

-- name: CreatePodadminAddressType :exec
INSERT INTO pod_address_types(id, name, code, on_roadside, has_restrictions, created_at, updated_at, deleted_at)
VALUES(?, 'Workplace', 20, 0, 1, '2015-04-07 09:23:03', '2015-04-07 09:23:03', NULL);

-- name: DeletePodadminAddressTypes :exec
DELETE FROM pod_address_types
WHERE id IN (sqlc.slice('ids'));

-- name: CreatePodadminAuthoriser :exec
INSERT INTO authorisers (id, uid, TYPE, deleted_at)
VALUES (?, ?, ?, ?);

-- name: CreatePodadminAuthoriserDefault :exec
INSERT INTO authorisers (uid, TYPE, deleted_at)
VALUES (?, ?, ?);

-- name: CreatePodadminAuthoriserWithGroup :exec
INSERT INTO authorisers (id, uid, TYPE, group_uid, deleted_at)
VALUES (?, ?, ?, ?, ?);

-- name: DeletePodadminAuthorisersInIDRange :exec
DELETE FROM authorisers
WHERE id BETWEEN ? AND ?;

-- name: DeletePodadminAuthorisers :exec
DELETE FROM authorisers
WHERE id IN (sqlc.slice('ids'));

-- name: CreatePodadminBillingAccount :exec
INSERT INTO billing_accounts (id,
                              user_id,
                              uid,
                              balance,
                              currency,
                              business_name,
                              line_1,
                              line_2,
                              postal_town,
                              postcode,
                              country,
                              phone,
                              mobile,
                              payment_processor_id)
VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?);

-- name: CreatePodadminBillingEvent :exec
INSERT INTO billing_events (id,
                            account_id,
                            presentment_amount,
                            presentment_currency,
                            exchange_rate,
                            settlement_amount,
                            settlement_currency,
                            amount,
                            description,
                            refunded_amount)
VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?);

-- name: RetrievePodadminBillingEvent :one
SELECT *
FROM billing_events
WHERE id = ?;

-- name: DeletePodadminBillingEvent :exec
DELETE FROM billing_events
WHERE id = ?;

-- name: CreatePodadminBillingAccountDefault :exec
INSERT INTO billing_accounts (user_id, uid, balance,
                              currency,
                              business_name,
                              line_1,
                              line_2,
                              postal_town,
                              postcode,
                              country,
                              phone,
                              mobile,
                              payment_processor_id)
VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?);

-- name: DeletePodadminBillingAccountsInIDRange :exec
DELETE FROM billing_accounts
WHERE id BETWEEN ? AND ?;

-- name: DeletePodadminBillingAccounts :exec
DELETE FROM `billing_accounts`
WHERE id IN (sqlc.slice('ids'));

-- name: CreateEvDriver :exec
INSERT INTO ev_drivers (id, email, group_id, first_name, last_name)
VALUES (?, ?, ?, ?, ?);

-- name: CreatePodadminLocation :exec
INSERT INTO pod_locations (id, uuid, address_id, longitude, latitude, geohash, description, payg_enabled,
                           contactless_enabled, midmeter_enabled, is_public, is_home, is_ev_zone, unit_id,
                           deleted_at, revenue_profile_id)
VALUES (?, ?, ?, ?, ?, ?, ' ', 0, 0, 0, ?, ?, 0, ?, ?, ?);

-- name: DeletePodadminLocations :exec
DELETE FROM pod_locations
WHERE id IN (sqlc.slice('ids'));

-- name: CreateMember :exec
INSERT INTO members (id, email, group_id, first_name, last_name)
VALUES (?, ?, ?, ?, ?);

-- name: CreateOrganisation :exec
INSERT INTO `groups` (id, uid, type_id, name, contact_name, phone, business_name, line_1, line_2, postal_town,
                      postcode, country, fee_percentage, created_at)
VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?);

-- name: CreatePodadminRole :exec
INSERT INTO roles (id)
VALUES (?);

-- name: CreatePodadminRoleDefault :exec
INSERT INTO roles ()
VALUES ();

-- name: DeletePodadminRolesInIDRange :exec
DELETE FROM roles
WHERE id BETWEEN ? AND ?;

-- name: DeletePodadminRoles :exec
DELETE FROM roles
WHERE id IN (sqlc.slice('ids'));

-- name: CreateModel :exec
INSERT INTO pod_models(id, vendor_id, range_id, pcb_configuration_id, name, supports_payg, supports_ocpp, supports_contactless, supports_simultaneous_dc_charging, image_url, created_at, updated_at, deleted_at)
VALUES(?, 1, 1, NULL, ?, 1, 0, 0, 0, NULL, '2015-04-07 09:23:03', '2015-10-14 15:01:09', NULL);

-- name: CreateUnit :exec
INSERT INTO pod_units (id, ppid, NAME, model_id, status_id, relay_weld_flag, deleted_at)
VALUES (?, ?, 'Kent-Jake', ?, 1, 0, ?);

-- name: CreatePodadminUser :exec
INSERT INTO users (id, auth_id, group_id, role_id, email, salutation, first_name, last_name, password,
                   created_at, deleted_at, is_emailed_usage_data)
VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, 0);

-- name: CreatePodadminUserDefault :exec
INSERT INTO users (auth_id, group_id, role_id, email, salutation, first_name, last_name, password,
                   created_at, deleted_at, is_emailed_usage_data)
VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, 0);

-- name: DeletePodadminUsersInIDRange :exec
DELETE FROM users
WHERE id BETWEEN ? AND ?;

-- name: DeletePodadminUsers :exec
DELETE FROM `users`
WHERE id IN (sqlc.slice('ids'));

-- name: RetrievePodAdminCharge :one
SELECT * FROM charges c WHERE c.id = ?;

-- name: RetrievePodAdminBillingEventByCharge :one
SELECT billingevent.id,
       billingevent.account_id,
       billingevent.presentment_amount,
       billingevent.presentment_currency,
       billingevent.exchange_rate,
       billingevent.settlement_amount,
       billingevent.settlement_currency,
       billingevent.amount,
       billingevent.transaction_provider,
       billingevent.transaction_id,
       billingevent.description,
       billingevent.refunded_amount
FROM   charges charge
       INNER JOIN billing_events billingevent
               ON billingevent.id = charge.billing_event_id
WHERE  charge.id = ?;

-- name: DeletePodadminBillingEvents :exec
DELETE FROM billing_events
WHERE  id IN ( sqlc.Slice('ids') );

-- name: CreatePodadminBillingEventDefault :exec
INSERT INTO billing_events
            (amount,
             presentment_amount,
             settlement_amount,
             created_at,
             settlement_currency,
             presentment_currency,
             refunded_amount,
             exchange_rate)
VALUES      (?,
             ?,
             ?,
             NOW(),
             'GBP',
             'GBP',
             0,
             "1");

-- name: DeletePodadminBillingEventsInIDRange :exec
DELETE FROM billing_events
WHERE  id BETWEEN ? AND ?;

-- name: DeletePodadminVendors :exec
DELETE FROM pod_vendors
WHERE  id IN ( sqlc.Slice('ids') );

-- name: DeletePodadminVendorsInIDRange :exec
DELETE FROM pod_vendors
WHERE  id BETWEEN ? AND ?;

-- name: DeletePodadminModels :exec
DELETE FROM pod_models
WHERE  id IN ( sqlc.Slice('ids') );

-- name: DeletePodadminModelsInIDRange :exec
DELETE FROM pod_models
WHERE  id BETWEEN ? AND ?;

-- name: GetPodadminClaimedCharge :one
SELECT *
FROM claimed_charges
WHERE id = ?;

-- name: CreatePodadminDoor :exec
INSERT INTO pod_doors (id, name, created_at, updated_at, deleted_at)
VALUES (?, ?, ?, ?, ?);

-- name: CreatePodadminGroupBillingAccount :exec
INSERT INTO group_billing_accounts (id, group_id, billing_account_id)
VALUES (?, ?, ?);

-- name: DeleteGroupBillingAccountsInIDRange :exec
DELETE FROM group_billing_accounts
WHERE id BETWEEN ? AND ?;

-- name: DeletePodadminGroupBillingAccounts :exec
DELETE FROM group_billing_accounts
WHERE id IN ( sqlc.Slice('ids') );

-- name: DeleteClaimedChargesWithAuthoriserInIDRange :exec
DELETE FROM claimed_charges
WHERE authoriser_id BETWEEN ? AND ?;

-- name: DeletePodAddressesInIDRange :exec
DELETE FROM pod_addresses
WHERE id BETWEEN ? AND ?;

-- name: DeletePodLocationsInIDRange :exec
DELETE FROM pod_locations
WHERE id BETWEEN ? AND ?;

-- name: DeletePodadminDoors :exec
DELETE FROM pod_doors
WHERE id IN ( sqlc.Slice('ids') );

-- name: DeletePodadminClaimedChargesWithAuthoriser :exec
DELETE FROM claimed_charges
WHERE authoriser_id IN ( sqlc.Slice('ids') );

-- name: RetrievePodadminClaimedChargesByAuthoriser :one
SELECT * FROM claimed_charges
WHERE authoriser_id = ?;

-- name: CreatePodadminGroupBillingAccountDefault :exec
INSERT INTO group_billing_accounts (group_id, billing_account_id)
VALUES (?, ?);

-- name: CreatePodadminAddressDefault :exec
INSERT INTO pod_addresses (group_id, business_name, line_1, line_2, postal_town, postcode, country, description, type_id, tariff_id)
VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?);

-- name: CreatePodadminLocationDefault :exec
INSERT INTO pod_locations (uuid, address_id, longitude, latitude, description, payg_enabled, contactless_enabled, midmeter_enabled, is_public, is_home, is_ev_zone, unit_id)
VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?);

-- name: CreatePodadminAuthoriserWithGroupDefault :exec
INSERT INTO authorisers (uid, type, group_uid)
VALUES (?, ?, ?);

-- name: CreatePodadminTariffDefault :exec
INSERT INTO tariffs (currency, name)
VALUES (?, ?);

-- name: CreatePodadminTariffTierDefault :exec
INSERT INTO tariff_tiers (tariff_id, rate)
VALUES (?, ?);

-- name: DeletePodadminTariffs :exec
DELETE FROM tariffs
WHERE id IN (sqlc.slice('ids'));

-- name: DeletePodadminTariffTiers :exec
DELETE FROM tariff_tiers
WHERE id IN (sqlc.slice('ids'));

-- name: RetrievePodadminBillingAccount :one
SELECT *
FROM billing_accounts
WHERE id = ?;

-- name: CreatePodAdminClaimedCharge :exec
INSERT INTO claimed_charges
            (id,
             pod_location_id,
             pod_door_id,
             created_at,
             updated_at,
             authoriser_id,
             billing_account_id)
VALUES      (?,
             ?,
             ?,
             NOW(),
             NOW(),
             ?,
             ?);

-- name: CreatePodadminClaimedChargeDefault :exec
INSERT INTO claimed_charges
            (pod_location_id,
             pod_door_id,
             created_at,
             updated_at,
             authoriser_id,
             billing_account_id)
VALUES      (?, ?, NOW(), NOW(), ?, ?);

-- name: RetrievePodadminClaimedChargeByBillingEventID :one
SELECT *
FROM claimed_charges
WHERE billing_event_id = ?;

-- name: DeletePodadminClaimedChargesWithBillingEvent :exec
DELETE FROM claimed_charges
WHERE billing_event_id IN ( sqlc.Slice('ids') );
