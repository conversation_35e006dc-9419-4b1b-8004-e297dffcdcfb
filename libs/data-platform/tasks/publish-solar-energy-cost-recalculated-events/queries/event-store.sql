-- name: CreateSolarEnergyCostRecalculatedEvent :one
INSERT INTO event_store.solar_energy_cost_recalculated_historic (data, processed_at, unplugged_at) VALUES (sqlc.arg(data), sqlc.arg(processed_at), sqlc.arg(unplugged_at)::timestamp)
RETURNING id;

-- name: RetrieveSolarEnergyCostRecalculatedEventByID :one
SELECT *
FROM event_store.solar_energy_cost_recalculated_historic
WHERE id = sqlc.arg(id);

-- name: RetrieveSolarEnergyCostRecalculatedEventsInDateRange :many
SELECT id, data
FROM event_store.solar_energy_cost_recalculated_historic
WHERE processed_at is null
AND unplugged_at between sqlc.arg(from_date)::timestamp and sqlc.arg(to_date)::timestamp
ORDER BY unplugged_at DESC
LIMIT sqlc.arg(chunk_size);

-- name: MarkSolarEnergyCostRecalculatedEventAsProcessed :exec
UPDATE event_store.solar_energy_cost_recalculated_historic
SET processed_at = sqlc.arg(processed_at)::timestamp
WHERE id = $1;

-- name: MarkSolarEnergyCostRecalculatedEventsAsProcessed :exec
UPDATE event_store.solar_energy_cost_recalculated_historic
SET processed_at = sqlc.arg(processed_at)::timestamp
WHERE ID = ANY (@ids::bigint[]);
