package energycost

import (
	"encoding/json"
	"errors"
	costcalculation "experience/libs/data-platform/cost-calculation"
	energyMetricsAPI "experience/libs/shared/go/external/energy-metrics-client/src"
	"net/http"
	"net/http/httptest"
	"testing"
	"time"

	"github.com/stretchr/testify/require"
)

func TestEnergyMetricsAPI_GetChargeEnergyMetrics(t *testing.T) {
	tests := []struct {
		name       string
		statusCode int
		response   func() *energyMetricsAPI.HalfHourlyEnergyMetricResponseDto
		want       []costcalculation.ChargeInterval
		wantErr    bool
		err        error
		fromDate   time.Time
		toDate     time.Time
		adaptor    EnergyMetricsAPIToChargeIntervalAdapter
	}{
		{
			name:       "status OK",
			statusCode: http.StatusOK,
			response:   energyMetricsAPI.NewHalfHourlyEnergyMetricResponseDtoWithDefaults,
			want:       []costcalculation.ChargeInterval(nil),
			fromDate:   time.Date(2020, time.May, 1, 0, 0, 0, 0, time.UTC),
			toDate:     time.Date(2020, time.May, 1, 0, 0, 0, 0, time.UTC),
		},
		{
			name:       "status not found",
			statusCode: http.StatusNotFound,
			response:   energyMetricsAPI.NewHalfHourlyEnergyMetricResponseDtoWithDefaults,
			fromDate:   time.Date(2020, time.May, 1, 0, 0, 0, 0, time.UTC),
			toDate:     time.Date(2020, time.May, 1, 0, 0, 0, 0, time.UTC),
			wantErr:    true,
			err:        errors.New("get charge energy metrics by ppid: PP-12345, evseID: EVSE-67890, chargeID: CHARGE-12345, error: 404 Not Found"),
		},
		{
			name:       "error from server",
			statusCode: http.StatusInternalServerError,
			response:   func() *energyMetricsAPI.HalfHourlyEnergyMetricResponseDto { return nil },
			fromDate:   time.Date(2020, time.May, 1, 0, 0, 0, 0, time.UTC),
			toDate:     time.Date(2020, time.May, 1, 0, 0, 0, 0, time.UTC),
			wantErr:    true,
			err:        errors.New("get charge energy metrics by ppid: PP-12345, evseID: EVSE-67890, chargeID: CHARGE-12345, error: 500 Internal Server Error"),
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			testServer := httptest.NewServer(http.HandlerFunc(func(res http.ResponseWriter, req *http.Request) {
				res.Header().Set("Content-Type", "application/json")
				res.WriteHeader(tt.statusCode)
				require.NotEmpty(t, req.URL.Query().Get("from"))
				require.NotEmpty(t, req.URL.Query().Get("to"))
				bytes, err := json.Marshal(tt.response())
				require.NoError(t, err)
				_, _ = res.Write(bytes)
			}))
			defer testServer.Close()

			client, err := energyMetricsAPI.NewChargeMetricsClient(true, testServer.URL)
			require.NoError(t, err)

			service := NewEnergyMetricsService(client, tt.adaptor)
			ppid := "PP-12345"
			evseID := "EVSE-67890"
			chargeID := "CHARGE-12345"

			got, err := service.GetChargeEnergyMetrics(t.Context(), ppid, evseID, chargeID, tt.fromDate, tt.toDate)
			if tt.wantErr {
				require.Error(t, err)
				require.Equal(t, tt.err.Error(), err.Error())
				return
			}
			require.Equal(t, tt.want, got)
		})
	}
}
