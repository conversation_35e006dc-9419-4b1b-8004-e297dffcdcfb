package energycost

import (
	"context"
	costcalculation "experience/libs/data-platform/cost-calculation"
	energyMetricsAPI "experience/libs/shared/go/external/energy-metrics-client/src"
	"fmt"
	"net/http"
	"time"
)

type EnergyMetricsService struct {
	client  energyMetricsAPI.ChargeMetricsAPI
	adapter EnergyMetricsAPIToChargeIntervalAdapter
}

func NewEnergyMetricsService(client energyMetricsAPI.ChargeMetricsAPI, adapter EnergyMetricsAPIToChargeIntervalAdapter) EnergyMetricsService {
	return EnergyMetricsService{
		client:  client,
		adapter: adapter,
	}
}

func (s EnergyMetricsService) GetChargeEnergyMetrics(ctx context.Context, ppid, evseID, chargeID string, fromDate, toDate time.Time) ([]costcalculation.ChargeInterval, error) {
	payload, response, err := s.client.GetChargeEnergyMetrics(ctx, ppid, evseID, chargeID).
		From(fromDate.String()).
		To(toDate.String()).
		Execute()
	defer func(response *http.Response) {
		if response != nil && response.Body != nil {
			_ = response.Body.Close()
		}
	}(response)

	if response != nil {
		if response.StatusCode >= 500 && response.StatusCode < 600 {
			return nil, &ServerError{context: "get charge energy metrics", ppid: ppid, evseID: evseID, chargeID: chargeID, error: err}
		}
		if response.StatusCode >= 400 && response.StatusCode < 500 {
			return nil, &ClientError{context: "get charge energy metrics", ppid: ppid, evseID: evseID, chargeID: chargeID, error: err}
		}
	}
	if err != nil {
		return nil, &ServerError{context: "get charge energy metrics", ppid: ppid, evseID: evseID, chargeID: chargeID, error: err}
	}

	intervals := make([]costcalculation.ChargeInterval, 0, len(payload.Metrics))
	for i := range payload.Metrics {
		ci, convErr := s.adapter(&payload.Metrics[i])
		if convErr != nil {
			return nil, fmt.Errorf("convert metric[%d] for ppid %s: %w", i, ppid, convErr)
		}
		intervals = append(intervals, *ci)
	}

	return intervals, nil
}
