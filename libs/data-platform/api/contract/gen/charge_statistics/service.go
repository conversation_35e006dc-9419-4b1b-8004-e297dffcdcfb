// Code generated by goa v3.20.1, DO NOT EDIT.
//
// Charge Statistics service
//
// Command:
// $ goa gen experience/libs/data-platform/api/contract/design

package chargestatistics

import (
	"context"

	goa "goa.design/goa/v3/pkg"
)

// Retrieve aggregated charge data.
type Service interface {
	// Charge statistics for a group from projections.<br><br>The from and to
	// attributes are used to filter on the unpluggedAt field. Both the from and to
	// fields are inclusive.<br><br>Predecessor: /charges/group/{organisationId}
	GroupChargeStatisticsEndpoint(context.Context, *GroupChargeStatisticsPayload) (res *ProjectiongroupChargeStatisticsResponse, err error)
	// Charge statistics for a site from projections.<br><br>The from and to
	// attributes are used to filter on the unpluggedAt field. Both the from and to
	// fields are inclusive.<br><br>Predecessor: /charges/site/{siteId}
	SiteChargeStatisticsEndpoint(context.Context, *SiteChargeStatisticsPayload) (res *ProjectionsiteChargeStatisticsResponse, err error)
	// Charge statistics for a charger, derived from projections.<br><br>The from
	// and to attributes are used to filter on the unpluggedAt field. Both the from
	// and to fields are inclusive.<br><br>Predecessor:
	// /charges/charger/{locationId}
	ChargerChargeStatisticsEndpoint(context.Context, *ChargerChargeStatisticsPayload) (res *ProjectionchargerChargeStatisticsResponse, err error)
	// Usage summaries for all sites within the group, grouped by the given
	// interval.<br><br>The from and to attributes are used to filter on the
	// unpluggedAt field. Both the from and to fields are
	// inclusive.<br><br>Predecessor: /organisations/{organisationId}/stats
	GroupUsageSummaries(context.Context, *GroupUsageSummariesPayload) (res *UsageResponse, err error)
	// Usage summaries for a site within the group, grouped by the given
	// interval.<br><br>The from and to attributes are used to filter on the
	// unpluggedAt field. Both the from and to fields are
	// inclusive.<br><br>Predecessor:
	// /organisations/{organisationId}/sites/{siteId}/stats
	GroupAndSiteUsageSummaries(context.Context, *GroupAndSiteUsageSummariesPayload) (res *UsageResponse, err error)
	// Usage summaries for a charger within a group, sorted by the given
	// interval.<br><br>The from and to attributes are used to filter on the
	// unpluggedAt field. Both the from and to fields are
	// inclusive.<br><br>Predecessor:
	// /organisations/{organisationId}/chargers/{locationId}/stats
	GroupAndChargerUsageSummaries(context.Context, *GroupAndChargerUsageSummariesPayload) (res *UsageResponse, err error)
}

// APIName is the name of the API as defined in the design.
const APIName = "Data Platform"

// APIVersion is the version of the API as defined in the design.
const APIVersion = "0.0.1"

// ServiceName is the name of the service as defined in the design. This is the
// same value that is set in the endpoint request contexts under the ServiceKey
// key.
const ServiceName = "Charge Statistics"

// MethodNames lists the service method names as defined in the design. These
// are the same values that are set in the endpoint request contexts under the
// MethodKey key.
var MethodNames = [6]string{"Group Charge Statistics", "Site Charge Statistics", "Charger Charge Statistics", "Group Usage Summaries", "Group and Site Usage Summaries", "Group and Charger Usage Summaries"}

type ChargerChargeStatistics struct {
	// sum of charging duration in seconds
	ChargingDuration int
	// CO2 saved in kg = (Average ICE CO2 emission per mile - Average EV CO2
	// emission per mile) x Total Energy Used in kWh x Average EV mileage per 1 kWh
	Co2Savings float64
	Energy     *EnergyStatistics
	// count of distinct charge uuids
	NumberOfCharges int
	// count of distinct user ids
	NumberOfUsers int
	// sum of settlement amount in pence
	RevenueGenerated int
}

// ChargerChargeStatisticsPayload is the payload type of the Charge Statistics
// service Charger Charge Statistics method.
type ChargerChargeStatisticsPayload struct {
	// ID of the charger (equivalent to PPID).
	ChargerID string
	// Inclusive from date used for filtering on unpluggedAt
	From string
	// Inclusive to date used for filtering on unpluggedAt
	To string
}

type EnergyStatistics struct {
	// Energy used this period in kWh.
	TotalUsage float64
	// Energy used this period in kWh, filtering out any unclaimed charges.
	ClaimedUsage float64
	// Energy used this period in kWh by claimed charges with a positive revenue.
	RevenueGeneratingClaimedUsage float64
	// Energy used this period in kWh, filtering out any claimed charges.
	UnclaimedUsage float64
	// Energy cost in pence.
	Cost int
}

// GroupAndChargerUsageSummariesPayload is the payload type of the Charge
// Statistics service Group and Charger Usage Summaries method.
type GroupAndChargerUsageSummariesPayload struct {
	// UUID of the group.
	GroupID string
	// ID of the charger (equivalent to PPID).
	ChargerID string
	// Reporting interval
	Interval string
	// Inclusive from date used for filtering on unpluggedAt
	From string
	// Inclusive to date used for filtering on unpluggedAt
	To string
}

// GroupAndSiteUsageSummariesPayload is the payload type of the Charge
// Statistics service Group and Site Usage Summaries method.
type GroupAndSiteUsageSummariesPayload struct {
	// UUID of the group.
	GroupID string
	// UUID of the site.
	SiteID string
	// Reporting interval
	Interval string
	// Inclusive from date used for filtering on unpluggedAt
	From string
	// Inclusive to date used for filtering on unpluggedAt
	To string
}

type GroupChargeStatistics struct {
	// sum of charging duration in seconds
	ChargingDuration int
	// CO2 saved in kg = (Average ICE CO2 emission per mile - Average EV CO2
	// emission per mile) x Total Energy Used in kWh x Average EV mileage per 1 kWh
	Co2Savings float64
	Energy     *EnergyStatistics
	// count of distinct charge uuids
	NumberOfCharges int
	// count of distinct user ids
	NumberOfUsers int
	// sum of settlement amount in pence
	RevenueGenerated int
	// count of distinct charger ids
	NumberOfChargers int
	// count of distinct sites
	NumberOfSites int
}

// GroupChargeStatisticsPayload is the payload type of the Charge Statistics
// service Group Charge Statistics method.
type GroupChargeStatisticsPayload struct {
	// UUID of the group.
	GroupID string
	// Inclusive from date used for filtering on unpluggedAt
	From string
	// Inclusive to date used for filtering on unpluggedAt
	To string
}

// GroupNotFound is the error returned when there is no group for a given
// {groupId}.
type GroupNotFound struct {
	Reason string
	Status int
}

// GroupUsageSummariesPayload is the payload type of the Charge Statistics
// service Group Usage Summaries method.
type GroupUsageSummariesPayload struct {
	// UUID of the group.
	GroupID string
	// Reporting interval
	Interval string
	// Inclusive from date used for filtering on unpluggedAt
	From string
	// Inclusive to date used for filtering on unpluggedAt
	To string
}

type Meta struct {
	// Passed parameters
	Params map[string]any
}

// ProjectionchargerChargeStatisticsResponse is the result type of the Charge
// Statistics service Charger Charge Statistics method.
type ProjectionchargerChargeStatisticsResponse struct {
	Data *ChargerChargeStatistics
	Meta *Meta
}

// ProjectiongroupChargeStatisticsResponse is the result type of the Charge
// Statistics service Group Charge Statistics method.
type ProjectiongroupChargeStatisticsResponse struct {
	Data *GroupChargeStatistics
	Meta *Meta
}

// ProjectionsiteChargeStatisticsResponse is the result type of the Charge
// Statistics service Site Charge Statistics method.
type ProjectionsiteChargeStatisticsResponse struct {
	Data *SiteChargeStatistics
	Meta *Meta
}

type SiteChargeStatistics struct {
	// count of distinct charger ids
	NumberOfChargers int
	// sum of charging duration in seconds
	ChargingDuration int
	// CO2 saved in kg = (Average ICE CO2 emission per mile - Average EV CO2
	// emission per mile) x Total Energy Used in kWh x Average EV mileage per 1 kWh
	Co2Savings float64
	Energy     *EnergyStatistics
	// count of distinct charge uuids
	NumberOfCharges int
	// count of distinct user ids
	NumberOfUsers int
	// sum of settlement amount in pence
	RevenueGenerated int
}

// SiteChargeStatisticsPayload is the payload type of the Charge Statistics
// service Site Charge Statistics method.
type SiteChargeStatisticsPayload struct {
	// UUID of the site.
	SiteID string
	// Inclusive from date used for filtering on unpluggedAt
	From string
	// Inclusive to date used for filtering on unpluggedAt
	To string
}

type Usage struct {
	// The start date of this usage interval
	IntervalStartDate string
	// Energy used this period in kWh
	TotalUsage float64
	// CO2 in kg = (Average ICE CO2 emission per mile - Average EV CO2 emission per
	// mile) x Total Energy Used in kWh x Average EV mileage per 1 kWh. Energy
	// saved = ((0.28 - 0.14) x Total Energy Used in kWh x 4)
	Co2Savings float64
	// Revenue generated in pence
	RevenueGenerated int
	// Energy cost in pence
	Cost int
}

// UsageResponse is the result type of the Charge Statistics service Group
// Usage Summaries method.
type UsageResponse struct {
	Usage []*Usage
	Meta  *Meta
}

// Error returns an error description.
func (e *GroupNotFound) Error() string {
	return "GroupNotFound is the error returned when there is no group for a given {groupId}."
}

// ErrorName returns "GroupNotFound".
//
// Deprecated: Use GoaErrorName - https://github.com/goadesign/goa/issues/3105
func (e *GroupNotFound) ErrorName() string {
	return e.GoaErrorName()
}

// GoaErrorName returns "GroupNotFound".
func (e *GroupNotFound) GoaErrorName() string {
	return "group_not_found"
}

// MakeBadRequest builds a goa.ServiceError from an error.
func MakeBadRequest(err error) *goa.ServiceError {
	return goa.NewServiceError(err, "bad_request", false, false, false)
}
