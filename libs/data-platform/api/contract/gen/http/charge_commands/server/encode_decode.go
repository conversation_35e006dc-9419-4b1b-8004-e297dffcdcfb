// Code generated by goa v3.20.1, DO NOT EDIT.
//
// Charge commands HTTP server encoders and decoders
//
// Command:
// $ goa gen experience/libs/data-platform/api/contract/design

package server

import (
	"context"
	"errors"
	chargecommands "experience/libs/data-platform/api/contract/gen/charge_commands"
	"io"
	"net/http"

	goahttp "goa.design/goa/v3/http"
	goa "goa.design/goa/v3/pkg"
)

// EncodeCorrectEnergyCostResponse returns an encoder for responses returned by
// the Charge commands Correct energy cost endpoint.
func EncodeCorrectEnergyCostResponse(encoder func(context.Context, http.ResponseWriter) goahttp.Encoder) func(context.Context, http.ResponseWriter, any) error {
	return func(ctx context.Context, w http.ResponseWriter, v any) error {
		res, _ := v.(*chargecommands.AggregateCostCorrectedResponse)
		enc := encoder(ctx, w)
		body := NewCorrectEnergyCostResponseBody(res)
		w.<PERSON>rite<PERSON>eader(http.StatusOK)
		return enc.Encode(body)
	}
}

// DecodeCorrectEnergyCostRequest returns a decoder for requests sent to the
// Charge commands Correct energy cost endpoint.
func DecodeCorrectEnergyCostRequest(mux goahttp.Muxer, decoder func(*http.Request) goahttp.Decoder) func(*http.Request) (any, error) {
	return func(r *http.Request) (any, error) {
		var (
			body CorrectEnergyCostRequestBody
			err  error
		)
		err = decoder(r).Decode(&body)
		if err != nil {
			if err == io.EOF {
				return nil, goa.MissingPayloadError()
			}
			var gerr *goa.ServiceError
			if errors.As(err, &gerr) {
				return nil, gerr
			}
			return nil, goa.DecodePayloadError(err.Error())
		}
		err = ValidateCorrectEnergyCostRequestBody(&body)
		if err != nil {
			return nil, err
		}

		var (
			chargeID string

			params = mux.Vars(r)
		)
		chargeID = params["chargeID"]
		err = goa.MergeErrors(err, goa.ValidateFormat("chargeID", chargeID, goa.FormatUUID))
		if err != nil {
			return nil, err
		}
		payload := NewCorrectEnergyCostPayload(&body, chargeID)

		return payload, nil
	}
}

// EncodeCorrectEnergyCostError returns an encoder for errors returned by the
// Correct energy cost Charge commands endpoint.
func EncodeCorrectEnergyCostError(encoder func(context.Context, http.ResponseWriter) goahttp.Encoder, formatter func(ctx context.Context, err error) goahttp.Statuser) func(context.Context, http.ResponseWriter, error) error {
	encodeError := goahttp.ErrorEncoder(encoder, formatter)
	return func(ctx context.Context, w http.ResponseWriter, v error) error {
		var en goa.GoaErrorNamer
		if !errors.As(v, &en) {
			return encodeError(ctx, w, v)
		}
		switch en.GoaErrorName() {
		case "charge_expensed":
			var res *chargecommands.ChargeExpensed
			errors.As(v, &res)
			enc := encoder(ctx, w)
			var body any
			if formatter != nil {
				body = formatter(ctx, res)
			} else {
				body = NewCorrectEnergyCostChargeExpensedResponseBody(res)
			}
			w.Header().Set("goa-error", res.GoaErrorName())
			w.WriteHeader(http.StatusBadRequest)
			return enc.Encode(body)
		case "bad_request":
			var res *goa.ServiceError
			errors.As(v, &res)
			enc := encoder(ctx, w)
			var body any
			if formatter != nil {
				body = formatter(ctx, res)
			} else {
				body = NewCorrectEnergyCostBadRequestResponseBody(res)
			}
			w.Header().Set("goa-error", res.GoaErrorName())
			w.WriteHeader(http.StatusBadRequest)
			return enc.Encode(body)
		case "charge_not_found":
			var res *chargecommands.ChargeNotFound
			errors.As(v, &res)
			enc := encoder(ctx, w)
			var body any
			if formatter != nil {
				body = formatter(ctx, res)
			} else {
				body = NewCorrectEnergyCostChargeNotFoundResponseBody(res)
			}
			w.Header().Set("goa-error", res.GoaErrorName())
			w.WriteHeader(http.StatusNotFound)
			return enc.Encode(body)
		case "duplicate_requests":
			var res *chargecommands.EventOutOfDate
			errors.As(v, &res)
			enc := encoder(ctx, w)
			var body any
			if formatter != nil {
				body = formatter(ctx, res)
			} else {
				body = NewCorrectEnergyCostDuplicateRequestsResponseBody(res)
			}
			w.Header().Set("goa-error", res.GoaErrorName())
			w.WriteHeader(http.StatusConflict)
			return enc.Encode(body)
		default:
			return encodeError(ctx, w, v)
		}
	}
}

// EncodeCorrectSettlementAmountResponse returns an encoder for responses
// returned by the Charge commands Correct settlement amount endpoint.
func EncodeCorrectSettlementAmountResponse(encoder func(context.Context, http.ResponseWriter) goahttp.Encoder) func(context.Context, http.ResponseWriter, any) error {
	return func(ctx context.Context, w http.ResponseWriter, v any) error {
		res, _ := v.(*chargecommands.AggregateSettlementAmountCorrectedResponse)
		enc := encoder(ctx, w)
		body := NewCorrectSettlementAmountResponseBody(res)
		w.WriteHeader(http.StatusOK)
		return enc.Encode(body)
	}
}

// DecodeCorrectSettlementAmountRequest returns a decoder for requests sent to
// the Charge commands Correct settlement amount endpoint.
func DecodeCorrectSettlementAmountRequest(mux goahttp.Muxer, decoder func(*http.Request) goahttp.Decoder) func(*http.Request) (any, error) {
	return func(r *http.Request) (any, error) {
		var (
			body CorrectSettlementAmountRequestBody
			err  error
		)
		err = decoder(r).Decode(&body)
		if err != nil {
			if err == io.EOF {
				return nil, goa.MissingPayloadError()
			}
			var gerr *goa.ServiceError
			if errors.As(err, &gerr) {
				return nil, gerr
			}
			return nil, goa.DecodePayloadError(err.Error())
		}
		err = ValidateCorrectSettlementAmountRequestBody(&body)
		if err != nil {
			return nil, err
		}

		var (
			chargeID string

			params = mux.Vars(r)
		)
		chargeID = params["chargeID"]
		err = goa.MergeErrors(err, goa.ValidateFormat("chargeID", chargeID, goa.FormatUUID))
		if err != nil {
			return nil, err
		}
		payload := NewCorrectSettlementAmountPayload(&body, chargeID)

		return payload, nil
	}
}

// EncodeCorrectSettlementAmountError returns an encoder for errors returned by
// the Correct settlement amount Charge commands endpoint.
func EncodeCorrectSettlementAmountError(encoder func(context.Context, http.ResponseWriter) goahttp.Encoder, formatter func(ctx context.Context, err error) goahttp.Statuser) func(context.Context, http.ResponseWriter, error) error {
	encodeError := goahttp.ErrorEncoder(encoder, formatter)
	return func(ctx context.Context, w http.ResponseWriter, v error) error {
		var en goa.GoaErrorNamer
		if !errors.As(v, &en) {
			return encodeError(ctx, w, v)
		}
		switch en.GoaErrorName() {
		case "charge_not_found":
			var res *chargecommands.ChargeNotFound
			errors.As(v, &res)
			enc := encoder(ctx, w)
			var body any
			if formatter != nil {
				body = formatter(ctx, res)
			} else {
				body = NewCorrectSettlementAmountChargeNotFoundResponseBody(res)
			}
			w.Header().Set("goa-error", res.GoaErrorName())
			w.WriteHeader(http.StatusNotFound)
			return enc.Encode(body)
		case "duplicate_requests":
			var res *chargecommands.EventOutOfDate
			errors.As(v, &res)
			enc := encoder(ctx, w)
			var body any
			if formatter != nil {
				body = formatter(ctx, res)
			} else {
				body = NewCorrectSettlementAmountDuplicateRequestsResponseBody(res)
			}
			w.Header().Set("goa-error", res.GoaErrorName())
			w.WriteHeader(http.StatusConflict)
			return enc.Encode(body)
		case "bad_request":
			var res *goa.ServiceError
			errors.As(v, &res)
			enc := encoder(ctx, w)
			var body any
			if formatter != nil {
				body = formatter(ctx, res)
			} else {
				body = NewCorrectSettlementAmountBadRequestResponseBody(res)
			}
			w.Header().Set("goa-error", res.GoaErrorName())
			w.WriteHeader(http.StatusBadRequest)
			return enc.Encode(body)
		default:
			return encodeError(ctx, w, v)
		}
	}
}
