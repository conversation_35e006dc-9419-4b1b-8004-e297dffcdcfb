// Code generated by goa v3.20.1, DO NOT EDIT.
//
// Link user client
//
// Command:
// $ goa gen experience/libs/data-platform/api/contract/design

package linkuser

import (
	"context"

	goa "goa.design/goa/v3/pkg"
)

// C<PERSON> is the "Link user" service client.
type Client struct {
	LinkUserToHomeChargerEndpoint goa.Endpoint
}

// NewClient initializes a "Link user" service client given the endpoints.
func NewClient(linkUserToHomeCharger goa.Endpoint) *Client {
	return &Client{
		LinkUserToHomeChargerEndpoint: linkUserToHomeCharger,
	}
}

// Link<PERSON>serToHomeCharger calls the "Link user to home charger" endpoint of the
// "Link user" service.
// LinkUserToHomeCharger may return the following errors:
//   - "internal_server_error" (type *goa.ServiceError)
//   - "charger_not_found" (type *ChargerNotFound): charger not found
//   - "user_not_found" (type *UserNotFound): User not found
//   - "bad_request" (type *goa.ServiceError)
//   - error: internal error
func (c *Client) LinkUserToHomeCharger(ctx context.Context, p *LinkUserToHomeChargerPayload) (err error) {
	_, err = c.LinkUserToHomeChargerEndpoint(ctx, p)
	return
}
