// Code generated by goa v3.20.1, DO NOT EDIT.
//
// Organisation charges client
//
// Command:
// $ goa gen experience/libs/data-platform/api/contract/design

package organisationcharges

import (
	"context"

	goa "goa.design/goa/v3/pkg"
)

// Client is the "Organisation charges" service client.
type Client struct {
	ExpensesByOrganisationEndpoint                goa.Endpoint
	ExpensesByOrganisationGroupedByDriverEndpoint goa.Endpoint
	SubmittedChargesForDriverEndpoint             goa.Endpoint
	MarkSubmittedChargesAsProcessedEndpoint       goa.Endpoint
	FleetUsageByOrganisationEndpoint              goa.Endpoint
}

// NewClient initializes a "Organisation charges" service client given the
// endpoints.
func NewClient(expensesByOrganisation, expensesByOrganisationGroupedByDriver, submittedChargesForDriver, markSubmittedChargesAsProcessed, fleetUsageByOrganisation goa.Endpoint) *Client {
	return &Client{
		ExpensesByOrganisationEndpoint:                expensesByOrganisation,
		ExpensesByOrganisationGroupedByDriverEndpoint: expensesByOrganisationGroupedByDriver,
		SubmittedChargesForDriverEndpoint:             submittedChargesForDriver,
		MarkSubmittedChargesAsProcessedEndpoint:       markSubmittedChargesAsProcessed,
		FleetUsageByOrganisationEndpoint:              fleetUsageByOrganisation,
	}
}

// ExpensesByOrganisation calls the "Expenses by organisation" endpoint of the
// "Organisation charges" service.
// ExpensesByOrganisation may return the following errors:
//   - "organisation_not_found" (type *OrganisationNotFound): Organisation not found
//   - "time_range_out_of_bounds" (type *TimeRangeOutOfBounds): requested time range is too wide (maximum 1 month)
//   - "bad_request" (type *goa.ServiceError)
//   - error: internal error
func (c *Client) ExpensesByOrganisation(ctx context.Context, p *ExpensesByOrganisationPayload) (res *OrganisationChargesResponse, err error) {
	var ires any
	ires, err = c.ExpensesByOrganisationEndpoint(ctx, p)
	if err != nil {
		return
	}
	return ires.(*OrganisationChargesResponse), nil
}

// ExpensesByOrganisationGroupedByDriver calls the "Expenses by organisation,
// grouped by driver" endpoint of the "Organisation charges" service.
// ExpensesByOrganisationGroupedByDriver may return the following errors:
//   - "organisation_not_found" (type *OrganisationNotFound): Organisation not found
//   - "time_range_out_of_bounds" (type *TimeRangeOutOfBounds): requested time range is too wide (maximum 1 month)
//   - "bad_request" (type *goa.ServiceError)
//   - error: internal error
func (c *Client) ExpensesByOrganisationGroupedByDriver(ctx context.Context, p *ExpensesByOrganisationGroupedByDriverPayload) (res *OrganisationChargesDriverSummaryResponse, err error) {
	var ires any
	ires, err = c.ExpensesByOrganisationGroupedByDriverEndpoint(ctx, p)
	if err != nil {
		return
	}
	return ires.(*OrganisationChargesDriverSummaryResponse), nil
}

// SubmittedChargesForDriver calls the "Submitted charges for driver" endpoint
// of the "Organisation charges" service.
// SubmittedChargesForDriver may return the following errors:
//   - "organisation_not_found" (type *OrganisationNotFound): organisation not found
//   - "driver_not_found" (type *DriverNotFound): driver not found
//   - "bad_request" (type *goa.ServiceError)
//   - error: internal error
func (c *Client) SubmittedChargesForDriver(ctx context.Context, p *SubmittedChargesForDriverPayload) (res *SubmittedChargesResponse, err error) {
	var ires any
	ires, err = c.SubmittedChargesForDriverEndpoint(ctx, p)
	if err != nil {
		return
	}
	return ires.(*SubmittedChargesResponse), nil
}

// MarkSubmittedChargesAsProcessed calls the "Mark submitted charges as
// processed" endpoint of the "Organisation charges" service.
// MarkSubmittedChargesAsProcessed may return the following errors:
//   - "organisation_not_found" (type *OrganisationNotFound)
//   - "approver_not_found" (type *ApproverNotFound)
//   - "charge_not_found" (type *ChargeNotFound)
//   - "charge_not_found_in_organisation" (type *ChargeNotFoundInOrganisation)
//   - error: internal error
func (c *Client) MarkSubmittedChargesAsProcessed(ctx context.Context, p *MarkSubmittedChargesAsProcessedPayload) (err error) {
	_, err = c.MarkSubmittedChargesAsProcessedEndpoint(ctx, p)
	return
}

// FleetUsageByOrganisation calls the "Fleet usage by organisation" endpoint of
// the "Organisation charges" service.
// FleetUsageByOrganisation may return the following errors:
//   - "organisation_not_found" (type *OrganisationNotFound): organisation not found
//   - "bad_request" (type *goa.ServiceError)
//   - error: internal error
func (c *Client) FleetUsageByOrganisation(ctx context.Context, p *FleetUsageByOrganisationPayload) (res *FleetUsageResponse, err error) {
	var ires any
	ires, err = c.FleetUsageByOrganisationEndpoint(ctx, p)
	if err != nil {
		return
	}
	return ires.(*FleetUsageResponse), nil
}
