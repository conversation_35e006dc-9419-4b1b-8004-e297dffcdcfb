package guestbilling

import (
	"context"
	"experience/libs/data-platform/event-sourcing/guestbilling"
	"experience/libs/data-platform/event-sourcing/guestbilling/infra/sqlc"
	"fmt"
	"log"
)

type repository struct {
	queriesRead *sqlc.Queries
	logger      *log.Logger
}

func NewRepository(logger *log.Logger, db sqlc.DBTX) guestbilling.Repository {
	return &repository{
		logger:      logger,
		queriesRead: sqlc.New(db),
	}
}

func (r *repository) GetBillingEventIDAssociatedToCharge(ctx context.Context, chargeID int64) (*int64, error) {
	billingEventID, err := r.queriesRead.GetBillingEventIDAssociatedToCharge(ctx, chargeID)
	if err != nil {
		r.logger.Printf("failed to get billing event for charge ID %d: %v", chargeID, err)
		return nil, err
	}

	if !billingEventID.Valid {
		r.logger.Printf("no billing event found for charge ID %d", chargeID)
		return nil, fmt.Errorf("no billing event found for charge ID %d", chargeID)
	}

	return &billingEventID.Int64, nil
}
