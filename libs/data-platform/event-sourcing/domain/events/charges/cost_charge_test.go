package charges_test

import (
	"bytes"
	"context"
	"encoding/json"
	"errors"
	costcalculation "experience/libs/data-platform/cost-calculation"
	energycost "experience/libs/data-platform/cost-calculation/energy-cost"
	mockenergycost "experience/libs/data-platform/cost-calculation/energy-cost/mock"
	charge "experience/libs/data-platform/event-sourcing/domain/events/charges"
	mockcost "experience/libs/data-platform/event-sourcing/domain/events/charges/cost/mock"
	mockcharges "experience/libs/data-platform/event-sourcing/domain/events/charges/mock"
	"experience/libs/shared/go/converters"
	assetserviceapi "experience/libs/shared/go/external/asset-service-api/src"
	"experience/libs/shared/go/numbers"
	"regexp"
	"time"

	eventstore "experience/libs/shared/go/event-store"
	mockstore "experience/libs/shared/go/event-store/test/mock"
	"experience/libs/shared/go/sqs"
	mocksqs "experience/libs/shared/go/sqs/test/mock"
	"fmt"
	"log"
	"strconv"
	"testing"

	"github.com/jinzhu/copier"

	"github.com/google/uuid"
	"github.com/stretchr/testify/require"
	"go.uber.org/mock/gomock"
	"k8s.io/utils/ptr"
)

type costChargeServiceMocks struct {
	sqsClient                *mocksqs.MockClientOperations
	aggregateLoader          *mockcharges.MockAggregateLoader
	msgHandler               *mocksqs.MockMessageHandler
	costCalcRepo             *mockcost.MockRepository
	energyCostCalculator     *mockenergycost.MockCalculator
	uUIDStringToIntConverter copier.TypeConverter
	store                    *mockstore.MockStore
	assetSummaryService      *mockcost.MockAssetSummary
	tariffAPIService         *mockcost.MockTariffData
	energyMetricsService     *mockcost.MockEnergyMetrics
}

func newCostChargeServiceMocks(ctrl *gomock.Controller) *costChargeServiceMocks {
	return &costChargeServiceMocks{
		sqsClient:                mocksqs.NewMockClientOperations(ctrl),
		aggregateLoader:          mockcharges.NewMockAggregateLoader(ctrl),
		msgHandler:               mocksqs.NewMockMessageHandler(ctrl),
		costCalcRepo:             mockcost.NewMockRepository(ctrl),
		energyCostCalculator:     mockenergycost.NewMockCalculator(ctrl),
		uUIDStringToIntConverter: stringUUIDToIntConverter(),
		store:                    mockstore.NewMockStore(ctrl),
		assetSummaryService:      mockcost.NewMockAssetSummary(ctrl),
		tariffAPIService:         mockcost.NewMockTariffData(ctrl),
		energyMetricsService:     mockcost.NewMockEnergyMetrics(ctrl),
	}
}

func TestCostChargeCommand(t *testing.T) {
	tests := []struct {
		name                      string
		aggregateID               uuid.UUID
		energyTariffCurrency      string
		energyTariffCurrencyError error
		calculatedEnergyCost      *int
		expectedEnergyCost        *int
		chargeEvents              []eventstore.Record
		loadMockError             error
		msgHandlerMockError       error
		sqsMockError              error
		sqsCallCount              int
		messageProcessorCallCount int
		expectedError             string
		assetSummaryError         error
		tariffError               error
		expectedLogMessages       []string
		ownership                 assetserviceapi.Ownership
		chargeIntervals           []costcalculation.ChargeInterval
		energyMetricsError        error
	}{
		{
			name:                      "cost charge command invokes message handler successfully",
			aggregateID:               uuid.MustParse("5e0f5caf-79d2-4396-b52a-0bb603162375"),
			expectedEnergyCost:        ptr.To(456),
			energyTariffCurrency:      "GBP",
			energyTariffCurrencyError: nil,
			calculatedEnergyCost:      ptr.To(456),
			chargeEvents:              sampleChargeCompletedEventWithCharger(),
			messageProcessorCallCount: 1,
			chargeIntervals: []costcalculation.ChargeInterval{{
				Energy:    10.355,
				EndedAt:   ptr.To(time.Date(2023, 6, 14, 1, 30, 0, 0, time.UTC)),
				StartedAt: ptr.To(time.Date(2023, 6, 14, 1, 0, 0, 0, time.UTC)),
			}},
		},
		{
			name:                      "cost is calculated from charge completed event when historic is true",
			aggregateID:               uuid.MustParse("5e0f5caf-79d2-4396-b52a-0bb603162375"),
			expectedEnergyCost:        ptr.To(789),
			energyTariffCurrency:      "GBP",
			energyTariffCurrencyError: nil,
			calculatedEnergyCost:      ptr.To(456),
			chargeEvents:              sampleHistoricChargeCompletedEvent(),
			messageProcessorCallCount: 1,
		},
		{
			name:                      "cost charge command sends to sqs on msghandler error",
			aggregateID:               uuid.MustParse("5e0f5caf-79d2-4396-b52a-0bb603162375"),
			energyTariffCurrency:      "GBP",
			energyTariffCurrencyError: nil,
			calculatedEnergyCost:      ptr.To(456),
			expectedEnergyCost:        ptr.To(456),
			msgHandlerMockError:       errors.New("failed to call directly"),
			sqsCallCount:              1,
			messageProcessorCallCount: 1,
			chargeEvents:              sampleChargeCompletedEventWithCharger(),
		},
		{
			name:                      "sqs error passed back to caller",
			aggregateID:               uuid.MustParse("5e0f5caf-79d2-4396-b52a-0bb603162375"),
			energyTariffCurrency:      "GBP",
			energyTariffCurrencyError: nil,
			calculatedEnergyCost:      ptr.To(456),
			expectedEnergyCost:        ptr.To(456),
			chargeEvents:              sampleChargeCompletedEventWithCharger(),
			msgHandlerMockError:       errors.New("failed to call directly"),
			sqsCallCount:              1,
			messageProcessorCallCount: 1,
			sqsMockError:              errSendToSqsFailed,
			expectedError:             "sqs message failed to send",
		},
		{
			name:                      "load charge error passed back to caller",
			aggregateID:               uuid.New(),
			energyTariffCurrency:      "GBP",
			energyTariffCurrencyError: nil,
			loadMockError:             errLoadChargeFailed,
			expectedError:             "failed to load charge from records",
		},
		{
			name:                      "cost charge command invokes message handler successfully when calculator panics",
			aggregateID:               uuid.MustParse("5e0f5caf-79d2-4396-b52a-0bb603162375"),
			energyTariffCurrency:      "GBP",
			energyTariffCurrencyError: nil,
			calculatedEnergyCost:      ptr.To(456),
			chargeEvents:              sampleChargeCompletedEventWithCharger(),
			messageProcessorCallCount: 1,
		},
		{
			name:                      "cost charge uses GBP by default when tariff is not found",
			aggregateID:               uuid.MustParse("5e0f5caf-79d2-4396-b52a-0bb603162375"),
			energyTariffCurrency:      "GBP",
			energyTariffCurrencyError: errFindTariffFailed,
			calculatedEnergyCost:      ptr.To(456),
			expectedEnergyCost:        ptr.To(456),
			chargeEvents:              sampleChargeCompletedEventWithCharger(),
			messageProcessorCallCount: 1,
		},
		{
			name:                      "successful cost calculation with ownership and tariff info retrieved",
			aggregateID:               uuid.MustParse("5e0f5caf-79d2-4396-b52a-0bb603162375"),
			energyTariffCurrency:      "GBP",
			energyTariffCurrencyError: nil,
			calculatedEnergyCost:      ptr.To(456),
			expectedEnergyCost:        ptr.To(456),
			chargeEvents:              sampleChargeCompletedEventWithCharger(),
			messageProcessorCallCount: 1,
			expectedLogMessages: []string{
				"retrieved ownership POD_POINT on charger PSL-532880 (aggregateID 570b2ccf-824d-4076-9da5-000000bbdef8)",
				"cost calculation successful with ownership=POD_POINT on charger=PSL-532880 with energy cost: 456 (aggregateID 570b2ccf-824d-4076-9da5-000000bbdef8)",
			},
			ownership: assetserviceapi.OWNERSHIP_POD_POINT,
		},
		{
			name:                      "error log when fetching charger ownership from the asset-service-api",
			energyTariffCurrency:      "GBP",
			energyTariffCurrencyError: nil,
			calculatedEnergyCost:      ptr.To(456),
			expectedEnergyCost:        ptr.To(456),
			assetSummaryError:         errors.New("ownership fetch failed"),
			expectedLogMessages: []string{
				"failed to fetch ownership for charger PSL-532880 (aggregateID 570b2ccf-824d-4076-9da5-000000bbdef8)",
			},
			chargeEvents:              sampleChargeCompletedEventWithCharger(),
			messageProcessorCallCount: 1,
			ownership:                 assetserviceapi.OWNERSHIP_POD_POINT,
		},
		{
			name:                      "error when fetching charger tariff from the tariffs-api",
			energyTariffCurrency:      "GBP",
			energyTariffCurrencyError: nil,
			calculatedEnergyCost:      ptr.To(456),
			expectedEnergyCost:        ptr.To(456),
			tariffError:               errors.New("tariff fetch failed"),
			chargeEvents:              sampleChargeCompletedEventWithCharger(),
			messageProcessorCallCount: 0,
			ownership:                 assetserviceapi.OWNERSHIP_POD_POINT,
		},
		{
			name:                      "error when fetching charge energy metrics",
			aggregateID:               uuid.MustParse("5e0f5caf-79d2-4396-b52a-0bb603162375"),
			expectedEnergyCost:        ptr.To(456),
			energyTariffCurrency:      "GBP",
			energyTariffCurrencyError: nil,
			calculatedEnergyCost:      ptr.To(456),
			chargeEvents:              sampleChargeCompletedEventWithCharger(),
			messageProcessorCallCount: 1,
			energyMetricsError:        errors.New("energy fetch failed"),
			expectedLogMessages: []string{
				"GetChargeEnergyMetrics error: energy fetch failed, http status: 0, response body: , ppid: PSL-532880, ref: IOUNWEFWIFI, from: 2023-06-14 09:17:16 +0000 UTC, to: 2023-06-14 09:27:16 +0000 UTC",
			},
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			var buf bytes.Buffer
			testLogger := log.New(&buf, "", 0)

			ctrl := gomock.NewController(t)
			serviceMocks := newCostChargeServiceMocks(ctrl)

			serviceMocks.store.EXPECT().
				Load(gomock.AssignableToTypeOf(context.Background()), tt.aggregateID).
				Return(tt.chargeEvents, tt.loadMockError)

			serviceMocks.msgHandler.EXPECT().
				Process(gomock.AssignableToTypeOf(context.Background()), gomock.AssignableToTypeOf(sqs.Message{})).
				Times(tt.messageProcessorCallCount).
				Return(tt.msgHandlerMockError)

			serviceMocks.costCalcRepo.EXPECT().
				GetEnergyTariffTiersByID(gomock.AssignableToTypeOf(context.Background()), gomock.Any()).
				AnyTimes()

			serviceMocks.costCalcRepo.EXPECT().
				GetCountryByID(gomock.AssignableToTypeOf(context.Background()), gomock.Any()).
				AnyTimes()

			serviceMocks.costCalcRepo.EXPECT().
				GetEnergyTariffCurrencyByLocationID(gomock.AssignableToTypeOf(context.Background()), gomock.Any()).
				Return(tt.energyTariffCurrency, tt.energyTariffCurrencyError).
				AnyTimes()

			serviceMocks.assetSummaryService.EXPECT().
				GetOwner(gomock.Any(), gomock.Any()).
				Return(tt.ownership, tt.assetSummaryError).
				AnyTimes()

			serviceMocks.tariffAPIService.EXPECT().
				Get(gomock.Any(), gomock.Any(), gomock.Any()).
				Return(sampleTariff(), tt.tariffError).
				AnyTimes()

			serviceMocks.energyMetricsService.EXPECT().
				GetChargeEnergyMetrics(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).
				Return(tt.chargeIntervals, tt.energyMetricsError).
				AnyTimes()

			if tt.expectedEnergyCost != nil {
				payload := fmt.Sprintf(`{"energyCost":%v,"energyCostCurrency":%q}`, *tt.expectedEnergyCost, tt.energyTariffCurrency)
				expectedMessageBodyRegex := charge.GenerateExpectedMessageBodyRegex(tt.aggregateID, charge.TypeCosted, payload)

				serviceMocks.sqsClient.
					EXPECT().SendMessage(gomock.AssignableToTypeOf(context.Background()), gomock.Regex(expectedMessageBodyRegex)).
					Return(ptr.To("message-1"), tt.sqsMockError).
					Times(tt.sqsCallCount)
			}

			if tt.calculatedEnergyCost != nil {
				calculatedEnergyCost, err := numbers.Convert[int, uint32](*tt.calculatedEnergyCost)
				require.NoError(t, err)
				serviceMocks.energyCostCalculator.EXPECT().EnergyCost(gomock.Any(), gomock.Any()).AnyTimes().Return(calculatedEnergyCost)
			}

			aggregateLoader := charge.NewAggregateLoader(serviceMocks.store)

			service := charge.NewCostChargeService(testLogger, serviceMocks.sqsClient, aggregateLoader, serviceMocks.msgHandler, serviceMocks.energyCostCalculator, serviceMocks.costCalcRepo, converters.UUIDStringToIntConverter, serviceMocks.assetSummaryService, serviceMocks.tariffAPIService, serviceMocks.energyMetricsService)

			err := service.CostCharge(context.Background(), tt.aggregateID)
			if tt.expectedError != "" {
				require.ErrorContains(t, err, tt.expectedError)
			}

			if len(tt.expectedLogMessages) > 0 {
				out := buf.String()
				for _, expected := range tt.expectedLogMessages {
					require.Contains(t, out, expected)
				}
			}
		})
	}
}

// This test initiates a processing of CostCharge() with multiple
// Charge.Completed events on the charge aggregate. In this scenario, the
// Charge.Costed event that is being sent/processed should use the values of the
// latest Charge.Completed event.
func TestCostChargeUsesTheLatestCompletedEventForCostCalculation(t *testing.T) {
	mockCalculatedCost := 999
	firstCompletedEventSettlementAmount := 123
	secondCompletedEventSettlementAmount := 456

	chargeEvents := []eventstore.Record{
		{
			Version: 1,
			Data:    []byte(fmt.Sprintf(`{"event":"Charge.Completed","aggregateID":"5e0f5caf-79d2-4396-b52a-0bb603162375","payload":{"billing":{"settlementAmount":%d,"settlementCurrency":"GBP"},"energy":{"energyCost":123,"energyCostCurrency":"GBP"},"charge":{"id":12312312,"ref":"IOUNWEFWIFI","pluggedInAt":"2023-06-14T09:17:16Z","unpluggedAt":"2023-06-14T09:27:16Z","energyTotal":10.355,"generationEnergyTotal":2.345,"gridEnergyTotal":8.010,"chargeDurationTotal":7726,"updatedAt":"2023-06-14T09:28:16Z","startedAt":"2023-06-14T09:19:16Z","endedAt":"2023-06-14T09:27:10Z"}}}`, firstCompletedEventSettlementAmount)),
		},
		{
			Version: 2,
			Data:    []byte(fmt.Sprintf(`{"event":"Charge.Completed","aggregateID":"5e0f5caf-79d2-4396-b52a-0bb603162375","payload":{"billing":{"settlementAmount":%d,"settlementCurrency":"GBP"},"energy":{"energyCost":456,"energyCostCurrency":"GBP"},"charge":{"id":12312312,"ref":"IOUNWEFWIFI","pluggedInAt":"2023-06-14T09:17:16Z","unpluggedAt":"2023-06-14T09:27:16Z","energyTotal":10.355,"generationEnergyTotal":2.345,"gridEnergyTotal":8.010,"chargeDurationTotal":7726,"updatedAt":"2023-06-14T09:28:16Z","startedAt":"2023-06-14T09:19:16Z","endedAt":"2023-06-14T09:27:10Z"}}}`, secondCompletedEventSettlementAmount)),
		},
	}

	aggregateID := uuid.New()

	ctrl := gomock.NewController(t)
	serviceMocks := newCostChargeServiceMocks(ctrl)

	serviceMocks.store.EXPECT().
		Load(gomock.AssignableToTypeOf(context.Background()), aggregateID).
		Return(chargeEvents, nil)

	serviceMocks.costCalcRepo.EXPECT().
		GetEnergyTariffCurrencyByLocationID(gomock.AssignableToTypeOf(context.Background()), gomock.Any()).
		Return("GBP", nil)

	serviceMocks.energyMetricsService.EXPECT().
		GetChargeEnergyMetrics(gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any(), gomock.Any()).
		Return([]costcalculation.ChargeInterval{}, nil).
		AnyTimes()

	serviceMocks.msgHandler.EXPECT().
		Process(gomock.Any(), gomock.Any()).
		Return(errors.New("mock_error_to_continue_over_to_sqs"))

	payload := fmt.Sprintf(`{"energyCost":%v,"energyCostCurrency":%q}`, mockCalculatedCost, "GBP")
	expectedMessageBodyRegex := charge.GenerateExpectedMessageBodyRegex(aggregateID, charge.TypeCosted, payload)

	serviceMocks.sqsClient.EXPECT().
		SendMessage(gomock.Any(), gomock.Regex(expectedMessageBodyRegex))

	serviceMocks.energyCostCalculator.
		EXPECT().
		EnergyCost(gomock.Any(), gomock.Any()).
		AnyTimes().
		Return(uint32(mockCalculatedCost))

	serviceMocks.assetSummaryService.EXPECT().
		GetOwner(gomock.Any(), gomock.Any()).
		Return(assetserviceapi.OWNERSHIP_POD_POINT, nil).
		AnyTimes()

	serviceMocks.tariffAPIService.EXPECT().
		Get(gomock.Any(), gomock.Any(), gomock.Any()).
		Return(sampleTariff(), nil).
		AnyTimes()

	aggregateLoader := charge.NewAggregateLoader(serviceMocks.store)

	service := charge.NewCostChargeService(log.Default(), serviceMocks.sqsClient, aggregateLoader, serviceMocks.msgHandler, serviceMocks.energyCostCalculator, serviceMocks.costCalcRepo, converters.UUIDStringToIntConverter, serviceMocks.assetSummaryService, serviceMocks.tariffAPIService, serviceMocks.energyMetricsService)

	err := service.CostCharge(context.Background(), aggregateID)
	require.NoError(t, err)
}

func TestRecalculateEnergyCostCommand(t *testing.T) {
	aggregateID := uuid.MustParse("5e0f5caf-79d2-4396-b52a-0000000cfa00")

	tests := []struct {
		name            string
		expectedPayload charge.EnergyCostCorrectedPayload
		chargeEvents    []eventstore.Record
		expectedError   error
		mockErrors      mockErrors
		converter       copier.TypeConverter
	}{
		{
			name: "successful recalculation and update",
			expectedPayload: charge.EnergyCostCorrectedPayload{
				Cost:        5,
				Change:      5,
				SubmittedBy: ptr.To("XDP-1599"),
			},
			chargeEvents: sampleChargeCompletedEventWithCharger(),
			converter:    stringUUIDToIntConverter(),
		},
		{
			name: "error when loading charge event",
			expectedPayload: charge.EnergyCostCorrectedPayload{
				Cost:        7,
				Change:      3,
				SubmittedBy: ptr.To("XDP-1599"),
			},
			mockErrors: mockErrors{
				LoadMockError: errLoadChargeFailed,
			},
			converter:     stringUUIDToIntConverter(),
			expectedError: fmt.Errorf("failed to load charge: 5e0f5caf-79d2-4396-b52a-0000000cfa00, %w", errLoadChargeFailed),
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			ctrl := gomock.NewController(t)
			serviceMocks := newCostChargeServiceMocks(ctrl)

			serviceMocks.store.EXPECT().
				Load(gomock.AssignableToTypeOf(context.Background()), aggregateID).
				Return(tt.chargeEvents, tt.mockErrors.LoadMockError).
				AnyTimes()

			if tt.expectedError == nil {
				cost, err := numbers.Convert[int32, uint32](tt.expectedPayload.Cost)
				require.NoError(t, err)
				serviceMocks.energyCostCalculator.EXPECT().
					EnergyCost(gomock.Any(), gomock.Any()).
					Return(cost).
					AnyTimes()

				payload, _ := json.Marshal(tt.expectedPayload)
				expectedMessageBodyRegex := charge.GenerateExpectedMessageBodyRegex(aggregateID, charge.TypeEnergyCostCorrected, string(payload))

				serviceMocks.msgHandler.EXPECT().
					Process(context.Background(), gomock.Any()).
					DoAndReturn(func(_ context.Context, msg sqs.Message) error {
						regexMatch, err := regexp.MatchString(expectedMessageBodyRegex, *msg.Body)
						require.NoError(t, err)
						require.True(t, regexMatch)
						return nil
					}).
					AnyTimes()
			}

			serviceMocks.sqsClient.EXPECT().
				SendMessage(gomock.AssignableToTypeOf(context.Background()), gomock.Any()).
				Times(0)

			serviceMocks.assetSummaryService.EXPECT().
				GetOwner(gomock.Any(), gomock.Any()).
				Return(assetserviceapi.OWNERSHIP_POD_POINT, nil).
				AnyTimes()

			serviceMocks.tariffAPIService.EXPECT().
				Get(gomock.Any(), gomock.Any(), gomock.Any()).
				Return(sampleTariff(), nil).
				AnyTimes()

			serviceMocks.uUIDStringToIntConverter = tt.converter

			aggregateLoader := charge.NewAggregateLoader(serviceMocks.store)

			service := charge.NewCostChargeService(log.Default(), serviceMocks.sqsClient, aggregateLoader, serviceMocks.msgHandler, serviceMocks.energyCostCalculator, serviceMocks.costCalcRepo, tt.converter, serviceMocks.assetSummaryService, serviceMocks.tariffAPIService, serviceMocks.energyMetricsService)

			err := service.RecalculateEnergyCost(context.Background(), aggregateID, *tt.expectedPayload.SubmittedBy)
			require.Equal(t, tt.expectedError, err)
		})
	}
}

func stringUUIDToIntConverter() copier.TypeConverter {
	return copier.TypeConverter{
		SrcType: copier.String,
		DstType: copier.Int,
		Fn: func(src any) (any, error) {
			s, ok := src.(string)
			if !ok {
				return nil, errors.New("src must be a string")
			}
			_, err := uuid.Parse(s)
			if err != nil {
				return nil, errors.New("src must be a valid UUID")
			}
			hex := s[24:]
			i, err := strconv.ParseInt(hex, 16, strconv.IntSize)
			if err != nil {
				return nil, err
			}
			return int(i), nil
		},
	}
}

func sampleTariff() *energycost.Tariff {
	begin := time.Date(1, time.January, 1, 0, 0, 0, 0, time.UTC)
	end := time.Date(1, time.January, 1, 6, 0, 0, 0, time.UTC)

	tier := energycost.TariffTier{
		Rate:      30,
		BeginTime: &begin,
		EndTime:   &end,
		Days: []time.Weekday{
			time.Monday,
			time.Tuesday,
			time.Wednesday,
			time.Thursday,
			time.Friday,
			time.Saturday,
			time.Sunday,
		},
	}

	return &energycost.Tariff{
		Tiers: []energycost.TariffTier{tier},
	}
}

func sampleChargeCompletedEventWithCharger() []eventstore.Record {
	return []eventstore.Record{
		{
			Version: 0,
			Data: []byte(`{
				"event": "Charge.Completed",
				"aggregateID": "5e0f5caf-79d2-4396-b52a-0bb603162375",
				"payload": {
					"billing": {
						"settlementAmount": 123,
						"settlementCurrency": "GBP"
					},
					"energy": {
						"energyCost": 456,
						"energyCostCurrency": "GBP"
					},
					"chargingStation": {
						"id": "PSL-532880",
						"doorId": "A"
					},
					"charge": {
						"id": 12312312,
						"ref": "IOUNWEFWIFI",
						"pluggedInAt": "2023-06-14T09:17:16Z",
						"unpluggedAt": "2023-06-14T09:27:16Z",
						"energyTotal": 10.355,
						"generationEnergyTotal": 2.345,
						"gridEnergyTotal": 8.010,
						"chargeDurationTotal": 7726,
						"updatedAt": "2023-06-14T09:28:16Z",
						"startedAt": "2023-06-14T09:19:16Z",
						"endedAt": "2023-06-14T09:27:10Z"
					}
				}
			}`),
		},
	}
}

func sampleHistoricChargeCompletedEvent() []eventstore.Record {
	return []eventstore.Record{
		{
			Version: 0,
			Data: []byte(`{
				"event": "Charge.Completed",
				"aggregateID": "5e0f5caf-79d2-4396-b52a-0bb603162375",
				"payload": {
					"historic": true,
					"billing": {
						"settlementAmount": 123,
						"settlementCurrency": "GBP"
					},
					"energy": {
						"energyCost": 789,
						"energyCostCurrency": "GBP"
					},
					"chargingStation": {
						"id": "PSL-532880",
						"doorId": "A"
					},
					"charge": {
						"id": 12312312,
						"ref": "IOUNWEFWIFI",
						"pluggedInAt": "2023-06-14T09:17:16Z",
						"unpluggedAt": "2023-06-14T09:27:16Z",
						"energyTotal": 10.355,
						"generationEnergyTotal": 2.345,
						"gridEnergyTotal": 8.010,
						"chargeDurationTotal": 7726,
						"updatedAt": "2023-06-14T09:28:16Z",
						"startedAt": "2023-06-14T09:19:16Z",
						"endedAt": "2023-06-14T09:27:10Z"
					}
				}
			}`),
		},
	}
}
