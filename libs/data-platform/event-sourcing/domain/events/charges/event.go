package charges

import (
	"experience/libs/data-platform/event-sourcing/domain/events/charges/billing"
	"experience/libs/data-platform/event-sourcing/domain/events/charges/chargers"
	eventstore "experience/libs/shared/go/event-store"
	"experience/libs/shared/go/service/utils"
	"time"

	"github.com/google/uuid"
)

const (
	TypeCompleted                  eventstore.EventType = "Charge.Completed"
	TypeClaimed                    eventstore.EventType = "Charge.Claimed"
	TypeConfirmed                  eventstore.EventType = "Charge.Confirmed"
	TypeExpensed                   eventstore.EventType = "Charge.Expensed"
	TypeCosted                     eventstore.EventType = "Charge.Costed"
	TypeUserAdded                  eventstore.EventType = "Charge.UserAdded"
	TypeEnergyCostUpdated          eventstore.EventType = "Charge.EnergyCostUpdated"
	TypeEnergyCostCorrected        eventstore.EventType = "Charge.EnergyCostCorrected"
	TypeSettlementAmountCorrected  eventstore.EventType = "Charge.SettlementAmountCorrected"
	TypeBilled                     eventstore.EventType = "Charge.Billed"
	TypeRewardableEnergyAttributed eventstore.EventType = "Charge.RewardableEnergyAttributed"
)

func DefaultPublishedAt() time.Time {
	return time.Now().UTC().Round(time.Second)
}

type Base struct {
	AggregateID uuid.UUID            `json:"aggregateID"`
	Event       eventstore.EventType `json:"event"`
	EventID     string               `json:"eventID"`
	PublishedAt time.Time            `json:"publishedAt"`
	Historic    bool                 `json:"historic"`
}

func (e *Base) GetAggregateID() uuid.UUID {
	return e.AggregateID
}

func (e *Base) GetType() eventstore.EventType {
	return e.Event
}

type Completed struct {
	Base
	Payload CompletedPayload `json:"payload"`
}

type Payload struct {
	ID                    int        `json:"id"`
	Ref                   string     `json:"ref"`
	PluggedInAt           *time.Time `json:"pluggedInAt"`
	UnpluggedAt           *time.Time `json:"unpluggedAt"`
	EnergyTotal           *float64   `json:"energyTotal"`
	GenerationEnergyTotal *float64   `json:"generationEnergyTotal"`
	GridEnergyTotal       *float64   `json:"gridEnergyTotal"`
	ChargeDurationTotal   *int32     `json:"chargeDurationTotal"`
	UpdatedAt             *time.Time `json:"updatedAt"`
	StartedAt             *time.Time `json:"startedAt"`
	EndedAt               *time.Time `json:"endedAt"`
}

type ChargingStation struct {
	ID     chargers.StationID `json:"id"`
	DoorID string             `json:"doorId"`
}

type Billing struct {
	SettlementAmount   int    `json:"settlementAmount"`
	SettlementCurrency string `json:"settlementCurrency"`
}

type Location struct {
	ID int `json:"id"`
}

type Authorisation struct {
	Ref             any     `json:"ref"`
	Type            *string `json:"type"`
	ClaimedChargeID *int    `json:"claimedChargeId"`
	AuthoriserID    *int    `json:"authoriserId"`
	UserID          int     `json:"userId"`
}

type StatesSummary struct {
	StartedAt        string  `json:"startedAt"`
	EndedAt          string  `json:"endedAt"`
	Energy           float64 `json:"energy"`
	GenerationEnergy float64 `json:"generationEnergy"`
	GridEnergy       float64 `json:"gridEnergy"`
	State            string  `json:"state"`
}

type Energy struct {
	EnergyCost         *int32 `json:"energyCost"`
	EnergyCostCurrency string `json:"energyCostCurrency"`
}

type CompletedPayload struct {
	ChargingStation ChargingStation `json:"chargingStation"`
	Location        Location        `json:"location"`
	Authorisation   Authorisation   `json:"authorisation"`
	Charge          Payload         `json:"charge"`
	Billing         *Billing        `json:"billing"`
	StatesSummary   []StatesSummary `json:"statesSummary"`
	Energy          *Energy         `json:"energy"`
}

func NewClaimed(aggregateID uuid.UUID, groupID, siteID *uuid.UUID, userUUIDs []uuid.UUID, chargerID chargers.StationID, doorID, chargerType string, chargerName, chargerTimezone, groupName, siteName *string) Claimed {
	return Claimed{
		Base: NewBase(aggregateID, TypeClaimed),
		Payload: ClaimedPayload{
			UserIDs:         userUUIDs,
			ChargerID:       chargerID,
			ChargerName:     chargerName,
			Door:            doorID,
			ChargerType:     chargerType,
			ChargerTimezone: chargerTimezone,
			SiteName:        siteName,
			GroupID:         groupID,
			GroupName:       groupName,
			SiteID:          siteID,
		},
	}
}

type Claimed struct {
	Base
	Payload ClaimedPayload `json:"payload"`
}

type ClaimedPayload struct {
	UserIDs         []uuid.UUID        `json:"userIDs"`
	ChargerID       chargers.StationID `json:"chargerID"`
	ChargerName     *string            `json:"chargerName"`
	Door            string             `json:"door"`
	ChargerType     string             `json:"chargerType"`
	ChargerTimezone *string            `json:"chargerTimezone"`
	SiteName        *string            `json:"siteName"`
	SiteID          *uuid.UUID         `json:"siteID"`
	GroupID         *uuid.UUID         `json:"groupID"`
	GroupName       *string            `json:"groupName"`
}

func NewConfirmed(aggregateID, chargeAuthorisationID uuid.UUID, authoriserType, authoriserID *string) Confirmed {
	return Confirmed{
		Base: NewBase(aggregateID, TypeConfirmed),
		Payload: ConfirmedPayload{
			AuthoriserType:        authoriserType,
			AuthoriserID:          authoriserID,
			ChargeAuthorisationID: &chargeAuthorisationID,
		},
	}
}

type Confirmed struct {
	Base
	Payload ConfirmedPayload `json:"payload"`
}

type ConfirmedPayload struct {
	AuthoriserType        *string    `json:"authoriserType"`
	AuthoriserID          *string    `json:"authoriserID"`
	ChargeAuthorisationID *uuid.UUID `json:"chargeAuthorisationID"`
}

func NewExpensed(chargeID uint32, groupID uuid.UUID, groupName string) Expensed {
	return Expensed{
		Base:    NewBase(utils.FabricateUUIDFromNumericID(int(chargeID)), TypeExpensed),
		Payload: ExpensedPayload{GroupID: groupID, GroupName: groupName},
	}
}

type Expensed struct {
	Base
	Payload ExpensedPayload `json:"payload"`
}

type ExpensedPayload struct {
	GroupID   uuid.UUID `json:"groupID"`
	GroupName string    `json:"groupName"`
}

func NewCosted(aggregateID uuid.UUID, energyCostAmount *int32, energyCostCurrency string) Costed {
	return Costed{
		Base: NewBase(aggregateID, TypeCosted),
		Payload: CostedPayload{
			EnergyCost:         energyCostAmount,
			EnergyCostCurrency: energyCostCurrency,
		},
	}
}

type Costed struct {
	Base
	Payload CostedPayload `json:"payload"`
}

type CostedPayload struct {
	EnergyCost         *int32 `json:"energyCost"`
	EnergyCostCurrency string `json:"energyCostCurrency"`
}

type UserAdded struct {
	Base
	Payload UserAddedPayload `json:"payload"`
}

type UserAddedPayload struct {
	UserID uuid.UUID `json:"userID"`
}

type UserAddedFactory interface {
	New(aggregateID, userID uuid.UUID) UserAdded
}

type UserAdder struct{}

func (u *UserAdder) New(aggregateID, userID uuid.UUID) UserAdded {
	return UserAdded{
		Base:    NewBase(aggregateID, TypeUserAdded),
		Payload: UserAddedPayload{UserID: userID},
	}
}

type EnergyCostUpdated struct {
	Base
	Payload EnergyCostUpdatedPayload `json:"payload"`
}

type EnergyCostUpdatedPayload struct {
	EnergyCost         int32  `json:"energyCost"`
	EnergyCostCurrency string `json:"energyCostCurrency"`
}

type EnergyCostCorrectedPayload struct {
	Cost        int32   `json:"cost"`
	Change      int32   `json:"change"`
	SubmittedBy *string `json:"submittedBy"`
}

type EnergyCostCorrected struct {
	Base
	Payload EnergyCostCorrectedPayload `json:"payload"`
}

func NewEnergyCostCorrected(aggregateID uuid.UUID, cost, change int32, submittedBy *string) eventstore.Event {
	return &EnergyCostCorrected{
		Base: NewBase(aggregateID, TypeEnergyCostCorrected),
		Payload: EnergyCostCorrectedPayload{
			Cost:        cost,
			Change:      change,
			SubmittedBy: submittedBy,
		},
	}
}

type SettlementAmountCorrectedPayload struct {
	SettlementAmount int32   `json:"settlementAmount"`
	Change           int32   `json:"change"`
	SubmittedBy      *string `json:"submittedBy"`
}

type SettlementAmountCorrected struct {
	Base
	Payload SettlementAmountCorrectedPayload `json:"payload"`
}

func NewSettlementAmountCorrected(aggregateID uuid.UUID, settlementAmount, change int32, submittedBy *string) eventstore.Event {
	return &SettlementAmountCorrected{
		Base: NewBase(aggregateID, TypeSettlementAmountCorrected),
		Payload: SettlementAmountCorrectedPayload{
			SettlementAmount: settlementAmount,
			Change:           change,
			SubmittedBy:      submittedBy,
		},
	}
}

func NewBilled(aggregateID uuid.UUID, settlementAmount int32, settlementCurrency string, billingType billing.Type) Billed {
	return Billed{
		Base: NewBase(aggregateID, TypeBilled),
		Payload: BilledPayload{
			SettlementAmount:   settlementAmount,
			SettlementCurrency: settlementCurrency,
			BillingType:        billingType.String(),
		},
	}
}

type Billed struct {
	Base
	Payload BilledPayload `json:"payload"`
}

type BilledPayload struct {
	SettlementAmount   int32  `json:"settlementAmount"`
	SettlementCurrency string `json:"settlementCurrency"`
	BillingType        string `json:"billingType"`
}

func NewRewardableEnergyAttributed(aggregateID uuid.UUID, eligibleEnergy float32, vehicleID *uuid.UUID) RewardableEnergyAttributed {
	return RewardableEnergyAttributed{
		Base: NewBase(aggregateID, TypeRewardableEnergyAttributed),
		Payload: RewardableEnergyAttributedPayload{
			EligibleEnergy: eligibleEnergy,
			VehicleID:      vehicleID,
		},
	}
}

type RewardableEnergyAttributedPayload struct {
	EligibleEnergy float32    `json:"eligibleEnergy"`
	VehicleID      *uuid.UUID `json:"vehicleID"`
}

type RewardableEnergyAttributed struct {
	Base
	Payload RewardableEnergyAttributedPayload `json:"payload"`
}

func NewBase(id uuid.UUID, eventType eventstore.EventType) Base {
	return Base{
		AggregateID: id,
		Event:       eventType,
		EventID:     uuid.NewString(),
		PublishedAt: DefaultPublishedAt(),
	}
}
