package charges_test

import (
	"encoding/json"
	charge "experience/libs/data-platform/event-sourcing/domain/events/charges"
	eventstore "experience/libs/shared/go/event-store"
	"experience/libs/shared/go/sqs"
	"testing"
	"time"

	"k8s.io/utils/ptr"

	"github.com/stretchr/testify/require"

	"github.com/google/uuid"
)

func TestTransformer_Transform(t *testing.T) {
	fabricatedUUID, _ := uuid.Parse("570b2ccf-824d-4076-9da5-" + "000000bbdef8")

	tests := []struct {
		name             string
		messageInput     sqs.Message
		realUUID         bool
		expectedEvent    eventstore.Event
		expectedErrorStr string
	}{
		{
			name: "transform successful charge completed message",
			messageInput: sqs.Message{
				Body: &chargeCompleteBody,
			},
			expectedEvent: &charge.Completed{
				Base: charge.Base{
					AggregateID: fabricatedUUID,
					Event:       "Charge.Completed",
					EventID:     "UUID",
					PublishedAt: time.Date(2023, 6, 14, 9, 30, 0, 0, time.UTC),
				},
				Payload: completedChargePayload,
			},
		},
		{
			name: "transform successful historic completed message",
			messageInput: sqs.Message{
				Body: &chargeCompleteHistoricBody,
			},
			expectedEvent: &charge.Completed{
				Base: charge.Base{
					AggregateID: fabricatedUUID,
					Event:       "Charge.Completed",
					EventID:     "UUID",
					PublishedAt: time.Date(2023, 6, 14, 9, 30, 0, 0, time.UTC),
					Historic:    true,
				},
				Payload: completedChargePayload,
			},
		},
		{
			name: "transform successful missing charge id gives random uuid for aggregate",
			messageInput: sqs.Message{
				Body: ptr.To(`{"event":"Charge.Completed","publishedAt":"2023-06-14T09:30:00Z","payload":{}}`),
			},
			realUUID: true,
			expectedEvent: &charge.Completed{
				Base: charge.Base{
					AggregateID: fabricatedUUID,
					Event:       charge.TypeCompleted,
					EventID:     "",
					PublishedAt: time.Date(2023, 6, 14, 9, 30, 0, 0, time.UTC),
				},
				Payload: charge.CompletedPayload{},
			},
		},
		{
			name: "transform error invalid json event",
			messageInput: sqs.Message{
				Body: ptr.To(":!abc"),
			},
			expectedErrorStr: "invalid character",
		},
		{
			name: "transform throws expectedErr for other charge event",
			messageInput: sqs.Message{
				Body: ptr.To(`{"event": "Charge.Other"}`),
			},
			expectedErrorStr: "unexpected event type received",
		},
		{
			name: "transform throws error for empty event",
			messageInput: sqs.Message{
				Body: ptr.To(`{}`),
			},
			expectedErrorStr: "unexpected event type received",
		},
		{
			name: "transform charge claimed message",
			messageInput: sqs.Message{
				Body: &chargeClaimedBody,
			},
			expectedEvent: &charge.Claimed{
				Base: charge.Base{
					AggregateID: uuid.MustParse("ced0d74d-b171-40ff-8661-8a43c331f930"),
					Event:       charge.TypeClaimed,
				},
				Payload: charge.ClaimedPayload{
					ChargerID:   "PSL-12345",
					ChargerName: ptr.To("Charger-Name"),
				},
			},
		},
		{
			name: "transform charge confirmed message",
			messageInput: sqs.Message{
				Body: &chargeConfirmedBody,
			},
			expectedEvent: &charge.Confirmed{
				Base: charge.Base{
					AggregateID: uuid.MustParse("deb0d74d-b171-40ff-8661-8a43c331f930"),
					Event:       charge.TypeConfirmed,
				},
				Payload: charge.ConfirmedPayload{
					AuthoriserType: ptr.To("rfid"),
					AuthoriserID:   ptr.To("d97fcb81-1d09-4e81-bfd3-3c51373a56f2"),
				},
			},
		},
		{
			name: "transform charge expensed message",
			messageInput: sqs.Message{
				Body: &chargeExpensedBody,
			},
			expectedEvent: &charge.Expensed{
				Base: charge.Base{
					AggregateID: uuid.MustParse("ced0d74d-b171-40ff-8661-8a43c331f930"),
					Event:       charge.TypeExpensed,
				},
				Payload: charge.ExpensedPayload{
					GroupName: "Test Group",
					GroupID:   uuid.MustParse("b903ceb3-0f1c-4c63-a6be-6823e4d0d60a"),
				},
			},
		},
		{
			name: "transform charge costed message",
			messageInput: sqs.Message{
				Body: &chargeCostedBody,
			},
			expectedEvent: &charge.Costed{
				Base: charge.Base{
					AggregateID: uuid.MustParse("2c5c6d2c-56d3-11ee-8c99-0242ac120002"),
					Event:       charge.TypeCosted,
				},
				Payload: charge.CostedPayload{
					EnergyCost:         ptr.To(int32(2)),
					EnergyCostCurrency: "GBP",
				},
			},
		},
		{
			name: "transform user added event",
			messageInput: sqs.Message{
				Body: &userAddedBody,
			},
			expectedEvent: &charge.UserAdded{
				Base: charge.Base{
					AggregateID: uuid.MustParse("dcce8f8b-203b-40dd-9504-1e3cfe196e88"),
					Event:       charge.TypeUserAdded,
				},
				Payload: charge.UserAddedPayload{
					UserID: uuid.MustParse("af85caec-8d37-4fc6-9d82-07cd4e15b1d3"),
				},
			},
		},
		{
			name: "transform energy cost updated event",
			messageInput: sqs.Message{
				Body: &energyCostUpdateBody,
			},
			expectedEvent: &charge.EnergyCostUpdated{
				Base: charge.Base{
					AggregateID: uuid.MustParse("a39d28b5-f104-4181-bb95-c9fe2d2add58"),
					Event:       charge.TypeEnergyCostUpdated,
					EventID:     "8a7852a4-cba9-49bd-8dd2-70e921bb6a17",
					PublishedAt: time.Date(2024, 2, 19, 15, 35, 27, 0, time.UTC),
					Historic:    false,
				},
				Payload: charge.EnergyCostUpdatedPayload{
					EnergyCost:         32,
					EnergyCostCurrency: "GBP",
				},
			},
		},
		{
			name: "transform energy cost corrected event",
			messageInput: sqs.Message{
				Body: &energyCostCorrectedBody,
			},
			expectedEvent: &charge.EnergyCostCorrected{
				Base: charge.Base{
					AggregateID: uuid.MustParse("2301fe62-8ab1-4b9a-809c-4fe747eb0c64"),
					Event:       charge.TypeEnergyCostCorrected,
					EventID:     "b908b4ec-cdef-421e-90a7-9335bb9974f7",
					PublishedAt: time.Date(2024, 5, 1, 11, 11, 27, 0, time.UTC),
					Historic:    false,
				},
				Payload: charge.EnergyCostCorrectedPayload{
					Cost:        19,
					Change:      2,
					SubmittedBy: ptr.To("test-submitter"),
				},
			},
		},
		{
			name: "transform settlement amount corrected event",
			messageInput: sqs.Message{
				Body: &settlementAmountCorrectedBody,
			},
			expectedEvent: &charge.SettlementAmountCorrected{
				Base: charge.Base{
					AggregateID: uuid.MustParse("f75dc1cb-36b1-4a3e-a5a0-4e2fb9aaedc0"),
					Event:       charge.TypeSettlementAmountCorrected,
					EventID:     "e1650736-2c5e-4214-88df-8d14dc5a6975",
					PublishedAt: time.Date(2024, 6, 25, 14, 40, 27, 0, time.UTC),
					Historic:    false,
				},
				Payload: charge.SettlementAmountCorrectedPayload{
					SettlementAmount: 49,
					Change:           10,
					SubmittedBy:      ptr.To("testy-mc-test"),
				},
			},
		},
		{
			name: "transform charge billed event",
			messageInput: sqs.Message{
				Body: &chargeBilledBody,
			},
			expectedEvent: &charge.Billed{
				Base: charge.Base{
					AggregateID: uuid.MustParse("f78033e6-3ca3-4ffa-99a7-728caf63d705"),
					Event:       charge.TypeBilled,
					EventID:     "f6e7e649-c35a-4418-b60f-6791b4ff3407",
					PublishedAt: time.Date(2024, 6, 25, 14, 40, 27, 0, time.UTC),
					Historic:    false,
				},
				Payload: charge.BilledPayload{
					SettlementAmount:   49,
					SettlementCurrency: "GBP",
					BillingType:        "wallet",
				},
			},
		},
		{
			name: "transform rewardable energy attributed event",
			messageInput: sqs.Message{
				Body: &rewardableEnergyAttributedBody,
			},
			expectedEvent: &charge.RewardableEnergyAttributed{
				Base: charge.Base{
					AggregateID: uuid.MustParse("f78033e6-3ca3-4ffa-99a7-728caf63d705"),
					Event:       charge.TypeRewardableEnergyAttributed,
					EventID:     "f6e7e649-c35a-4418-b60f-6791b4ff3407",
					PublishedAt: time.Date(2024, 6, 25, 14, 40, 27, 0, time.UTC),
					Historic:    false,
				},
				Payload: charge.RewardableEnergyAttributedPayload{
					EligibleEnergy: 49,
					VehicleID:      ptr.To(uuid.MustParse("f6e7e649-c35a-4418-b60f-6791b4ff3407")),
				},
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			transformer := charge.Transformer{}

			actualEvent, actualData, err := transformer.Transform(tt.messageInput.Body)

			if tt.expectedErrorStr == "" {
				require.NoError(t, err)
			} else {
				require.ErrorContains(t, err, tt.expectedErrorStr)
			}

			if tt.realUUID {
				require.Equal(t, json.RawMessage(*tt.messageInput.Body), actualData)
				require.IsType(t, uuid.UUID{}, actualEvent.GetAggregateID())
				require.NotEqual(t, tt.expectedEvent.GetAggregateID(), actualEvent.GetAggregateID())
			} else {
				require.Equal(t, tt.expectedEvent, actualEvent)
			}
		})
	}
}

var completedChargePayload = charge.CompletedPayload{
	ChargingStation: charge.ChargingStation{
		ID:     "PSL-532880",
		DoorID: "A",
	},
	Location: charge.Location{
		ID: 12345,
	},
	Authorisation: charge.Authorisation{
		Type:            ptr.To("user"),
		ClaimedChargeID: ptr.To(12345),
		AuthoriserID:    ptr.To(9898),
		UserID:          12345,
	},
	Charge: charge.Payload{
		ID:                    12312312,
		Ref:                   "IOUNWEFWIFI",
		PluggedInAt:           ptr.To(time.Date(2023, 6, 14, 9, 17, 16, 0, time.UTC)),
		UnpluggedAt:           ptr.To(time.Date(2023, 6, 14, 9, 27, 16, 0, time.UTC)),
		EnergyTotal:           ptr.To(10.355),
		GenerationEnergyTotal: ptr.To(2.345),
		GridEnergyTotal:       ptr.To(8.010),
		ChargeDurationTotal:   ptr.To(int32(7726)),
		UpdatedAt:             ptr.To(time.Date(2023, 6, 14, 9, 28, 16, 0, time.UTC)),
		StartedAt:             ptr.To(time.Date(2023, 6, 14, 9, 19, 16, 0, time.UTC)),
		EndedAt:               ptr.To(time.Date(2023, 6, 14, 9, 27, 10, 0, time.UTC)),
	},
	Billing: &charge.Billing{
		SettlementAmount:   1976,
		SettlementCurrency: "GBP",
	},
	StatesSummary: []charge.StatesSummary{{
		Energy:           0.1,
		GenerationEnergy: 0.012,
		GridEnergy:       0.088,
		State:            "Charging",
	}},
}

var chargeCompleteBody = `{
  "event": "Charge.Completed",
    "eventID": "UUID",
    "publishedAt": "2023-06-14T09:30:00Z",
  "payload": {
    "chargingStation": {
      "id": "PSL-532880",
      "doorId": "A"
    },
    "location": {
      "id": 12345
    },
    "authorisation": {
      "ref": null,
      "type": "user",
      "claimedChargeId": 12345,
      "authoriserId": 9898,
      "userId": 12345
    },
    "charge": {
      "id": 12312312,
      "ref": "IOUNWEFWIFI",
      "pluggedInAt": "2023-06-14T09:17:16Z",
      "unpluggedAt": "2023-06-14T09:27:16Z",
      "energyTotal": 10.355,
      "generationEnergyTotal": 2.345,
			"gridEnergyTotal": 8.010,
      "chargeDurationTotal" : 7726,
      "updatedAt":  "2023-06-14T09:28:16Z",
      "startedAt": "2023-06-14T09:19:16Z",
      "endedAt": "2023-06-14T09:27:10Z"
    },
    "billing": {
       "settlementAmount": 1976,
       "settlementCurrency": "GBP",
       "presentmentAmount": 1976,
       "presentmentCurrency": "GBP"
    },
    "statesSummary": [
      {
        "startedAt": "",
        "endedAt": "",
        "energy": 0.1,
        "generationEnergy": 0.012,
				"gridEnergy": 0.088,
        "state": "Charging"
      }
    ]
  }
}`

var chargeCompleteHistoricBody = `{
  "event": "Charge.Completed",
    "eventID": "UUID",
    "publishedAt": "2023-06-14T09:30:00Z",
    "historic": true,
  "payload": {
    "chargingStation": {
      "id": "PSL-532880",
      "doorId": "A"
    },
    "location": {
      "id": 12345
    },
    "authorisation": {
      "ref": null,
      "type": "user",
      "claimedChargeId": 12345,
      "authoriserId": 9898,
      "userId": 12345
    },
    "charge": {
      "id": 12312312,
      "ref": "IOUNWEFWIFI",
      "pluggedInAt": "2023-06-14T09:17:16Z",
      "unpluggedAt": "2023-06-14T09:27:16Z",
      "energyTotal": 10.355,
      "generationEnergyTotal": 2.345,
			"gridEnergyTotal": 8.010,
      "chargeDurationTotal" : 7726,
      "updatedAt":  "2023-06-14T09:28:16Z",
      "startedAt": "2023-06-14T09:19:16Z",
      "endedAt": "2023-06-14T09:27:10Z"
    },
    "billing": {
       "settlementAmount": 1976,
       "settlementCurrency": "GBP",
       "presentmentAmount": 1976,
       "presentmentCurrency": "GBP"
    },
    "statesSummary": [
      {
        "startedAt": "",
        "endedAt": "",
        "energy": 0.1,
        "generationEnergy": 0.012,
				"gridEnergy": 0.088,
        "state": "Charging"
      }
    ]
  }
}`

var chargeClaimedBody = `{
  "event": "Charge.Claimed",
  "aggregateID": "ced0d74d-b171-40ff-8661-8a43c331f930",
  "payload": {
		"chargerID": "PSL-12345",
		"chargerName": "Charger-Name"
  }
}`

var chargeConfirmedBody = `{
  "event": "Charge.Confirmed",
  "aggregateID": "deb0d74d-b171-40ff-8661-8a43c331f930",
  "payload": {
		"authoriserType": "rfid",
		"authoriserID": "d97fcb81-1d09-4e81-bfd3-3c51373a56f2"
  }
}`

var chargeExpensedBody = `{
  "event": "Charge.Expensed",
  "aggregateID": "ced0d74d-b171-40ff-8661-8a43c331f930",
  "payload": {
    "groupId": "b903ceb3-0f1c-4c63-a6be-6823e4d0d60a",
    "groupName": "Test Group"
  }
}`

var chargeCostedBody = `{
  "event": "Charge.Costed",
  "aggregateID": "2c5c6d2c-56d3-11ee-8c99-0242ac120002",
  "payload": {
    "settlementAmount": 4,
    "settlementCurrency": "GBP",
    "energyCost": 2,
    "energyCostCurrency": "GBP"
  }
}`

var userAddedBody = `{
  "event": "Charge.UserAdded",
  "aggregateID": "dcce8f8b-203b-40dd-9504-1e3cfe196e88",
  "payload": {
    "userID": "af85caec-8d37-4fc6-9d82-07cd4e15b1d3"
  }
}`

var energyCostUpdateBody = `{
  "aggregateID": "a39d28b5-f104-4181-bb95-c9fe2d2add58",
  "event": "Charge.EnergyCostUpdated",
  "eventID": "8a7852a4-cba9-49bd-8dd2-70e921bb6a17",
  "publishedAt": "2024-02-19T15:35:27Z",
  "historic": false,
  "payload": {
    "energyCost": 32,
    "energyCostCurrency": "GBP"
  }
}`

var energyCostCorrectedBody = `{
	"aggregateID": "2301fe62-8ab1-4b9a-809c-4fe747eb0c64",
	"event": "Charge.EnergyCostCorrected",
	"eventID": "b908b4ec-cdef-421e-90a7-9335bb9974f7",
	"publishedAt": "2024-05-01T11:11:27Z",
	"historic": false,
	"payload": {
	  "cost": 19,
	  "change": 2,
	  "submittedBy": "test-submitter"
	}
  }`

var settlementAmountCorrectedBody = `{
  "aggregateID": "f75dc1cb-36b1-4a3e-a5a0-4e2fb9aaedc0",
  "event": "Charge.SettlementAmountCorrected",
  "eventID": "e1650736-2c5e-4214-88df-8d14dc5a6975",
  "publishedAt": "2024-06-25T14:40:27Z",
  "historic": false,
  "payload": {
    "settlementAmount": 49,
    "change": 10,
	"submittedBy": "testy-mc-test"
  }
}`

var chargeBilledBody = `{
  "aggregateID": "f78033e6-3ca3-4ffa-99a7-728caf63d705",
  "event": "Charge.Billed",
  "eventID": "f6e7e649-c35a-4418-b60f-6791b4ff3407",
  "publishedAt": "2024-06-25T14:40:27Z",
  "historic": false,
  "payload": {
    "settlementAmount": 49,
    "settlementCurrency": "GBP",
    "billingType": "wallet"
  }
}`

var rewardableEnergyAttributedBody = `{
  "aggregateID": "f78033e6-3ca3-4ffa-99a7-728caf63d705",
  "event": "Charge.RewardableEnergyAttributed",
  "eventID": "f6e7e649-c35a-4418-b60f-6791b4ff3407",
  "publishedAt": "2024-06-25T14:40:27Z",
  "historic": false,
  "payload": {
    "eligibleEnergy": 49,
    "vehicleID": "f6e7e649-c35a-4418-b60f-6791b4ff3407"
  }
}`
