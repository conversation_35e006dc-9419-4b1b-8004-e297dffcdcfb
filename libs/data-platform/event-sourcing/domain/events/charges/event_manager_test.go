package charges_test

import (
	"bytes"
	"context"
	"encoding/json"
	"errors"
	"experience/libs/data-platform/event-sourcing/domain/events/charges"
	eventstore "experience/libs/shared/go/event-store"
	"experience/libs/shared/go/event-store/handlers"
	mockhandlers "experience/libs/shared/go/event-store/handlers/mock"
	mocks "experience/libs/shared/go/event-store/test/mock"
	"experience/libs/shared/go/sqs"
	"fmt"
	"log"
	"testing"
	"time"

	"github.com/google/uuid"
	"github.com/stretchr/testify/require"
	"k8s.io/utils/ptr"

	"go.uber.org/mock/gomock"
)

func TestStoreMessageHandler(t *testing.T) {
	tests := []struct {
		name                       string
		msgBody                    string
		msgID                      *string
		mockEvent                  eventstore.Event
		mockTransformError         error
		mockTransformResponse      json.RawMessage
		expectedResponse           error
		mockEventHandler1CallTimes int
		mockEventHandler1Error     error
		mockEventHandler2CallTimes int
		mockEventHandler2Error     error
		bindExtraHandler           bool
		mockEventHandler3CallTimes int
		mockEventHandler3Error     error
		handlerAllowsError         bool
		expectedLog                string
	}{
		{
			name:    "Message handler calls all registered event handlers",
			msgID:   ptr.To("msgID"),
			msgBody: "msgBody",
			mockEvent: &MockEvent{
				aggregateID: uuid.MustParse("00000000-0000-0000-0000-000000000000"),
				eventType:   "one",
			},
			mockTransformResponse:      json.RawMessage(`{"type": "one"}`),
			mockEventHandler1CallTimes: 1,
			mockEventHandler2CallTimes: 1,
			expectedLog:                "processing message msgID event of type one with aggregateID 00000000-0000-0000-0000-000000000000 at 2023-10-01T12:00:00.123456Z",
		},
		{
			name:                  "Message handler returns error on empty event",
			msgBody:               "msgBody",
			mockTransformResponse: json.RawMessage(`{"type": "one"}`),
		},
		{
			name:                  "Message handler surfaces transformer error",
			msgBody:               "msgBody",
			mockEvent:             &MockEvent{},
			mockTransformError:    errors.New("transformer failed"),
			mockTransformResponse: json.RawMessage(`{"type": "one"}`),
			expectedResponse:      errors.New("transformer failed"),
		},
		{
			name:                       "Message handler surfaces error from any of the event handlers",
			msgID:                      ptr.To("msgID"),
			msgBody:                    "msgBody",
			mockEvent:                  &MockEvent{},
			mockTransformResponse:      json.RawMessage(`{"type": "one"}`),
			expectedResponse:           fmt.Errorf("event manager for aggregate 00000000-0000-0000-0000-000000000000: %w", errors.New("handler1 error")),
			mockEventHandler1CallTimes: 1,
			mockEventHandler1Error:     errors.New("handler1 error"),
			mockEventHandler2CallTimes: 1,
		},
		{
			name:                       "Message handler surfaces errors from multiple handlers",
			msgID:                      ptr.To("msgID"),
			msgBody:                    "msgBody",
			mockEvent:                  &MockEvent{},
			mockTransformResponse:      json.RawMessage(`{"type": "one"}`),
			expectedResponse:           fmt.Errorf("event manager for aggregate 00000000-0000-0000-0000-000000000000: %w; %w", errors.New("handler1 error"), errors.New("handler2 error")),
			mockEventHandler1CallTimes: 1,
			mockEventHandler1Error:     errors.New("handler1 error"),
			mockEventHandler2CallTimes: 1,
			mockEventHandler2Error:     errors.New("handler2 error"),
		},
		{
			name:                       "Message handler surfaces errors from multiple handlers",
			msgID:                      ptr.To("msgID"),
			msgBody:                    "msgBody",
			mockEvent:                  &MockEvent{},
			mockTransformResponse:      json.RawMessage(`{"type": "one"}`),
			expectedResponse:           fmt.Errorf("event manager for aggregate 00000000-0000-0000-0000-000000000000: %w; %w; %w", errors.New("handler1 error"), errors.New("handler2 error"), errors.New("handler3 error")),
			mockEventHandler1CallTimes: 1,
			mockEventHandler1Error:     errors.New("handler1 error"),
			mockEventHandler2CallTimes: 1,
			mockEventHandler2Error:     errors.New("handler2 error"),
			bindExtraHandler:           true,
			mockEventHandler3CallTimes: 1,
			mockEventHandler3Error:     errors.New("handler3 error"),
		},
		{
			name:                       "Message handler ignores allowed errors from event handler",
			msgID:                      ptr.To("msgID"),
			msgBody:                    "msgBody",
			mockEvent:                  &MockEvent{},
			mockTransformResponse:      json.RawMessage(`{"type": "one"}`),
			mockEventHandler1CallTimes: 1,
			mockEventHandler1Error:     errors.New("handler1 error"),
			mockEventHandler2CallTimes: 1,
			handlerAllowsError:         true,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			ctrl := gomock.NewController(t)
			defer ctrl.Finish()

			mockTransformer := mocks.NewMockEventTransformer(ctrl)
			mockSyncEventHandler1 := mockhandlers.NewMockSyncEventHandler(ctrl)
			mockSyncEventHandler2 := mockhandlers.NewMockSyncEventHandler(ctrl)

			syncEventHandlers := []handlers.SyncEventHandler{mockSyncEventHandler1, mockSyncEventHandler2}

			var buf bytes.Buffer
			logger := log.New(&buf, "", 0)
			fixedTime, _ := time.Parse(time.RFC3339Nano, "2023-10-01T12:00:00.123456Z")
			msgHandler := charges.NewEventManager(logger, mockTransformer, syncEventHandlers, func() time.Time { return fixedTime })

			if tt.bindExtraHandler {
				mockSyncEventHandler3 := mockhandlers.NewMockSyncEventHandler(ctrl)
				mockSyncEventHandler3.EXPECT().Execute(context.Background(), tt.mockEvent, tt.mockTransformResponse).Times(tt.mockEventHandler3CallTimes).Return(tt.mockEventHandler3Error)
				mockSyncEventHandler3.EXPECT().ErrorAllowed(gomock.Any()).AnyTimes().Return(tt.handlerAllowsError)
				msgHandler.BindHandlers([]handlers.SyncEventHandler{mockSyncEventHandler3})
			}

			mockTransformer.EXPECT().Transform(gomock.Any()).Times(1).Return(tt.mockEvent, tt.mockTransformResponse, tt.mockTransformError)
			mockSyncEventHandler1.EXPECT().Execute(context.Background(), tt.mockEvent, tt.mockTransformResponse).Times(tt.mockEventHandler1CallTimes).Return(tt.mockEventHandler1Error)
			mockSyncEventHandler2.EXPECT().Execute(context.Background(), tt.mockEvent, tt.mockTransformResponse).Times(tt.mockEventHandler2CallTimes).Return(tt.mockEventHandler2Error)

			mockSyncEventHandler1.EXPECT().ErrorAllowed(gomock.Any()).AnyTimes().Return(tt.handlerAllowsError)
			mockSyncEventHandler2.EXPECT().ErrorAllowed(gomock.Any()).AnyTimes().Return(tt.handlerAllowsError)

			actual := msgHandler.Process(context.Background(), sqs.Message{
				MessageId: tt.msgID,
				Body:      &tt.msgBody,
			})

			if tt.expectedResponse != nil {
				require.EqualError(t, actual, tt.expectedResponse.Error())
			} else {
				require.Equal(t, tt.expectedResponse, actual)
			}

			if tt.expectedLog != "" {
				require.Contains(t, buf.String(), tt.expectedLog)
			}
		})
	}
}

type MockEvent struct {
	aggregateID uuid.UUID
	eventType   eventstore.EventType
}

func (m MockEvent) GetAggregateID() uuid.UUID {
	return m.aggregateID
}

func (m MockEvent) GetType() eventstore.EventType {
	return m.eventType
}
