package charges

import (
	"context"
	errorfmt "experience/libs/shared/go/error-fmt"
	eventstore "experience/libs/shared/go/event-store"
	"experience/libs/shared/go/event-store/handlers"
	"experience/libs/shared/go/sqs"
	"fmt"
	"log"
	"time"

	"k8s.io/utils/ptr"
)

type EventManager struct {
	logger        *log.Logger
	transformer   eventstore.EventTransformer
	eventHandlers []handlers.SyncEventHandler
	nowFunc       func() time.Time
}

func NewEventManager(logger *log.Logger, transformer eventstore.EventTransformer, syncEventHandlers []handlers.SyncEventHandler, nowFunc func() time.Time) *EventManager {
	return &EventManager{
		logger:        logger,
		transformer:   transformer,
		eventHandlers: syncEventHandlers,
		nowFunc:       nowFunc,
	}
}

func (e *EventManager) Process(ctx context.Context, message sqs.Message) error {
	event, data, err := e.transformer.Transform(message.Body)
	if err != nil {
		return err
	}
	if event == nil {
		return nil
	}
	e.logger.Printf("processing message %s event of type %s with aggregateID %s at %s", ptr.Deref(message.MessageId, ""), event.GetType(), event.GetAggregateID(), e.nowFunc().UTC().Format(time.RFC3339Nano))

	var errWrapper errorfmt.ErrorWrapper
	for _, handler := range e.eventHandlers {
		err = handler.Execute(ctx, event, data)
		if !handler.ErrorAllowed(err) {
			errWrapper.AddError(err)
		}
	}
	return errWrapper.SetPrefix(fmt.Sprintf("event manager for aggregate %s: ", event.GetAggregateID())).Wrap()
}

func (e *EventManager) BindHandlers(syncEventHandlers []handlers.SyncEventHandler) {
	e.eventHandlers = append(e.eventHandlers, syncEventHandlers...)
}
