package charges

import (
	"context"
	energycost "experience/libs/data-platform/cost-calculation/energy-cost"
	"experience/libs/data-platform/event-sourcing/domain/events/charges/cost"
	assetserviceapi "experience/libs/shared/go/external/asset-service-api/src"
	"experience/libs/shared/go/numbers"
	"experience/libs/shared/go/sqs"
	"fmt"
	"time"

	"github.com/jinzhu/copier"
	"github.com/zlasd/tzloc"

	"github.com/biter777/countries"

	"log"

	"k8s.io/utils/ptr"

	"github.com/google/uuid"
)

var evseMap = map[string]string{
	"A": "1",
	"B": "2",
	"C": "3",
}

type costChargeServiceImpl struct {
	logger                       *log.Logger
	sqsClient                    sqs.ClientOperations
	aggregateLoader              AggregateLoader
	msgHandler                   sqs.MessageHandler
	energyCostCalculator         energycost.Calculator
	costCalcRepository           cost.Repository
	podadminRepository           PodadminRepository
	aggregateToChargeIDConverter copier.TypeConverter
	assetSummary                 cost.AssetSummary
	tariffData                   cost.TariffData
	energyMetrics                cost.EnergyMetrics
}

func NewCostChargeService(logger *log.Logger, sqsClient sqs.ClientOperations, aggregateLoader AggregateLoader, msgHandler sqs.MessageHandler, energyCostCalculator energycost.Calculator, costCalcRepository cost.Repository, aggregateToChargeIDConverter copier.TypeConverter, assetSummary cost.AssetSummary, tariffData cost.TariffData, energyMetrics cost.EnergyMetrics) CostChargeService {
	return &costChargeServiceImpl{
		logger:                       logger,
		sqsClient:                    sqsClient,
		aggregateLoader:              aggregateLoader,
		msgHandler:                   msgHandler,
		energyCostCalculator:         energyCostCalculator,
		costCalcRepository:           costCalcRepository,
		aggregateToChargeIDConverter: aggregateToChargeIDConverter,
		assetSummary:                 assetSummary,
		tariffData:                   tariffData,
		energyMetrics:                energyMetrics,
	}
}

func (s *costChargeServiceImpl) CostCharge(ctx context.Context, aggregateID uuid.UUID) error {
	charge, err := s.aggregateLoader.LoadCharge(ctx, aggregateID)
	if err != nil {
		return fmt.Errorf("failed to load charge: %s, %w", aggregateID, err)
	}

	completedEvent, err := charge.FindCompletedEvent()
	if err != nil {
		return fmt.Errorf("failed to find completed event: %s, %w", aggregateID, err)
	}

	if completedEvent.Historic {
		event := NewCosted(aggregateID, completedEvent.Payload.Energy.EnergyCost, completedEvent.Payload.Energy.EnergyCostCurrency)
		return processDirectWithSQSFallback(ctx, &event, s.msgHandler, s.sqsClient)
	}

	ppid := string(completedEvent.Payload.ChargingStation.ID)
	ownership, err := s.assetSummary.GetOwner(ctx, ppid)
	if err != nil {
		s.logger.Printf("failed to fetch ownership for charger %s (aggregateID %s): %v", ppid, completedEvent.AggregateID, err)
	}

	evseID := evseMap[completedEvent.Payload.ChargingStation.DoorID]
	chargeID := completedEvent.Payload.Charge.Ref
	pluggedInAt := completedEvent.Payload.Charge.PluggedInAt
	unpluggedAt := completedEvent.Payload.Charge.UnpluggedAt
	chargeIntervals, err := s.energyMetrics.GetChargeEnergyMetrics(ctx, ppid, evseID, chargeID, *pluggedInAt, *unpluggedAt)
	if err != nil {
		var httpStatus int
		var responseBody string
		if apiErr, ok := err.(interface {
			StatusCode() int
			Body() string
		}); ok {
			httpStatus = apiErr.StatusCode()
			responseBody = apiErr.Body()
		}
		s.logger.Printf(
			"GetChargeEnergyMetrics error: %v, http status: %d, response body: %s, ppid: %s, ref: %s, from: %v, to: %v",
			err, httpStatus, responseBody, ppid, chargeID, pluggedInAt, unpluggedAt,
		)
	} else {
		s.logger.Printf(
			"GetChargeEnergyMetrics success: ppid: %s, ref: %s, from: %v, to: %v, charge intervals length: %d",
			ppid, chargeID, pluggedInAt, unpluggedAt, len(chargeIntervals),
		)
	}

	var calculatedCost int32
	// if the charger belongs to POD_POINT use the tariffs-api to calculate energy cost otherwise use the old cost calculation logic
	// anything below Arch 5 won't have ownership
	if ownership == assetserviceapi.OWNERSHIP_POD_POINT {
		s.logger.Printf("retrieved ownership %s on charger %s (aggregateID %s)", ownership, ppid, completedEvent.AggregateID)
		calculatedCost, err = s.calculateCostIfOwnedByPodPoint(ctx, completedEvent)
		if err != nil {
			return fmt.Errorf("failed to calculate cost for charger %s with ownership %s (aggregateID %s): %w", ppid, ownership, completedEvent.AggregateID, err)
		}
	} else {
		calculatedCost = s.calculateCost(ctx, completedEvent)
	}

	energyCostCurrency, err := s.costCalcRepository.GetEnergyTariffCurrencyByLocationID(ctx, completedEvent.Payload.Location.ID)
	if err != nil {
		energyCostCurrency = countries.CurrencyGBP.Alpha()
	}

	event := NewCosted(aggregateID, &calculatedCost, energyCostCurrency)
	return processDirectWithSQSFallback(ctx, &event, s.msgHandler, s.sqsClient)
}

func (s *costChargeServiceImpl) calculateCost(ctx context.Context, completedEvent *Completed) int32 {
	var (
		locationTariffs []energycost.TariffTier
		country         countries.CountryCode
		err             error
	)

	if completedEvent.Payload.Location.ID != 0 {
		locationTariffs, err = s.costCalcRepository.GetEnergyTariffTiersByID(ctx, completedEvent.Payload.Location.ID)
		if err != nil {
			s.logger.Printf("error retrieving tariff tiers for locationID %d eventID %s : %v", completedEvent.Payload.Location.ID, completedEvent.EventID, err)
		}
		country, err = s.costCalcRepository.GetCountryByID(ctx, completedEvent.Payload.Location.ID)
		if err != nil {
			s.logger.Printf("couldn't retrieve country for location id %d: %v", completedEvent.Payload.Location.ID, err)
		}
	}

	address := energycost.Address{
		Tariff: energycost.Tariff{
			Tiers:    locationTariffs,
			Timezone: s.getDefaultTimezone(),
		},
		Country: country.Alpha2(),
	}
	charge := toCostCalcCharge(&completedEvent.Payload)

	ec, err := numbers.Convert[uint32, int32](s.energyCostCalculator.EnergyCost(&address, charge))
	if err != nil {
		s.logger.Printf("Cost calculation - error converting energy cost: %v", err)
		// todo: reinstate the line below once connectivity fix for PAR-1285 is in place.
		// return 0, err
	}
	return ec
}

func (s *costChargeServiceImpl) calculateCostIfOwnedByPodPoint(ctx context.Context, completedEvent *Completed) (int32, error) {
	ppid := string(completedEvent.Payload.ChargingStation.ID)

	startedAt := completedEvent.Payload.Charge.StartedAt
	pluggedInAt := completedEvent.Payload.Charge.PluggedInAt
	var ts time.Time
	switch {
	case startedAt != nil && !startedAt.IsZero():
		ts = *startedAt
	case pluggedInAt != nil:
		ts = *pluggedInAt
	default:
		return 0, fmt.Errorf("both startedAt and pluggedInAt are unavailable for charger %s (aggregateID %s)", ppid, completedEvent.AggregateID)
	}

	start := ts.Format(time.DateOnly)
	tariff, err := s.tariffData.Get(ctx, ppid, start)
	if err != nil {
		return 0, fmt.Errorf("error fetching from tariffs-api for charger %s with date %s: %w", ppid, start, err)
	}

	var country countries.CountryCode
	if completedEvent.Payload.Location.ID != 0 {
		country, err = s.costCalcRepository.GetCountryByID(ctx, completedEvent.Payload.Location.ID)
		if err != nil {
			return 0, fmt.Errorf("couldn't retrieve country for location id %d: %v", completedEvent.Payload.Location.ID, err)
		}
	}

	address := energycost.Address{
		Tariff:  *tariff,
		Country: country.Alpha2(),
	}
	charge := toCostCalcCharge(&completedEvent.Payload)

	energyCost, err := numbers.Convert[uint32, int32](s.energyCostCalculator.EnergyCost(&address, charge))
	if err != nil {
		s.logger.Printf("cost calculation - error converting energy cost: %v", err)
		// todo: reinstate the line below once connectivity fix for PAR-1285 is in place.
		// return 0, err
	}
	s.logger.Printf("cost calculation successful with ownership=%s on charger=%s with energy cost: %d (aggregateID %s)", assetserviceapi.OWNERSHIP_POD_POINT, ppid, energyCost, completedEvent.AggregateID)
	return energyCost, nil
}

func (s *costChargeServiceImpl) RecalculateEnergyCost(ctx context.Context, aggregateID uuid.UUID, submittedBy string) error {
	aggregate, err := s.aggregateLoader.LoadCharge(ctx, aggregateID)
	if err != nil {
		return fmt.Errorf("failed to load charge: %s, %w", aggregateID, err)
	}

	completedEvent, err := aggregate.FindCompletedEvent()
	if err != nil {
		return fmt.Errorf("failed to find completed event: %s, %w", aggregateID, err)
	}

	energyCost := s.calculateCost(ctx, completedEvent)
	event := NewEnergyCostCorrected(aggregateID, energyCost, energyCost-ptr.Deref(aggregate.EnergyCost, 0), ptr.To(submittedBy))

	return processDirectWithSQSFallback(ctx, event, s.msgHandler, s.sqsClient)
}

func (s *costChargeServiceImpl) getDefaultTimezone() *time.Location {
	// prepare a default location - this should never fail
	location, _ := time.LoadLocation(tzloc.Europe_London)
	return location
}
