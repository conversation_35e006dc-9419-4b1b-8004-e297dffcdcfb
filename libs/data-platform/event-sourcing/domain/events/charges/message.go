package charges

import (
	"context"
	"encoding/json"
	eventstore "experience/libs/shared/go/event-store"
	"experience/libs/shared/go/sqs"
	"fmt"
	"strings"

	"github.com/google/uuid"

	"k8s.io/utils/ptr"
)

func processDirectWithSQSFallback(ctx context.Context, event eventstore.Event, msgHandler sqs.MessageHandler, sqsClient sqs.ClientOperations) error {
	msg, err := json.Marshal(event)
	if err != nil {
		return fmt.Errorf("failed to marshal event data: %w", err)
	}
	err = msgHandler.Process(ctx, sqs.Message{Body: ptr.To(string(msg))})
	if err == nil {
		return nil
	}

	err = send(ctx, msg, sqsClient)
	if err != nil {
		return fmt.Errorf("send SQS message: %w", err)
	}
	return nil
}

func send(ctx context.Context, msg json.RawMessage, sqsClient sqs.ClientOperations) error {
	_, err := sqsClient.SendMessage(ctx, string(msg))
	return err
}

const (
	UUIDRegex         = `[0-9a-fA-F]{8}-[0-9a-fA-F]{4}-[0-9a-fA-F]{4}-[0-9a-fA-F]{4}-[0-9a-fA-F]{12}`
	UTCTimestampRegex = `[0-9]{4}-[0-9]{2}-[0-9]{2}T[0-9]{2}:[0-9]{2}:[0-9]{2}(\.[0-9]+)?Z`
)

func GenerateExpectedMessageBodyRegex(aggregateID uuid.UUID, event eventstore.EventType, payload string) string {
	return fmt.Sprintf(`{"aggregateID":%q,"event":%q,"eventID":"%v","publishedAt":"%v","historic":false,"payload":%s}`,
		aggregateID,
		event,
		UUIDRegex,
		UTCTimestampRegex,
		escapeSpecialChars(payload))
}

func escapeSpecialChars(regexStr string) string {
	// escape [ and ]
	return strings.ReplaceAll(strings.ReplaceAll(regexStr, "[", "\\["), "]", "\\]")
}
