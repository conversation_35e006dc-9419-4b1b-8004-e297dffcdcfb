// Code generated by MockGen. DO NOT EDIT.
// Source: libs/data-platform/event-sourcing/domain/events/charges/cost/ports.go
//
// Generated by this command:
//
//	mockgen -destination libs/data-platform/event-sourcing/domain/events/charges/cost/mock/ports.go -source=libs/data-platform/event-sourcing/domain/events/charges/cost/ports.go
//

// Package mock_cost is a generated GoMock package.
package mock_cost

import (
	context "context"
	costcalculation "experience/libs/data-platform/cost-calculation"
	energycost "experience/libs/data-platform/cost-calculation/energy-cost"
	assetserviceapi "experience/libs/shared/go/external/asset-service-api/src"
	reflect "reflect"
	time "time"

	countries "github.com/biter777/countries"
	gomock "go.uber.org/mock/gomock"
)

// MockRepository is a mock of Repository interface.
type MockRepository struct {
	ctrl     *gomock.Controller
	recorder *MockRepositoryMockRecorder
	isgomock struct{}
}

// MockRepositoryMockRecorder is the mock recorder for MockRepository.
type MockRepositoryMockRecorder struct {
	mock *MockRepository
}

// NewMockRepository creates a new mock instance.
func NewMockRepository(ctrl *gomock.Controller) *MockRepository {
	mock := &MockRepository{ctrl: ctrl}
	mock.recorder = &MockRepositoryMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockRepository) EXPECT() *MockRepositoryMockRecorder {
	return m.recorder
}

// GetBillingAccountCurrencyByAuthoriserID mocks base method.
func (m *MockRepository) GetBillingAccountCurrencyByAuthoriserID(ctx context.Context, authoriserID int) (string, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetBillingAccountCurrencyByAuthoriserID", ctx, authoriserID)
	ret0, _ := ret[0].(string)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetBillingAccountCurrencyByAuthoriserID indicates an expected call of GetBillingAccountCurrencyByAuthoriserID.
func (mr *MockRepositoryMockRecorder) GetBillingAccountCurrencyByAuthoriserID(ctx, authoriserID any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetBillingAccountCurrencyByAuthoriserID", reflect.TypeOf((*MockRepository)(nil).GetBillingAccountCurrencyByAuthoriserID), ctx, authoriserID)
}

// GetCountryByID mocks base method.
func (m *MockRepository) GetCountryByID(ctx context.Context, locationID int) (countries.CountryCode, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetCountryByID", ctx, locationID)
	ret0, _ := ret[0].(countries.CountryCode)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetCountryByID indicates an expected call of GetCountryByID.
func (mr *MockRepositoryMockRecorder) GetCountryByID(ctx, locationID any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetCountryByID", reflect.TypeOf((*MockRepository)(nil).GetCountryByID), ctx, locationID)
}

// GetEnergyTariffCurrencyByLocationID mocks base method.
func (m *MockRepository) GetEnergyTariffCurrencyByLocationID(ctx context.Context, locationID int) (string, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetEnergyTariffCurrencyByLocationID", ctx, locationID)
	ret0, _ := ret[0].(string)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetEnergyTariffCurrencyByLocationID indicates an expected call of GetEnergyTariffCurrencyByLocationID.
func (mr *MockRepositoryMockRecorder) GetEnergyTariffCurrencyByLocationID(ctx, locationID any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetEnergyTariffCurrencyByLocationID", reflect.TypeOf((*MockRepository)(nil).GetEnergyTariffCurrencyByLocationID), ctx, locationID)
}

// GetEnergyTariffTiersByID mocks base method.
func (m *MockRepository) GetEnergyTariffTiersByID(ctx context.Context, locationID int) ([]energycost.TariffTier, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetEnergyTariffTiersByID", ctx, locationID)
	ret0, _ := ret[0].([]energycost.TariffTier)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetEnergyTariffTiersByID indicates an expected call of GetEnergyTariffTiersByID.
func (mr *MockRepositoryMockRecorder) GetEnergyTariffTiersByID(ctx, locationID any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetEnergyTariffTiersByID", reflect.TypeOf((*MockRepository)(nil).GetEnergyTariffTiersByID), ctx, locationID)
}

// MockAssetSummary is a mock of AssetSummary interface.
type MockAssetSummary struct {
	ctrl     *gomock.Controller
	recorder *MockAssetSummaryMockRecorder
	isgomock struct{}
}

// MockAssetSummaryMockRecorder is the mock recorder for MockAssetSummary.
type MockAssetSummaryMockRecorder struct {
	mock *MockAssetSummary
}

// NewMockAssetSummary creates a new mock instance.
func NewMockAssetSummary(ctrl *gomock.Controller) *MockAssetSummary {
	mock := &MockAssetSummary{ctrl: ctrl}
	mock.recorder = &MockAssetSummaryMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockAssetSummary) EXPECT() *MockAssetSummaryMockRecorder {
	return m.recorder
}

// GetOwner mocks base method.
func (m *MockAssetSummary) GetOwner(ctx context.Context, ppid string) (assetserviceapi.Ownership, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetOwner", ctx, ppid)
	ret0, _ := ret[0].(assetserviceapi.Ownership)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetOwner indicates an expected call of GetOwner.
func (mr *MockAssetSummaryMockRecorder) GetOwner(ctx, ppid any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetOwner", reflect.TypeOf((*MockAssetSummary)(nil).GetOwner), ctx, ppid)
}

// MockTariffData is a mock of TariffData interface.
type MockTariffData struct {
	ctrl     *gomock.Controller
	recorder *MockTariffDataMockRecorder
	isgomock struct{}
}

// MockTariffDataMockRecorder is the mock recorder for MockTariffData.
type MockTariffDataMockRecorder struct {
	mock *MockTariffData
}

// NewMockTariffData creates a new mock instance.
func NewMockTariffData(ctrl *gomock.Controller) *MockTariffData {
	mock := &MockTariffData{ctrl: ctrl}
	mock.recorder = &MockTariffDataMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockTariffData) EXPECT() *MockTariffDataMockRecorder {
	return m.recorder
}

// Get mocks base method.
func (m *MockTariffData) Get(ctx context.Context, ppid, effectiveFrom string) (*energycost.Tariff, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Get", ctx, ppid, effectiveFrom)
	ret0, _ := ret[0].(*energycost.Tariff)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// Get indicates an expected call of Get.
func (mr *MockTariffDataMockRecorder) Get(ctx, ppid, effectiveFrom any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Get", reflect.TypeOf((*MockTariffData)(nil).Get), ctx, ppid, effectiveFrom)
}

// MockEnergyMetrics is a mock of EnergyMetrics interface.
type MockEnergyMetrics struct {
	ctrl     *gomock.Controller
	recorder *MockEnergyMetricsMockRecorder
	isgomock struct{}
}

// MockEnergyMetricsMockRecorder is the mock recorder for MockEnergyMetrics.
type MockEnergyMetricsMockRecorder struct {
	mock *MockEnergyMetrics
}

// NewMockEnergyMetrics creates a new mock instance.
func NewMockEnergyMetrics(ctrl *gomock.Controller) *MockEnergyMetrics {
	mock := &MockEnergyMetrics{ctrl: ctrl}
	mock.recorder = &MockEnergyMetricsMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockEnergyMetrics) EXPECT() *MockEnergyMetricsMockRecorder {
	return m.recorder
}

// GetChargeEnergyMetrics mocks base method.
func (m *MockEnergyMetrics) GetChargeEnergyMetrics(ctx context.Context, ppid, evseID, chargeID string, fromDate, toDate time.Time) ([]costcalculation.ChargeInterval, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetChargeEnergyMetrics", ctx, ppid, evseID, chargeID, fromDate, toDate)
	ret0, _ := ret[0].([]costcalculation.ChargeInterval)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetChargeEnergyMetrics indicates an expected call of GetChargeEnergyMetrics.
func (mr *MockEnergyMetricsMockRecorder) GetChargeEnergyMetrics(ctx, ppid, evseID, chargeID, fromDate, toDate any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetChargeEnergyMetrics", reflect.TypeOf((*MockEnergyMetrics)(nil).GetChargeEnergyMetrics), ctx, ppid, evseID, chargeID, fromDate, toDate)
}
