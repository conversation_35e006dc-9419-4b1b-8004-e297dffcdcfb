package cost

import (
	"context"
	costcalculation "experience/libs/data-platform/cost-calculation"
	energycost "experience/libs/data-platform/cost-calculation/energy-cost"
	assetserviceapi "experience/libs/shared/go/external/asset-service-api/src"
	"fmt"
	"time"

	"github.com/biter777/countries"
)

type ServerError struct {
	context string
	ppid    string
	error   error
}

func (e ServerError) Error() string {
	return fmt.Sprintf("%s %s: %s", e.context, e.ppid, e.error)
}

type ClientError struct {
	context string
	ppid    string
	error   error
}

func (e ClientError) Error() string {
	return fmt.Sprintf("%s %s: %s", e.context, e.ppid, e.error)
}

type Repository interface {
	GetEnergyTariffTiersByID(ctx context.Context, locationID int) ([]energycost.TariffTier, error)
	GetCountryByID(ctx context.Context, locationID int) (countries.CountryCode, error)
	GetEnergyTariffCurrencyByLocationID(ctx context.Context, locationID int) (string, error)
	GetBillingAccountCurrencyByAuthoriserID(ctx context.Context, authoriserID int) (string, error)
}

type AssetSummary interface {
	GetOwner(ctx context.Context, ppid string) (assetserviceapi.Ownership, error)
}

type TariffData interface {
	Get(ctx context.Context, ppid, effectiveFrom string) (*energycost.Tariff, error)
}

type EnergyMetrics interface {
	GetChargeEnergyMetrics(ctx context.Context, ppid, evseID, chargeID string, fromDate, toDate time.Time) ([]costcalculation.ChargeInterval, error)
}
