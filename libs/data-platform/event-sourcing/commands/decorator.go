package commands

import (
	"context"
	"experience/libs/data-platform/event-sourcing/domain/events/charges"
	"log"

	"github.com/google/uuid"
	"github.com/jinzhu/copier"
)

type decorator struct {
	logger                       *log.Logger
	service                      Service
	podAdminRepository           PodadminRepository
	aggregateToChargeIDConverter copier.TypeConverter
}

func NewPodAdminDecorator(service Service, podAdminRepository PodadminRepository, logger *log.Logger, aggregateToChargeIDConverter copier.TypeConverter) Service {
	return &decorator{
		logger:                       logger,
		service:                      service,
		podAdminRepository:           podAdminRepository,
		aggregateToChargeIDConverter: aggregateToChargeIDConverter,
	}
}

func (d *decorator) RecordEnergyCostCorrectedEvent(ctx context.Context, aggregateID uuid.UUID, cost int32, submittedBy string) (charges.Aggregate, error) {
	aggregate, err := d.service.RecordEnergyCostCorrectedEvent(ctx, aggregateID, cost, submittedBy)
	if err != nil {
		return charges.Aggregate{}, err
	}

	chargeID, err := d.aggregateToChargeIDConverter.Fn(aggregateID.String())
	if err != nil {
		d.logger.Printf("error converting charge uuid to int: %v. chargeID: %+v", err, aggregateID)
		return charges.Aggregate{}, err
	}

	//nolint:errcheck // error from podadmin is not critical
	err = d.podAdminRepository.UpdateEnergyCostOfCharge(ctx, int64(chargeID.(int)), cost)

	if err != nil {
		d.logger.Printf("podadmin charge update of energy cost error: %v. chargeID: %+v", err, aggregateID)
	}

	return aggregate, nil
}

func (d *decorator) RecordSettlementAmountCorrectedEvent(ctx context.Context, aggregateID uuid.UUID, settlementAmount int32, submittedBy string) (charges.Aggregate, error) {
	aggregate, err := d.service.RecordSettlementAmountCorrectedEvent(ctx, aggregateID, settlementAmount, submittedBy)
	if err != nil {
		return charges.Aggregate{}, err
	}

	chargeID, err := d.aggregateToChargeIDConverter.Fn(aggregateID.String())
	if err != nil {
		d.logger.Printf("error converting charge uuid to int: %v. chargeID: %+v", err, aggregateID)
		return charges.Aggregate{}, err
	}

	// In podadmin, the polarity of presentment_amount and settlement_amount
	// have a different meaning than in charge projections. In podadmin, a
	// positive number means that a user is credited with money, and a negative
	// number means that money is being taken off the user's wallet. That's why
	// we flip the polarity by multiplying by -1.

	//nolint:errcheck // Suppressing errcheck because error from podadmin is not critical
	err = d.podAdminRepository.UpdateSettlementAmountOfCharge(ctx, int64(chargeID.(int)), settlementAmount*-1)
	if err != nil {
		d.logger.Printf("podadmin charge update of settlement amount error: %v. chargeID: %+v", err, aggregateID)
	}

	return aggregate, nil
}
