-- name: RetrieveBasicUsageByGroup :many
WITH
  charges_by_group_between_dates AS
    (SELECT DATE_TRUNC(sqlc.arg(interval), unplugged_at)         AS interval_start_date,
            COALESCE(energy_total, 0)::numeric(18, 2)  AS total_usage,
            COALESCE(ABS(settlement_amount), 0)::integer         AS revenue_generated,
            COALESCE(energy_cost, 0) ::integer                   AS cost
     FROM projections.charges
     WHERE group_id = sqlc.arg(group_id)::uuid
       AND (sqlc.narg(siteID)::uuid IS NULL OR site_id = sqlc.narg(siteID)::uuid)
       AND (sqlc.narg(chargerID)::varchar IS NULL OR charger_id = sqlc.narg(chargerID)::varchar)
       AND unplugged_at >= sqlc.arg(from_date)::timestamptz
       AND unplugged_at < sqlc.arg(to_date)::timestamptz),
  date_series AS
    (SELECT GENERATE_SERIES(sqlc.arg(from_date)::timestamp, sqlc.arg(to_date)::timestamp - INTERVAL '1 day',
                            CONCAT('1 ', sqlc.arg(interval))::interval) AS interval_start_date,
            0                                           AS total_usage,
            0                                           AS revenue_generated,
            0                                           AS cost)
SELECT TO_CHAR(interval_start_date, 'YYYY-MM-DD') AS interval_start_date,
       COALESCE(SUM(total_usage), 0)::numeric(18, 2)     AS total_usage,
       COALESCE(SUM(revenue_generated), 0)::integer      AS revenue_generated,
       COALESCE(SUM(cost), 0) ::integer                  AS cost
FROM charges_by_group_between_dates
GROUP BY interval_start_date
UNION
SELECT TO_CHAR(DATE_TRUNC(sqlc.arg(interval), interval_start_date), 'YYYY-MM-DD'),
       revenue_generated,
       total_usage,
       cost
FROM date_series ds
WHERE NOT EXISTS(SELECT interval_start_date, revenue_generated, total_usage, cost
                 FROM charges_by_group_between_dates b
                 WHERE TO_CHAR(DATE_TRUNC(sqlc.arg(interval), b.interval_start_date), 'YYYY-MM-DD') =
                       TO_CHAR(DATE_TRUNC(sqlc.arg(interval), ds.interval_start_date), 'YYYY-MM-DD'))
ORDER BY interval_start_date;
