// Code generated by sqlc. DO NOT EDIT.
// versions:
//   sqlc v1.29.0
// source: sites.sql

package sqlc

import (
	"context"
	"database/sql"

	"github.com/google/uuid"
)

const retrieveChargeStatsGroupedBySite = `-- name: RetrieveChargeStatsGroupedBySite :many
SELECT site_id,
       site_name,
       group_id,
       group_name,
       SUM(energy_total)::float AS energy_total,
       SUM(COALESCE(settlement_amount,0))   AS revenue_generated
FROM projections.charges
WHERE ((unplugged_at AT TIME ZONE 'UTC')) >= $1 AND ((unplugged_at AT TIME ZONE 'UTC')) < $2
  AND energy_total > 0.0
  AND site_id IS NOT NULL
  AND charger_type IN ('public'::access, 'private'::access)
GROUP BY site_id, site_name, group_id, group_name
`

type RetrieveChargeStatsGroupedBySiteParams struct {
	UnpluggedAt   sql.NullTime `json:"unplugged_at"`
	UnpluggedAt_2 sql.NullTime `json:"unplugged_at_2"`
}

type RetrieveChargeStatsGroupedBySiteRow struct {
	SiteID           uuid.NullUUID  `json:"site_id"`
	SiteName         sql.NullString `json:"site_name"`
	GroupID          uuid.NullUUID  `json:"group_id"`
	GroupName        sql.NullString `json:"group_name"`
	EnergyTotal      float64        `json:"energy_total"`
	RevenueGenerated int64          `json:"revenue_generated"`
}

func (q *Queries) RetrieveChargeStatsGroupedBySite(ctx context.Context, arg RetrieveChargeStatsGroupedBySiteParams) ([]RetrieveChargeStatsGroupedBySiteRow, error) {
	rows, err := q.db.QueryContext(ctx, retrieveChargeStatsGroupedBySite, arg.UnpluggedAt, arg.UnpluggedAt_2)
	if err != nil {
		return nil, err
	}
	defer rows.Close()
	var items []RetrieveChargeStatsGroupedBySiteRow
	for rows.Next() {
		var i RetrieveChargeStatsGroupedBySiteRow
		if err := rows.Scan(
			&i.SiteID,
			&i.SiteName,
			&i.GroupID,
			&i.GroupName,
			&i.EnergyTotal,
			&i.RevenueGenerated,
		); err != nil {
			return nil, err
		}
		items = append(items, i)
	}
	if err := rows.Close(); err != nil {
		return nil, err
	}
	if err := rows.Err(); err != nil {
		return nil, err
	}
	return items, nil
}
