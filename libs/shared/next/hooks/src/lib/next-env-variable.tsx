import { convertSnakeCaseToCamelCase } from '@experience/shared/typescript/utils';
import { useEnvContext } from 'next-runtime-env';
import getConfig from 'next/config';

export const useNextEnvVariable = (key: string): string | undefined => {
  const runtimeEnvContext = useEnvContext() ?? {};

  const { [key]: runtimeEnv } = runtimeEnvContext;

  if (runtimeEnv) {
    return runtimeEnv;
  }

  const { publicRuntimeConfig = {} } = getConfig() ?? {};

  if (Object.keys(publicRuntimeConfig).length > 0) {
    const publicRuntimeKey = key.includes('NEXT_PUBLIC_')
      ? convertSnakeCaseToCamelCase(key.split('NEXT_PUBLIC_')[1])
      : convertSnakeCaseToCamelCase(key);
    const { [publicRuntimeKey]: publicRuntimeConfigEnv } = publicRuntimeConfig;

    return publicRuntimeConfigEnv;
  }

  return process.env[key];
};
