import {
  AccessToken,
  ClaimRequest,
  Response,
  SalesforceAppointment,
  SalesforceAsset,
  SalesforceConfig,
} from './types';
import { Logger } from '@nestjs/common';

import { sign } from 'jsonwebtoken';
import { stringify } from 'qs';
import axios, {
  AxiosError,
  AxiosInstance,
  AxiosRequestConfig,
  AxiosResponse,
  Method,
} from 'axios';

const logContentTypes = ['text/plain', 'application/json'];

export class SalesforceClient {
  private readonly logger = new Logger(SalesforceClient.name);

  private readonly clientId: string;
  private readonly username: string;
  private readonly audience: string;
  private readonly maxRetry: number;
  private readonly privateKey: string;
  private readonly expiresIn: number = 3600;
  private readonly client: AxiosInstance;

  private authToken = '';

  constructor({
    instanceUrl,
    clientId,
    username,
    audience,
    privateKey,
    userAgent,
    maxRetry,
  }: SalesforceConfig) {
    this.clientId = clientId;
    this.username = username;
    this.privateKey = privateKey;
    this.audience = audience || instanceUrl;
    this.maxRetry = maxRetry;

    this.logger = new Logger(SalesforceClient.name);

    this.client = axios.create({
      baseURL: instanceUrl,
      headers: {
        'User-Agent': userAgent,
      },
    });
  }

  public async getAsset(ppid: string): Promise<SalesforceAsset> {
    return this.get<object>(
      `/services/data/v61.0/sobjects/Asset/Unit_PSL_PG_Number__c/${ppid}`
    );
  }

  public async getAppointmentByReference(
    reference: string
  ): Promise<AxiosResponse<SalesforceAppointment>> {
    const response = await this.makeRequest<SalesforceAppointment>(
      'POST',
      '/services/apexrest/appointments/verify',
      { reference }
    );

    return response;
  }

  public async setSlickApplicationUrl(
    orderReference: string,
    applicationUrl: string
  ) {
    return this.patch(
      `/services/data/v63.0/sobjects/Order/OrderNumber/${orderReference}`,
      {
        Slick_Application_Url__c: applicationUrl,
      }
    );
  }

  public async setSubscriptionInterventionReason(
    orderReference: string,
    interventionReason:
      | 'affordability'
      | 'direct_debit'
      | 'direct_debit_recovered'
      | 'affordability_recovered'
  ) {
    return this.patch(
      `/services/data/v63.0/sobjects/Order/OrderNumber/${orderReference}`,
      {
        Subscription_Intervention_Reason__c: interventionReason,
      }
    );
  }

  public async setSubscriptionSetupComplete(
    orderReference: string,
    complete: boolean
  ) {
    return this.patch(
      `/services/data/v63.0/sobjects/Order/OrderNumber/${orderReference}`,
      {
        Subscription_Setup_Complete__c: complete,
      }
    );
  }

  public async patch<TResponse>(
    path: string,
    data: object,
    args?: AxiosRequestConfig
  ) {
    const { data: response } = await this.makeRequest<TResponse>(
      'PATCH',
      path,
      data,
      args
    );

    return response;
  }

  public async get<TResponse>(
    path: string,
    params?: object,
    args?: AxiosRequestConfig
  ) {
    const { data } = await this.makeRequest<TResponse>(
      'GET',
      path,
      {},
      { params, ...args }
    );

    return data;
  }

  private async makeRequest<TResponse>(
    method: Method,
    path: string,
    data: object,
    args: AxiosRequestConfig = {},
    retry = 0
  ): Promise<AxiosResponse<TResponse>> {
    await this.generateAuthorization();

    const contentType =
      args.headers?.['Content-Type']?.split(';')[0] ?? 'application/json';

    try {
      if (logContentTypes.includes(contentType)) {
        this.logger.log(`Calling ${method} ${path}...`, { data, args });
      } else {
        this.logger.log(`Calling ${method} ${path}...`, { args });
      }

      const response = await this.client.request<TResponse>({
        url: path,
        method,
        data,
        ...args,
        headers: {
          Authorization: this.authToken,
          ...args.headers,
        },
      });

      this.logger.log(
        `Calling ${method} ${path} returned with ${response.status} HTTP status code`,
        response.data
      );

      return response;
    } catch (error) {
      if (logContentTypes.includes(contentType)) {
        this.logger.log(`Error calling ${method} path ${path}`, error);
      } else {
        this.logger.log(`Error calling ${method} path ${path}`);
      }

      if (error instanceof AxiosError && error.response) {
        this.logger.log(`${method} error response`, error.response.data);

        if (retry >= this.maxRetry) {
          this.logger.log('Max retries reached, giving up...');
          throw error;
        }

        if ([401, 403].includes(error.response.status)) {
          this.authToken = '';
          this.logger.log('Token expired, generating new token...');
          await this.generateAuthorization();
          this.logger.log(`Retrying request...attempt ${retry}`);
          return this.makeRequest<TResponse>(
            method,
            path,
            data,
            args,
            retry + 1
          );
        }
      }

      throw error;
    }
  }

  private async generateAuthorization() {
    if (this.authToken) return;

    const { token_type, access_token } = await this.generateJsonWebToken();

    this.authToken = `${token_type} ${access_token}`;
  }

  private async generateJsonWebToken(): Promise<AccessToken> {
    try {
      const assertion = await this.generateClaimSignature({
        clientId: this.clientId,
        username: this.username,
        audience: this.audience,
        expiresIn: this.expiresIn,
        privateKey: this.privateKey,
      });

      const response = await this.client.post(
        '/services/oauth2/token',
        stringify({
          grant_type: 'urn:ietf:params:oauth:grant-type:jwt-bearer',
          assertion,
        }),
        {
          headers: {
            'Content-Type': 'application/x-www-form-urlencoded',
          },
        }
      );

      const responseBody: Response = response.data;

      this.logger.log(`Authenticated with ${responseBody.token_type} token.`);

      return {
        access_token: responseBody.access_token,
        created_at: Math.floor(Date.now() / 1000),
        expires_in: this.expiresIn,
        token_type: responseBody.token_type,
      };
    } catch (error) {
      this.logger.log('Access token error', error);

      if (error instanceof AxiosError && error.response) {
        this.logger.log('Failed to authenticate', error.response.data);
      }

      throw error;
    }
  }

  async generateClaimSignature(params: ClaimRequest) {
    try {
      const signature = sign({ sub: params.username }, params.privateKey, {
        issuer: params.clientId,
        audience: params.audience,
        expiresIn: params.expiresIn,
        algorithm: 'RS256',
      });

      this.logger.log(
        'JSON Web Token claim signature generated successfully',
        signature
      );

      return signature;
    } catch (error) {
      const message = error instanceof Error ? error.message : 'Unknown';
      throw new Error(
        `Could not generate JSON Web Token claim signature - ${message}`
      );
    }
  }
}
