import { ATTR_SERVICE_NAME } from '@opentelemetry/semantic-conventions';
import { AWSXRayIdGenerator } from '@opentelemetry/id-generator-aws-xray';
import { AWSXRayPropagator } from '@opentelemetry/propagator-aws-xray';
import {
  BatchSpanProcessor,
  TraceIdRatioBasedSampler,
} from '@opentelemetry/sdk-trace-base';
import { Instrumentation } from '@opentelemetry/instrumentation';
import { NodeSDK } from '@opentelemetry/sdk-node';
import { OTLPTraceExporter } from '@opentelemetry/exporter-trace-otlp-grpc';
import { Resource } from '@opentelemetry/resources';
import { getNodeAutoInstrumentations } from '@opentelemetry/auto-instrumentations-node';
import { z } from 'zod';

const otlpTraceExporter = new OTLPTraceExporter();
const batchSpanProcessor = new BatchSpanProcessor(otlpTraceExporter);

const { TRACING_SAMPLING_RATIO: tracingSamplingRatio } = z
  .object({ TRACING_SAMPLING_RATIO: z.coerce.number().default(0.01) })
  .parse(process.env);

export const startOpenTelemetryWithXRayPropagator = (
  serviceName: string,
  instrumentations: (Instrumentation | Instrumentation[])[] = []
) => {
  const sdk = new NodeSDK({
    idGenerator: new AWSXRayIdGenerator(),
    instrumentations: [
      getNodeAutoInstrumentations({
        '@opentelemetry/instrumentation-fs': { enabled: false },
      }),
      ...instrumentations,
    ],
    resource: Resource.default().merge(
      new Resource({ [ATTR_SERVICE_NAME]: serviceName })
    ),
    sampler: new TraceIdRatioBasedSampler(tracingSamplingRatio),
    spanProcessors: [batchSpanProcessor],
    textMapPropagator: new AWSXRayPropagator(),
    traceExporter: otlpTraceExporter,
  });

  /* eslint-disable no-console */
  process.on('SIGTERM', () => {
    sdk
      .shutdown()
      .then(() => console.log('Tracing terminated'))
      .catch((error) => console.log('Error terminating tracing', error));
  });
  /* eslint-enable no-console */

  sdk.start();
};
