import { DEFAULT_LANGUAGE, SUPPORTED_LANGUAGES } from '@experience/shared/i18n';
import { Logger } from '@nestjs/common';

const logger = new Logger('language');
export const getLanguageFromCode = (code: string) => {
  let language = DEFAULT_LANGUAGE;
  try {
    ({ language } = new Intl.Locale(code));
  } catch (error) {
    logger.error({ error }, 'failed to parse language code');
  }
  if (!SUPPORTED_LANGUAGES.includes(language)) {
    logger.warn(
      { code, language },
      `supplied language code is not supported - using '${DEFAULT_LANGUAGE}'.`
    );
    language = DEFAULT_LANGUAGE;
  }
  return language;
};
