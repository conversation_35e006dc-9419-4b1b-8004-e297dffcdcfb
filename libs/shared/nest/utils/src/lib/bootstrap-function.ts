import { CommandFactory } from 'nest-commander';
import { DocumentBuilder, SwaggerModule } from '@nestjs/swagger';
import { INestApplication, Type, ValidationPipe } from '@nestjs/common';
import { Logger } from 'nestjs-pino';
import { NestFactory } from '@nestjs/core';
import { PINO_LOGGER } from './pino-logger';
import { SentryInterceptor } from './sentry-interceptor';
import { VersioningOptions } from '@nestjs/common/interfaces/version-options.interface';
import { authCheckMiddleware } from './basic-auth-middleware';
import helmet from 'helmet';
import nocache from 'nocache';

export interface BootstrapOpts<T> {
  callback?: (
    app: INestApplication,
    documentBuilder: DocumentBuilder
  ) => DocumentBuilder;
  globalPrefix?: string;
  hidePoweredBy?: boolean;
  module: T;
  noCache?: boolean;
  port: number;
  preCallback?: () => void;
  protectSwagger?: boolean;
  rawBody?: boolean;
  setupSwagger?: boolean;
  strictTransportSecurity?: boolean;
  versioningOptions?: VersioningOptions;
}

export const bootstrap = async <T>({
  callback,
  globalPrefix,
  hidePoweredBy,
  module,
  noCache,
  port,
  preCallback,
  protectSwagger,
  rawBody,
  setupSwagger = true,
  strictTransportSecurity,
  versioningOptions,
}: BootstrapOpts<T>): Promise<INestApplication> => {
  preCallback ? preCallback() : null;

  const app = await NestFactory.create(module, {
    logger: PINO_LOGGER,
    rawBody,
  });

  app.getHttpAdapter().getInstance().set('etag', false);

  useGlobalInterceptors(app);
  useGlobalPipes(app);
  useLogger(app);

  if (globalPrefix) {
    app.setGlobalPrefix(globalPrefix, { exclude: ['/health'] });
  }

  if (hidePoweredBy) {
    app.use(helmet.hidePoweredBy());
  }

  if (noCache) {
    app.use(nocache());
  }

  if (strictTransportSecurity) {
    app.use(helmet.strictTransportSecurity());
  }

  if (versioningOptions) {
    app.enableVersioning(versioningOptions);
  }

  app.enableShutdownHooks();

  if (setupSwagger) {
    const documentBuilder = callback
      ? callback(app, new DocumentBuilder())
      : new DocumentBuilder();
    const document = SwaggerModule.createDocument(app, documentBuilder.build());
    SwaggerModule.setup('api', app, document);

    if (protectSwagger) {
      app.use('^/api(-yaml)?$', authCheckMiddleware);
    }
  }

  const server = await app.listen(port);
  server.keepAliveTimeout = 75000;

  return app;
};

const useGlobalInterceptors = (app: INestApplication) => {
  app.useGlobalInterceptors(new SentryInterceptor());
};

export const useGlobalPipes = (app: INestApplication) => {
  app.useGlobalPipes(
    new ValidationPipe({ stopAtFirstError: true, transform: true })
  );
};

export const useLogger = (app: INestApplication) => {
  app.useLogger(app.get(Logger));
};

export const bootstrapCommandRunner = async <T>(rootModule: Type<T>) => {
  const errorHandler = (error: Error) => {
    PINO_LOGGER.error({ error }, 'error running command');
    process.exitCode = 1;
  };
  return CommandFactory.run(rootModule, {
    logger: PINO_LOGGER,
    errorHandler: errorHandler,
    serviceErrorHandler: errorHandler,
  });
};
