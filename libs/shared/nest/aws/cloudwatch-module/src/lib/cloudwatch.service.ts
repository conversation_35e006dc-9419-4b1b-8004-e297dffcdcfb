import {
  CloudWatchClient,
  PutMetricDataCommand,
} from '@aws-sdk/client-cloudwatch';
import { Injectable, Logger } from '@nestjs/common';

@Injectable()
export class CloudWatchService {
  private readonly logger = new Logger(CloudWatchService.name);

  constructor(private cloudWatchClient: CloudWatchClient) {}

  public async publishMetricData(
    namespace: string,
    metricName: string,
    metricValue: number,
    dimensions?: { name: string; value: string }[]
  ): Promise<void> {
    this.logger.log(
      { namespace, metricName, metricValue },
      'publishing metric data'
    );
    await this.cloudWatchClient
      .send(
        new PutMetricDataCommand({
          MetricData: [
            {
              MetricName: metricName,
              Timestamp: new Date(),
              Value: metricValue,
              Dimensions: dimensions?.map((dimension) => ({
                Name: dimension.name,
                Value: dimension.value,
              })),
            },
          ],
          Namespace: namespace,
        })
      )
      .then((result) => this.logger.log({ result }, 'published metric data'))
      .catch((error) =>
        this.logger.error({ error }, 'failed to publish metric data')
      );
  }
}

export default CloudWatchService;
