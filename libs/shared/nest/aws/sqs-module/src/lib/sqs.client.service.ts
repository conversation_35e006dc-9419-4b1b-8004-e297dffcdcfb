import { Injectable, Logger } from '@nestjs/common';
import {
  SQSClient,
  SendMessageBatchCommand,
  SendMessageCommand,
} from '@aws-sdk/client-sqs';

export interface SqsMessageAttribute {
  DataType: string;
  StringValue?: string;
  BinaryValue?: Uint8Array;
}

export interface SqsMessageProps {
  messageBody: string;
  messageAttributes?: Record<string, SqsMessageAttribute>;
  messageGroupId?: string;
  messageDeduplicationId?: string;
}

export type SqsBatchMessageProps = SqsMessageProps & { messageId: string };

@Injectable()
export class SqsClientService {
  private readonly logger = new Logger(SqsClientService.name);
  constructor(private sqsClient: SQSClient) {}

  async sendMessage(
    queueUrl: string,
    {
      messageBody,
      messageAttributes,
      messageGroupId,
      messageDeduplicationId,
    }: SqsMessageProps
  ): Promise<void> {
    const command = new SendMessageCommand({
      QueueUrl: queueUrl,
      MessageBody: messageBody,
      MessageAttributes: messageAttributes,
      MessageGroupId: messageGroupId,
      MessageDeduplicationId: messageDeduplicationId,
    });

    this.sqsClient
      .send(command)
      .then((output) => {
        this.logger.log({ output }, 'sending message succeeded');
      })
      .catch((error) => {
        this.logger.error({ error }, 'sending message failed');
      });
  }

  async sendBatchMessages(
    queueUrl: string,
    entries: SqsBatchMessageProps[]
  ): Promise<void> {
    const command = new SendMessageBatchCommand({
      QueueUrl: queueUrl,
      Entries: entries.map(
        ({
          messageId,
          messageAttributes,
          messageBody,
          messageGroupId,
          messageDeduplicationId,
        }) => ({
          Id: messageId,
          MessageBody: messageBody,
          MessageAttributes: messageAttributes,
          MessageGroupId: messageGroupId,
          MessageDeduplicationId: messageDeduplicationId,
        })
      ),
    });

    return this.sqsClient
      .send(command)
      .then((output) => {
        if (output?.Failed?.length && output.Failed.length > 0) {
          this.logger.error(
            { output },
            'some messages failed to be sent to the queue'
          );
          return;
        }
        this.logger.log({ output }, 'successfully sent messages to the queue');
      })
      .catch((error) => {
        this.logger.error({ error }, 'failed to send messages to the queue');
      });
  }
}

export default SqsClientService;
