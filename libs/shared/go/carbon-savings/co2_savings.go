package carbonsavings

import "math"

// A safe assumption provided by PD team.
const evMilesPerKwh = 4

// Petrol figure supplied by Carbon Trust in 2014. EV figure derived from DEFRA figure of 0.49023 kg per kWh &
// avg consumption of 29 kWh/100mi for UK electric car.
const evKgCO2PerMile = 0.14
const petrolKgCO2PerMile = 0.28

func Co2Savings(kwhUsed float64) float64 {
	milesDriven := kwhUsed * evMilesPerKwh
	co2EV := milesDriven * evKgCO2PerMile
	co2Petrol := milesDriven * petrolKgCO2PerMile

	var co2Savings = co2Petrol - co2EV
	f := math.Round(co2Savings*100) / 100
	return f
}
