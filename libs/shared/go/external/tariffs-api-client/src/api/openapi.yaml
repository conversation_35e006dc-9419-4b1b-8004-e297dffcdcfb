openapi: 3.0.0
info:
  contact: {}
  description: API for managing tariffs api
  title: Tariffs Api
  version: '1.0'
servers:
  - url: /
tags:
  - description: ''
    name: tariffs-api
paths:
  /health:
    get:
      operationId: HealthController_check
      parameters: []
      responses:
        '200':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/HealthController_check_200_response'
          description: The Health Check is successful
        '503':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/HealthController_check_503_response'
          description: The Health Check is not successful
      tags:
        - Health
  /suppliers:
    get:
      description: Get a list of suppliers
      operationId: get-suppliers
      parameters: []
      responses:
        '200':
          content:
            application/json:
              schema:
                items:
                  $ref: '#/components/schemas/SupplierDto'
                type: array
          description: List of suppliers
      summary: Get suppliers
      tags:
        - Suppliers
  /charging-stations/{ppid}/tariffs:
    delete:
      description: 'Given a PPID, delete all the tariffs for the charging station'
      operationId: deleteAllTariffForChargingStation
      parameters:
        - description: Charging Station PPID
          explode: false
          in: path
          name: ppid
          required: true
          schema:
            type: string
          style: simple
      responses:
        '204':
          description: All tariffs deleted or no tariffs found for deletion
        '400':
          description: The PPID is invalid
        '500':
          description: Internal server error.
      summary: Delete all tariffs
      tags:
        - charging-stations-tariffs
    get:
      description:
        "Given a PPID, return the tariffs with optional effective date\
        \ filtering."
      operationId: get-tariffs-by-ppid
      parameters:
        - description: Charging Station PPID
          explode: false
          in: path
          name: ppid
          required: true
          schema:
            example: PSL-123456
            type: string
          style: simple
        - description: Include tariffs effective from this date (inclusive).
          explode: true
          in: query
          name: effectiveFrom
          required: false
          schema:
            example: 2023-01-01
            type: string
          style: form
        - description: Include tariffs effective to this date (inclusive).
          explode: true
          in: query
          name: effectiveTo
          required: false
          schema:
            example: 2023-12-31
            type: string
          style: form
      responses:
        '200':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ChargingStationTariffSearchResponseDto'
          description: Search response
        '400':
          description: The PPID or date filters are invalid.
        '500':
          description: Internal server error.
      summary: Get Tariffs by Charging Station PPID
      tags:
        - charging-stations-tariffs
    post:
      description: Creates a new tariff for a charging station
      operationId: create-tariff-for-charging-station
      parameters:
        - description: Charging Station PPID
          explode: false
          in: path
          name: ppid
          required: true
          schema:
            example: PSL-123456
            type: string
          style: simple
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/TariffRowDto'
        required: true
      responses:
        '201':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/PersistedTariffRowDto'
          description: The newly created tariff
        '400':
          description: The tariff format or supplier are invalid
        '409':
          description: Tariff start dates overlap
        '500':
          description: Internal server error
      summary: Create a new tariff
      tags:
        - charging-stations-tariffs
  /charging-stations/{ppid}/tariffs/{tariffId}:
    delete:
      description: 'Given a PPID and tariff ID, delete the tariff'
      operationId: deleteTariff
      parameters:
        - description: Charging Station PPID
          explode: false
          in: path
          name: ppid
          required: true
          schema:
            example: PSL-123456
            type: string
          style: simple
        - description: Tariff ID
          explode: false
          in: path
          name: tariffId
          required: true
          schema:
            type: string
          style: simple
      responses:
        '204':
          description: ''
      summary: Delete a tariff
      tags:
        - charging-stations-tariffs
    put:
      description:
        "Given a PPID and tariff ID, replace the tariff details with the\
        \ provided details"
      operationId: updateTariff
      parameters:
        - description: Charging Station PPID
          explode: false
          in: path
          name: ppid
          required: true
          schema:
            example: PSL-123456
            type: string
          style: simple
        - description: Tariff ID
          explode: false
          in: path
          name: tariffId
          required: true
          schema:
            type: string
          style: simple
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/TariffRowDto'
        required: true
      responses:
        '200':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/PersistedTariffRowDto'
          description: ''
        '409':
          description: Tariff start dates overlap
        '500':
          description: Internal server error
      summary: Update a tariff
      tags:
        - charging-stations-tariffs
components:
  schemas:
    TariffInfoDto:
      example:
        price: 0.3
        start: 00:00:00
        days:
          - MONDAY
          - TUESDAY
          - WEDNESDAY
          - THURSDAY
          - FRIDAY
          - SATURDAY
          - SUNDAY
        end: 00:00:00
      properties:
        start:
          description: Start time in the format HH:mm:ss.
          example: 00:00:00
          pattern: "\\d{2}:\\d{2}:\\d{2}"
          type: string
        end:
          description: End time in the format HH:mm:ss.
          example: 00:00:00
          pattern: "\\d{2}:\\d{2}:\\d{2}"
          type: string
        price:
          description: 'Price per unit (e.g., £0.30).'
          example: 0.3
          type: number
        days:
          description:
            Days of the week this tariff applies to. A tariff should always
            cover the entire week - all hours of each day.
          example:
            - MONDAY
            - TUESDAY
            - WEDNESDAY
            - THURSDAY
            - FRIDAY
            - SATURDAY
            - SUNDAY
          items:
            enum:
              - MONDAY
              - TUESDAY
              - WEDNESDAY
              - THURSDAY
              - FRIDAY
              - SATURDAY
              - SUNDAY
            type: string
          type: array
      required:
        - days
        - end
        - price
        - start
      type: object
    SupplierDto:
      example:
        name: name
        icon: icon
        defaultTariffInfo:
          - price: 0.3
            start: 00:00:00
            days:
              - MONDAY
              - TUESDAY
              - WEDNESDAY
              - THURSDAY
              - FRIDAY
              - SATURDAY
              - SUNDAY
            end: 00:00:00
          - price: 0.3
            start: 00:00:00
            days:
              - MONDAY
              - TUESDAY
              - WEDNESDAY
              - THURSDAY
              - FRIDAY
              - SATURDAY
              - SUNDAY
            end: 00:00:00
        timeZone: Europe/London
        id: id
      properties:
        id:
          type: string
        name:
          type: string
        timeZone:
          description: The timezone of the off peak hours
          enum:
            - Europe/London
            - Europe/Madrid
            - Europe/Paris
            - Etc/UTC
          example: Europe/London
          nullable: false
          type: string
        icon:
          type: string
        defaultTariffInfo:
          description:
            Array of tariff information applicable to specific days and
            times.
          items:
            $ref: '#/components/schemas/TariffInfoDto'
          type: array
      required:
        - defaultTariffInfo
        - icon
        - id
        - name
        - timeZone
      type: object
    TariffRowDto:
      example:
        supplierId: supplierId
        timezone: Europe/London
        tariffInfo:
          - price: 0.3
            start: 00:00:00
            days:
              - MONDAY
              - TUESDAY
              - WEDNESDAY
              - THURSDAY
              - FRIDAY
              - SATURDAY
              - SUNDAY
            end: 00:00:00
          - price: 0.3
            start: 00:00:00
            days:
              - MONDAY
              - TUESDAY
              - WEDNESDAY
              - THURSDAY
              - FRIDAY
              - SATURDAY
              - SUNDAY
            end: 00:00:00
        effectiveFrom: 2021-01-01
        ppid: ppid
      properties:
        ppid:
          description: The charging station PPID
          type: string
        supplierId:
          description: Reference to the supplier. Null if the supplier is unknown.
          nullable: true
          type: string
        tariffInfo:
          description:
            Array of tariff information applicable to specific days and
            times.
          items:
            $ref: '#/components/schemas/TariffInfoDto'
          type: array
        timezone:
          description: Timezone the tariff information applies to.
          example: Europe/London
          type: string
        effectiveFrom:
          description: The date from which the tariff is effective in the format YYYY-MM-DD
          example: 2021-01-01
          format: date
          type: string
      required:
        - effectiveFrom
        - ppid
        - supplierId
        - tariffInfo
        - timezone
      type: object
    PersistedTariffRowDto:
      example:
        supplierId: supplierId
        timezone: Europe/London
        tariffInfo:
          - price: 0.3
            start: 00:00:00
            days:
              - MONDAY
              - TUESDAY
              - WEDNESDAY
              - THURSDAY
              - FRIDAY
              - SATURDAY
              - SUNDAY
            end: 00:00:00
          - price: 0.3
            start: 00:00:00
            days:
              - MONDAY
              - TUESDAY
              - WEDNESDAY
              - THURSDAY
              - FRIDAY
              - SATURDAY
              - SUNDAY
            end: 00:00:00
        id: 046b6c7f-0b8a-43b9-b35d-6489e6daee91
        effectiveFrom: 2021-01-01
        ppid: ppid
        cheapestUnitPrice: 0.2
      properties:
        ppid:
          description: The charging station PPID
          type: string
        supplierId:
          description: Reference to the supplier. Null if the supplier is unknown.
          nullable: true
          type: string
        tariffInfo:
          description:
            Array of tariff information applicable to specific days and
            times.
          items:
            $ref: '#/components/schemas/TariffInfoDto'
          type: array
        timezone:
          description: Timezone the tariff information applies to.
          example: Europe/London
          type: string
        effectiveFrom:
          description: The date from which the tariff is effective in the format YYYY-MM-DD
          example: 2021-01-01
          format: date
          type: string
        id:
          description: Unique ID for the tariff
          format: uuid
          type: string
        cheapestUnitPrice:
          description: 'The cheapest unit price (e.g., £0.10).'
          example: 0.2
          type: number
      required:
        - cheapestUnitPrice
        - effectiveFrom
        - id
        - ppid
        - supplierId
        - tariffInfo
        - timezone
      type: object
    ChargingStationTariffSearchCriteriaDto:
      properties:
        ppid:
          description: Charging Station PPID
          type: string
        effectiveFrom:
          description: The effective from date
          format: date-time
          type: string
        effectiveTo:
          description: The effective to date
          format: date-time
          type: string
      required:
        - ppid
      type: object
    ChargingStationTariffSearchMetadataDto:
      example:
        criteria: ''
      properties:
        criteria:
          allOf:
            - $ref: '#/components/schemas/ChargingStationTariffSearchCriteriaDto'
          description: The search criteria used
      required:
        - criteria
      type: object
    ChargingStationTariffSearchResponseDto:
      example:
        metadata:
          criteria: ''
        data:
          - supplierId: supplierId
            timezone: Europe/London
            tariffInfo:
              - price: 0.3
                start: 00:00:00
                days:
                  - MONDAY
                  - TUESDAY
                  - WEDNESDAY
                  - THURSDAY
                  - FRIDAY
                  - SATURDAY
                  - SUNDAY
                end: 00:00:00
              - price: 0.3
                start: 00:00:00
                days:
                  - MONDAY
                  - TUESDAY
                  - WEDNESDAY
                  - THURSDAY
                  - FRIDAY
                  - SATURDAY
                  - SUNDAY
                end: 00:00:00
            id: 046b6c7f-0b8a-43b9-b35d-6489e6daee91
            effectiveFrom: 2021-01-01
            ppid: ppid
            cheapestUnitPrice: 0.2
          - supplierId: supplierId
            timezone: Europe/London
            tariffInfo:
              - price: 0.3
                start: 00:00:00
                days:
                  - MONDAY
                  - TUESDAY
                  - WEDNESDAY
                  - THURSDAY
                  - FRIDAY
                  - SATURDAY
                  - SUNDAY
                end: 00:00:00
              - price: 0.3
                start: 00:00:00
                days:
                  - MONDAY
                  - TUESDAY
                  - WEDNESDAY
                  - THURSDAY
                  - FRIDAY
                  - SATURDAY
                  - SUNDAY
                end: 00:00:00
            id: 046b6c7f-0b8a-43b9-b35d-6489e6daee91
            effectiveFrom: 2021-01-01
            ppid: ppid
            cheapestUnitPrice: 0.2
      properties:
        data:
          description: The tariffs matching the given criteria
          items:
            $ref: '#/components/schemas/PersistedTariffRowDto'
          type: array
        metadata:
          $ref: '#/components/schemas/ChargingStationTariffSearchMetadataDto'
      required:
        - data
        - metadata
      type: object
    HealthController_check_200_response_info_value:
      additionalProperties: true
      properties:
        status:
          type: string
      required:
        - status
      type: object
    HealthController_check_200_response:
      example:
        details:
          database:
            status: up
        error: {}
        status: ok
        info:
          database:
            status: up
      properties:
        status:
          example: ok
          type: string
        info:
          additionalProperties:
            $ref: '#/components/schemas/HealthController_check_200_response_info_value'
          example:
            database:
              status: up
          nullable: true
          type: object
        error:
          additionalProperties:
            $ref: '#/components/schemas/HealthController_check_200_response_info_value'
          example: {}
          nullable: true
          type: object
        details:
          additionalProperties:
            $ref: '#/components/schemas/HealthController_check_200_response_info_value'
          example:
            database:
              status: up
          type: object
      type: object
    HealthController_check_503_response:
      example:
        details:
          database:
            status: up
          redis:
            status: down
            message: Could not connect
        error:
          redis:
            status: down
            message: Could not connect
        status: error
        info:
          database:
            status: up
      properties:
        status:
          example: error
          type: string
        info:
          additionalProperties:
            $ref: '#/components/schemas/HealthController_check_200_response_info_value'
          example:
            database:
              status: up
          nullable: true
          type: object
        error:
          additionalProperties:
            $ref: '#/components/schemas/HealthController_check_200_response_info_value'
          example:
            redis:
              status: down
              message: Could not connect
          nullable: true
          type: object
        details:
          additionalProperties:
            $ref: '#/components/schemas/HealthController_check_200_response_info_value'
          example:
            database:
              status: up
            redis:
              status: down
              message: Could not connect
          type: object
      type: object
