/*
Smart Charging Service Api

API for managing smart charging service api

API version: 1.0
*/

// Code generated by OpenAPI Generator (https://openapi-generator.tech); DO NOT EDIT.

package smartchargingserviceclient

import (
	"encoding/json"
)

// checks if the VehicleChargeInfoDtoChargingStation type satisfies the MappedNullable interface at compile time
var _ MappedNullable = &VehicleChargeInfoDtoChargingStation{}

// VehicleChargeInfoDtoChargingStation struct for VehicleChargeInfoDtoChargingStation
type VehicleChargeInfoDtoChargingStation struct {
	Ppid *string `json:"ppid,omitempty"`
}

// NewVehicleChargeInfoDtoChargingStation instantiates a new VehicleChargeInfoDtoChargingStation object
// This constructor will assign default values to properties that have it defined,
// and makes sure properties required by API are set, but the set of arguments
// will change when the set of required properties is changed
func NewVehicleChargeInfoDtoChargingStation() *VehicleChargeInfoDtoChargingStation {
	this := VehicleChargeInfoDtoChargingStation{}
	return &this
}

// NewVehicleChargeInfoDtoChargingStationWithDefaults instantiates a new VehicleChargeInfoDtoChargingStation object
// This constructor will only assign default values to properties that have it defined,
// but it doesn't guarantee that properties required by API are set
func NewVehicleChargeInfoDtoChargingStationWithDefaults() *VehicleChargeInfoDtoChargingStation {
	this := VehicleChargeInfoDtoChargingStation{}
	return &this
}

// GetPpid returns the Ppid field value if set, zero value otherwise.
func (o *VehicleChargeInfoDtoChargingStation) GetPpid() string {
	if o == nil || IsNil(o.Ppid) {
		var ret string
		return ret
	}
	return *o.Ppid
}

// GetPpidOk returns a tuple with the Ppid field value if set, nil otherwise
// and a boolean to check if the value has been set.
func (o *VehicleChargeInfoDtoChargingStation) GetPpidOk() (*string, bool) {
	if o == nil || IsNil(o.Ppid) {
		return nil, false
	}
	return o.Ppid, true
}

// HasPpid returns a boolean if a field has been set.
func (o *VehicleChargeInfoDtoChargingStation) HasPpid() bool {
	if o != nil && !IsNil(o.Ppid) {
		return true
	}

	return false
}

// SetPpid gets a reference to the given string and assigns it to the Ppid field.
func (o *VehicleChargeInfoDtoChargingStation) SetPpid(v string) {
	o.Ppid = &v
}

func (o VehicleChargeInfoDtoChargingStation) MarshalJSON() ([]byte, error) {
	toSerialize, err := o.ToMap()
	if err != nil {
		return []byte{}, err
	}
	return json.Marshal(toSerialize)
}

func (o VehicleChargeInfoDtoChargingStation) ToMap() (map[string]interface{}, error) {
	toSerialize := map[string]interface{}{}
	if !IsNil(o.Ppid) {
		toSerialize["ppid"] = o.Ppid
	}
	return toSerialize, nil
}

type NullableVehicleChargeInfoDtoChargingStation struct {
	value *VehicleChargeInfoDtoChargingStation
	isSet bool
}

func (v NullableVehicleChargeInfoDtoChargingStation) Get() *VehicleChargeInfoDtoChargingStation {
	return v.value
}

func (v *NullableVehicleChargeInfoDtoChargingStation) Set(val *VehicleChargeInfoDtoChargingStation) {
	v.value = val
	v.isSet = true
}

func (v NullableVehicleChargeInfoDtoChargingStation) IsSet() bool {
	return v.isSet
}

func (v *NullableVehicleChargeInfoDtoChargingStation) Unset() {
	v.value = nil
	v.isSet = false
}

func NewNullableVehicleChargeInfoDtoChargingStation(val *VehicleChargeInfoDtoChargingStation) *NullableVehicleChargeInfoDtoChargingStation {
	return &NullableVehicleChargeInfoDtoChargingStation{value: val, isSet: true}
}

func (v NullableVehicleChargeInfoDtoChargingStation) MarshalJSON() ([]byte, error) {
	return json.Marshal(v.value)
}

func (v *NullableVehicleChargeInfoDtoChargingStation) UnmarshalJSON(src []byte) error {
	v.isSet = true
	return json.Unmarshal(src, &v.value)
}
