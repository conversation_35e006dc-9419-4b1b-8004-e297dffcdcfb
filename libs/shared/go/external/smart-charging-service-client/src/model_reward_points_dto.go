/*
Smart Charging Service Api

API for managing smart charging service api

API version: 1.0
*/

// Code generated by OpenAPI Generator (https://openapi-generator.tech); DO NOT EDIT.

package smartchargingserviceclient

import (
	"bytes"
	"encoding/json"
	"fmt"
)

// checks if the RewardPointsDto type satisfies the MappedNullable interface at compile time
var _ MappedNullable = &RewardPointsDto{}

// RewardPointsDto struct for RewardPointsDto
type RewardPointsDto struct {
	// The number of points the charge is worth
	Points float32 `json:"points"`
	//
	Reason string `json:"reason"`
}

type _RewardPointsDto RewardPointsDto

// NewRewardPointsDto instantiates a new RewardPointsDto object
// This constructor will assign default values to properties that have it defined,
// and makes sure properties required by API are set, but the set of arguments
// will change when the set of required properties is changed
func NewRewardPointsDto(points float32, reason string) *RewardPointsDto {
	this := RewardPointsDto{}
	this.Points = points
	this.Reason = reason
	return &this
}

// NewRewardPointsDtoWithDefaults instantiates a new RewardPointsDto object
// This constructor will only assign default values to properties that have it defined,
// but it doesn't guarantee that properties required by API are set
func NewRewardPointsDtoWithDefaults() *RewardPointsDto {
	this := RewardPointsDto{}
	return &this
}

// GetPoints returns the Points field value
func (o *RewardPointsDto) GetPoints() float32 {
	if o == nil {
		var ret float32
		return ret
	}

	return o.Points
}

// GetPointsOk returns a tuple with the Points field value
// and a boolean to check if the value has been set.
func (o *RewardPointsDto) GetPointsOk() (*float32, bool) {
	if o == nil {
		return nil, false
	}
	return &o.Points, true
}

// SetPoints sets field value
func (o *RewardPointsDto) SetPoints(v float32) {
	o.Points = v
}

// GetReason returns the Reason field value
func (o *RewardPointsDto) GetReason() string {
	if o == nil {
		var ret string
		return ret
	}

	return o.Reason
}

// GetReasonOk returns a tuple with the Reason field value
// and a boolean to check if the value has been set.
func (o *RewardPointsDto) GetReasonOk() (*string, bool) {
	if o == nil {
		return nil, false
	}
	return &o.Reason, true
}

// SetReason sets field value
func (o *RewardPointsDto) SetReason(v string) {
	o.Reason = v
}

func (o RewardPointsDto) MarshalJSON() ([]byte, error) {
	toSerialize, err := o.ToMap()
	if err != nil {
		return []byte{}, err
	}
	return json.Marshal(toSerialize)
}

func (o RewardPointsDto) ToMap() (map[string]interface{}, error) {
	toSerialize := map[string]interface{}{}
	toSerialize["points"] = o.Points
	toSerialize["reason"] = o.Reason
	return toSerialize, nil
}

func (o *RewardPointsDto) UnmarshalJSON(data []byte) (err error) {
	// This validates that all required properties are included in the JSON object
	// by unmarshalling the object into a generic map with string keys and checking
	// that every required field exists as a key in the generic map.
	requiredProperties := []string{
		"points",
		"reason",
	}

	allProperties := make(map[string]interface{})

	err = json.Unmarshal(data, &allProperties)

	if err != nil {
		return err
	}

	for _, requiredProperty := range requiredProperties {
		if _, exists := allProperties[requiredProperty]; !exists {
			return fmt.Errorf("no value given for required property %v", requiredProperty)
		}
	}

	varRewardPointsDto := _RewardPointsDto{}

	decoder := json.NewDecoder(bytes.NewReader(data))
	decoder.DisallowUnknownFields()
	err = decoder.Decode(&varRewardPointsDto)

	if err != nil {
		return err
	}

	*o = RewardPointsDto(varRewardPointsDto)

	return err
}

type NullableRewardPointsDto struct {
	value *RewardPointsDto
	isSet bool
}

func (v NullableRewardPointsDto) Get() *RewardPointsDto {
	return v.value
}

func (v *NullableRewardPointsDto) Set(val *RewardPointsDto) {
	v.value = val
	v.isSet = true
}

func (v NullableRewardPointsDto) IsSet() bool {
	return v.isSet
}

func (v *NullableRewardPointsDto) Unset() {
	v.value = nil
	v.isSet = false
}

func NewNullableRewardPointsDto(val *RewardPointsDto) *NullableRewardPointsDto {
	return &NullableRewardPointsDto{value: val, isSet: true}
}

func (v NullableRewardPointsDto) MarshalJSON() ([]byte, error) {
	return json.Marshal(v.value)
}

func (v *NullableRewardPointsDto) UnmarshalJSON(src []byte) error {
	v.isSet = true
	return json.Unmarshal(src, &v.value)
}
