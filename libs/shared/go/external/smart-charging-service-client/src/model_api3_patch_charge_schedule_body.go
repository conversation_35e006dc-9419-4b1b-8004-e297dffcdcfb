/*
Smart Charging Service Api

API for managing smart charging service api

API version: 1.0
*/

// Code generated by OpenAPI Generator (https://openapi-generator.tech); DO NOT EDIT.

package smartchargingserviceclient

import (
	"encoding/json"
)

// checks if the API3PatchChargeScheduleBody type satisfies the MappedNullable interface at compile time
var _ MappedNullable = &API3PatchChargeScheduleBody{}

// API3PatchChargeScheduleBody struct for API3PatchChargeScheduleBody
type API3PatchChargeScheduleBody struct {
	StartDay  *float32               `json:"start_day,omitempty"`
	StartTime map[string]interface{} `json:"start_time,omitempty"`
	EndDay    *float32               `json:"end_day,omitempty"`
	EndTime   map[string]interface{} `json:"end_time,omitempty"`
	Status    *ChargeScheduleStatus  `json:"status,omitempty"`
}

// NewAPI3PatchChargeScheduleBody instantiates a new API3PatchChargeScheduleBody object
// This constructor will assign default values to properties that have it defined,
// and makes sure properties required by API are set, but the set of arguments
// will change when the set of required properties is changed
func NewAPI3PatchChargeScheduleBody() *API3PatchChargeScheduleBody {
	this := API3PatchChargeScheduleBody{}
	return &this
}

// NewAPI3PatchChargeScheduleBodyWithDefaults instantiates a new API3PatchChargeScheduleBody object
// This constructor will only assign default values to properties that have it defined,
// but it doesn't guarantee that properties required by API are set
func NewAPI3PatchChargeScheduleBodyWithDefaults() *API3PatchChargeScheduleBody {
	this := API3PatchChargeScheduleBody{}
	return &this
}

// GetStartDay returns the StartDay field value if set, zero value otherwise.
func (o *API3PatchChargeScheduleBody) GetStartDay() float32 {
	if o == nil || IsNil(o.StartDay) {
		var ret float32
		return ret
	}
	return *o.StartDay
}

// GetStartDayOk returns a tuple with the StartDay field value if set, nil otherwise
// and a boolean to check if the value has been set.
func (o *API3PatchChargeScheduleBody) GetStartDayOk() (*float32, bool) {
	if o == nil || IsNil(o.StartDay) {
		return nil, false
	}
	return o.StartDay, true
}

// HasStartDay returns a boolean if a field has been set.
func (o *API3PatchChargeScheduleBody) HasStartDay() bool {
	if o != nil && !IsNil(o.StartDay) {
		return true
	}

	return false
}

// SetStartDay gets a reference to the given float32 and assigns it to the StartDay field.
func (o *API3PatchChargeScheduleBody) SetStartDay(v float32) {
	o.StartDay = &v
}

// GetStartTime returns the StartTime field value if set, zero value otherwise.
func (o *API3PatchChargeScheduleBody) GetStartTime() map[string]interface{} {
	if o == nil || IsNil(o.StartTime) {
		var ret map[string]interface{}
		return ret
	}
	return o.StartTime
}

// GetStartTimeOk returns a tuple with the StartTime field value if set, nil otherwise
// and a boolean to check if the value has been set.
func (o *API3PatchChargeScheduleBody) GetStartTimeOk() (map[string]interface{}, bool) {
	if o == nil || IsNil(o.StartTime) {
		return map[string]interface{}{}, false
	}
	return o.StartTime, true
}

// HasStartTime returns a boolean if a field has been set.
func (o *API3PatchChargeScheduleBody) HasStartTime() bool {
	if o != nil && !IsNil(o.StartTime) {
		return true
	}

	return false
}

// SetStartTime gets a reference to the given map[string]interface{} and assigns it to the StartTime field.
func (o *API3PatchChargeScheduleBody) SetStartTime(v map[string]interface{}) {
	o.StartTime = v
}

// GetEndDay returns the EndDay field value if set, zero value otherwise.
func (o *API3PatchChargeScheduleBody) GetEndDay() float32 {
	if o == nil || IsNil(o.EndDay) {
		var ret float32
		return ret
	}
	return *o.EndDay
}

// GetEndDayOk returns a tuple with the EndDay field value if set, nil otherwise
// and a boolean to check if the value has been set.
func (o *API3PatchChargeScheduleBody) GetEndDayOk() (*float32, bool) {
	if o == nil || IsNil(o.EndDay) {
		return nil, false
	}
	return o.EndDay, true
}

// HasEndDay returns a boolean if a field has been set.
func (o *API3PatchChargeScheduleBody) HasEndDay() bool {
	if o != nil && !IsNil(o.EndDay) {
		return true
	}

	return false
}

// SetEndDay gets a reference to the given float32 and assigns it to the EndDay field.
func (o *API3PatchChargeScheduleBody) SetEndDay(v float32) {
	o.EndDay = &v
}

// GetEndTime returns the EndTime field value if set, zero value otherwise.
func (o *API3PatchChargeScheduleBody) GetEndTime() map[string]interface{} {
	if o == nil || IsNil(o.EndTime) {
		var ret map[string]interface{}
		return ret
	}
	return o.EndTime
}

// GetEndTimeOk returns a tuple with the EndTime field value if set, nil otherwise
// and a boolean to check if the value has been set.
func (o *API3PatchChargeScheduleBody) GetEndTimeOk() (map[string]interface{}, bool) {
	if o == nil || IsNil(o.EndTime) {
		return map[string]interface{}{}, false
	}
	return o.EndTime, true
}

// HasEndTime returns a boolean if a field has been set.
func (o *API3PatchChargeScheduleBody) HasEndTime() bool {
	if o != nil && !IsNil(o.EndTime) {
		return true
	}

	return false
}

// SetEndTime gets a reference to the given map[string]interface{} and assigns it to the EndTime field.
func (o *API3PatchChargeScheduleBody) SetEndTime(v map[string]interface{}) {
	o.EndTime = v
}

// GetStatus returns the Status field value if set, zero value otherwise.
func (o *API3PatchChargeScheduleBody) GetStatus() ChargeScheduleStatus {
	if o == nil || IsNil(o.Status) {
		var ret ChargeScheduleStatus
		return ret
	}
	return *o.Status
}

// GetStatusOk returns a tuple with the Status field value if set, nil otherwise
// and a boolean to check if the value has been set.
func (o *API3PatchChargeScheduleBody) GetStatusOk() (*ChargeScheduleStatus, bool) {
	if o == nil || IsNil(o.Status) {
		return nil, false
	}
	return o.Status, true
}

// HasStatus returns a boolean if a field has been set.
func (o *API3PatchChargeScheduleBody) HasStatus() bool {
	if o != nil && !IsNil(o.Status) {
		return true
	}

	return false
}

// SetStatus gets a reference to the given ChargeScheduleStatus and assigns it to the Status field.
func (o *API3PatchChargeScheduleBody) SetStatus(v ChargeScheduleStatus) {
	o.Status = &v
}

func (o API3PatchChargeScheduleBody) MarshalJSON() ([]byte, error) {
	toSerialize, err := o.ToMap()
	if err != nil {
		return []byte{}, err
	}
	return json.Marshal(toSerialize)
}

func (o API3PatchChargeScheduleBody) ToMap() (map[string]interface{}, error) {
	toSerialize := map[string]interface{}{}
	if !IsNil(o.StartDay) {
		toSerialize["start_day"] = o.StartDay
	}
	if !IsNil(o.StartTime) {
		toSerialize["start_time"] = o.StartTime
	}
	if !IsNil(o.EndDay) {
		toSerialize["end_day"] = o.EndDay
	}
	if !IsNil(o.EndTime) {
		toSerialize["end_time"] = o.EndTime
	}
	if !IsNil(o.Status) {
		toSerialize["status"] = o.Status
	}
	return toSerialize, nil
}

type NullableAPI3PatchChargeScheduleBody struct {
	value *API3PatchChargeScheduleBody
	isSet bool
}

func (v NullableAPI3PatchChargeScheduleBody) Get() *API3PatchChargeScheduleBody {
	return v.value
}

func (v *NullableAPI3PatchChargeScheduleBody) Set(val *API3PatchChargeScheduleBody) {
	v.value = val
	v.isSet = true
}

func (v NullableAPI3PatchChargeScheduleBody) IsSet() bool {
	return v.isSet
}

func (v *NullableAPI3PatchChargeScheduleBody) Unset() {
	v.value = nil
	v.isSet = false
}

func NewNullableAPI3PatchChargeScheduleBody(val *API3PatchChargeScheduleBody) *NullableAPI3PatchChargeScheduleBody {
	return &NullableAPI3PatchChargeScheduleBody{value: val, isSet: true}
}

func (v NullableAPI3PatchChargeScheduleBody) MarshalJSON() ([]byte, error) {
	return json.Marshal(v.value)
}

func (v *NullableAPI3PatchChargeScheduleBody) UnmarshalJSON(src []byte) error {
	v.isSet = true
	return json.Unmarshal(src, &v.value)
}
