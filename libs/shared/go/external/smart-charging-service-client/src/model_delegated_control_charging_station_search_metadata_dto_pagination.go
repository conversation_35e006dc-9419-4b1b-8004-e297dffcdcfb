/*
Smart Charging Service Api

API for managing smart charging service api

API version: 1.0
*/

// Code generated by OpenAPI Generator (https://openapi-generator.tech); DO NOT EDIT.

package smartchargingserviceclient

import (
	"encoding/json"
)

// checks if the DelegatedControlChargingStationSearchMetadataDtoPagination type satisfies the MappedNullable interface at compile time
var _ MappedNullable = &DelegatedControlChargingStationSearchMetadataDtoPagination{}

// DelegatedControlChargingStationSearchMetadataDtoPagination Pagination information
type DelegatedControlChargingStationSearchMetadataDtoPagination struct {
	Pages        *int32 `json:"pages,omitempty"`
	Total        *int32 `json:"total,omitempty"`
	Page         *int32 `json:"page,omitempty"`
	ItemsPerPage *int32 `json:"itemsPerPage,omitempty"`
}

// NewDelegatedControlChargingStationSearchMetadataDtoPagination instantiates a new DelegatedControlChargingStationSearchMetadataDtoPagination object
// This constructor will assign default values to properties that have it defined,
// and makes sure properties required by API are set, but the set of arguments
// will change when the set of required properties is changed
func NewDelegatedControlChargingStationSearchMetadataDtoPagination() *DelegatedControlChargingStationSearchMetadataDtoPagination {
	this := DelegatedControlChargingStationSearchMetadataDtoPagination{}
	return &this
}

// NewDelegatedControlChargingStationSearchMetadataDtoPaginationWithDefaults instantiates a new DelegatedControlChargingStationSearchMetadataDtoPagination object
// This constructor will only assign default values to properties that have it defined,
// but it doesn't guarantee that properties required by API are set
func NewDelegatedControlChargingStationSearchMetadataDtoPaginationWithDefaults() *DelegatedControlChargingStationSearchMetadataDtoPagination {
	this := DelegatedControlChargingStationSearchMetadataDtoPagination{}
	return &this
}

// GetPages returns the Pages field value if set, zero value otherwise.
func (o *DelegatedControlChargingStationSearchMetadataDtoPagination) GetPages() int32 {
	if o == nil || IsNil(o.Pages) {
		var ret int32
		return ret
	}
	return *o.Pages
}

// GetPagesOk returns a tuple with the Pages field value if set, nil otherwise
// and a boolean to check if the value has been set.
func (o *DelegatedControlChargingStationSearchMetadataDtoPagination) GetPagesOk() (*int32, bool) {
	if o == nil || IsNil(o.Pages) {
		return nil, false
	}
	return o.Pages, true
}

// HasPages returns a boolean if a field has been set.
func (o *DelegatedControlChargingStationSearchMetadataDtoPagination) HasPages() bool {
	if o != nil && !IsNil(o.Pages) {
		return true
	}

	return false
}

// SetPages gets a reference to the given int32 and assigns it to the Pages field.
func (o *DelegatedControlChargingStationSearchMetadataDtoPagination) SetPages(v int32) {
	o.Pages = &v
}

// GetTotal returns the Total field value if set, zero value otherwise.
func (o *DelegatedControlChargingStationSearchMetadataDtoPagination) GetTotal() int32 {
	if o == nil || IsNil(o.Total) {
		var ret int32
		return ret
	}
	return *o.Total
}

// GetTotalOk returns a tuple with the Total field value if set, nil otherwise
// and a boolean to check if the value has been set.
func (o *DelegatedControlChargingStationSearchMetadataDtoPagination) GetTotalOk() (*int32, bool) {
	if o == nil || IsNil(o.Total) {
		return nil, false
	}
	return o.Total, true
}

// HasTotal returns a boolean if a field has been set.
func (o *DelegatedControlChargingStationSearchMetadataDtoPagination) HasTotal() bool {
	if o != nil && !IsNil(o.Total) {
		return true
	}

	return false
}

// SetTotal gets a reference to the given int32 and assigns it to the Total field.
func (o *DelegatedControlChargingStationSearchMetadataDtoPagination) SetTotal(v int32) {
	o.Total = &v
}

// GetPage returns the Page field value if set, zero value otherwise.
func (o *DelegatedControlChargingStationSearchMetadataDtoPagination) GetPage() int32 {
	if o == nil || IsNil(o.Page) {
		var ret int32
		return ret
	}
	return *o.Page
}

// GetPageOk returns a tuple with the Page field value if set, nil otherwise
// and a boolean to check if the value has been set.
func (o *DelegatedControlChargingStationSearchMetadataDtoPagination) GetPageOk() (*int32, bool) {
	if o == nil || IsNil(o.Page) {
		return nil, false
	}
	return o.Page, true
}

// HasPage returns a boolean if a field has been set.
func (o *DelegatedControlChargingStationSearchMetadataDtoPagination) HasPage() bool {
	if o != nil && !IsNil(o.Page) {
		return true
	}

	return false
}

// SetPage gets a reference to the given int32 and assigns it to the Page field.
func (o *DelegatedControlChargingStationSearchMetadataDtoPagination) SetPage(v int32) {
	o.Page = &v
}

// GetItemsPerPage returns the ItemsPerPage field value if set, zero value otherwise.
func (o *DelegatedControlChargingStationSearchMetadataDtoPagination) GetItemsPerPage() int32 {
	if o == nil || IsNil(o.ItemsPerPage) {
		var ret int32
		return ret
	}
	return *o.ItemsPerPage
}

// GetItemsPerPageOk returns a tuple with the ItemsPerPage field value if set, nil otherwise
// and a boolean to check if the value has been set.
func (o *DelegatedControlChargingStationSearchMetadataDtoPagination) GetItemsPerPageOk() (*int32, bool) {
	if o == nil || IsNil(o.ItemsPerPage) {
		return nil, false
	}
	return o.ItemsPerPage, true
}

// HasItemsPerPage returns a boolean if a field has been set.
func (o *DelegatedControlChargingStationSearchMetadataDtoPagination) HasItemsPerPage() bool {
	if o != nil && !IsNil(o.ItemsPerPage) {
		return true
	}

	return false
}

// SetItemsPerPage gets a reference to the given int32 and assigns it to the ItemsPerPage field.
func (o *DelegatedControlChargingStationSearchMetadataDtoPagination) SetItemsPerPage(v int32) {
	o.ItemsPerPage = &v
}

func (o DelegatedControlChargingStationSearchMetadataDtoPagination) MarshalJSON() ([]byte, error) {
	toSerialize, err := o.ToMap()
	if err != nil {
		return []byte{}, err
	}
	return json.Marshal(toSerialize)
}

func (o DelegatedControlChargingStationSearchMetadataDtoPagination) ToMap() (map[string]interface{}, error) {
	toSerialize := map[string]interface{}{}
	if !IsNil(o.Pages) {
		toSerialize["pages"] = o.Pages
	}
	if !IsNil(o.Total) {
		toSerialize["total"] = o.Total
	}
	if !IsNil(o.Page) {
		toSerialize["page"] = o.Page
	}
	if !IsNil(o.ItemsPerPage) {
		toSerialize["itemsPerPage"] = o.ItemsPerPage
	}
	return toSerialize, nil
}

type NullableDelegatedControlChargingStationSearchMetadataDtoPagination struct {
	value *DelegatedControlChargingStationSearchMetadataDtoPagination
	isSet bool
}

func (v NullableDelegatedControlChargingStationSearchMetadataDtoPagination) Get() *DelegatedControlChargingStationSearchMetadataDtoPagination {
	return v.value
}

func (v *NullableDelegatedControlChargingStationSearchMetadataDtoPagination) Set(val *DelegatedControlChargingStationSearchMetadataDtoPagination) {
	v.value = val
	v.isSet = true
}

func (v NullableDelegatedControlChargingStationSearchMetadataDtoPagination) IsSet() bool {
	return v.isSet
}

func (v *NullableDelegatedControlChargingStationSearchMetadataDtoPagination) Unset() {
	v.value = nil
	v.isSet = false
}

func NewNullableDelegatedControlChargingStationSearchMetadataDtoPagination(val *DelegatedControlChargingStationSearchMetadataDtoPagination) *NullableDelegatedControlChargingStationSearchMetadataDtoPagination {
	return &NullableDelegatedControlChargingStationSearchMetadataDtoPagination{value: val, isSet: true}
}

func (v NullableDelegatedControlChargingStationSearchMetadataDtoPagination) MarshalJSON() ([]byte, error) {
	return json.Marshal(v.value)
}

func (v *NullableDelegatedControlChargingStationSearchMetadataDtoPagination) UnmarshalJSON(src []byte) error {
	v.isSet = true
	return json.Unmarshal(src, &v.value)
}
