export const COMMON_ARRAY_ERROR = 'Validation failed (array is expected)';
export const COMMON_CURRENCY_PENCE_ERROR =
  'Input must be a valid currency value in pence';
export const COMMON_EMAIL_MAX_LENGTH = 255;
export const COMMON_EMAIL_MAX_LENGTH_ERROR = `Email address cannot be longer than ${COMMON_EMAIL_MAX_LENGTH} characters`;
export const COMMON_ENUM_STRING_ERROR =
  'Validation failed (enum string is expected)';
export const COMMON_INTERNAL_SERVER_ERROR =
  'Something went wrong. Please try again.';
export const COMMON_INVALID_EMAIL_ERROR = 'Email address must be valid';
export const COMMON_NOT_EMPTY_ERROR = 'This field cannot be empty';
export const COMMON_NUMERIC_ARRAY_ERROR =
  'Validation failed (numeric array is expected)';
export const COMMON_NUMERIC_STRING_ERROR =
  'Validation failed (numeric string is expected)';
export const COMMON_PRICE_DECIMAL_PLACES_ERROR =
  'Price must be within three decimal places';
export const COMMON_PRICE_MAX_VALUE_ERROR = (value = '10'): string =>
  `Price must be less than £${value}`;
export const COMMON_PRICE_MIN_VALUE_ERROR = (value = '0'): string =>
  `Price must be greater than £${value}`;
export const COMMON_REQUIRED_ERROR = 'This field is required';
export const COMMON_UUID_STRING_ERROR = 'Validation failed (uuid is expected)';
export const COMMON_INVALID_TIME_ERROR = 'Must be a valid time';
export const COMMON_VALUE_GREATER_THAN_ZERO_ERROR =
  'Value must be greater than 0';
