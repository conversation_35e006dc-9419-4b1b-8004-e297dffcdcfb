import * as Sequelize from 'sequelize';
import { DataTypes, Model, Optional } from 'sequelize';
import type { Users, UsersId } from './users';

export interface UserPreferencesAttributes {
  id: number;
  userId: number;
  key: string;
  value?: string;
  createdAt?: Date;
  updatedAt?: Date;
}

export type UserPreferencesPk = 'id';
export type UserPreferencesId = UserPreferences[UserPreferencesPk];
export type UserPreferencesOptionalAttributes =
  | 'id'
  | 'value'
  | 'createdAt'
  | 'updatedAt';
export type UserPreferencesCreationAttributes = Optional<
  UserPreferencesAttributes,
  UserPreferencesOptionalAttributes
>;

export class UserPreferences
  extends Model<UserPreferencesAttributes, UserPreferencesCreationAttributes>
  implements UserPreferencesAttributes
{
  id!: number;
  userId!: number;
  key!: string;
  value?: string;
  createdAt?: Date;
  updatedAt?: Date;

  // UserPreferences belongsTo Users via userId
  user!: Users;
  getUser!: Sequelize.BelongsToGetAssociationMixin<Users>;
  setUser!: Sequelize.BelongsToSetAssociationMixin<Users, UsersId>;
  createUser!: Sequelize.BelongsToCreateAssociationMixin<Users>;

  static initModel(sequelize: Sequelize.Sequelize): typeof UserPreferences {
    return UserPreferences.init(
      {
        id: {
          autoIncrement: true,
          type: DataTypes.INTEGER.UNSIGNED,
          allowNull: false,
          primaryKey: true,
        },
        userId: {
          type: DataTypes.INTEGER.UNSIGNED,
          allowNull: false,
          references: {
            model: 'users',
            key: 'id',
          },
          field: 'user_id',
        },
        key: {
          type: DataTypes.STRING(255),
          allowNull: false,
        },
        value: {
          type: DataTypes.TEXT,
          allowNull: true,
        },
        createdAt: {
          type: DataTypes.DATE,
          allowNull: true,
          field: 'created_at',
        },
        updatedAt: {
          type: DataTypes.DATE,
          allowNull: true,
          field: 'updated_at',
        },
      },
      {
        sequelize,
        tableName: 'user_preferences',
        timestamps: false,
        paranoid: true,
        indexes: [
          {
            name: 'PRIMARY',
            unique: true,
            using: 'BTREE',
            fields: [{ name: 'id' }],
          },
          {
            name: 'user_preferences_user_id_foreign',
            using: 'BTREE',
            fields: [{ name: 'user_id' }],
          },
        ],
      }
    );
  }
}
