import { GroupLocation } from '../group-location';
import { Groups } from '../groups';
import { PodAddresses } from '../pod-addresses';
import { TEST_DATES } from './embedded/dates';
import {
  TEST_GROUP_TYPES_ENTITY,
  TEST_HOST_GROUP_TYPES_ENTITY,
  TEST_SCHEME_GROUP_TYPES_ENTITY,
} from './group-types.entity';

export const TEST_GROUP_ENTITY: Groups = {
  ...TEST_DATES,
  contactName: 'The Keeper',
  groupLocations: [] as GroupLocation[],
  id: 1,
  name: 'Registers of Scotland',
  type: TEST_HOST_GROUP_TYPES_ENTITY,
  typeId: 2,
  uid: 'afe96d8c-b1bb-44ec-b7e7-8d370a7fab52',
} as Groups;

export const TEST_SCHEME_GROUP_ENTITY: Groups = {
  ...TEST_DATES,
  groupLocations: [] as GroupLocation[],
  id: 2,
  name: 'Registers of England',
  podAddresses: [] as PodAddresses[],
  type: TEST_SCHEME_GROUP_TYPES_ENTITY,
  typeId: 3,
  uid: '058ab627-7790-4a51-9482-3a14c15a283a',
} as Groups;

export const TEST_GROUP_ENTITY_WITH_TYPE: Groups = {
  ...TEST_DATES,
  contactName: 'Morris Smith',
  groupLocations: [] as GroupLocation[],
  id: 3,
  name: 'Registers of Ireland',
  phone: '123-234',
  type: TEST_GROUP_TYPES_ENTITY,
  typeId: 1,
  uid: '4a1a7033-67ef-4af4-ad50-a000facc7e1b',
} as Groups;
