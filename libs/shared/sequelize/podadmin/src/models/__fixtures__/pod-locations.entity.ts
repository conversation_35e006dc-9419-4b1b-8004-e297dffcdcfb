import { PodLocations } from '../pod-locations';
import { TEST_DATES } from './embedded/dates';
import { TEST_POD_ADDRESSES_ENTITY } from './pod-addresses.entity';
import { TEST_POD_LOCATION_UNIT_ENTITY } from './pod-location-unit.entity';
import {
  TEST_POD_UNIT_ENTITY,
  TEST_POD_UNIT_ENTITY_WITH_NO_NAME,
} from './pod-units.entity';
import { TEST_SCHEME_GROUP_ENTITY } from './group.entity';

export const TEST_POD_LOCATIONS_ENTITY: PodLocations = {
  ...TEST_DATES,
  contactlessEnabled: 1,
  description: 'A test pod.',
  groupLocations: [
    {
      ...TEST_DATES,
      group: TEST_SCHEME_GROUP_ENTITY,
      groupId: 1,
      locationId: 1,
    },
  ],
  id: 1,
  isEvZone: 1,
  isPublic: 1,
  latitude: 162.726933,
  longitude: -48.86372,
  midmeterEnabled: 1,
  podLocationUnits: [TEST_POD_LOCATION_UNIT_ENTITY],
  unit: TEST_POD_UNIT_ENTITY,
} as PodLocations;

export const TEST_POD_LOCATIONS_ENTITY_WITH_ADDRESS: PodLocations = {
  ...TEST_POD_LOCATIONS_ENTITY,
  addressId: 1,
  address: {
    ...TEST_POD_ADDRESSES_ENTITY,
    podLocations: [] as PodLocations[],
  },
} as PodLocations;

export const TEST_POD_LOCATIONS_ENTITY_WITH_NO_UNIT_NAME: PodLocations = {
  ...TEST_POD_LOCATIONS_ENTITY_WITH_ADDRESS,
  unit: TEST_POD_UNIT_ENTITY_WITH_NO_NAME,
} as PodLocations;
