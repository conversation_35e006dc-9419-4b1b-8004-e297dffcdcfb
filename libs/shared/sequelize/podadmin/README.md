# Podadmin Sequelize

This library contains a NestJS module which can be used to integrate with the podadmin database.

## Running unit tests

Run `nx test shared-sequelize-podadmin` to execute the unit tests via [Jest](https://jestjs.io).

## Generating model

Run `nx generate-sources shared-sequelize-podadmin --pass=**********` to generate a TypeScript model from
the
sequelize-auto.config.json file.

**Note:** use double quotes and escape any breaking characters such as '&' when injecting the password.

The password can be found in AWS Secrets Manager under the 'experience-commercial-staging' account.

Note that it is then necessary to run `npx nx format:write` across the workspace in order to format the generated source
files in accordance with the workspace wide lint conventions.

## Configuring the database connection

In your application which is consuming this library you should configure the database connection as shown below:

```
DB_HOST=localhost
DB_PORT=3306
DB_USERNAME=root
DB_PASSWORD=secret
DB_DATABASE=podpoint
```

## Table permissions

This section attempts to document which podadmin tables the site administration modules interact with and what
permissions would be required.

| Table                      | SELECT | INSERT | UPDATE | DELETE |
| -------------------------- | ------ | ------ | ------ | ------ |
| authorisers                | [x]    | [x]    | [x]    |        |
| billing_accounts           | [x]    |        |        |        |
| billing_events             | [x]    |        |        |        |
| charge_schedule_statuses   | [x]    |        |        |        |
| charge_schedules           | [x]    |        |        |        |
| charge_states              | [x]    |        |        |        |
| charges                    | [x]    |        |        |        |
| ev_driver_domains          | [x]    | [x]    | [x]    |        |
| ev_drivers                 | [x]    | [x]    | [x]    |        |
| group_types                | [x]    |        |        |        |
| groups                     | [x]    |        |        |        |
| members                    | [x]    | [x]    | [x]    |        |
| parking_opening_time_notes | [x]    | [x]    | [x]    | [x]    |
| parking_opening_times      | [x]    | [x]    | [x]    | [x]    |
| pcbs                       | [x]    |        |        |        |
| pod_addresses              | [x]    |        | [x]    |        |
| pod_connectors             | [x]    |        |        |        |
| pod_doors                  | [x]    |        |        |        |
| pod_location_unit          | [x]    |        |        |        |
| pod_locations              | [x]    |        | [x]    |        |
| pod_models                 | [x]    |        |        |        |
| pod_statuses               | [x]    |        |        |        |
| pod_unit_charge_schedules  | [x]    |        |        |        |
| revenue_profile_tiers      | [x]    | [x]    | [x]    |        |
| revenue_profiles           | [x]    | [x]    | [x]    |        |
| users                      | [x]    |        | [x]    |        |
| vehicle_makes              | [x]    |        |        |        |
| vehicle_model_user         | [x]    |        |        |        |
| vehicle_models             | [x]    |        |        |        |
