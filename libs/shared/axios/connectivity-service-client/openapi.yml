openapi: 3.0.1
info:
  contact:
    email: <EMAIL>
    name: Connectivity Squad (Parrot)
    url: https://pod-point.com
  description: |-
    An API for querying the current connected state of a charging station.
    The Connectivity Service monitors (PPCP) messages received from charging stations, and persists the last message
    This status api leverages the last message seen from a device.
    This status api also tracks which devices are currently connected via websockets
    ### NB: All date/time inputs are to be provided in ISO format and timezone UTC e.g. `2023-01-03T16:09:59.000Z`.
  title: Connectivity Service Status API
  version: '1.0'
servers:
  - url: //http://status.connectivity-service.connectivity.prod/
paths:
  /connectivity/charging-stations/{ppid}:
    get:
      description: Provide Connectivity Status for a given PPID
      operationId: getChargingStationStatus
      parameters:
        - description: Charging Station ID
          in: path
          name: ppid
          required: true
          schema:
            type: string
      responses:
        '200':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/data.StatusResponse'
          description: OK
        '400':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/server.ApiErrorResponse'
          description: Bad Request
        '404':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/server.ApiErrorResponse'
          description: Not Found
        '500':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/server.ApiErrorResponse'
          description: Internal Server Error
      summary: Provide Connectivity Status for a given PPID
      tags:
        - ConnectivityStatus
  /connectivity/charging-stations/{ppid}/messages:
    get:
      description: Provide last messages for a given PPID
      operationId: getChargingStationMessages
      parameters:
        - description: Charging Station ID
          in: path
          name: ppid
          required: true
          schema:
            type: string
      responses:
        '200':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/handlers.MessagesResponse'
          description: OK
        '400':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/server.ApiErrorResponse'
          description: Bad Request
        '404':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/server.ApiErrorResponse'
          description: Not Found
        '500':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/server.ApiErrorResponse'
          description: Internal Server Error
      summary: Provide last messages for a given PPID
      tags:
        - ConnectivityStatus
components:
  schemas:
    data.StatusResponse:
      example:
        evses:
          - connectors:
              - door: door
                id: 1
                activeFaults:
                  - LatestOccurrence: LatestOccurrence
                    podPointFault: podPointFault
                    firstOccurrence: firstOccurrence
                    errorCode: errorCode
                    vendorErrorCode: vendorErrorCode
                    info: info
                  - LatestOccurrence: LatestOccurrence
                    podPointFault: podPointFault
                    firstOccurrence: firstOccurrence
                    errorCode: errorCode
                    vendorErrorCode: vendorErrorCode
                    info: info
                chargingState: chargingState
              - door: door
                id: 1
                activeFaults:
                  - LatestOccurrence: LatestOccurrence
                    podPointFault: podPointFault
                    firstOccurrence: firstOccurrence
                    errorCode: errorCode
                    vendorErrorCode: vendorErrorCode
                    info: info
                  - LatestOccurrence: LatestOccurrence
                    podPointFault: podPointFault
                    firstOccurrence: firstOccurrence
                    errorCode: errorCode
                    vendorErrorCode: vendorErrorCode
                    info: info
                chargingState: chargingState
            macAddress: macAddress
            serialNumber: serialNumber
            id: 5
            connectivityState:
              lastSeenAt: lastSeenAt
              connectionQuality: 0
              protocol: protocol
              lastMessageAt: lastMessageAt
              offlineSubStatus: offlineSubStatus
              signalStrength: 6
              connectionStartedAt: connectionStartedAt
              connectivityStatus: connectivityStatus
            architecture: architecture
          - connectors:
              - door: door
                id: 1
                activeFaults:
                  - LatestOccurrence: LatestOccurrence
                    podPointFault: podPointFault
                    firstOccurrence: firstOccurrence
                    errorCode: errorCode
                    vendorErrorCode: vendorErrorCode
                    info: info
                  - LatestOccurrence: LatestOccurrence
                    podPointFault: podPointFault
                    firstOccurrence: firstOccurrence
                    errorCode: errorCode
                    vendorErrorCode: vendorErrorCode
                    info: info
                chargingState: chargingState
              - door: door
                id: 1
                activeFaults:
                  - LatestOccurrence: LatestOccurrence
                    podPointFault: podPointFault
                    firstOccurrence: firstOccurrence
                    errorCode: errorCode
                    vendorErrorCode: vendorErrorCode
                    info: info
                  - LatestOccurrence: LatestOccurrence
                    podPointFault: podPointFault
                    firstOccurrence: firstOccurrence
                    errorCode: errorCode
                    vendorErrorCode: vendorErrorCode
                    info: info
                chargingState: chargingState
            macAddress: macAddress
            serialNumber: serialNumber
            id: 5
            connectivityState:
              lastSeenAt: lastSeenAt
              connectionQuality: 0
              protocol: protocol
              lastMessageAt: lastMessageAt
              offlineSubStatus: offlineSubStatus
              signalStrength: 6
              connectionStartedAt: connectionStartedAt
              connectivityStatus: connectivityStatus
            architecture: architecture
        connectedComponents:
          - connectedComponents
          - connectedComponents
        chargingStation:
          connectivityState:
            lastSeenAt: lastSeenAt
            connectionQuality: 0
            protocol: protocol
            lastMessageAt: lastMessageAt
            offlineSubStatus: offlineSubStatus
            signalStrength: 6
            connectionStartedAt: connectionStartedAt
            connectivityStatus: connectivityStatus
        ppid: ppid
      properties:
        chargingStation:
          $ref: '#/components/schemas/types.ChargingStation'
        connectedComponents:
          items:
            type: string
          type: array
        evses:
          items:
            $ref: '#/components/schemas/types.Evse'
          type: array
        ppid:
          type: string
      type: object
    handlers.MessagesResponse:
      example:
        messages:
          - '{}'
          - '{}'
        ppid: ppid
      properties:
        messages:
          items:
            type: object
          type: array
        ppid:
          type: string
      type: object
    server.ApiErrorResponse:
      example:
        reason: Validation error
        status: Rejected
      properties:
        reason:
          description: Verbose error message
          example: Validation error
          type: string
        status:
          description: The overall status of the request
          example: Rejected
          type: string
      type: object
    types.ChargingStation:
      example:
        connectivityState:
          lastSeenAt: lastSeenAt
          connectionQuality: 0
          protocol: protocol
          lastMessageAt: lastMessageAt
          offlineSubStatus: offlineSubStatus
          signalStrength: 6
          connectionStartedAt: connectionStartedAt
          connectivityStatus: connectivityStatus
      properties:
        connectivityState:
          $ref: '#/components/schemas/types.ConnectivityState'
      type: object
    types.ConnectivityState:
      example:
        lastSeenAt: lastSeenAt
        connectionQuality: 0
        protocol: protocol
        lastMessageAt: lastMessageAt
        offlineSubStatus: offlineSubStatus
        signalStrength: 6
        connectionStartedAt: connectionStartedAt
        connectivityStatus: connectivityStatus
      properties:
        connectionQuality:
          type: integer
        connectionStartedAt:
          type: string
        connectivityStatus:
          type: string
        lastMessageAt:
          type: string
        lastSeenAt:
          type: string
        offlineSubStatus:
          type: string
        protocol:
          type: string
        signalStrength:
          type: integer
      type: object
    types.Connector:
      example:
        door: door
        id: 1
        activeFaults:
          - LatestOccurrence: LatestOccurrence
            podPointFault: podPointFault
            firstOccurrence: firstOccurrence
            errorCode: errorCode
            vendorErrorCode: vendorErrorCode
            info: info
          - LatestOccurrence: LatestOccurrence
            podPointFault: podPointFault
            firstOccurrence: firstOccurrence
            errorCode: errorCode
            vendorErrorCode: vendorErrorCode
            info: info
        chargingState: chargingState
      properties:
        activeFaults:
          items:
            $ref: '#/components/schemas/types.Fault'
          type: array
        chargingState:
          type: string
        door:
          type: string
        id:
          type: integer
      type: object
    types.Evse:
      example:
        connectors:
          - door: door
            id: 1
            activeFaults:
              - LatestOccurrence: LatestOccurrence
                podPointFault: podPointFault
                firstOccurrence: firstOccurrence
                errorCode: errorCode
                vendorErrorCode: vendorErrorCode
                info: info
              - LatestOccurrence: LatestOccurrence
                podPointFault: podPointFault
                firstOccurrence: firstOccurrence
                errorCode: errorCode
                vendorErrorCode: vendorErrorCode
                info: info
            chargingState: chargingState
          - door: door
            id: 1
            activeFaults:
              - LatestOccurrence: LatestOccurrence
                podPointFault: podPointFault
                firstOccurrence: firstOccurrence
                errorCode: errorCode
                vendorErrorCode: vendorErrorCode
                info: info
              - LatestOccurrence: LatestOccurrence
                podPointFault: podPointFault
                firstOccurrence: firstOccurrence
                errorCode: errorCode
                vendorErrorCode: vendorErrorCode
                info: info
            chargingState: chargingState
        macAddress: macAddress
        serialNumber: serialNumber
        id: 5
        connectivityState:
          lastSeenAt: lastSeenAt
          connectionQuality: 0
          protocol: protocol
          lastMessageAt: lastMessageAt
          offlineSubStatus: offlineSubStatus
          signalStrength: 6
          connectionStartedAt: connectionStartedAt
          connectivityStatus: connectivityStatus
        architecture: architecture
      properties:
        architecture:
          type: string
        connectivityState:
          $ref: '#/components/schemas/types.ConnectivityState'
        connectors:
          items:
            $ref: '#/components/schemas/types.Connector'
          type: array
        id:
          type: integer
        macAddress:
          type: string
        serialNumber:
          type: string
      type: object
    types.Fault:
      example:
        LatestOccurrence: LatestOccurrence
        podPointFault: podPointFault
        firstOccurrence: firstOccurrence
        errorCode: errorCode
        vendorErrorCode: vendorErrorCode
        info: info
      properties:
        LatestOccurrence:
          type: string
        errorCode:
          type: string
        firstOccurrence:
          type: string
        info:
          type: string
        podPointFault:
          type: string
        vendorErrorCode:
          type: string
      type: object
x-original-swagger-version: '2.0'
