/**
 * This file is AUTO GENERATED by [msw-auto-mock](https://github.com/zoubingwu/msw-auto-mock)
 * Feel free to commit/edit it as you need.
 */
/* eslint-disable */
/* tslint:disable */
import { HttpResponse, http } from 'msw';
import { faker } from '@faker-js/faker';

faker.seed(1);

const baseURL = '';
const MAX_ARRAY_LENGTH = 20;

let i = 0;
const next = () => {
  if (i === Number.MAX_SAFE_INTEGER - 1) {
    i = 0;
  }
  return i++;
};

export const handlers = [
  http.post(
    `${baseURL}/commands/charging-stations/:ppid/availability/set`,
    async () => {
      const resultArray = [
        [
          await getPostCommandsChargingStationsPpidAvailabilitySet200Response(),
          { status: 200 },
        ],
        [
          await getPostCommandsChargingStationsPpidAvailabilitySet202Response(),
          { status: 202 },
        ],
      ];

      return HttpResponse.json(...resultArray[next() % resultArray.length]);
    }
  ),
  http.post(
    `${baseURL}/commands/charging-stations/:ppid/charging-profile/clear`,
    async () => {
      const resultArray = [
        [
          await getPostCommandsChargingStationsPpidChargingProfileClear200Response(),
          { status: 200 },
        ],
        [
          await getPostCommandsChargingStationsPpidChargingProfileClear202Response(),
          { status: 202 },
        ],
      ];

      return HttpResponse.json(...resultArray[next() % resultArray.length]);
    }
  ),
  http.post(
    `${baseURL}/commands/charging-stations/:ppid/charging-profile/set`,
    async () => {
      const resultArray = [
        [
          await getPostCommandsChargingStationsPpidChargingProfileSet200Response(),
          { status: 200 },
        ],
        [
          await getPostCommandsChargingStationsPpidChargingProfileSet202Response(),
          { status: 202 },
        ],
      ];

      return HttpResponse.json(...resultArray[next() % resultArray.length]);
    }
  ),
  http.post(
    `${baseURL}/commands/charging-stations/:ppid/config/variables/get`,
    async () => {
      const resultArray = [
        [
          await getPostCommandsChargingStationsPpidConfigVariablesGet200Response(),
          { status: 200 },
        ],
        [
          await getPostCommandsChargingStationsPpidConfigVariablesGet202Response(),
          { status: 202 },
        ],
      ];

      return HttpResponse.json(...resultArray[next() % resultArray.length]);
    }
  ),
  http.post(
    `${baseURL}/commands/charging-stations/:ppid/config/variables/set`,
    async () => {
      const resultArray = [
        [
          await getPostCommandsChargingStationsPpidConfigVariablesSet200Response(),
          { status: 200 },
        ],
        [
          await getPostCommandsChargingStationsPpidConfigVariablesSet202Response(),
          { status: 202 },
        ],
      ];

      return HttpResponse.json(...resultArray[next() % resultArray.length]);
    }
  ),
  http.post(`${baseURL}/commands/charging-stations/:ppid/debug`, async () => {
    const resultArray = [
      [
        await getPostCommandsChargingStationsPpidDebug200Response(),
        { status: 200 },
      ],
      [
        await getPostCommandsChargingStationsPpidDebug202Response(),
        { status: 202 },
      ],
    ];

    return HttpResponse.json(...resultArray[next() % resultArray.length]);
  }),
  http.post(
    `${baseURL}/commands/charging-stations/:ppid/firmware/update`,
    async () => {
      const resultArray = [
        [
          await getPostCommandsChargingStationsPpidFirmwareUpdate200Response(),
          { status: 200 },
        ],
        [
          await getPostCommandsChargingStationsPpidFirmwareUpdate202Response(),
          { status: 202 },
        ],
      ];

      return HttpResponse.json(...resultArray[next() % resultArray.length]);
    }
  ),
  http.post(
    `${baseURL}/commands/charging-stations/:ppid/local-list/send`,
    async () => {
      const resultArray = [
        [
          await getPostCommandsChargingStationsPpidLocalListSend200Response(),
          { status: 200 },
        ],
        [
          await getPostCommandsChargingStationsPpidLocalListSend202Response(),
          { status: 202 },
        ],
      ];

      return HttpResponse.json(...resultArray[next() % resultArray.length]);
    }
  ),
  http.post(
    `${baseURL}/commands/charging-stations/:ppid/logs/get`,
    async () => {
      const resultArray = [
        [
          await getPostCommandsChargingStationsPpidLogsGet200Response(),
          { status: 200 },
        ],
        [
          await getPostCommandsChargingStationsPpidLogsGet202Response(),
          { status: 202 },
        ],
      ];

      return HttpResponse.json(...resultArray[next() % resultArray.length]);
    }
  ),
  http.post(`${baseURL}/commands/charging-stations/:ppid/reset`, async () => {
    const resultArray = [
      [
        await getPostCommandsChargingStationsPpidReset200Response(),
        { status: 200 },
      ],
      [
        await getPostCommandsChargingStationsPpidReset202Response(),
        { status: 202 },
      ],
    ];

    return HttpResponse.json(...resultArray[next() % resultArray.length]);
  }),
  http.post(
    `${baseURL}/commands/charging-stations/:ppid/schedules`,
    async () => {
      const resultArray = [
        [
          await getPostCommandsChargingStationsPpidSchedules200Response(),
          { status: 200 },
        ],
        [
          await getPostCommandsChargingStationsPpidSchedules202Response(),
          { status: 202 },
        ],
      ];

      return HttpResponse.json(...resultArray[next() % resultArray.length]);
    }
  ),
  http.post(
    `${baseURL}/commands/charging-stations/:ppid/transaction/start`,
    async () => {
      const resultArray = [
        [
          await getPostCommandsChargingStationsPpidTransactionStart200Response(),
          { status: 200 },
        ],
        [
          await getPostCommandsChargingStationsPpidTransactionStart202Response(),
          { status: 202 },
        ],
      ];

      return HttpResponse.json(...resultArray[next() % resultArray.length]);
    }
  ),
  http.post(
    `${baseURL}/commands/charging-stations/:ppid/transaction/stop`,
    async () => {
      const resultArray = [
        [
          await getPostCommandsChargingStationsPpidTransactionStop200Response(),
          { status: 200 },
        ],
        [
          await getPostCommandsChargingStationsPpidTransactionStop202Response(),
          { status: 202 },
        ],
      ];

      return HttpResponse.json(...resultArray[next() % resultArray.length]);
    }
  ),
  http.post(`${baseURL}/commands/charging-stations/:ppid/trigger`, async () => {
    const resultArray = [
      [
        await getPostCommandsChargingStationsPpidTrigger200Response(),
        { status: 200 },
      ],
      [
        await getPostCommandsChargingStationsPpidTrigger202Response(),
        { status: 202 },
      ],
    ];

    return HttpResponse.json(...resultArray[next() % resultArray.length]);
  }),
  http.post(
    `${baseURL}/commands/charging-stations/:ppid/unlock-connector`,
    async () => {
      const resultArray = [
        [
          await getPostCommandsChargingStationsPpidUnlockConnector200Response(),
          { status: 200 },
        ],
        [
          await getPostCommandsChargingStationsPpidUnlockConnector202Response(),
          { status: 202 },
        ],
      ];

      return HttpResponse.json(...resultArray[next() % resultArray.length]);
    }
  ),
  http.post(
    `${baseURL}/commands/pcbs/:pcbSerialNumber/certificate-signed`,
    async () => {
      const resultArray = [
        [
          await getPostCommandsPcbsPcbSerialNumberCertificateSigned200Response(),
          { status: 200 },
        ],
        [
          await getPostCommandsPcbsPcbSerialNumberCertificateSigned202Response(),
          { status: 202 },
        ],
      ];

      return HttpResponse.json(...resultArray[next() % resultArray.length]);
    }
  ),
  http.post(
    `${baseURL}/commands/pcbs/:pcbSerialNumber/config/variables/get`,
    async () => {
      const resultArray = [
        [
          await getPostCommandsPcbsPcbSerialNumberConfigVariablesGet200Response(),
          { status: 200 },
        ],
      ];

      return HttpResponse.json(...resultArray[next() % resultArray.length]);
    }
  ),
  http.post(
    `${baseURL}/commands/pcbs/:pcbSerialNumber/config/variables/set`,
    async () => {
      const resultArray = [
        [
          await getPostCommandsPcbsPcbSerialNumberConfigVariablesSet200Response(),
          { status: 200 },
        ],
      ];

      return HttpResponse.json(...resultArray[next() % resultArray.length]);
    }
  ),
  http.post(`${baseURL}/commands/pcbs/:pcbSerialNumber/reset`, async () => {
    const resultArray = [
      [
        await getPostCommandsPcbsPcbSerialNumberReset200Response(),
        { status: 200 },
      ],
    ];

    return HttpResponse.json(...resultArray[next() % resultArray.length]);
  }),
  http.post(`${baseURL}/commands/pcbs/:pcbSerialNumber/trigger`, async () => {
    const resultArray = [
      [
        await getPostCommandsPcbsPcbSerialNumberTrigger200Response(),
        { status: 200 },
      ],
    ];

    return HttpResponse.json(...resultArray[next() % resultArray.length]);
  }),
];

export function getPostCommandsChargingStationsPpidAvailabilitySet200Response() {
  return {
    commandType: 'GetVariables',
    response: '{}',
    messageId: 'b98e38a2-35d0-4eef-b04c-16a7a9240557',
    commandId: '715eea26-1719-4655-b99c-7f561f888703',
    statusMessage: 'statusMessage',
    status: 'Success',
  };
}

export function getPostCommandsChargingStationsPpidAvailabilitySet202Response() {
  return {
    commandType: 'GetVariables',
    response: '{}',
    messageId: 'b98e38a2-35d0-4eef-b04c-16a7a9240557',
    commandId: '715eea26-1719-4655-b99c-7f561f888703',
    statusMessage: 'statusMessage',
    status: 'Success',
  };
}

export function getPostCommandsChargingStationsPpidChargingProfileClear200Response() {
  return {
    commandType: 'GetVariables',
    response: '{}',
    messageId: 'b98e38a2-35d0-4eef-b04c-16a7a9240557',
    commandId: '715eea26-1719-4655-b99c-7f561f888703',
    statusMessage: 'statusMessage',
    status: 'Success',
  };
}

export function getPostCommandsChargingStationsPpidChargingProfileClear202Response() {
  return {
    commandType: 'GetVariables',
    response: '{}',
    messageId: 'b98e38a2-35d0-4eef-b04c-16a7a9240557',
    commandId: '715eea26-1719-4655-b99c-7f561f888703',
    statusMessage: 'statusMessage',
    status: 'Success',
  };
}

export function getPostCommandsChargingStationsPpidChargingProfileSet200Response() {
  return {
    commandType: 'GetVariables',
    response: '{}',
    messageId: 'b98e38a2-35d0-4eef-b04c-16a7a9240557',
    commandId: '715eea26-1719-4655-b99c-7f561f888703',
    statusMessage: 'statusMessage',
    status: 'Success',
  };
}

export function getPostCommandsChargingStationsPpidChargingProfileSet202Response() {
  return {
    commandType: 'GetVariables',
    response: '{}',
    messageId: 'b98e38a2-35d0-4eef-b04c-16a7a9240557',
    commandId: '715eea26-1719-4655-b99c-7f561f888703',
    statusMessage: 'statusMessage',
    status: 'Success',
  };
}

export function getPostCommandsChargingStationsPpidConfigVariablesGet200Response() {
  return {
    commandType: 'GetVariables',
    response: '{}',
    messageId: 'b98e38a2-35d0-4eef-b04c-16a7a9240557',
    commandId: '715eea26-1719-4655-b99c-7f561f888703',
    statusMessage: 'statusMessage',
    status: 'Success',
  };
}

export function getPostCommandsChargingStationsPpidConfigVariablesGet202Response() {
  return {
    commandType: 'GetVariables',
    response: '{}',
    messageId: 'b98e38a2-35d0-4eef-b04c-16a7a9240557',
    commandId: '715eea26-1719-4655-b99c-7f561f888703',
    statusMessage: 'statusMessage',
    status: 'Success',
  };
}

export function getPostCommandsChargingStationsPpidConfigVariablesSet200Response() {
  return {
    commandType: 'GetVariables',
    response: '{}',
    messageId: 'b98e38a2-35d0-4eef-b04c-16a7a9240557',
    commandId: '715eea26-1719-4655-b99c-7f561f888703',
    statusMessage: 'statusMessage',
    status: 'Success',
  };
}

export function getPostCommandsChargingStationsPpidConfigVariablesSet202Response() {
  return {
    commandType: 'GetVariables',
    response: '{}',
    messageId: 'b98e38a2-35d0-4eef-b04c-16a7a9240557',
    commandId: '715eea26-1719-4655-b99c-7f561f888703',
    statusMessage: 'statusMessage',
    status: 'Success',
  };
}

export function getPostCommandsChargingStationsPpidDebug200Response() {
  return {
    commandType: 'GetVariables',
    response: '{}',
    messageId: 'b98e38a2-35d0-4eef-b04c-16a7a9240557',
    commandId: '715eea26-1719-4655-b99c-7f561f888703',
    statusMessage: 'statusMessage',
    status: 'Success',
  };
}

export function getPostCommandsChargingStationsPpidDebug202Response() {
  return {
    commandType: 'GetVariables',
    response: '{}',
    messageId: 'b98e38a2-35d0-4eef-b04c-16a7a9240557',
    commandId: '715eea26-1719-4655-b99c-7f561f888703',
    statusMessage: 'statusMessage',
    status: 'Success',
  };
}

export function getPostCommandsChargingStationsPpidFirmwareUpdate200Response() {
  return {
    commandType: 'GetVariables',
    response: '{}',
    messageId: 'b98e38a2-35d0-4eef-b04c-16a7a9240557',
    commandId: '715eea26-1719-4655-b99c-7f561f888703',
    statusMessage: 'statusMessage',
    status: 'Success',
  };
}

export function getPostCommandsChargingStationsPpidFirmwareUpdate202Response() {
  return {
    commandType: 'GetVariables',
    response: '{}',
    messageId: 'b98e38a2-35d0-4eef-b04c-16a7a9240557',
    commandId: '715eea26-1719-4655-b99c-7f561f888703',
    statusMessage: 'statusMessage',
    status: 'Success',
  };
}

export function getPostCommandsChargingStationsPpidLocalListSend200Response() {
  return {
    commandType: 'GetVariables',
    response: '{}',
    messageId: 'b98e38a2-35d0-4eef-b04c-16a7a9240557',
    commandId: '715eea26-1719-4655-b99c-7f561f888703',
    statusMessage: 'statusMessage',
    status: 'Success',
  };
}

export function getPostCommandsChargingStationsPpidLocalListSend202Response() {
  return {
    commandType: 'GetVariables',
    response: '{}',
    messageId: 'b98e38a2-35d0-4eef-b04c-16a7a9240557',
    commandId: '715eea26-1719-4655-b99c-7f561f888703',
    statusMessage: 'statusMessage',
    status: 'Success',
  };
}

export function getPostCommandsChargingStationsPpidLogsGet200Response() {
  return {
    commandType: 'GetVariables',
    response: '{}',
    messageId: 'b98e38a2-35d0-4eef-b04c-16a7a9240557',
    commandId: '715eea26-1719-4655-b99c-7f561f888703',
    statusMessage: 'statusMessage',
    status: 'Success',
  };
}

export function getPostCommandsChargingStationsPpidLogsGet202Response() {
  return {
    commandType: 'GetVariables',
    response: '{}',
    messageId: 'b98e38a2-35d0-4eef-b04c-16a7a9240557',
    commandId: '715eea26-1719-4655-b99c-7f561f888703',
    statusMessage: 'statusMessage',
    status: 'Success',
  };
}

export function getPostCommandsChargingStationsPpidReset200Response() {
  return {
    commandType: 'GetVariables',
    response: '{}',
    messageId: 'b98e38a2-35d0-4eef-b04c-16a7a9240557',
    commandId: '715eea26-1719-4655-b99c-7f561f888703',
    statusMessage: 'statusMessage',
    status: 'Success',
  };
}

export function getPostCommandsChargingStationsPpidReset202Response() {
  return {
    commandType: 'GetVariables',
    response: '{}',
    messageId: 'b98e38a2-35d0-4eef-b04c-16a7a9240557',
    commandId: '715eea26-1719-4655-b99c-7f561f888703',
    statusMessage: 'statusMessage',
    status: 'Success',
  };
}

export function getPostCommandsChargingStationsPpidSchedules200Response() {
  return {
    commandType: 'GetVariables',
    response: '{}',
    messageId: 'b98e38a2-35d0-4eef-b04c-16a7a9240557',
    commandId: '715eea26-1719-4655-b99c-7f561f888703',
    statusMessage: 'statusMessage',
    status: 'Success',
  };
}

export function getPostCommandsChargingStationsPpidSchedules202Response() {
  return {
    commandType: 'GetVariables',
    response: '{}',
    messageId: 'b98e38a2-35d0-4eef-b04c-16a7a9240557',
    commandId: '715eea26-1719-4655-b99c-7f561f888703',
    statusMessage: 'statusMessage',
    status: 'Success',
  };
}

export function getPostCommandsChargingStationsPpidTransactionStart200Response() {
  return {
    commandType: 'GetVariables',
    response: '{}',
    messageId: 'b98e38a2-35d0-4eef-b04c-16a7a9240557',
    commandId: '715eea26-1719-4655-b99c-7f561f888703',
    statusMessage: 'statusMessage',
    status: 'Success',
  };
}

export function getPostCommandsChargingStationsPpidTransactionStart202Response() {
  return {
    commandType: 'GetVariables',
    response: '{}',
    messageId: 'b98e38a2-35d0-4eef-b04c-16a7a9240557',
    commandId: '715eea26-1719-4655-b99c-7f561f888703',
    statusMessage: 'statusMessage',
    status: 'Success',
  };
}

export function getPostCommandsChargingStationsPpidTransactionStop200Response() {
  return {
    commandType: 'GetVariables',
    response: '{}',
    messageId: 'b98e38a2-35d0-4eef-b04c-16a7a9240557',
    commandId: '715eea26-1719-4655-b99c-7f561f888703',
    statusMessage: 'statusMessage',
    status: 'Success',
  };
}

export function getPostCommandsChargingStationsPpidTransactionStop202Response() {
  return {
    commandType: 'GetVariables',
    response: '{}',
    messageId: 'b98e38a2-35d0-4eef-b04c-16a7a9240557',
    commandId: '715eea26-1719-4655-b99c-7f561f888703',
    statusMessage: 'statusMessage',
    status: 'Success',
  };
}

export function getPostCommandsChargingStationsPpidTrigger200Response() {
  return {
    commandType: 'GetVariables',
    response: '{}',
    messageId: 'b98e38a2-35d0-4eef-b04c-16a7a9240557',
    commandId: '715eea26-1719-4655-b99c-7f561f888703',
    statusMessage: 'statusMessage',
    status: 'Success',
  };
}

export function getPostCommandsChargingStationsPpidTrigger202Response() {
  return {
    commandType: 'GetVariables',
    response: '{}',
    messageId: 'b98e38a2-35d0-4eef-b04c-16a7a9240557',
    commandId: '715eea26-1719-4655-b99c-7f561f888703',
    statusMessage: 'statusMessage',
    status: 'Success',
  };
}

export function getPostCommandsChargingStationsPpidUnlockConnector200Response() {
  return {
    commandType: 'GetVariables',
    response: '{}',
    messageId: 'b98e38a2-35d0-4eef-b04c-16a7a9240557',
    commandId: '715eea26-1719-4655-b99c-7f561f888703',
    statusMessage: 'statusMessage',
    status: 'Success',
  };
}

export function getPostCommandsChargingStationsPpidUnlockConnector202Response() {
  return {
    commandType: 'GetVariables',
    response: '{}',
    messageId: 'b98e38a2-35d0-4eef-b04c-16a7a9240557',
    commandId: '715eea26-1719-4655-b99c-7f561f888703',
    statusMessage: 'statusMessage',
    status: 'Success',
  };
}

export function getPostCommandsPcbsPcbSerialNumberCertificateSigned200Response() {
  return {
    commandType: 'GetVariables',
    response: '{}',
    messageId: 'b98e38a2-35d0-4eef-b04c-16a7a9240557',
    commandId: '715eea26-1719-4655-b99c-7f561f888703',
    statusMessage: 'statusMessage',
    status: 'Success',
  };
}

export function getPostCommandsPcbsPcbSerialNumberCertificateSigned202Response() {
  return {
    commandType: 'GetVariables',
    response: '{}',
    messageId: 'b98e38a2-35d0-4eef-b04c-16a7a9240557',
    commandId: '715eea26-1719-4655-b99c-7f561f888703',
    statusMessage: 'statusMessage',
    status: 'Success',
  };
}

export function getPostCommandsPcbsPcbSerialNumberConfigVariablesGet200Response() {
  return {
    commandType: 'GetVariables',
    response: '{}',
    messageId: 'b98e38a2-35d0-4eef-b04c-16a7a9240557',
    commandId: '715eea26-1719-4655-b99c-7f561f888703',
    statusMessage: 'statusMessage',
    status: 'Success',
  };
}

export function getPostCommandsPcbsPcbSerialNumberConfigVariablesSet200Response() {
  return {
    commandType: 'GetVariables',
    response: '{}',
    messageId: 'b98e38a2-35d0-4eef-b04c-16a7a9240557',
    commandId: '715eea26-1719-4655-b99c-7f561f888703',
    statusMessage: 'statusMessage',
    status: 'Success',
  };
}

export function getPostCommandsPcbsPcbSerialNumberReset200Response() {
  return {
    commandType: 'GetVariables',
    response: '{}',
    messageId: 'b98e38a2-35d0-4eef-b04c-16a7a9240557',
    commandId: '715eea26-1719-4655-b99c-7f561f888703',
    statusMessage: 'statusMessage',
    status: 'Success',
  };
}

export function getPostCommandsPcbsPcbSerialNumberTrigger200Response() {
  return {
    commandType: 'GetVariables',
    response: '{}',
    messageId: 'b98e38a2-35d0-4eef-b04c-16a7a9240557',
    commandId: '715eea26-1719-4655-b99c-7f561f888703',
    statusMessage: 'statusMessage',
    status: 'Success',
  };
}
