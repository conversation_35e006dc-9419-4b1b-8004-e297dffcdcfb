{"openapi": "3.0.0", "paths": {"/admins": {"get": {"operationId": "AdminController_findByGroupId", "parameters": [{"name": "groupId", "required": true, "in": "query", "schema": {"type": "number"}}], "responses": {"200": {"description": "", "content": {"application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/Admin"}}}}}}, "tags": ["Admin"]}, "post": {"operationId": "AdminController_createByGroupId", "parameters": [{"name": "groupId", "required": true, "in": "query", "schema": {"type": "number"}}, {"name": "adminUid", "required": true, "in": "query", "schema": {"type": "string"}}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/CreateAdminRequest"}}}}, "responses": {"201": {"description": ""}}, "tags": ["Admin"]}}, "/admins/{id}/status": {"post": {"operationId": "AdminController_reactivateByGroupIdAndAdminId", "parameters": [{"name": "id", "required": true, "in": "path", "schema": {"type": "number"}}, {"name": "groupId", "required": true, "in": "query", "schema": {"type": "number"}}, {"name": "adminId", "required": true, "in": "query", "schema": {"type": "number"}}], "responses": {"200": {"description": ""}}, "tags": ["Admin"]}}, "/admins/{id}": {"delete": {"operationId": "AdminController_deleteByGroupId", "parameters": [{"name": "id", "required": true, "in": "path", "schema": {"type": "number"}}, {"name": "groupId", "required": true, "in": "query", "schema": {"type": "number"}}, {"name": "adminId", "required": true, "in": "query", "schema": {"type": "number"}}], "responses": {"204": {"description": ""}}, "tags": ["Admin"]}}, "/admins/{id}/invitation": {"post": {"operationId": "AdminController_inviteByGroupIdAndDriverId", "parameters": [{"name": "id", "required": true, "in": "path", "schema": {"type": "number"}}, {"name": "groupId", "required": true, "in": "query", "schema": {"type": "number"}}, {"name": "adminUid", "required": true, "in": "query", "schema": {"type": "string"}}], "responses": {"204": {"description": ""}}, "tags": ["Admin"]}}, "/admins/email-verification": {"post": {"operationId": "EmailVerificationController_sendEmailVerification", "parameters": [], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/SendEmailVerificationRequest"}}}}, "responses": {"202": {"description": ""}}, "tags": ["EmailVerification"]}}, "/user/group": {"get": {"operationId": "GroupController_findByGroupId", "parameters": [{"name": "groupId", "required": true, "in": "query", "schema": {"type": "number"}}], "responses": {"200": {"description": "", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Group"}}}}}, "tags": ["Group"]}}, "/user/groups": {"get": {"operationId": "GroupController_findAllByGroupId", "parameters": [{"name": "groupId", "required": true, "in": "query", "schema": {"type": "number"}}], "responses": {"200": {"description": "", "content": {"application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/Group"}}}}}}, "tags": ["Group"]}}, "/user/group/charges/csv": {"get": {"operationId": "GroupController_generateCsv", "parameters": [{"name": "groupId", "required": true, "in": "query", "schema": {"type": "number"}}, {"name": "date", "required": true, "in": "query", "schema": {"type": "string"}}], "responses": {"200": {"description": ""}}, "tags": ["Group"]}}, "/admins/password-reset": {"post": {"operationId": "PasswordResetController_sendPasswordReset", "parameters": [], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/SendPasswordResetRequest"}}}}, "responses": {"202": {"description": ""}}, "tags": ["PasswordReset"]}}, "/admins/sign-in-with-email": {"post": {"operationId": "SignInWithEmailController_sendSignInWithEmail", "parameters": [], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/SendSignInWithEmailRequest"}}}}, "responses": {"202": {"description": ""}}, "tags": ["SignInWithEmail"]}}, "/drivers": {"get": {"operationId": "DriverController_findByGroupId", "parameters": [{"name": "groupId", "required": true, "in": "query", "schema": {"type": "number"}}], "responses": {"200": {"description": "", "content": {"application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/Driver"}}}}}}, "tags": ["Driver"]}, "post": {"operationId": "DriverController_createByGroupId", "parameters": [{"name": "groupId", "required": true, "in": "query", "schema": {"type": "number"}}, {"name": "adminId", "required": true, "in": "query", "schema": {"type": "number"}}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/CreateDriverRequest"}}}}, "responses": {"201": {"description": "", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Driver"}}}}}, "tags": ["Driver"]}}, "/drivers/{driverId}": {"put": {"operationId": "DriverController_updateByGroupId", "parameters": [{"name": "groupId", "required": true, "in": "query", "schema": {"type": "number"}}, {"name": "driverId", "required": true, "in": "path", "schema": {"type": "number"}}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/UpdateDriverRequest"}}}}, "responses": {"204": {"description": ""}}, "tags": ["Driver"]}, "delete": {"operationId": "DriverController_deleteByGroupId", "parameters": [{"name": "groupId", "required": true, "in": "query", "schema": {"type": "number"}}, {"name": "driverId", "required": true, "in": "path", "schema": {"type": "number"}}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/DeleteDriverRequest"}}}}, "responses": {"204": {"description": ""}}, "tags": ["Driver"]}}, "/drivers/upload": {"post": {"operationId": "DriverController_bulkCreateByGroupId", "parameters": [{"name": "groupId", "required": true, "in": "query", "schema": {"type": "number"}}], "responses": {"201": {"description": "", "content": {"application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/Driver"}}}}}}, "tags": ["Driver"]}}, "/drivers/{driverId}/invitation": {"post": {"operationId": "DriverController_inviteByGroupIdAndDriverId", "parameters": [{"name": "groupId", "required": true, "in": "query", "schema": {"type": "number"}}, {"name": "adminId", "required": true, "in": "query", "schema": {"type": "number"}}, {"name": "driverId", "required": true, "in": "path", "schema": {"type": "number"}}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/InviteDriverRequest"}}}}, "responses": {"204": {"description": ""}}, "tags": ["Driver"]}}, "/drivers/{driverId}/charges": {"get": {"operationId": "DriverChargesController_findByGroupIdAndDriverId", "parameters": [{"name": "groupId", "required": true, "in": "query", "schema": {"type": "number"}}, {"name": "groupUid", "required": true, "in": "query", "schema": {"type": "string"}}, {"name": "tariffTier", "required": true, "in": "query", "schema": {"type": "string"}}, {"name": "driverId", "required": true, "in": "path", "schema": {"type": "number"}}], "responses": {"200": {"description": "", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Driver"}}}}}, "tags": ["DriverCharges"]}}, "/drivers/{driverId}/charges/csv": {"get": {"operationId": "DriverChargesController_generateCsv", "parameters": [{"name": "groupId", "required": true, "in": "query", "schema": {"type": "number"}}, {"name": "groupUid", "required": true, "in": "query", "schema": {"type": "string"}}, {"name": "tariffTier", "required": true, "in": "query", "schema": {"type": "string"}}, {"name": "driverId", "required": true, "in": "path", "schema": {"type": "number"}}, {"name": "date", "required": true, "in": "query", "schema": {"type": "string"}}, {"name": "all", "required": true, "in": "query", "schema": {"type": "string"}}], "responses": {"200": {"description": ""}}, "tags": ["DriverCharges"]}}, "/adverts": {"get": {"operationId": "AdvertController_findAll", "parameters": [], "responses": {"200": {"description": "", "content": {"application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/Advert"}}}}}}, "tags": ["Advert"]}}, "/adverts/{advertId}": {"get": {"operationId": "AdvertController_findByAdvertId", "parameters": [{"name": "advertId", "required": true, "in": "path", "schema": {"type": "number"}}], "responses": {"200": {"description": "", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Advert"}}}}}, "tags": ["Advert"]}}, "/adverts/{advertId}/pods": {"post": {"operationId": "AdvertController_assignByAdvertId", "parameters": [{"name": "advertId", "required": true, "in": "path", "schema": {"type": "number"}}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/AssignPodToAdvertRequest"}}}}, "responses": {"204": {"description": ""}}, "tags": ["Advert"]}}, "/adverts/{advertId}/pods/{podId}": {"delete": {"operationId": "AdvertController_removeByPodId", "parameters": [{"name": "advertId", "required": true, "in": "path", "schema": {"type": "number"}}, {"name": "podId", "required": true, "in": "path", "schema": {"type": "number"}}], "responses": {"204": {"description": ""}}, "tags": ["Advert"]}}, "/billing": {"get": {"operationId": "BillingController_findBillingInformationByGroupUid", "parameters": [{"name": "groupUid", "required": true, "in": "query", "schema": {"type": "string"}}], "responses": {"200": {"description": "", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/BillingInformation"}}}}}, "tags": ["Billing"]}}, "/billing/invoices/{invoiceId}/pdf": {"get": {"operationId": "BillingInvoiceController_getInvoicePdf", "parameters": [{"name": "invoiceId", "required": true, "in": "path", "schema": {"type": "string"}}], "responses": {"200": {"description": ""}}, "tags": ["BillingInvoice"]}}, "/billing/statements/{statementId}/pdf": {"get": {"operationId": "BillingStatementController_getStatementPdf", "parameters": [{"name": "statementId", "required": true, "in": "path", "schema": {"type": "string"}}], "responses": {"200": {"description": ""}}, "tags": ["BillingStatement"]}}, "/domains": {"get": {"operationId": "DomainController_findByGroupId", "parameters": [{"name": "groupId", "required": true, "in": "query", "schema": {"type": "number"}}], "responses": {"200": {"description": "", "content": {"application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/Domain"}}}}}}, "tags": ["Domain"]}, "post": {"operationId": "DomainController_createByGroupId", "parameters": [{"name": "groupId", "required": true, "in": "query", "schema": {"type": "number"}}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/CreateDomainRequest"}}}}, "responses": {"201": {"description": "", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Domain"}}}}}, "tags": ["Domain"]}}, "/domains/{domainId}": {"put": {"operationId": "DomainController_updateByGroupId", "parameters": [{"name": "groupId", "required": true, "in": "query", "schema": {"type": "number"}}, {"name": "domainId", "required": true, "in": "path", "schema": {"type": "number"}}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/CreateDomainRequest"}}}}, "responses": {"204": {"description": ""}}, "tags": ["Domain"]}, "delete": {"operationId": "DomainController_deleteByGroupId", "parameters": [{"name": "groupId", "required": true, "in": "query", "schema": {"type": "number"}}, {"name": "domainId", "required": true, "in": "path", "schema": {"type": "number"}}], "responses": {"204": {"description": ""}}, "tags": ["Domain"]}}, "/expenses/new": {"get": {"operationId": "ExpenseController_findNewByGroup", "parameters": [{"name": "groupUid", "required": true, "in": "query", "schema": {"type": "string"}}, {"name": "year", "required": true, "in": "query", "schema": {"type": "number"}}, {"name": "month", "required": true, "in": "query", "schema": {"type": "number"}}], "responses": {"200": {"description": "", "content": {"application/json": {"schema": {"type": "array", "items": {"type": "object"}}}}}}, "tags": ["Expense"]}}, "/expenses/processed": {"get": {"operationId": "ExpenseController_findProcessedByGroup", "parameters": [{"name": "groupUid", "required": true, "in": "query", "schema": {"type": "string"}}, {"name": "year", "required": true, "in": "query", "schema": {"type": "number"}}, {"name": "month", "required": true, "in": "query", "schema": {"type": "number"}}], "responses": {"200": {"description": "", "content": {"application/json": {"schema": {"type": "array", "items": {"type": "object"}}}}}}, "tags": ["Expense"]}}, "/expenses/new/grouped": {"get": {"operationId": "ExpenseController_findNewByGroupGroupedByDriver", "parameters": [{"name": "groupUid", "required": true, "in": "query", "schema": {"type": "string"}}, {"name": "year", "required": true, "in": "query", "schema": {"type": "number"}}, {"name": "month", "required": true, "in": "query", "schema": {"type": "number"}}], "responses": {"200": {"description": "", "content": {"application/json": {"schema": {"type": "array", "items": {"type": "object"}}}}}}, "tags": ["Expense"]}}, "/expenses/drivers/{driverId}": {"get": {"operationId": "ExpenseController_findAllByGroupUidAndDriverId", "parameters": [{"name": "groupUid", "required": true, "in": "query", "schema": {"type": "string"}}, {"name": "driverId", "required": true, "in": "path", "schema": {"type": "number"}}], "responses": {"200": {"description": ""}}, "tags": ["Expense"]}}, "/expenses/process": {"post": {"operationId": "ExpenseController_markExpensesAsProcessed", "parameters": [{"name": "adminId", "required": true, "in": "query", "schema": {"type": "number"}}, {"name": "groupUid", "required": true, "in": "query", "schema": {"type": "string"}}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ProcessExpensesRequest"}}}}, "responses": {"201": {"description": ""}}, "tags": ["Expense"]}}, "/expenses/stats/monthly": {"get": {"operationId": "ExpenseController_getMonthlyUsageForOrganisation", "parameters": [{"name": "groupUid", "required": true, "in": "query", "schema": {"type": "string"}}], "responses": {"200": {"description": "", "content": {"application/json": {"schema": {"type": "object"}}}}}, "tags": ["Expense"]}}, "/health": {"get": {"operationId": "HealthController_check", "parameters": [], "responses": {"200": {"description": "The Health Check is successful", "content": {"application/json": {"schema": {"type": "object", "properties": {"status": {"type": "string", "example": "ok"}, "info": {"type": "object", "example": {"database": {"status": "up"}}, "additionalProperties": {"type": "object", "required": ["status"], "properties": {"status": {"type": "string"}}, "additionalProperties": true}, "nullable": true}, "error": {"type": "object", "example": {}, "additionalProperties": {"type": "object", "required": ["status"], "properties": {"status": {"type": "string"}}, "additionalProperties": true}, "nullable": true}, "details": {"type": "object", "example": {"database": {"status": "up"}}, "additionalProperties": {"type": "object", "required": ["status"], "properties": {"status": {"type": "string"}}, "additionalProperties": true}}}}}}}, "503": {"description": "The Health Check is not successful", "content": {"application/json": {"schema": {"type": "object", "properties": {"status": {"type": "string", "example": "error"}, "info": {"type": "object", "example": {"database": {"status": "up"}}, "additionalProperties": {"type": "object", "required": ["status"], "properties": {"status": {"type": "string"}}, "additionalProperties": true}, "nullable": true}, "error": {"type": "object", "example": {"redis": {"status": "down", "message": "Could not connect"}}, "additionalProperties": {"type": "object", "required": ["status"], "properties": {"status": {"type": "string"}}, "additionalProperties": true}, "nullable": true}, "details": {"type": "object", "example": {"database": {"status": "up"}, "redis": {"status": "down", "message": "Could not connect"}}, "additionalProperties": {"type": "object", "required": ["status"], "properties": {"status": {"type": "string"}}, "additionalProperties": true}}}}}}}}, "tags": ["Health"]}}, "/insights": {"get": {"operationId": "InsightsController_findByGroupUid", "parameters": [{"name": "groupUid", "required": true, "in": "query", "schema": {"type": "string"}}, {"name": "year", "required": true, "in": "query", "schema": {"type": "number"}}], "responses": {"200": {"description": "", "content": {"application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/Insight"}}}}}}, "tags": ["Insights"]}}, "/insights/csv": {"get": {"operationId": "InsightsController_generateCsvByGroupUid", "parameters": [{"name": "groupId", "required": true, "in": "query", "schema": {"type": "number"}}, {"name": "groupUid", "required": true, "in": "query", "schema": {"type": "string"}}, {"name": "year", "required": true, "in": "query", "schema": {"type": "number"}}], "responses": {"200": {"description": ""}}, "tags": ["Insights"]}}, "/insights/sites/{siteId}": {"get": {"operationId": "InsightsController_findByGroupUidAndSiteId", "parameters": [{"name": "groupUid", "required": true, "in": "query", "schema": {"type": "string"}}, {"name": "siteId", "required": true, "in": "path", "schema": {"type": "number"}}, {"name": "year", "required": true, "in": "query", "schema": {"type": "number"}}], "responses": {"200": {"description": "", "content": {"application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/Insight"}}}}}}, "tags": ["Insights"]}}, "/insights/sites/{siteId}/csv": {"get": {"operationId": "InsightsController_generateCsvByGroupUidAndSiteId", "parameters": [{"name": "groupUid", "required": true, "in": "query", "schema": {"type": "string"}}, {"name": "siteId", "required": true, "in": "path", "schema": {"type": "number"}}, {"name": "year", "required": true, "in": "query", "schema": {"type": "number"}}], "responses": {"200": {"description": ""}}, "tags": ["Insights"]}}, "/insights/chargers/{chargerId}": {"get": {"operationId": "InsightsController_findByGroupUidAndChargerId", "parameters": [{"name": "groupUid", "required": true, "in": "query", "schema": {"type": "string"}}, {"name": "chargerId", "required": true, "in": "path", "schema": {"type": "number"}}, {"name": "year", "required": true, "in": "query", "schema": {"type": "number"}}], "responses": {"200": {"description": "", "content": {"application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/Insight"}}}}}}, "tags": ["Insights"]}}, "/insights/chargers/{chargerId}/csv": {"get": {"operationId": "InsightsController_generateCsvByGroupUidAndChargerId", "parameters": [{"name": "groupId", "required": true, "in": "query", "schema": {"type": "number"}}, {"name": "groupUid", "required": true, "in": "query", "schema": {"type": "string"}}, {"name": "chargerId", "required": true, "in": "path", "schema": {"type": "number"}}, {"name": "year", "required": true, "in": "query", "schema": {"type": "number"}}], "responses": {"200": {"description": ""}}, "tags": ["Insights"]}}, "/pods": {"get": {"operationId": "PodController_findByGroupId", "parameters": [{"name": "groupId", "required": true, "in": "query", "schema": {"type": "number"}}], "responses": {"200": {"description": "", "content": {"application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/Pod"}}}}}}, "tags": ["Pod"]}}, "/pods/{podId}": {"get": {"operationId": "PodController_findByGroupIdAndPodId", "parameters": [{"name": "groupId", "required": true, "in": "query", "schema": {"type": "number"}}, {"name": "podId", "required": true, "in": "path", "schema": {"type": "string"}}], "responses": {"200": {"description": "", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Pod"}}}}}, "tags": ["Pod"]}}, "/pods/{podId}/charges": {"get": {"operationId": "PodController_generateCsv", "parameters": [{"name": "groupId", "required": true, "in": "query", "schema": {"type": "number"}}, {"name": "groupUid", "required": true, "in": "query", "schema": {"type": "string"}}, {"name": "podId", "required": true, "in": "path", "schema": {"type": "number"}}, {"name": "date", "required": true, "in": "query", "schema": {"type": "string"}}], "responses": {"200": {"description": ""}}, "tags": ["Pod"]}, "post": {"operationId": "PodChargesController_confirmChargeByGroupIdAndPodId", "parameters": [{"name": "groupId", "required": true, "in": "query", "schema": {"type": "number"}}, {"name": "adminId", "required": true, "in": "query", "schema": {"type": "number"}}, {"name": "podId", "required": true, "in": "path", "schema": {"type": "number"}}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ConfirmChargeRequest"}}}}, "responses": {"204": {"description": ""}}, "tags": ["PodCharges"]}}, "/pods/{podId}/events/security": {"get": {"operationId": "PodController_findSecurityEventsByGroupIdAndPodId", "parameters": [{"name": "podId", "required": true, "in": "path", "schema": {"type": "string"}}, {"name": "groupId", "required": true, "in": "query", "schema": {"type": "number"}}], "responses": {"200": {"description": "", "content": {"application/json": {"schema": {"type": "object"}}}}}, "tags": ["Pod"]}}, "/pods/{podId}/tariff": {"put": {"operationId": "PodTariffController_updateByGroupId", "parameters": [{"name": "groupId", "required": true, "in": "query", "schema": {"type": "number"}}, {"name": "podId", "required": true, "in": "path", "schema": {"type": "number"}}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/UpdateTariffRequest"}}}}, "responses": {"204": {"description": ""}}, "tags": ["<PERSON><PERSON><PERSON><PERSON><PERSON>"]}, "delete": {"operationId": "PodTariffController_removeByGroupId", "parameters": [{"name": "groupId", "required": true, "in": "query", "schema": {"type": "number"}}, {"name": "podId", "required": true, "in": "path", "schema": {"type": "number"}}], "responses": {"204": {"description": ""}}, "tags": ["<PERSON><PERSON><PERSON><PERSON><PERSON>"]}}, "/tariffs": {"get": {"operationId": "TariffController_findAll", "parameters": [{"name": "groupId", "required": true, "in": "query", "schema": {"type": "number"}}], "responses": {"200": {"description": "", "content": {"application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/Tariff"}}}}}}, "tags": ["Tariff"]}, "post": {"operationId": "TariffController_createByGroupId", "parameters": [{"name": "groupId", "required": true, "in": "query", "schema": {"type": "number"}}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/CreateTariffRequest"}}}}, "responses": {"201": {"description": "", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Tariff"}}}}}, "tags": ["Tariff"]}}, "/tariffs/{tariffId}": {"get": {"operationId": "TariffController_findByGroupIdAndTariffId", "parameters": [{"name": "groupId", "required": true, "in": "query", "schema": {"type": "number"}}, {"name": "tariffId", "required": true, "in": "path", "schema": {"type": "number"}}], "responses": {"200": {"description": "", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Tariff"}}}}}, "tags": ["Tariff"]}, "put": {"operationId": "TariffController_updateByGroupId", "parameters": [{"name": "groupId", "required": true, "in": "query", "schema": {"type": "number"}}, {"name": "tariffId", "required": true, "in": "path", "schema": {"type": "number"}}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/CreateTariffRequest"}}}}, "responses": {"204": {"description": ""}}, "tags": ["Tariff"]}, "delete": {"operationId": "TariffController_deleteByGroupId", "parameters": [{"name": "groupId", "required": true, "in": "query", "schema": {"type": "number"}}, {"name": "tariffId", "required": true, "in": "path", "schema": {"type": "number"}}], "responses": {"204": {"description": ""}}, "tags": ["Tariff"]}}, "/tariffs/{tariffId}/schedules": {"post": {"operationId": "ScheduleController_createScheduleByGroupIdAndTariffId", "parameters": [{"name": "groupId", "required": true, "in": "query", "schema": {"type": "number"}}, {"name": "tariffId", "required": true, "in": "path", "schema": {"type": "number"}}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/CreateTariffScheduleRequest"}}}}, "responses": {"201": {"description": ""}}, "tags": ["Schedule"]}, "delete": {"operationId": "ScheduleController_deleteByGroupId", "parameters": [{"name": "groupId", "required": true, "in": "query", "schema": {"type": "number"}}, {"name": "tariffId", "required": true, "in": "path", "schema": {"type": "number"}}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/DeleteTariffScheduleRequest"}}}}, "responses": {"204": {"description": ""}}, "tags": ["Schedule"]}}, "/tariffs/{tariffId}/schedules/daynight": {"post": {"operationId": "ScheduleController_createDayNightScheduleByGroupIdAndTariffId", "parameters": [{"name": "groupId", "required": true, "in": "query", "schema": {"type": "number"}}, {"name": "tariffId", "required": true, "in": "path", "schema": {"type": "number"}}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/CreateDayNightTariffScheduleRequest"}}}}, "responses": {"201": {"description": ""}}, "tags": ["Schedule"]}}, "/tariffs/{tariffId}/schedules/workweek": {"post": {"operationId": "ScheduleController_createWorkWeekScheduleByGroupIdAndTariffId", "parameters": [{"name": "groupId", "required": true, "in": "query", "schema": {"type": "number"}}, {"name": "tariffId", "required": true, "in": "path", "schema": {"type": "number"}}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/CreateWorkWeekTariffScheduleRequest"}}}}, "responses": {"201": {"description": ""}}, "tags": ["Schedule"]}}, "/tariffs/{tariffId}/schedules/{scheduleId}": {"put": {"operationId": "ScheduleController_updateScheduleByGroupIdAndTariffId", "parameters": [{"name": "groupId", "required": true, "in": "query", "schema": {"type": "number"}}, {"name": "tariffId", "required": true, "in": "path", "schema": {"type": "number"}}, {"name": "scheduleId", "required": true, "in": "path", "schema": {"type": "number"}}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/CreateTariffScheduleRequest"}}}}, "responses": {"204": {"description": ""}}, "tags": ["Schedule"]}}, "/tariffs/{tariffId}/pods": {"put": {"operationId": "TariffPodController_updateByGroupIdAndTariffId", "parameters": [{"name": "groupId", "required": true, "in": "query", "schema": {"type": "number"}}, {"name": "tariffId", "required": true, "in": "path", "schema": {"type": "number"}}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/AssignTariffPodsRequest"}}}}, "responses": {"204": {"description": ""}}, "tags": ["TariffPod"]}}, "/sites/{siteId}/additional-information": {"post": {"operationId": "AdditionalInfoController_upsertByGroupUid", "parameters": [{"name": "groupUid", "required": true, "in": "query", "schema": {"type": "string"}}, {"name": "siteId", "required": true, "in": "path", "schema": {"type": "number"}}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/UpdateAdditionalInfoRequest"}}}}, "responses": {"201": {"description": ""}}, "tags": ["AdditionalInfo"]}}, "/sites/{siteId}/contact-details": {"put": {"operationId": "ContactDetailsController_updateContactDetailsByGroupUidAndSiteId", "parameters": [{"name": "groupUid", "required": true, "in": "query", "schema": {"type": "string"}}, {"name": "siteId", "required": true, "in": "path", "schema": {"type": "number"}}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/UpdateContactDetailsRequest"}}}}, "responses": {"204": {"description": ""}}, "tags": ["ContactDetails"]}}, "/sites/{siteId}/energy-cost": {"put": {"operationId": "EnergyCostController_updateContactDetailsByGroupUidAndSiteId", "parameters": [{"name": "groupUid", "required": true, "in": "query", "schema": {"type": "string"}}, {"name": "siteId", "required": true, "in": "path", "schema": {"type": "number"}}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/UpdateEnergyCostRequest"}}}}, "responses": {"204": {"description": ""}}, "tags": ["EnergyCost"]}}, "/sites/{siteId}/opening-times": {"post": {"operationId": "OpeningTimesController_upsertByGroupUid", "parameters": [{"name": "groupUid", "required": true, "in": "query", "schema": {"type": "string"}}, {"name": "siteId", "required": true, "in": "path", "schema": {"type": "number"}}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/UpsertOpeningTimesRequest"}}}}, "responses": {"201": {"description": ""}}, "tags": ["OpeningTimes"]}}, "/sites": {"get": {"operationId": "SiteController_findByGroupUid", "parameters": [{"name": "groupUid", "required": true, "in": "query", "schema": {"type": "string"}}], "responses": {"200": {"description": "", "content": {"application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/Site"}}}}}}, "tags": ["Site"]}}, "/sites/{siteId}": {"get": {"operationId": "SiteController_findByGroupUidAndSiteId", "parameters": [{"name": "groupUid", "required": true, "in": "query", "schema": {"type": "string"}}, {"name": "siteId", "required": true, "in": "path", "schema": {"type": "number"}}], "responses": {"200": {"description": "", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Site"}}}}}, "tags": ["Site"]}}, "/sites/{siteId}/charges": {"get": {"operationId": "SiteController_generateCsv", "parameters": [{"name": "groupUid", "required": true, "in": "query", "schema": {"type": "string"}}, {"name": "siteId", "required": true, "in": "path", "schema": {"type": "number"}}, {"name": "date", "required": true, "in": "query", "schema": {"type": "string"}}], "responses": {"200": {"description": ""}}, "tags": ["Site"]}}, "/sites/{siteId}/statement": {"get": {"operationId": "StatementController_findByGroupUidAndSiteId", "parameters": [{"name": "groupUid", "required": true, "in": "query", "schema": {"type": "string"}}, {"name": "siteId", "required": true, "in": "path", "schema": {"type": "number"}}, {"name": "period", "required": true, "in": "query", "schema": {"type": "number"}}], "responses": {"200": {"description": "", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/SiteStatement"}}}}}, "tags": ["Statement"]}, "post": {"operationId": "StatementController_createByGroupUidAndSiteId", "parameters": [{"name": "groupUid", "required": true, "in": "query", "schema": {"type": "string"}}, {"name": "siteId", "required": true, "in": "path", "schema": {"type": "number"}}, {"name": "period", "required": true, "in": "query", "schema": {"type": "number"}}, {"name": "dateOverride", "required": true, "in": "query", "schema": {"type": "string"}}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/CreateSiteStatementRequest"}}}}, "responses": {"201": {"description": "", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/SiteStatement"}}}}}, "tags": ["Statement"]}}, "/sites/stats/csv": {"get": {"operationId": "StatsController_generateCsv", "parameters": [{"name": "groupId", "required": true, "in": "query", "schema": {"type": "number"}}, {"name": "date", "required": true, "in": "query", "schema": {"type": "string"}}], "responses": {"200": {"description": ""}}, "tags": ["Stats"]}}, "/rfid/cards": {"get": {"operationId": "RfidCardController_findByGroupId", "parameters": [{"name": "groupUid", "required": true, "in": "query", "schema": {"type": "string"}}], "responses": {"200": {"description": "", "content": {"application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/RfidCard"}}}}}}, "tags": ["RfidCard"]}, "post": {"operationId": "RfidCardController_createByGroupId", "parameters": [{"name": "groupUid", "required": true, "in": "query", "schema": {"type": "string"}}, {"name": "adminUid", "required": true, "in": "query", "schema": {"type": "string"}}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/CreateRfidCardRequest"}}}}, "responses": {"201": {"description": ""}}, "tags": ["RfidCard"]}}, "/rfid/cards/{cardUid}": {"put": {"operationId": "RfidCardController_updateByGroupId", "parameters": [{"name": "groupUid", "required": true, "in": "query", "schema": {"type": "string"}}, {"name": "adminUid", "required": true, "in": "query", "schema": {"type": "string"}}, {"name": "cardUid", "required": true, "in": "path", "schema": {"type": "string"}}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/UpdateRfidCardRequest"}}}}, "responses": {"204": {"description": ""}}, "tags": ["RfidCard"]}}, "/support/emails/bounced": {"get": {"operationId": "BouncedEmailsController_findAll", "parameters": [], "responses": {"200": {"description": "", "content": {"application/json": {"schema": {"type": "array", "items": {"type": "string"}}}}}}, "tags": ["BouncedEmails"]}}, "/support/emails/bounced/{emailAddress}": {"delete": {"operationId": "BouncedEmailsController_removeByEmailAddress", "parameters": [{"name": "emailAddress", "required": true, "in": "path", "schema": {"type": "string"}}], "responses": {"204": {"description": ""}}, "tags": ["BouncedEmails"]}}, "/support/chargers/{identifier}": {"get": {"operationId": "ChargerController_findByIdentifier", "parameters": [{"name": "identifier", "required": true, "in": "path", "schema": {"type": "string"}}], "responses": {"200": {"description": "", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/Charger"}}}}}, "tags": ["Charger"]}}, "/support/chargers/{name}/ppid": {"get": {"operationId": "ChargerController_findPpidByChargerName", "parameters": [{"name": "name", "required": true, "in": "path", "schema": {"type": "string"}}], "responses": {"200": {"description": "", "content": {"application/json": {"schema": {"type": "string"}}}}}, "tags": ["Charger"]}}, "/support/chargers/{ppid}/name": {"get": {"operationId": "ChargerController_findChargerNameByPpid", "parameters": [{"name": "ppid", "required": true, "in": "path", "schema": {"type": "string"}}], "responses": {"200": {"description": "", "content": {"application/json": {"schema": {"type": "string"}}}}}, "tags": ["Charger"]}}, "/user": {"get": {"operationId": "UserController_findByUserId", "parameters": [{"name": "adminUid", "required": true, "in": "query", "schema": {"type": "string"}}], "responses": {"200": {"description": "", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/User"}}}}}, "tags": ["User"]}, "put": {"operationId": "UserController_updateByUserUid", "parameters": [{"name": "adminUid", "required": true, "in": "query", "schema": {"type": "string"}}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/UpdateUserRequest"}}}}, "responses": {"204": {"description": ""}}, "tags": ["User"]}}, "/user/terms-and-conditions/acceptance": {"put": {"operationId": "UserController_acceptTermsAndConditions", "parameters": [{"name": "adminUid", "required": true, "in": "query", "schema": {"type": "string"}}], "responses": {"204": {"description": ""}}, "tags": ["User"]}}, "/version": {"get": {"operationId": "VersionController_getVersion", "parameters": [], "responses": {"200": {"description": "application version"}}, "summary": "get application version", "tags": ["Version"]}}}, "info": {"title": "", "description": "", "version": "1.0.0", "contact": {}}, "tags": [], "servers": [], "components": {"schemas": {"Admin": {"type": "object", "properties": {"activatedOn": {"format": "date-time", "type": "string"}, "authId": {"type": "string"}, "email": {"type": "string"}, "emailBounced": {"type": "boolean"}, "firstName": {"type": "string"}, "groupId": {"type": "number"}, "groupName": {"type": "string"}, "groupUid": {"type": "string"}, "id": {"type": "number"}, "lastLogin": {"format": "date-time", "type": "string"}, "lastName": {"type": "string"}, "status": {"type": "string", "enum": ["Registered", "Pending", "Deactivated"]}}, "required": ["authId", "email", "emailBounced", "id", "status"]}, "CreateAdminRequest": {"type": "object", "properties": {"email": {"type": "string", "maxLength": 255, "format": "email"}}, "required": ["email"]}, "SendEmailVerificationRequest": {"type": "object", "properties": {"email": {"type": "string", "maxLength": 255, "format": "email"}}, "required": ["email"]}, "GroupChargeSummary": {"type": "object", "properties": {"chargingDuration": {"type": "number"}, "co2Savings": {"type": "number"}, "energyCost": {"type": "number"}, "energyDelivered": {"type": "number"}, "numberOfChargers": {"type": "number"}, "numberOfCharges": {"type": "number"}, "numberOfDrivers": {"type": "number"}, "numberOfSites": {"type": "number"}, "revenueGenerated": {"type": "number"}}, "required": ["chargingDuration", "co2Savings", "energyCost", "energyDelivered", "numberOfChargers", "numberOfCharges", "numberOfDrivers", "numberOfSites", "revenueGenerated"]}, "Group": {"type": "object", "properties": {"activatedOn": {"format": "date-time", "type": "string"}, "contactName": {"type": "string"}, "contactNumber": {"type": "string"}, "id": {"type": "number"}, "name": {"type": "string"}, "readOnly": {"type": "boolean"}, "stats": {"nullable": true, "allOf": [{"$ref": "#/components/schemas/GroupChargeSummary"}]}, "type": {"type": "string"}, "uid": {"type": "string"}}, "required": ["id", "name", "uid"]}, "SendPasswordResetRequest": {"type": "object", "properties": {"email": {"type": "string", "maxLength": 255, "format": "email"}}, "required": ["email"]}, "SendSignInWithEmailRequest": {"type": "object", "properties": {"email": {"type": "string", "maxLength": 255, "format": "email"}}, "required": ["email"]}, "Charge": {"type": "object", "properties": {"chargingDuration": {"type": "string"}, "confirmed": {"type": "boolean"}, "confirmedBy": {"type": "string", "enum": ["driver", "other"]}, "co2Savings": {"type": "string"}, "door": {"type": "string"}, "endedAt": {"type": "string"}, "energyCost": {"type": "string"}, "energyUsage": {"type": "string"}, "locationId": {"type": "number"}, "pluggedIn": {"type": "string"}, "podName": {"type": "string"}, "ppid": {"type": "string"}, "revenueGenerated": {"type": "string"}, "startedAt": {"type": "string"}, "siteName": {"type": "string"}, "totalDuration": {"type": "string"}, "userEmail": {"type": "string"}, "userName": {"type": "string"}, "vehicle": {"type": "string"}}, "required": ["chargingDuration", "confirmed", "co2Savings", "energyCost", "energyUsage", "pluggedIn", "revenueGenerated", "startedAt"]}, "DriverCharges": {"type": "object", "properties": {"charges": {"type": "array", "items": {"$ref": "#/components/schemas/Charge"}}, "co2Avoided": {"type": "string"}, "energyCost": {"type": "string"}, "energyDelivered": {"type": "string"}, "revenue": {"type": "string"}}, "required": ["charges", "co2Avoided", "energyCost", "energyDelivered", "revenue"]}, "Driver": {"type": "object", "properties": {"canExpense": {"type": "boolean"}, "chargingStats": {"$ref": "#/components/schemas/DriverCharges"}, "email": {"type": "string"}, "emailBounced": {"type": "boolean"}, "firstName": {"type": "string"}, "fullName": {"type": "string"}, "group": {"$ref": "#/components/schemas/Group"}, "id": {"type": "number"}, "lastName": {"type": "string"}, "registeredDate": {"type": "string"}, "status": {"enum": ["Deleted", "Deactivated", "Pending", "Registered"], "type": "string"}, "tariffTier": {"enum": ["Driver", "Member"], "type": "string"}}, "required": ["canExpense", "email", "emailBounced", "firstName", "fullName", "id", "lastName", "registeredDate", "status", "tariffTier"]}, "CreateDriverRequest": {"type": "object", "properties": {"canExpense": {"type": "string", "enum": [true, false, "Yes", "No"]}, "email": {"type": "string", "maxLength": 255, "format": "email"}, "firstName": {"type": "string", "maxLength": 255}, "lastName": {"type": "string", "maxLength": 255}, "tariffTier": {"type": "string", "enum": ["Driver", "Member"]}}, "required": ["canExpense", "email", "firstName", "lastName", "tariffTier"]}, "UpdateDriverRequest": {"type": "object", "properties": {"canExpense": {"type": "string", "enum": [true, false, "Yes", "No"]}, "email": {"type": "string", "maxLength": 255, "format": "email"}, "firstName": {"type": "string", "maxLength": 255}, "lastName": {"type": "string", "maxLength": 255}, "tariffTier": {"enum": ["Driver", "Member"], "type": "string"}, "originalTariffTier": {"type": "string", "enum": ["Driver", "Member"]}}, "required": ["canExpense", "email", "firstName", "lastName", "tariffTier"]}, "DeleteDriverRequest": {"type": "object", "properties": {"tariffTier": {"type": "string", "enum": ["Driver", "Member"]}}, "required": ["tariffTier"]}, "InviteDriverRequest": {"type": "object", "properties": {"tariffTier": {"type": "string", "enum": ["Driver", "Member"]}}, "required": ["tariffTier"]}, "ChargeSummary": {"type": "object", "properties": {"chargingDuration": {"type": "number"}, "claimedEnergyUsage": {"type": "number"}, "co2Savings": {"type": "number"}, "energyCost": {"type": "number"}, "energyDelivered": {"type": "number"}, "energyUsage": {"type": "number"}, "numberOfCharges": {"type": "number"}, "revenueGenerated": {"type": "number"}, "revenueGeneratingClaimedUsage": {"type": "number"}}, "required": ["chargingDuration", "claimedEnergyUsage", "co2Savings", "energyCost", "energyDelivered", "energyUsage", "numberOfCharges", "revenueGenerated", "revenueGeneratingClaimedUsage"]}, "Coordinates": {"type": "object", "properties": {"latitude": {"type": "number"}, "longitude": {"type": "number"}}}, "Schedule": {"type": "object", "properties": {"endDay": {"type": "number"}, "endTime": {"type": "string"}, "isActive": {"type": "boolean"}, "startDay": {"type": "number"}, "startTime": {"type": "string"}}, "required": ["endDay", "endTime", "isActive", "startDay", "startTime"]}, "Address": {"type": "object", "properties": {"country": {"type": "string"}, "line1": {"type": "string"}, "line2": {"type": "string"}, "name": {"type": "string"}, "postcode": {"type": "string"}, "prettyPrint": {"type": "string"}, "town": {"type": "string"}}, "required": ["country", "line1", "line2", "name", "postcode", "<PERSON><PERSON><PERSON><PERSON>", "town"]}, "ContactDetailsInterface": {"type": "object", "properties": {"email": {"type": "string"}, "name": {"type": "string"}, "telephone": {"type": "string"}}, "required": ["email", "name", "telephone"]}, "OpeningTimes": {"type": "object", "properties": {"notes": {"type": "array", "items": {"type": "string"}}}, "required": ["notes"]}, "Parking": {"type": "object", "properties": {"openingTimes": {"$ref": "#/components/schemas/OpeningTimes"}}, "required": ["openingTimes"]}, "Site": {"type": "object", "properties": {"address": {"$ref": "#/components/schemas/Address"}, "chargeSummary": {"$ref": "#/components/schemas/ChargeSummary"}, "contactDetails": {"$ref": "#/components/schemas/ContactDetailsInterface"}, "description": {"type": "string"}, "energyCost": {"type": "number"}, "group": {"$ref": "#/components/schemas/Group"}, "id": {"type": "number"}, "parking": {"$ref": "#/components/schemas/Parking"}, "pods": {"type": "array", "items": {"$ref": "#/components/schemas/Pod"}}}, "required": ["address", "contactDetails", "description", "group", "id", "parking"]}, "Socket": {"type": "object", "properties": {"door": {"type": "string"}, "firmwareVersion": {"type": "string"}, "isUpdateAvailable": {"type": "boolean"}, "lastContact": {"type": "string"}, "serialNumber": {"type": "string"}, "status": {"type": "string", "enum": ["Available", "Charging", "Offline", "Unavailable"]}}, "required": ["door", "firmwareVersion", "isUpdateAvailable", "serialNumber", "status"]}, "TariffSummary": {"type": "object", "properties": {"id": {"type": "number"}, "name": {"type": "string"}}, "required": ["id", "name"]}, "Pod": {"type": "object", "properties": {"ageYears": {"type": "number"}, "confirmChargeEnabled": {"type": "boolean"}, "chargeSummary": {"$ref": "#/components/schemas/ChargeSummary"}, "coordinates": {"$ref": "#/components/schemas/Coordinates"}, "description": {"type": "string"}, "id": {"type": "number"}, "installDate": {"type": "string"}, "isEvZone": {"type": "boolean"}, "isPublic": {"type": "boolean"}, "lastContact": {"type": "string"}, "model": {"type": "string"}, "mostRecentCharge": {"$ref": "#/components/schemas/Charge"}, "name": {"type": "string"}, "ppid": {"type": "string"}, "recentCharges": {"type": "array", "items": {"$ref": "#/components/schemas/Charge"}}, "schedules": {"type": "array", "items": {"$ref": "#/components/schemas/Schedule"}}, "schemes": {"type": "array", "items": {"$ref": "#/components/schemas/Group"}}, "site": {"$ref": "#/components/schemas/Site"}, "sockets": {"type": "array", "items": {"$ref": "#/components/schemas/Socket"}}, "status": {"type": "string"}, "supportsConfirmCharge": {"type": "boolean"}, "supportsContactless": {"type": "boolean"}, "supportsEnergyTariff": {"type": "boolean"}, "supportsOcpp": {"type": "boolean"}, "supportsPerKwh": {"type": "boolean"}, "supportsRfid": {"type": "boolean"}, "supportsTariffs": {"type": "boolean"}, "tariff": {"$ref": "#/components/schemas/TariffSummary"}}, "required": ["confirmChargeEnabled", "coordinates", "description", "id", "isEvZone", "isPublic", "model", "ppid", "schemes", "sockets", "status", "supportsContactless", "supportsEnergyTariff", "supportsPerKwh", "supportsTariffs"]}, "Advert": {"type": "object", "properties": {"id": {"type": "number"}, "image": {"type": "string"}, "pods": {"type": "array", "items": {"$ref": "#/components/schemas/Pod"}}, "url": {"type": "string"}}, "required": ["id", "image", "pods", "url"]}, "AssignPodToAdvertRequest": {"type": "object", "properties": {"name": {"type": "string"}}, "required": ["name"]}, "CustomerDetails": {"type": "object", "properties": {"address": {"type": "object", "properties": {"line1": {"type": "string"}, "line2": {"type": "string"}, "town": {"type": "string"}, "county": {"type": "string"}, "postcode": {"type": "string"}}, "required": []}, "email": {"type": "string"}, "name": {"type": "string"}, "accountReference": {"type": "string"}, "poNumber": {"type": "string"}}, "required": ["email", "name"]}, "BillingStatement": {"type": "object", "properties": {"siteName": {"type": "string"}, "month": {"type": "string"}, "feeInvoiceNumber": {"type": "string"}, "feeInvoiceStatus": {"type": "string"}, "hostedInvoiceUrl": {"type": "string"}, "invoiceId": {"type": "string"}, "revenuePayoutStatus": {"type": "string"}, "statementId": {"type": "string"}}, "required": ["siteName", "month", "feeInvoiceNumber", "feeInvoiceStatus", "invoiceId", "revenuePayoutStatus", "statementId"]}, "SubscriptionCharger": {"type": "object", "properties": {"ppid": {"type": "string"}, "socket": {"type": "object"}}, "required": ["ppid", "socket"]}, "SubscriptionInvoice": {"type": "object", "properties": {"status": {"type": "string"}, "created": {"type": "string"}, "amount": {"type": "number"}, "due": {"type": "string"}, "hostedInvoiceUrl": {"type": "string"}, "invoiceNumber": {"type": "string"}, "invoicePdfUrl": {"type": "string"}, "customerEmail": {"type": "string"}}, "required": ["status", "created", "amount", "due", "hostedInvoiceUrl", "invoiceNumber", "invoicePdfUrl", "customerEmail"]}, "Subscription": {"type": "object", "properties": {"chargers": {"type": "array", "items": {"$ref": "#/components/schemas/SubscriptionCharger"}}, "status": {"type": "string"}, "invoices": {"type": "array", "items": {"$ref": "#/components/schemas/SubscriptionInvoice"}}}, "required": ["chargers", "status", "invoices"]}, "BillingInformation": {"type": "object", "properties": {"customerDetails": {"$ref": "#/components/schemas/CustomerDetails"}, "onboardingLink": {"type": "string"}, "statements": {"type": "array", "items": {"$ref": "#/components/schemas/BillingStatement"}}, "subscription": {"$ref": "#/components/schemas/Subscription"}}, "required": ["customerDetails", "onboardingLink", "statements", "subscription"]}, "Domain": {"type": "object", "properties": {"activatedOn": {"format": "date-time", "type": "string"}, "domainName": {"type": "string"}, "id": {"type": "number"}, "groupId": {"type": "number"}}, "required": ["activatedOn", "domainName", "id", "groupId"]}, "CreateDomainRequest": {"type": "object", "properties": {"domainName": {"type": "string", "maxLength": 63}}, "required": ["domainName"]}, "ProcessExpensesRequest": {"type": "object", "properties": {}}, "Insight": {"type": "object", "properties": {"co2Savings": {"type": "number"}, "cost": {"type": "number"}, "intervalStartDate": {"type": "string"}, "revenueGenerated": {"type": "number"}, "totalUsage": {"type": "number"}}, "required": ["co2Savings", "cost", "intervalStartDate", "revenueGenerated", "totalUsage"]}, "UpdateTariffRequest": {"type": "object", "properties": {"id": {"type": "number"}}, "required": ["id"]}, "ConfirmChargeRequest": {"type": "object", "properties": {"door": {"type": "string"}, "driverEmail": {"type": "string"}}, "required": ["door", "driverEmail"]}, "TariffScheduleBand": {"type": "object", "properties": {"id": {"type": "number"}, "after": {"type": "number"}, "cost": {"type": "number"}, "prettyPrint": {"type": "string"}}, "required": ["id", "after", "cost", "<PERSON><PERSON><PERSON><PERSON>"]}, "TariffSchedule": {"type": "object", "properties": {"id": {"type": "number"}, "bands": {"type": "array", "items": {"$ref": "#/components/schemas/TariffScheduleBand"}}, "endDay": {"type": "number"}, "endTime": {"type": "string"}, "pricingModel": {"type": "string", "enum": ["duration", "energy", "fixed"]}, "startDay": {"type": "number"}, "startTime": {"type": "string"}, "tariffTier": {"type": "string", "enum": ["public", "members", "drivers"]}}, "required": ["id", "bands", "endDay", "endTime", "pricingModel", "startDay", "startTime", "tariffTier"]}, "TariffScheduleTiers": {"type": "object", "properties": {"drivers": {"type": "array", "items": {"$ref": "#/components/schemas/TariffSchedule"}}, "members": {"type": "array", "items": {"$ref": "#/components/schemas/TariffSchedule"}}, "public": {"type": "array", "items": {"$ref": "#/components/schemas/TariffSchedule"}}}}, "Tariff": {"type": "object", "properties": {"id": {"type": "number"}, "name": {"type": "string"}, "currency": {"type": "string"}, "pods": {"type": "array", "items": {"$ref": "#/components/schemas/Pod"}}, "schedule": {"$ref": "#/components/schemas/TariffScheduleTiers"}}, "required": ["id", "name", "currency", "pods"]}, "CreateTariffRequest": {"type": "object", "properties": {"tariffName": {"type": "string"}}, "required": ["tariffName"]}, "CreateTariffScheduleRequest": {"type": "object", "properties": {"pricingModel": {"enum": ["duration", "energy", "fixed"], "type": "string"}, "tariffTier": {"enum": ["public", "members", "drivers"], "type": "string"}, "endDay": {"type": "number"}, "endTime": {"type": "string"}, "price": {"type": "string"}, "startDay": {"type": "number"}, "startSecond": {"type": "number", "minimum": 0}, "startTime": {"type": "string"}}, "required": ["pricingModel", "tariffTier", "endDay", "endTime", "price", "startDay", "startTime"]}, "CreateDayNightTariffScheduleRequest": {"type": "object", "properties": {"dayPrice": {"type": "string"}, "dayStartTime": {"type": "string"}, "nightPrice": {"type": "string"}, "nightStartTime": {"type": "string"}, "tariffTier": {"type": "string", "enum": ["public", "members", "drivers"]}}, "required": ["dayPrice", "dayStartTime", "nightPrice", "nightStartTime", "tariffTier"]}, "CreateWorkWeekTariffScheduleRequest": {"type": "object", "properties": {"weekdayPrice": {"type": "string"}, "weekendPrice": {"type": "string"}, "tariffTier": {"type": "string", "enum": ["public", "members", "drivers"]}}, "required": ["weekdayPrice", "weekendPrice", "tariffTier"]}, "DeleteTariffScheduleRequest": {"type": "object", "properties": {"scheduleIds": {"minItems": 1, "type": "array", "items": {"type": "number"}}}, "required": ["scheduleIds"]}, "AssignTariffPodsRequest": {"type": "object", "properties": {"podIds": {"type": "array", "items": {"type": "object"}}}, "required": ["podIds"]}, "UpdateAdditionalInfoRequest": {"type": "object", "properties": {"additionalInfo": {"type": "string", "maxLength": 1000}}}, "UpdateContactDetailsRequest": {"type": "object", "properties": {"email": {"type": "string", "maxLength": 255, "format": "email"}, "name": {"type": "string"}, "telephone": {"type": "string"}}, "required": ["email", "name", "telephone"]}, "UpdateEnergyCostRequest": {"type": "object", "properties": {"energyCost": {"type": "string"}}, "required": ["energyCost"]}, "OpeningTimeWithDay": {"type": "object", "properties": {"allDay": {"type": "boolean"}, "day": {"type": "string", "enum": ["mon", "tue", "wed", "thu", "fri", "sat", "sun"]}, "from": {"type": "string"}, "to": {"type": "string"}}, "required": ["allDay", "day", "from", "to"]}, "UpsertOpeningTimesRequest": {"type": "object", "properties": {"openingTimes": {"type": "array", "items": {"$ref": "#/components/schemas/OpeningTimeWithDay"}}}, "required": ["openingTimes"]}, "AccountingTotals": {"type": "object", "properties": {"gross": {"type": "number"}, "net": {"type": "number"}, "vat": {"type": "number"}}, "required": ["gross", "net", "vat"]}, "SiteSubStatement": {"type": "object", "properties": {"date": {"type": "string"}, "groupName": {"type": "string"}, "reference": {"type": "string"}, "siteAddress": {"type": "string"}, "siteName": {"type": "string"}, "claimedEnergyDelivered": {"type": "number"}, "energyDelivered": {"type": "number"}, "fees": {"$ref": "#/components/schemas/AccountingTotals"}, "numberOfCharges": {"type": "number"}, "paidEnergyDelivered": {"type": "number"}, "revenue": {"$ref": "#/components/schemas/AccountingTotals"}}, "required": ["date", "groupName", "reference", "siteAddress", "siteName", "claimedEnergyDelivered", "energyDelivered", "fees", "numberOfCharges", "paidEnergyDelivered", "revenue"]}, "SiteStatement": {"type": "object", "properties": {"breakdown": {"type": "array", "items": {"$ref": "#/components/schemas/SiteSubStatement"}}, "date": {"type": "string"}, "groupName": {"type": "string"}, "reference": {"type": "string"}, "siteAddress": {"type": "string"}, "siteName": {"type": "string"}, "claimedEnergyDelivered": {"type": "number"}, "energyDelivered": {"type": "number"}, "fees": {"$ref": "#/components/schemas/AccountingTotals"}, "numberOfCharges": {"type": "number"}, "paidEnergyDelivered": {"type": "number"}, "revenue": {"$ref": "#/components/schemas/AccountingTotals"}}, "required": ["date", "groupName", "reference", "siteAddress", "siteName", "claimedEnergyDelivered", "energyDelivered", "fees", "numberOfCharges", "paidEnergyDelivered", "revenue"]}, "AdjustedFee": {"type": "object", "properties": {"ppid": {"type": "string"}, "fee": {"type": "number", "minimum": 0, "maximum": 0.99}}, "required": ["ppid"]}, "CreateSiteStatementRequest": {"type": "object", "properties": {"adjustedFees": {"type": "array", "items": {"$ref": "#/components/schemas/AdjustedFee"}}}, "required": ["adjustedFees"]}, "RfidCard": {"type": "object", "properties": {"active": {"type": "boolean"}, "reference": {"type": "string"}, "tags": {"type": "array", "items": {"type": "string"}}, "uid": {"type": "string"}}, "required": ["active", "reference", "tags", "uid"]}, "CreateRfidCardRequest": {"type": "object", "properties": {"reference": {"type": "string"}, "tag": {"type": "string"}, "uid": {"type": "string"}}, "required": ["reference", "tag", "uid"]}, "UpdateRfidCardRequest": {"type": "object", "properties": {"tag": {"type": "string"}}, "required": ["tag"]}, "ChargerSettings": {"type": "object", "properties": {"confirmCharge": {"type": "boolean"}}, "required": ["confirmCharge"]}, "Charger": {"type": "object", "properties": {"admins": {"type": "array", "items": {"$ref": "#/components/schemas/Admin"}}, "billing": {"$ref": "#/components/schemas/BillingInformation"}, "domains": {"type": "array", "items": {"$ref": "#/components/schemas/Domain"}}, "drivers": {"type": "array", "items": {"$ref": "#/components/schemas/Driver"}}, "group": {"$ref": "#/components/schemas/Group"}, "name": {"type": "string"}, "ppid": {"type": "string"}, "rfidCards": {"type": "array", "items": {"$ref": "#/components/schemas/RfidCard"}}, "settings": {"$ref": "#/components/schemas/ChargerSettings"}, "site": {"$ref": "#/components/schemas/Site"}, "tariff": {"$ref": "#/components/schemas/Tariff"}}, "required": ["admins", "billing", "domains", "drivers", "group", "ppid", "rfidCards", "settings", "site", "tariff"]}, "BillingAccount": {"type": "object", "properties": {"balance": {"type": "number"}, "id": {"type": "number"}, "uid": {"type": "string"}}, "required": ["balance", "id", "uid"]}, "User": {"type": "object", "properties": {"activatedOn": {"format": "date-time", "type": "string"}, "authId": {"type": "string"}, "billingAccount": {"$ref": "#/components/schemas/BillingAccount"}, "email": {"type": "string"}, "emailVerified": {"type": "boolean"}, "firstName": {"type": "string"}, "groupId": {"type": "number"}, "groupName": {"type": "string"}, "groupUid": {"type": "string"}, "groupType": {"type": "string", "enum": ["podPoint", "host", "scheme", "manufacturer"]}, "id": {"type": "number"}, "lastName": {"type": "string"}, "status": {"type": "string", "enum": ["Activated", "Deactivated"]}, "termsAndConditions": {"type": "object"}}, "required": ["authId", "email", "emailVerified", "id", "status", "termsAndConditions"]}, "UpdateUserRequest": {"type": "object", "properties": {"firstName": {"type": "string", "maxLength": 255}, "lastName": {"type": "string", "maxLength": 255}}, "required": ["firstName", "lastName"]}}}}