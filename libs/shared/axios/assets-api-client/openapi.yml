openapi: 3.0.2
info:
  title: asset-service-api
  version: '1.0'
  description: |
    An API for managing assets - Charging stations & Sub-assemblies (EVSEs/PCBs)
    ### NB: All date/time inputs are to be provided in ISO format and timezone UTC e.g. `2023-01-03T16:09:59.000Z`.
  contact:
    name: Software Team
    email: <EMAIL>
    url: 'https://pod-point.com'
servers:
  - description: local
    url: 'http://localhost:4000'
  - description: dev
    url: 'http://asset-service-api-dev.pod-point.com'
  - description: staging
    url: 'http://asset-service-api-staging.pod-point.com'
  - description: prod
    url: 'http://asset-service-api.pod-point.com'
tags:
  - name: Asset Summary
  - name: Decommission
  - name: Pcb
  - name: Charging Station
  - name: Location
  - name: Address
  - name: Tag
paths:
  /charging-stations:
    get:
      summary: Search for the charging station
      description: Search for a charging station by EVSE mac address or serial number, results are keyed by the values of query parameter
      operationId: search-charging-stations
      parameters:
        - $ref: '#/components/parameters/q_macAddress'
        - $ref: '#/components/parameters/q_serialNumber'
      tags:
        - Asset Summary
        - Charging Station
        - Tag
      responses:
        '200':
          $ref: '#/components/responses/ChargingStationsSummaryResponse'
        '400':
          description: Will throw an error when both the macAddress and serialNumber are provided or when no macAddress or serialNumber are provided
    post:
      summary: Create a new charging station (only ownership information for now)
      description: Create a new charging station in Asset Service DB (associate existing device with ownership information)
      operationId: create-charging-station
      requestBody:
        $ref: '#/components/requestBodies/CreateChargingStationRequest'
      tags:
        - Charging Station
        - Ownership
      responses:
        '201':
          description: CREATED
        '400':
          description: Will throw an error if the request body is invalid
        '409':
          description: Will throw an error if the charging station already exists'
        '500':
          description: Will throw an error if charging station creation fails
  /charging-stations/ownership:
    post:
      summary: Update a charging stations ownership in Asset Service DB
      description: Update a charging stations ownership in Asset Service DB
      operationId: update-charging-station-ownership
      requestBody:
        $ref: '#/components/requestBodies/ChangeOwnershipRequest'
      tags:
        - Charging Station
        - Ownership
      responses:
        '203':
          description: Updated
        '400':
          description: Will throw an error if the request body is invalid
        '404':
          description: Charging station doesn't exist in pod admin and/or assets DB
        '500':
          description: Will throw an error if charging station creation fails
  /charging-stations/{ppid}:
    get:
      summary: Return asset details for a given charging station
      description: Lookup an asset summary based on the ppid (serial number) of the charging station
      operationId: get-charging-station
      parameters:
        - $ref: '#/components/parameters/p_ppid'
      tags:
        - Asset Summary
        - Charging Station
        - Tag
      responses:
        '200':
          $ref: '#/components/responses/ChargingStationSummaryResponse'
        '404':
          description: Charging station doesn't exist
  /charging-stations/{ppid}/decommission:
    post:
      summary: Decommission a charging station
      description: Decommissions a charging station for a given ppid
      operationId: decommission-a-charging-station
      parameters:
        - $ref: '#/components/parameters/p_ppid'
      requestBody:
        $ref: '#/components/requestBodies/DecommissionRequest'
      tags:
        - Charging Station
        - Decommission
      responses:
        '204':
          description: OK
        '400':
          description: Will throw an error when the charging station has a location or a PCB attached
        '404':
          description: Will throw an error when the charging station doesn't exist or when it has already been decommissioned
  /pcbs/decommission:
    post:
      summary: Batch Decommission multiple PCBs
      description: Batch Decommission multiple PCBs given by serial numbers
      operationId: batch-decommission-pcbs
      requestBody:
        $ref: '#/components/requestBodies/BatchDecommissionRequest'
      tags:
        - Pcb
        - Decommission
      responses:
        '200':
          $ref: '#/components/responses/BatchDecommissionPcbsResponse'
  /pcbs/{serialNumber}:
    get:
      summary: Get PCB information
      description: Get information about a pcb
      operationId: get-pcb
      parameters:
        - $ref: '#/components/parameters/p_serialNumber'
      tags:
        - Pcb
      responses:
        '200':
          $ref: '#/components/responses/GetPcbResponse'
  /pcbs/{serialNumber}/decommission:
    post:
      summary: Decommission a PCB
      description: Decommissions a PCB for a given serial number
      operationId: decommission-a-pcb
      parameters:
        - $ref: '#/components/parameters/p_serialNumber'
      requestBody:
        $ref: '#/components/requestBodies/DecommissionRequest'
      tags:
        - Pcb
        - Decommission
      responses:
        '204':
          description: OK
        '400':
          description: Will throw an error when the PCB is linked to a charging station
        '404':
          description: Will throw an error when the PCB doesn't exist or when it has already been decommissioned
  /locations/{locationId}:
    get:
      summary: Return all information about a specified location
      description: Return all information about a specified location
      operationId: get-location
      parameters:
        - $ref: '#/components/parameters/p_locationId'
      tags:
        - Location
      responses:
        '200':
          $ref: '#/components/responses/LocationResponse'
        '404':
          description: Location not found
  /locations:
    post:
      summary: Create a new location
      description: Create a new location
      operationId: create-location
      requestBody:
        $ref: '#/components/requestBodies/LocationRequest'
      tags:
        - Location
      responses:
        '201':
          description: CREATED
        '404':
          description: Will throw an error if address ID is not found or does not exist
        '500':
          description: Will throw an error if location creation fails
  /addresses:
    post:
      summary: Create a new address
      description: Create a new address
      operationId: create-address
      requestBody:
        $ref: '#/components/requestBodies/AddressRequest'
      tags:
        - Address
      responses:
        '201':
          $ref: '#/components/responses/CreateAddressResponse'
        '400':
          description: Bad Request
        '500':
          description: Will throw an error if address creation fails
  /tags/charging-stations:
    get:
      summary: Return information about charging stations with a tag
      description: Return all charging stations that have a tag
      operationId: get-charging-stations-for-tags
      parameters:
        - $ref: '#/components/parameters/q_key'
        - $ref: '#/components/parameters/q_value'
      tags:
        - Tag
      responses:
        '200':
          $ref: '#/components/responses/TaggedChargingStationResponse'
        '404':
          description: Tag not found
  /tags/charging-stations/{ppid}:
    post:
      summary: Add tags to charging station
      description: Add tags to a specified charging station
      operationId: add-tags
      parameters:
        - $ref: '#/components/parameters/p_ppid'
      requestBody:
        $ref: '#/components/requestBodies/TagsRequest'
      tags:
        - Tag
      responses:
        '200':
          description: OK
        '400':
          description: Charging station with ppid already has tag
    delete:
      summary: Remove tags from charging station
      description: Remove tags from a specified charging station
      operationId: remove-tags
      parameters:
        - $ref: '#/components/parameters/p_ppid'
      requestBody:
        $ref: '#/components/requestBodies/TagsRequest'
      tags:
        - Tag
      responses:
        '202':
          description: Accepted
components:
  schemas:
    BatchDecommissionPcbsResponse:
      type: object
      properties:
        decommissioned:
          type: array
          items:
            $ref: '#/components/schemas/SerialNumber'
        failed:
          type: array
          items:
            $ref: '#/components/schemas/SerialNumberWithError'
    SerialNumberWithError:
      type: object
      properties:
        serialNumber:
          $ref: '#/components/schemas/SerialNumber'
        reason:
          type: string
          example: 'Pcb can not be decommissioned'
    ChargingStationsSummary:
      type: object
      properties:
        chargingStations:
          type: object
          additionalProperties:
            $ref: '#/components/schemas/ChargingStationSummary'
    ChargingStationSummary:
      type: object
      properties:
        ppid:
          type: string
          example: PSL-12345
        rfid:
          $ref: '#/components/schemas/RFID'
        evses:
          type: array
          items:
            $ref: '#/components/schemas/EvseSummary'
        ownership:
          $ref: '#/components/schemas/Ownership'
        model:
          $ref: '#/components/schemas/ModelSummary'
        router:
          $ref: '#/components/schemas/RouterSummary'
        location:
          $ref: '#/components/schemas/LocationSummary'
        tags:
          type: array
          items:
            $ref: '#/components/schemas/TagWithId'
        provisioningInfo:
          $ref: '#/components/schemas/ProvisioningInfo'
      required:
        - ppid
        - evses
        - ownership
        - model
        - location
        - tags
    EvseSummary:
      type: object
      properties:
        evseId:
          $ref: '#/components/schemas/EvseId'
        ocpiEvseId:
          $ref: '#/components/schemas/OcpiEvseId'
        connectors:
          type: array
          items:
            $ref: '#/components/schemas/Connector'
        serialNumber:
          allOf:
            - $ref: '#/components/schemas/SerialNumber'
            - deprecated: true
        macAddress:
          allOf:
            - $ref: '#/components/schemas/MacAddress'
            - deprecated: true
        pcb:
          $ref: '#/components/schemas/PcbSummary'
      required:
        - evseId
        - connectors
    EvseId:
      description: 'Each EVSE within a Charging Station should have a unique number starting from 1'
      type: number
      minimum: 1
      maximum: 32
      example: 1
    OcpiEvseId:
      description: 'The OCPI EVSE ID is a unique identifier for an EVSE within the OCPI network'
      type: string
      example: 'GB*POD*E*PG12345E1'
    MacAddress:
      description: 'A MAC (Media Access Control) address, sometimes referred to as a hardware or physical address, is a unique, 12-character alphanumeric attribute that is used to identify individual electronic devices on a network. Each PodPoint EVSE (or PCB) has a unique MAC address'
      type: string
      example: 0cb2b703c778
    PPID:
      description: The Pod Point ID is a formatted identifier for a charging station (aka unit). A unit will contain one or more EVSEs (or PCBs)
      type: string
      example: PSL-12345
    RFID:
      description: Details of the RFID controller of a charging station
      type: object
      properties:
        serialNumber:
          $ref: '#/components/schemas/SerialNumber'
        macAddress:
          $ref: '#/components/schemas/MacAddress'
    SerialNumber:
      description: The serial number of an EVSE (aka PCB)
      type: string
      example: '30303030'
    Tag:
      type: object
      properties:
        key:
          type: string
        value:
          type: string
    TagWithId:
      type: object
      properties:
        id:
          type: string
          format: uuid
        key:
          type: string
        value:
          type: string
    TaggedChargingStations:
      type: array
      items:
        type: object
        properties:
          chargingStations:
            type: array
            items:
              type: object
              properties:
                ppid:
                  $ref: '#/components/schemas/PPID'
    Door:
      description: The door ID/Name of a Connector
      oneOf:
        - type: string
          enum: [A, B, C]
        - type: integer
    Connector:
      title: Connector
      x-stoplight:
        id: oufz6cnlf8gta
      type: object
      properties:
        id:
          type: number
          minimum: 1
        door:
          $ref: '#/components/schemas/Door'
        modelPowerRating:
          type: number
          example: 3.6
          nullable: true
    CreateChargingStationRequestType:
      type: object
      properties:
        clientRef:
          type: string
          example: '<EMAIL>'
        ppid:
          $ref: '#/components/schemas/PPID'
        ownership:
          $ref: '#/components/schemas/RequestOwnership'
      required:
        - ppid
        - ownership
    ChangeOwnershipRequestType:
      type: object
      properties:
        clientRef:
          type: string
          example: '<EMAIL>'
        ppid:
          $ref: '#/components/schemas/PPID'
        ownership:
          $ref: '#/components/schemas/RequestOwnership'
      required:
        - ppid
        - ownership
    DecommissionRequestType:
      type: object
      properties:
        user:
          type: string
          example: 'a9307fa9-e8ad-4496-97d6-eb411342ff62'
        reason:
          type: string
          example: 'Decommissioned by MIS'
      required:
        - user
    BatchDecommissionRequestType:
      type: object
      properties:
        user:
          type: string
          example: 'a9307fa9-e8ad-4496-97d6-eb411342ff62'
        reason:
          type: string
          example: 'Decommissioned by MIS'
        serialNumbers:
          type: array
          items:
            $ref: '#/components/schemas/SerialNumber'
      required:
        - user
        - serialNumbers
    TagsRequestType:
      type: array
      items:
        $ref: '#/components/schemas/Tag'
    Location:
      type: object
      properties:
        id:
          $ref: '#/components/schemas/LocationId'
        isPublic:
          type: boolean
        type:
          $ref: '#/components/schemas/LocationType'
        confirmRequired:
          type: boolean
        description:
          type: string
        contactlessEnabled:
          type: boolean
        midMeterEnabled:
          type: boolean
        isEvZone:
          type: boolean
        mpan:
          type: string
        localTimezone:
          type: string
          description: (FUTURE) The timezone (abbreviation) that the charger is operating in
          example: Europe/London
        latitude:
          type: number
          example: 1
        longitude:
          type: number
          example: 1
        chargingStation:
          $ref: '#/components/schemas/PPID'
        createdAt:
          type: string
          description: The date and time the location was created at
          format: date-time
          example: '2023-06-01T00:00:00.000Z'
        updatedAt:
          type: string
          description: The date and time the location was created at
          format: date-time
          example: '2023-08-01T00:00:00.000Z'
        unitLinkedAt:
          type: string
          description: The date and time the charging station was linked to the location
          format: date-time
          example: '2023-07-01T00:00:00.000Z'
    LocationRequestType:
      type: object
      properties:
        addressId:
          type: number
          example: 1
        latitude:
          type: number
          example: 2
        longitude:
          type: number
          example: 3
        locationType:
          type: string
          enum:
            - DOMESTIC
            - COMMERCIAL
          example: 'DOMESTIC'
        options:
          type: object
          properties:
            revenueProfileId:
              type: number
            advertId:
              type: number
            geohash:
              type: number
            name:
              type: string
            description:
              type: string
            paygEnabled:
              type: boolean
            contactlessEnabled:
              type: boolean
            midmeterEnabled:
              type: boolean
            isPublic:
              type: boolean
            isHome:
              type: boolean
            isEvZone:
              type: boolean
            timezone:
              type: string
            unitId:
              type: number
            mpan:
              type: string
      required:
        - addressId
        - latitude
        - longitude
        - locationType
    AddressRequestType:
      type: object
      properties:
        line1:
          type: string
          example: '222 Grays Inn Road'
        line2:
          type: string
        postalTown:
          type: string
          example: 'London'
        postcode:
          type: string
          example: 'WC1X 8HB'
        country:
          type: string
          example: 'GB'
        groupId:
          type: number
        contactName:
          type: string
        email:
          type: string
        telephone:
          type: string
        businessName:
          type: string
        description:
          type: string
        tariffId:
          type: number
        costPerKwh:
          type: number
      required:
        - line1
        - postalTown
        - postcode
        - country
    LocationId:
      description: The identifier of a location (from pod_locations)
      type: string
      format: uuid
      example: dcc774eb-5bb3-42f3-a4f3-3eb00b20bdb3
    LocationSummary:
      title: Location Summary
      type: object
      nullable: true
      properties:
        id:
          $ref: '#/components/schemas/LocationId'
        isPublic:
          type: boolean
        type:
          $ref: '#/components/schemas/LocationType'
    LocationType:
      description: 'The type of the location: domestic, commercial. A replacement for the isHome flag, to allow a more nuanced distinction in future'
      type: string
      enum:
        - DOMESTIC
        - COMMERCIAL
    Ownership:
      type: string
      nullable: true
      enum:
        - POD_POINT
        - CUSTOMER
    ModelSummary:
      type: object
      properties:
        sku:
          type: string
          example: 'T7-S-07-AMC-BLK'
        range:
          type: object
          properties:
            name:
              type: string
              example: 'solo'
          required:
            - name
        vendor:
          type: object
          properties:
            name:
              type: string
              example: 'Pod Point'
          required:
            - name
        regions:
          type: array
          items:
            type: object
            properties:
              name:
                type: string
                enum:
                  - UK
                  - FR
                  - ES
                example: 'UK'
      required:
        - sku
        - range
        - vendor
        - regions
    PcbSummary:
      type: object
      properties:
        revision:
          type: string
          example: PP-A-220270-1
        architecture:
          $ref: '#/components/schemas/PcbArchitecture'
        serialNumber:
          $ref: '#/components/schemas/SerialNumber'
        macAddress:
          $ref: '#/components/schemas/MacAddress'
        generation:
          $ref: '#/components/schemas/Generation'
      required:
        - revision
        - architecture
        - serialNumber
        - macAddress
        - generation
    PcbArchitecture:
      description: The hardware architecture of the PCB (only applicable to PodPoint units)
      type: string
      enum:
        - '1.0'
        - '2.0'
        - '2.4'
        - '3.0'
        - '5.0'
      example: '5.0'
    Generation:
      description: The hardware generation of the PCB (only applicable to PodPoint units)
      type: string
      enum:
        - '1'
        - '2'
        - '2.4'
        - '3'
        - '5'
      example: '5'
    ProvisioningInfo:
      description: Contains information about provisioning of the charging station
      type: object
      properties:
        provisionedAt:
          type: string
          format: date-time
          example: '2023-06-01T00:00:00.000Z'
    RequestOwnership:
      description: Contains information about the ownership of the charging station
      type: string
      enum:
        - POD_POINT
        - CUSTOMER
    RouterSummary:
      type: object
      properties:
        serialNumber:
          type: string
          example: '1102538429'
        macAddress:
          type: string
          example: '001E4223CF13'
        simNumber:
          type: string
          example: '123456789123000043'
        model:
          type: string
          example: 'RUT241'
      required:
        - serialNumber
        - macAddress
        - simNumber
        - model
  parameters:
    q_macAddress:
      name: macAddress
      description: mac address(es) from an existing EVSE(s) (aka PCB)
      example:
        - '0cb2b703c778'
        - '0cb2b703c779'
      in: query
      required: false
      style: form
      explode: false
      schema:
        type: array
        items:
          $ref: '#/components/schemas/MacAddress'
    q_serialNumber:
      name: serialNumber
      description: serial number(s) from an existing EVSE(s) (aka PCB)
      example:
        - '220514394'
        - '220810240'
      in: query
      required: false
      style: form
      explode: false
      schema:
        type: array
        items:
          $ref: '#/components/schemas/SerialNumber'
    q_key:
      name: key
      description: key of a tag
      example: key1
      in: query
      required: true
      schema:
        type: string
    q_value:
      name: value
      description: value of a tag
      example: value1
      in: query
      required: true
      schema:
        type: string
    p_serialNumber:
      name: serialNumber
      description: serial number from an existing PCB
      example: 220514394,220810240
      in: path
      required: true
      schema:
        $ref: '#/components/schemas/SerialNumber'
    p_ppid:
      name: ppid
      description: ppid from an existing unit
      example: PSL-123456
      in: path
      required: true
      schema:
        $ref: '#/components/schemas/PPID'
    p_locationId:
      name: locationId
      description: unique identifier of a location
      in: path
      required: true
      schema:
        $ref: '#/components/schemas/LocationId'
  requestBodies:
    CreateChargingStationRequest:
      content:
        application/json:
          schema:
            $ref: '#/components/schemas/CreateChargingStationRequestType'
    DecommissionRequest:
      content:
        application/json:
          schema:
            $ref: '#/components/schemas/DecommissionRequestType'
    BatchDecommissionRequest:
      content:
        application/json:
          schema:
            $ref: '#/components/schemas/BatchDecommissionRequestType'
    LocationRequest:
      content:
        application/json:
          schema:
            $ref: '#/components/schemas/LocationRequestType'
    AddressRequest:
      content:
        application/json:
          schema:
            $ref: '#/components/schemas/AddressRequestType'
    TagsRequest:
      content:
        application/json:
          schema:
            $ref: '#/components/schemas/TagsRequestType'
    ChangeOwnershipRequest:
      content:
        application/json:
          schema:
            $ref: '#/components/schemas/ChangeOwnershipRequestType'
  responses:
    BatchDecommissionPcbsResponse:
      description: Example response
      content:
        application/json:
          schema:
            $ref: '#/components/schemas/BatchDecommissionPcbsResponse'
    ChargingStationSummaryResponse:
      description: Example response
      content:
        application/json:
          schema:
            $ref: '#/components/schemas/ChargingStationSummary'
    ChargingStationsSummaryResponse:
      description: Example response
      content:
        application/json:
          schema:
            $ref: '#/components/schemas/ChargingStationsSummary'
    GetPcbResponse:
      description: Get information about a PCB
      content:
        application/json:
          schema:
            properties:
              architecture:
                $ref: '#/components/schemas/PcbArchitecture'
              generation:
                $ref: '#/components/schemas/Generation'
    LocationResponse:
      description: Details of a location
      content:
        application/json:
          schema:
            $ref: '#/components/schemas/Location'
    CreateAddressResponse:
      description: Returns the id of the address.
      content:
        application/json:
          schema:
            type: object
            properties:
              id:
                type: number
                example: 5
    TaggedChargingStationResponse:
      description: Charging station ppids with a tag
      content:
        application/json:
          schema:
            $ref: '#/components/schemas/TaggedChargingStations'
