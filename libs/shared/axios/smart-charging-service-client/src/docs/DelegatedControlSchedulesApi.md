# DelegatedControlSchedulesApi

All URIs are relative to _http://localhost_

| Method                                                                                                            | HTTP request                                    | Description                                                                                             |
| ----------------------------------------------------------------------------------------------------------------- | ----------------------------------------------- | ------------------------------------------------------------------------------------------------------- |
| [**clearTargetScheduleForChargingStation**](#cleartargetscheduleforchargingstation)                               | **DELETE** /delegated-controls/{ppid}/schedules | Clear target schedule(s) for a delegated control charging station                                       |
| [**setTargetSchedulesForDelegatedControlChargingStation**](#settargetschedulesfordelegatedcontrolchargingstation) | **PUT** /delegated-controls/{ppid}/schedules    | Request target schedules be set for connected vehicle connected to a delegated control charging station |

# **clearTargetScheduleForChargingStation**

> clearTargetScheduleForChargingStation()

This endpoint will clear the target schedule(s) for a delegated control charging station and set the default schedule (0030 - 0430 daily)

### Example

```typescript
import { DelegatedControlSchedulesApi, Configuration } from './api';

const configuration = new Configuration();
const apiInstance = new DelegatedControlSchedulesApi(configuration);

let ppid: string; //The PPID of the charging station (default to undefined)

const { status, data } = await apiInstance.clearTargetScheduleForChargingStation(ppid);
```

### Parameters

| Name     | Type         | Description                      | Notes                 |
| -------- | ------------ | -------------------------------- | --------------------- |
| **ppid** | [**string**] | The PPID of the charging station | defaults to undefined |

### Return type

void (empty response body)

### Authorization

No authorization required

### HTTP request headers

- **Content-Type**: Not defined
- **Accept**: Not defined

### HTTP response details

| Status code | Description                                                          | Response headers |
| ----------- | -------------------------------------------------------------------- | ---------------- |
| **204**     | No-charge schedules set successfully                                 | -                |
| **404**     | Unit not found                                                       | -                |
| **422**     | Charging Station architecture info is not available or not supported | -                |

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **setTargetSchedulesForDelegatedControlChargingStation**

> setTargetSchedulesForDelegatedControlChargingStation()

Request target schedules be set for the likely connected or default vehicle of a delegated control charging station

### Example

```typescript
import { DelegatedControlSchedulesApi, Configuration } from './api';

const configuration = new Configuration();
const apiInstance = new DelegatedControlSchedulesApi(configuration);

let ppid: string; //The PPID of the charging station (default to undefined)
let trigger: 'PLUGIN' | 'INTENT_UPDATE'; //What triggered this request (if not provided, this is assumed to be PLUGIN) (optional) (default to undefined)

const { status, data } = await apiInstance.setTargetSchedulesForDelegatedControlChargingStation(ppid, trigger);
```

### Parameters

| Name        | Type                  | Description                                                                            | Notes                                                                       |
| ----------- | --------------------- | -------------------------------------------------------------------------------------- | --------------------------------------------------------------------------- | -------------------------------- |
| **ppid**    | [**string**]          | The PPID of the charging station                                                       | defaults to undefined                                                       |
| **trigger** | [\*\*&#39;PLUGIN&#39; | &#39;INTENT_UPDATE&#39;**]**Array<&#39;PLUGIN&#39; &#124; &#39;INTENT_UPDATE&#39;>\*\* | What triggered this request (if not provided, this is assumed to be PLUGIN) | (optional) defaults to undefined |

### Return type

void (empty response body)

### Authorization

No authorization required

### HTTP request headers

- **Content-Type**: Not defined
- **Accept**: Not defined

### HTTP response details

| Status code | Description                                                  | Response headers |
| ----------- | ------------------------------------------------------------ | ---------------- |
| **202**     | Request accepted                                             | -                |
| **404**     | Unit not found                                               | -                |
| **422**     | Charging Station is not a delegated control charging station | -                |

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)
