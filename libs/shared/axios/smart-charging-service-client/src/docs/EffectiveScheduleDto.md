# EffectiveScheduleDto

## Properties

| Name       | Type       | Description                                            | Notes                  |
| ---------- | ---------- | ------------------------------------------------------ | ---------------------- |
| **period** | **string** | the period ISO string of the effective charge schedule | [default to undefined] |

## Example

```typescript
import { EffectiveScheduleDto } from './api';

const instance: EffectiveScheduleDto = {
  period,
};
```

[[Back to Model list]](../README.md#documentation-for-models) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to README]](../README.md)
