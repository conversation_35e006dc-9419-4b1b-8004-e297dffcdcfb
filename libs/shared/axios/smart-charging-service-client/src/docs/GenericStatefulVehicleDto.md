# GenericStatefulVehicleDto

## Properties

| Name                   | Type                                            | Description              | Notes                             |
| ---------------------- | ----------------------------------------------- | ------------------------ | --------------------------------- |
| **id**                 | **string**                                      | The vehicle ID           | [default to undefined]            |
| **enodeUserId**        | **string**                                      | The enode user ID        | [optional] [default to undefined] |
| **enodeVehicleId**     | **string**                                      | The vehicle enode ID     | [optional] [default to undefined] |
| **vehicleInformation** | [**VehicleInformation**](VehicleInformation.md) | The vehicle information  | [default to undefined]            |
| **chargeState**        | [**GenericChargeState**](GenericChargeState.md) | The vehicle charge state | [default to undefined]            |

## Example

```typescript
import { GenericStatefulVehicleDto } from './api';

const instance: GenericStatefulVehicleDto = {
  id,
  enodeUserId,
  enodeVehicleId,
  vehicleInformation,
  chargeState,
};
```

[[Back to Model list]](../README.md#documentation-for-models) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to README]](../README.md)
