# CreateVehicleRequestDto

## Properties

| Name                   | Type                                                            | Description                                  | Notes                             |
| ---------------------- | --------------------------------------------------------------- | -------------------------------------------- | --------------------------------- |
| **vehicleInformation** | [**ExtendedVehicleInformation**](ExtendedVehicleInformation.md) | Vehicle information                          | [optional] [default to undefined] |
| **chargeState**        | [**GenericChargeState**](GenericChargeState.md)                 | The vehicle charge state                     | [default to undefined]            |
| **enodeUserId**        | **string**                                                      | The vehicle user ID                          | [optional] [default to undefined] |
| **enodeVehicleId**     | **string**                                                      | The vehicle enode ID                         | [optional] [default to undefined] |
| **chargeLimitPercent** | **number**                                                      | The vehicle fallback charge limit percentage | [optional] [default to undefined] |

## Example

```typescript
import { CreateVehicleRequestDto } from './api';

const instance: CreateVehicleRequestDto = {
  vehicleInformation,
  chargeState,
  enodeUserId,
  enodeVehicleId,
  chargeLimitPercent,
};
```

[[Back to Model list]](../README.md#documentation-for-models) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to README]](../README.md)
