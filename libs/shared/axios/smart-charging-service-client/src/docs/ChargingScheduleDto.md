# ChargingScheduleDto

## Properties

| Name                       | Type                                                                       | Description              | Notes                             |
| -------------------------- | -------------------------------------------------------------------------- | ------------------------ | --------------------------------- |
| **duration**               | **number**                                                                 | Duration                 | [optional] [default to undefined] |
| **startSchedule**          | **string**                                                                 | Start schedule           | [optional] [default to undefined] |
| **chargingRateUnit**       | **string**                                                                 | Charging rate unit       | [default to undefined]            |
| **chargingSchedulePeriod** | [**Array&lt;ChargingSchedulePeriodDto&gt;**](ChargingSchedulePeriodDto.md) | Charging schedule period | [default to undefined]            |
| **minChargingRate**        | **number**                                                                 | Minimum charging rate    | [optional] [default to undefined] |

## Example

```typescript
import { ChargingScheduleDto } from './api';

const instance: ChargingScheduleDto = {
  duration,
  startSchedule,
  chargingRateUnit,
  chargingSchedulePeriod,
  minChargingRate,
};
```

[[Back to Model list]](../README.md#documentation-for-models) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to README]](../README.md)
