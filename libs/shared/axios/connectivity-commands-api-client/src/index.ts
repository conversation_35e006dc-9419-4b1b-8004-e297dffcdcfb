/* tslint:disable */
/* eslint-disable */
/**
 * Connectivity Service Commands API
 * An API for sending commands to a currently connected charging station. Only works for devices connected via a websocket ### NB: All date/time inputs are to be provided in ISO format and timezone UTC e.g. `2023-01-03T16:09:59.000Z`.
 *
 * The version of the OpenAPI document: 1.1
 * Contact: <EMAIL>
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */

export * from './api';
export * from './configuration';
