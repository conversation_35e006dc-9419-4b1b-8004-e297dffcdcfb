import { FirmwareStatusType } from '../src';

export const TEST_FIRMWARE_STATUS_TYPES: FirmwareStatusType[] = [
  {
    serialNumber: '181410663',
    versionInfo: {
      architecture: 'arch2',
      details: { dspVersion: '2.35F14C', wifiVersion: '01.14' },
      manifestId: 'A22P-2.35.0-00002',
    },
    updateStatus: { isUpdateAvailable: false },
  },
  {
    serialNumber: '181310113',
    versionInfo: {
      architecture: 'arch2',
      details: { dspVersion: '2.26F', wifiVersion: '01.13' },
      manifestId: 'A22P-2.26.0-00001',
    },
    updateStatus: { isUpdateAvailable: true },
  },
];

export const TEST_OUT_OF_DATE_FIRMWARE_STATUS_TYPE: FirmwareStatusType[] = [
  {
    serialNumber: '181410663',
    versionInfo: {
      architecture: 'arch2',
      details: { dspVersion: '2.26F', wifiVersion: '01.13' },
      manifestId: 'A22P-2.35.0-00002',
    },
    updateStatus: { isUpdateAvailable: true },
  },
];
