{"name": "shared-axios-currency-converter-client", "$schema": "../../../../node_modules/nx/schemas/project-schema.json", "sourceRoot": "src", "projectType": "library", "tags": ["shared"], "targets": {"test": {"executor": "@nx/jest:jest", "outputs": ["{workspaceRoot}/coverage/libs/shared/axios/currency-converter-client"], "options": {"jestConfig": "libs/shared/axios/currency-converter-client/jest.config.ts", "passWithNoTests": false}}}}