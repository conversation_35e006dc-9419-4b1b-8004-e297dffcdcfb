import { render } from '@testing-library/react';
import FormActionButton from './form-action-button';

const mockFormStatus = jest.fn();

jest.mock('react-dom', () => ({
  ...jest.requireActual('react-dom'),
  useFormStatus: () => mockFormStatus(),
}));

describe('FormActionButton', () => {
  it('should be defined', () => {
    mockFormStatus.mockReturnValueOnce({
      pending: false,
    });
    const { baseElement } = render(<FormActionButton>Action</FormActionButton>);
    expect(baseElement).toBeDefined();
  });

  it('should match snapshot', () => {
    mockFormStatus.mockReturnValueOnce({
      pending: false,
    });
    const { baseElement } = render(<FormActionButton>Action</FormActionButton>);
    expect(baseElement).toMatchSnapshot();
  });

  it('should match snapshot when action is pending', () => {
    mockFormStatus.mockReturnValueOnce({
      pending: true,
    });
    const { baseElement } = render(<FormActionButton>Action</FormActionButton>);
    expect(baseElement).toMatchSnapshot();
  });

  it('should be disabled when disabled prop is passed', () => {
    mockFormStatus.mockReturnValueOnce({
      pending: false,
    });
    const { baseElement } = render(
      <FormActionButton disabled>Action</FormActionButton>
    );
    expect(baseElement.querySelector('button')).toHaveAttribute('disabled');
  });
});
