import { fireEvent, render, screen } from '@testing-library/react';
import ShowTourButton, { ShowTourButtonProps } from './show-tour-button';

const mockCookie = jest.fn();
const mockRemoveCookie = jest.fn();
jest.mock('react-cookie', () => ({
  useCookies: () => [mockCookie, mockCookie, mockRemoveCookie],
}));

const defaultProps: ShowTourButtonProps = {
  tours: [
    {
      pathname: '/',
      cookie: 'pod-point.home-page-tour-seen',
    },
  ],
};

describe('ShowTourButton', () => {
  it('should render successfully', () => {
    const { baseElement } = render(<ShowTourButton {...defaultProps} />);
    expect(baseElement).toBeTruthy();
  });

  it('should match the snapshot', () => {
    const { baseElement } = render(<ShowTourButton {...defaultProps} />);
    expect(baseElement).toMatchSnapshot();
  });

  it.each([
    ['home', { pathname: '/', cookie: 'home-page-tour-seen' }],
    ['test', { pathname: '/test/', cookie: 'test-tour-summary-seen' }],
  ])(
    `should remove the correct cookie for a %s page when the button is clicked on the page`,
    (_, { pathname, cookie }) => {
      // eslint-disable-next-line @typescript-eslint/ban-ts-comment
      // @ts-ignore
      delete window.location;

      // eslint-disable-next-line @typescript-eslint/ban-ts-comment
      // @ts-ignore
      window.location = {
        pathname,
      };

      render(<ShowTourButton tours={[{ pathname, cookie }]} />);
      fireEvent.click(screen.getByRole('button', { name: 'Show tour' }));
      expect(mockRemoveCookie).toHaveBeenCalledWith(cookie, { path: '/' });
    }
  );
});
