import { NavigationLinkProps } from '../navigation-link/navigation-link';
import Sidebar from '../sidebar/sidebar';

export interface SidebarDesktopProps extends React.HTMLProps<HTMLElement> {
  bottomNavigationLinks?: React.ReactNode[];
  navigationLinks: NavigationLinkProps[];
  showNavigationBottom?: boolean;
}

export const SidebarDesktop = (props: SidebarDesktopProps) => (
  <aside className="hidden overflow-y-auto md:flex md:w-56 md:flex-col md:fixed md:inset-y-0 print:hidden md:print:hidden">
    <Sidebar {...props} />
  </aside>
);

export default SidebarDesktop;
