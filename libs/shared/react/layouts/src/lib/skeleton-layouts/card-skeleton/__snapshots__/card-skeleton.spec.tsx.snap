// Jest Snapshot v1, https://goo.gl/fbAQLP

exports[`card skeleton should match snapshot with card count and cols: 2 1`] = `
<body>
  <div>
    <div
      class="grid grid-cols-2 md:grid-cols-2 lg:grid-cols-2 p-2 gap-2"
    >
      <section
        class="p-3 rounded-sm bg-white w-full"
      >
        <div
          class="bg-gray-200 h-48 rounded-lg mb-4 animate-pulse"
        />
        <div
          class="bg-gray-200 h-6 rounded mb-2 animate-pulse"
        />
        <div
          class="bg-gray-200 h-6 w-3/4 rounded animate-pulse"
        />
      </section>
      <section
        class="p-3 rounded-sm bg-white w-full"
      >
        <div
          class="bg-gray-200 h-48 rounded-lg mb-4 animate-pulse"
        />
        <div
          class="bg-gray-200 h-6 rounded mb-2 animate-pulse"
        />
        <div
          class="bg-gray-200 h-6 w-3/4 rounded animate-pulse"
        />
      </section>
    </div>
  </div>
</body>
`;

exports[`card skeleton should match snapshot with card count and cols: 3 1`] = `
<body>
  <div>
    <div
      class="grid grid-cols-2 md:grid-cols-2 lg:grid-cols-3 p-2 gap-2"
    >
      <section
        class="p-3 rounded-sm bg-white w-full"
      >
        <div
          class="bg-gray-200 h-48 rounded-lg mb-4 animate-pulse"
        />
        <div
          class="bg-gray-200 h-6 rounded mb-2 animate-pulse"
        />
        <div
          class="bg-gray-200 h-6 w-3/4 rounded animate-pulse"
        />
      </section>
      <section
        class="p-3 rounded-sm bg-white w-full"
      >
        <div
          class="bg-gray-200 h-48 rounded-lg mb-4 animate-pulse"
        />
        <div
          class="bg-gray-200 h-6 rounded mb-2 animate-pulse"
        />
        <div
          class="bg-gray-200 h-6 w-3/4 rounded animate-pulse"
        />
      </section>
      <section
        class="p-3 rounded-sm bg-white w-full"
      >
        <div
          class="bg-gray-200 h-48 rounded-lg mb-4 animate-pulse"
        />
        <div
          class="bg-gray-200 h-6 rounded mb-2 animate-pulse"
        />
        <div
          class="bg-gray-200 h-6 w-3/4 rounded animate-pulse"
        />
      </section>
    </div>
  </div>
</body>
`;

exports[`card skeleton should match snapshot with card count and cols: 4 1`] = `
<body>
  <div>
    <div
      class="grid grid-cols-2 md:grid-cols-2 lg:grid-cols-4 p-2 gap-2"
    >
      <section
        class="p-3 rounded-sm bg-white w-full"
      >
        <div
          class="bg-gray-200 h-48 rounded-lg mb-4 animate-pulse"
        />
        <div
          class="bg-gray-200 h-6 rounded mb-2 animate-pulse"
        />
        <div
          class="bg-gray-200 h-6 w-3/4 rounded animate-pulse"
        />
      </section>
      <section
        class="p-3 rounded-sm bg-white w-full"
      >
        <div
          class="bg-gray-200 h-48 rounded-lg mb-4 animate-pulse"
        />
        <div
          class="bg-gray-200 h-6 rounded mb-2 animate-pulse"
        />
        <div
          class="bg-gray-200 h-6 w-3/4 rounded animate-pulse"
        />
      </section>
      <section
        class="p-3 rounded-sm bg-white w-full"
      >
        <div
          class="bg-gray-200 h-48 rounded-lg mb-4 animate-pulse"
        />
        <div
          class="bg-gray-200 h-6 rounded mb-2 animate-pulse"
        />
        <div
          class="bg-gray-200 h-6 w-3/4 rounded animate-pulse"
        />
      </section>
      <section
        class="p-3 rounded-sm bg-white w-full"
      >
        <div
          class="bg-gray-200 h-48 rounded-lg mb-4 animate-pulse"
        />
        <div
          class="bg-gray-200 h-6 rounded mb-2 animate-pulse"
        />
        <div
          class="bg-gray-200 h-6 w-3/4 rounded animate-pulse"
        />
      </section>
    </div>
  </div>
</body>
`;
