import { LayoutGap } from '../enums';
import React from 'react';
import classNames from 'classnames';

export interface FourColumnLayoutProps extends React.HTMLProps<HTMLElement> {
  gap?: LayoutGap;
}

export const FourColumnLayout = ({
  children,
  gap = LayoutGap.XS,
}: FourColumnLayoutProps) => (
  <div
    className={classNames(
      'grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4',
      gap
    )}
  >
    {children}
  </div>
);

export default FourColumnLayout;
