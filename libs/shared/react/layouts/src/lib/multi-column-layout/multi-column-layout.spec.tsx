import { ColumnCount, LayoutGap } from '../enums';
import { render } from '@testing-library/react';
import MultiColumnLayout from './multi-column-layout';

describe('multi column layout', () => {
  it('should render successfully', () => {
    const { baseElement } = render(
      <MultiColumnLayout columns={ColumnCount.THREE} />
    );
    expect(baseElement).toBeInTheDocument();
  });

  it.each([
    ColumnCount.TWO,
    ColumnCount.THREE,
    ColumnCount.FOUR,
    ColumnCount.FIVE,
    ColumnCount.SIX,
  ])('should match snapshot for column count', (columns) => {
    const { baseElement } = render(<MultiColumnLayout columns={columns} />);
    expect(baseElement).toMatchSnapshot();
  });

  it.each([
    LayoutGap.XS,
    LayoutGap.S,
    LayoutGap.M,
    LayoutGap.L,
    LayoutGap.XL,
  ] as LayoutGap[])('should match snapshot for layout gap %s', (gap) => {
    const { baseElement } = render(<MultiColumnLayout columns={3} gap={gap} />);
    expect(baseElement).toMatchSnapshot();
  });
});
