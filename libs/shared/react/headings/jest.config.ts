/* eslint-disable */
export default {
  coverageDirectory: '../../../../coverage/libs/shared/react/headings',
  displayName: 'shared-react-headings',
  moduleFileExtensions: ['ts', 'tsx', 'js', 'jsx'],
  preset: '../../../../jest.preset.js',
  setupFilesAfterEnv: ['@testing-library/jest-dom'],
  transform: {
    '^(?!.*\\.(js|jsx|ts|tsx|css|json)$)': '@nx/react/plugins/jest',
    '^.+\\.[tj]sx?$': ['babel-jest', { presets: ['@nx/react/babel'] }],
  },
};
