'use server';

import {
  ALLOWED_ROLES,
  OidcRoles,
} from '@experience/shared/typescript/oidc-utils';
import { cookies } from 'next/headers';

export const userHasRole = async (role: OidcRoles): Promise<boolean> => {
  const rolesCookie = cookies().get('x-amzn-oidc-data-roles');
  const roles =
    rolesCookie?.value
      .split(',')
      .filter((role) => ALLOWED_ROLES.includes(role as OidcRoles)) ?? [];

  return roles.includes(role);
};
