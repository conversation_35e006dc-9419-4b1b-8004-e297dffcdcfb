# Shared React Design System

A component library built for the Pod Point design system.

Browse the components in [Storybook](https://shared-design-system-storybook.vercel.app/).

## Design system references

You can find complete references for the Design system [here](https://podpoint.atlassian.net/wiki/spaces/DES/pages/1632763931/Pod+Point+UI+Design+System)

This also includes colours, components and Figma links.

## Icons

You can find all the existing icons in the [Icons Explorer](https://brand.pod-point.com/icons-explorer/)

Read more about icons and how to use them [here](https://podpoint.atlassian.net/wiki/spaces/DES/pages/3651502125/Icons)

### Adding a new icon from the design system

- Copy and paste an existing icon, renaming folders, files, tests and types as required.

- Copy the relevant SVG code from the [Pod Point GitHub project](https://github.com/Pod-Point/icons-webfont) and paste
  it into your new icon.

- Add icon to the gallery storybook stories, using an existing example for reference.

## Installing the design system

### Locally

You will need to set up authentication to access private npm packages, hosted on GitHub:

Create a personal access token by visiting https://github.com/settings/tokens and clicking generate new classic token. Provide the token with the read:packages scope.

Then either:

Run:

```
npm login --scope=@pod-point --auth-type=legacy --registry=https://npm.pkg.github.com
```

and enter your Github username, and your personal access token from the previous step as the password.

OR

Ensure that a `.npmrc` file at the root of the project contains the following lines:

```
@pod-point:registry=https://npm.pkg.github.com
_authToken = GITHUB_PERSONAL_ACCESS_TOKEN
```

and finally install the package:

```
npm install @pod-point/shared-react-design-system
```

### Using the design system

To ensure the necessary CSS is generated for your app, you need to configure Tailwind CSS to reference the design system installation. Update your tailwind.config.js file by setting the content array to include the relevant files:

```
content: [
    join(__dirname, "node_modules/@pod-point/**/*.{js,ts,jsx,tsx}"),
  ],
```

This configuration tells Tailwind CSS to scan the design system components for class names during build time.

### Pipeline

To have the `design-system` package install correctly in your pipeline for testing and building, you will need to following steps

1. Ask an administrator of the experience monorepo to allow your repository to consume the `design-system` package via the ‘Manage Action access’ section in the [Package settings](https://github.com/orgs/Pod-Point/packages/npm/shared-react-design-system/settings).
2. Update the permissions of the action you want to use to include `packages: read`
3. Configure your `node` steps in your pipeline to connect to our private NPM Github package registry with `registry-url: 'https://npm.pkg.github.com/'` and `scope: '@pod-point'`
4. Update your packages install step to use the Github Token
   ```yaml
   env:
     NODE_AUTH_TOKEN: ${{ secrets.GITHUB_TOKEN }}
   ```
   [Github documentation ](https://github.com/actions/setup-node/blob/main/docs/advanced-usage.md#use-private-packages)

## Running unit tests

Run `nx test shared-react-design-system` to execute the unit tests via [Jest](https://jestjs.io).

## Publishing the design system

We have yet to implement a pipeline for publishing the design system, but the manual process is not too onerous.

First, generate a personal access token (classic) in your github account under account -> settings -> developer settings, the token needs read:packages and write:packages permissions.

Configure SSO for the token and authorise it to be used against the Pod-Point organisation.

Login to github packages using your token:

```
npm login --auth-type=legacy --registry=https://npm.pkg.github.com
```

Then run:

```
npx nx run shared-react-design-system:publish --ver {version} --tag {tag}
```

Note: Specifying the tag is optional, as it defaults to `latest`.
