{"name": "shared-react-design-system", "$schema": "../../../../node_modules/nx/schemas/project-schema.json", "sourceRoot": "libs/shared/react/design-system/src", "projectType": "library", "tags": ["shared"], "targets": {"test": {"executor": "@nx/jest:jest", "outputs": ["{workspaceRoot}/coverage/libs/shared/react/design-system"], "options": {"jestConfig": "libs/shared/react/design-system/jest.config.ts", "passWithNoTests": false}}, "storybook": {"executor": "@nx/storybook:storybook", "options": {"port": 4400, "configDir": "libs/shared/react/design-system/.storybook"}, "configurations": {"ci": {"quiet": true}}}, "build-storybook": {"executor": "@nx/storybook:build", "outputs": ["{options.outputDir}"], "options": {"configDir": "libs/shared/react/design-system/.storybook", "outputDir": "dist/storybook/shared-design-system"}, "configurations": {"ci": {"quiet": true}}}, "build": {"executor": "@nx/js:tsc", "outputs": ["{options.outputPath}"], "options": {"outputPath": "dist/libs/shared/react/design-system", "tsConfig": "libs/shared/react/design-system/tsconfig.lib.json", "packageJson": "libs/shared/react/design-system/package.json", "main": "libs/shared/react/design-system/src/index.ts", "assets": ["libs/shared/react/design-system/*.md"], "rootDir": ".", "updateBuildableProjectDepsInPackageJson": true}}, "publish": {"command": "node tools/scripts/publish.mjs shared-react-design-system {args.ver} {args.tag}", "dependsOn": ["build"]}}}