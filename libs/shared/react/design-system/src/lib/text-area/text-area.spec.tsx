import { render, screen } from '@testing-library/react';
import TextArea, { TextAreaWidth } from './text-area';

describe('TextArea', () => {
  it('should render successfully', () => {
    const { baseElement } = render(<TextArea />);
    expect(baseElement).toBeTruthy();
  });

  it('should match the snapshot', () => {
    const { baseElement } = render(<TextArea />);
    expect(baseElement).toMatchSnapshot();
  });

  it.each([
    TextAreaWidth.FULL,
    TextAreaWidth.HALF,
    TextAreaWidth.ONE_THIRD,
    TextAreaWidth.TWO_THIRDS,
  ])('should match the snapshot when input width is %s', (width) => {
    const { baseElement } = render(<TextArea width={width} />);
    expect(baseElement).toMatchSnapshot();
  });

  it('should render an error message when an error occurs', () => {
    render(<TextArea errorMessage="There has been an error" />);

    expect(screen.getByText('There has been an error')).toBeTruthy();
  });

  it('should autofocus if isFocused is set', () => {
    render(<TextArea value="Hello world" isFocused={true} />);

    expect(screen.getByText('Hello world')).toHaveFocus();
  });
});
