import { Anchor } from './anchor';
import { Meta, StoryFn } from '@storybook/react';

export default {
  component: Anchor,
  title: 'Anchor',
} as Meta<typeof Anchor>;

const Template: StoryFn<typeof Anchor> = (args) => (
  <Anchor {...args}>Link</Anchor>
);

export const Playground = Template.bind({});
Playground.args = {
  href: 'https://pod-point.com',
  target: '_blank',
  isNativeLink: true,
};
