import {
  TEST_CHARGER_WITH_POD,
  TEST_GROUP,
} from '@experience/commercial/statement-service/shared';
import { render } from '@testing-library/react';
import ChargerFeesPage from './charger-fees-page';

const mockRouter = jest.fn();
jest.mock('next/navigation', () => ({
  useRouter: () => mockRouter(),
}));

const defaultProps = {
  chargers: [TEST_CHARGER_WITH_POD],
  groupId: TEST_GROUP.groupId,
  siteId: '75710',
};

describe('Charger fees page', () => {
  it('should render correctly', () => {
    const { baseElement } = render(<ChargerFeesPage {...defaultProps} />);
    expect(baseElement).toBeInTheDocument();
  });

  it('should match snapshot', () => {
    const { baseElement } = render(<ChargerFeesPage {...defaultProps} />);
    expect(baseElement).toMatchSnapshot();
  });
});
