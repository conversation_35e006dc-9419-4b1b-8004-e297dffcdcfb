import { GroupsTable } from './groups-table';
import {
  TEST_GROUP,
  TEST_GROUP_WITH_STRIPE_CUSTOMER,
} from '@experience/commercial/statement-service/shared';
import { render, screen, within } from '@testing-library/react';
import userEvent from '@testing-library/user-event';

const defaultProps = {
  groups: [TEST_GROUP],
};

describe('GroupsTable', () => {
  it('should render successfully', () => {
    const { baseElement } = render(<GroupsTable {...defaultProps} />);

    expect(baseElement).toBeInTheDocument();
  });

  it('should match snapshot', () => {
    const { baseElement } = render(<GroupsTable {...defaultProps} />);

    expect(baseElement).toMatchSnapshot();
  });

  it.each([
    ['has Stripe subscriptions enabled', 'sub_123456789', 'Yes'],
    ['does not have Stripe subscriptions enabled', '', 'No'],
  ])(
    'should display the correct icon if group %s',
    (_, stripeSubscriptionId, iconTitle) => {
      render(
        <GroupsTable groups={[{ ...TEST_GROUP, stripeSubscriptionId }]} />
      );

      const row = screen
        .getByRole('cell', {
          name: TEST_GROUP.groupName,
        })
        .closest('tr');
      if (!row) fail('No first row found');

      expect(within(row).getAllByTitle(iconTitle).length).toBeGreaterThan(0);
    }
  );

  it.each([
    ['is a Stripe customer', 'cus_NffrFeUfNV2Hib', 'Yes'],
    ['is not a Stripe customer', '', 'No'],
  ])(
    `should display the correct icon if group %s`,
    (_, stripeCustomerId, iconTitle) => {
      render(<GroupsTable groups={[{ ...TEST_GROUP, stripeCustomerId }]} />);

      const row = screen
        .getByRole('cell', {
          name: TEST_GROUP.groupName,
        })
        .closest('tr');
      if (!row) fail('No first row found');

      expect(within(row).getAllByTitle(iconTitle).length).toBeGreaterThan(0);
    }
  );

  it.each([
    [
      'group has a connected account and revenue payouts enabled',
      {
        stripeConnectedAccountId: 'acct_123456789',
        transfersEnabled: true,
      },
      'Yes',
    ],
    [
      'group has a connected account but revenue payouts are not enabled',
      {
        stripeConnectedAccountId: 'acct_123456789',
        transfersEnabled: false,
      },
      'Warning',
    ],
    [
      'group does not have a connected account',
      {
        stripeConnectedAccountId: undefined,
      },
      'No',
    ],
  ])('should display the correct icon if %s', (_, config, iconTitle) => {
    render(
      <GroupsTable
        groups={[{ ...TEST_GROUP_WITH_STRIPE_CUSTOMER, ...config }]}
      />
    );

    const row = screen
      .getByRole('cell', {
        name: TEST_GROUP_WITH_STRIPE_CUSTOMER.groupName,
      })
      .closest('tr');
    if (!row) fail('No first row found');

    expect(within(row).getAllByTitle(iconTitle).length).toBeGreaterThan(0);
  });

  it.each([
    ['All', 3],
    ['Yes', 2],
    ['No', 1],
  ])(
    'should change selected option and show correct rows for the option %s for the Stripe subscriptions filter select',
    async (selection, rows) => {
      render(
        <GroupsTable
          groups={[
            { ...TEST_GROUP, stripeSubscriptionId: 'sub_123456789' },
            { ...TEST_GROUP, stripeSubscriptionId: 'sub_987654321' },
            { ...TEST_GROUP, stripeSubscriptionId: '' },
          ]}
        />
      );

      const filterSelect = screen.getByLabelText('Subscription');
      await userEvent.click(filterSelect);
      await userEvent.click(screen.getByRole('option', { name: selection }));

      const table = screen.getByRole('table');
      expect(within(table).getAllByRole('row')).toHaveLength(rows + 1);

      await userEvent.click(filterSelect);
      await userEvent.click(screen.getByRole('option', { name: 'All' }));
    }
  );

  it.each([
    ['All', 3],
    ['Yes', 2],
    ['No', 1],
  ])(
    'should change selected option and show correct rows for the option %s for the Stripe customer filter select',
    async (selection, rows) => {
      render(
        <GroupsTable
          groups={[
            { ...TEST_GROUP, stripeCustomerId: 'cus_NffrFeUfNV2Hib' },
            { ...TEST_GROUP, stripeCustomerId: 'cus_XbbDaSfFDAds' },
            { ...TEST_GROUP, stripeCustomerId: '' },
          ]}
        />
      );

      const filterSelect = screen.getByLabelText('Stripe customer');
      await userEvent.click(filterSelect);
      await userEvent.click(screen.getByRole('option', { name: selection }));

      const table = screen.getByRole('table');
      expect(within(table).getAllByRole('row')).toHaveLength(rows + 1);

      await userEvent.click(filterSelect);
      await userEvent.click(screen.getByRole('option', { name: 'All' }));
    }
  );

  it.each([
    ['All', 3],
    ['Yes', 1],
    ['No', 1],
    ['Warning', 1],
  ])(
    'should change selected option and show correct rows for the option %s for the Stripe connected account filter select',
    async (selection, rows) => {
      render(
        <GroupsTable
          groups={[
            {
              ...TEST_GROUP_WITH_STRIPE_CUSTOMER,
              stripeConnectedAccountId: 'acct_123456789',
              transfersEnabled: true,
            },
            {
              ...TEST_GROUP_WITH_STRIPE_CUSTOMER,
              stripeConnectedAccountId: 'acct_123456789',
              transfersEnabled: false,
            },
            {
              ...TEST_GROUP_WITH_STRIPE_CUSTOMER,
              stripeConnectedAccountId: undefined,
            },
          ]}
        />
      );

      const filterSelect = screen.getByLabelText('Payout account');
      await userEvent.click(filterSelect);
      await userEvent.click(screen.getByRole('option', { name: selection }));

      const table = screen.getByRole('table');
      expect(within(table).getAllByRole('row')).toHaveLength(rows + 1);

      await userEvent.click(filterSelect);
      await userEvent.click(screen.getByRole('option', { name: 'All' }));
    }
  );
});
