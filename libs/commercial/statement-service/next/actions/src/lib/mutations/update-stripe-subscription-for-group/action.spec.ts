import { COMMON_VALUE_GREATER_THAN_ZERO_ERROR } from '@experience/shared/typescript/validation';
import { TEST_GROUP } from '@experience/commercial/statement-service/shared';
import { appRequestHandler } from '@experience/commercial/next/app-request-utils';
import { updateStripeSubscriptionForGroup } from './action';

jest.mock('@experience/commercial/next/app-request-utils');
const mockRequestHandler = jest.mocked(appRequestHandler);

describe('Update stripe subscription for group action', () => {
  it('should update a stripe subscription for a group', async () => {
    mockRequestHandler.mockResolvedValueOnce({
      success: true,
      message: 'Stripe subscription updated successfully',
    });

    await updateStripeSubscriptionForGroup(TEST_GROUP.groupId, 10);

    expect(mockRequestHandler).toHaveBeenCalledWith(
      `http://localhost:5102/stripe/subscriptions`,
      {
        headers: {
          'content-type': 'application/json',
        },
        method: 'PUT',
        body: JSON.stringify({
          groupId: TEST_GROUP.groupId,
          socketQuantity: 10,
        }),
      },
      {
        400: expect.any(Function),
        404: expect.any(Function),
      }
    );
  });

  it('should throw an error if the group ID is not provided', async () => {
    await expect(updateStripeSubscriptionForGroup('', 10)).rejects.toThrow(
      'Group ID is required'
    );
  });

  it('should return an error message if the group is not found', async () => {
    mockRequestHandler.mockResolvedValueOnce({
      statusCode: 404,
      message: 'Group not found',
    });

    const response = await updateStripeSubscriptionForGroup(
      TEST_GROUP.groupId,
      10
    );

    expect(response).toEqual({
      success: false,
      message: 'Group not found',
    });
  });

  it('should return an error message if the price is not found', async () => {
    mockRequestHandler.mockResolvedValueOnce({
      statusCode: 404,
      message: 'No price found for this subscription',
    });

    const response = await updateStripeSubscriptionForGroup(
      TEST_GROUP.groupId,
      10
    );

    expect(response).toEqual({
      success: false,
      message: 'No price found for this subscription',
    });
  });

  it('should return an error message if validation fails', async () => {
    mockRequestHandler.mockResolvedValueOnce({
      statusCode: 400,
      message: COMMON_VALUE_GREATER_THAN_ZERO_ERROR,
    });

    const response = await updateStripeSubscriptionForGroup(
      TEST_GROUP.groupId,
      0
    );

    expect(response).toEqual({
      success: false,
      message: COMMON_VALUE_GREATER_THAN_ZERO_ERROR,
    });
  });
});
