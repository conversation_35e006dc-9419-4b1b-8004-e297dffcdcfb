'use server';

import { STATEMENT_SERVICE_API_URL } from '@experience/commercial/statement-service/shared';
import { appRequestHandler } from '@experience/commercial/next/app-request-utils';

interface CreateStripeSubscriptionForGroupResponse {
  success: boolean;
  message: string;
}

export const createStripeSubscriptionForGroup = async (
  groupId: string,
  socketQuantity: number
): Promise<CreateStripeSubscriptionForGroupResponse> => {
  if (!groupId) {
    throw new Error('Group ID is required');
  }

  const response = await appRequestHandler(
    `${STATEMENT_SERVICE_API_URL}/stripe/subscriptions`,
    {
      headers: {
        'content-type': 'application/json',
      },
      method: 'POST',
      body: JSON.stringify({
        groupId,
        socketQuantity,
      }),
    },
    {
      400: async (response) => await response.json(),
      404: async (response) => await response.json(),
    }
  );

  if ([400, 404].includes(response.statusCode)) {
    return {
      success: false,
      message: response.message,
    };
  }

  return {
    success: true,
    message: 'Stripe subscription created successfully',
  };
};
