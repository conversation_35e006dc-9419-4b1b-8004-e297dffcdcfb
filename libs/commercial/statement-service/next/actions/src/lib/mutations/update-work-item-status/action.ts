'use server';

import { STATEMENT_SERVICE_API_URL } from '@experience/commercial/statement-service/shared';
import { appRequestHandler } from '@experience/commercial/next/app-request-utils';

export const updateWorkItemStatus = async (
  workItemId: string,
  data: string
) => {
  await appRequestHandler(
    `${STATEMENT_SERVICE_API_URL}/work-items/${workItemId}/status`,
    {
      headers: {
        'content-type': 'application/json',
      },
      method: 'PUT',
      body: data,
    }
  );
};
