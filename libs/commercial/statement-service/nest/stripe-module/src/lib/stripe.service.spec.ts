import { ConfigModule, ConfigService } from '@nestjs/config';
import {
  CreateStripeSubscriptionException,
  StripeConnectedAccountAlreadyExistsException,
  StripeConnectedAccountNotFoundException,
  StripeCustomerAlreadyExistsException,
  StripeCustomerDetailsMissingException,
  StripeCustomerNotFoundException,
  StripePriceNotFoundException,
  StripeSubscriptionAlreadyExistsException,
  StripeSubscriptionInvoiceNotFinalisedException,
  StripeSubscriptionInvoiceNotFoundException,
  StripeSubscriptionNotFoundException,
} from '@experience/commercial/statement-service/nest/shared';
import { GroupsService } from '@experience/commercial/statement-service/nest/groups-module';
import {
  MOCK_CREATE_OR_UPDATE_STRIPE_SUBSCRIPTION_REQUEST,
  MOCK_CREATE_STRIPE_CONNECTED_ACCOUNT_LINK_REQUEST,
  MOCK_CREATE_STRIPE_CONNECTED_ACCOUNT_REQUEST,
  MOCK_CREATE_STRIPE_CUSTOMER_REQUEST,
  MOCK_REMOVE_STRIPE_CONNECTED_ACCOUNT_REQUEST,
  TEST_GROUP,
  TEST_GROUP_WITH_ADDRESS,
  TEST_GROUP_WITH_STRIPE_CONNECTED_ACCOUNT,
  TEST_GROUP_WITH_STRIPE_SUBSCRIPTION,
  TEST_SUBSCRIPTION_INVOICE,
} from '@experience/commercial/statement-service/shared';
import { PrismaHealthIndicator } from '@nestjs/terminus';
import { StatementsPrismaClient } from '@experience/commercial/statement-service/prisma/statements/client';
import {
  StripeAccountService,
  StripeCustomerService,
  StripeInvoiceService,
  StripePricesService,
  StripeSubscriptionService,
  TEST_STRIPE_ACCOUNT_ONBOARDING_LINK,
  TEST_STRIPE_INVOICE_RESPONSE,
} from '@experience/shared/nest/stripe';
import { StripeService } from './stripe.service';
import { TEST_SUBSCRIPTION } from '@experience/shared/nest/stripe';
import { Test, TestingModule } from '@nestjs/testing';
import Stripe from 'stripe';
import axios from 'axios';

const STRIPE_CUSTOMER_ID = 'cus_NffrFeUfNV2Hib';
const STRIPE_CONNECTED_ACCOUNT_ID = 'acc_NffrFeUfNV2Hib';

jest.mock('@experience/commercial/statement-service/nest/groups-module');
jest.mock('@experience/shared/nest/stripe');
jest.mock('axios');

const mockAxiosGet = jest.mocked(axios.get);

const TEST_PRICE: Partial<Stripe.Price> = {
  id: 'price_1',
};

const mockStripeClientId = 'ca_123456789';

describe('stripe service', () => {
  let service: StripeService;
  let groupsService: GroupsService;
  let prismaClient: StatementsPrismaClient;
  let stripeAccountService: StripeAccountService;
  let stripeInvoiceService: StripeInvoiceService;
  let stripeCustomerService: StripeCustomerService;
  let stripePricesService: StripePricesService;
  let stripeSubscriptionService: StripeSubscriptionService;

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      imports: [
        await ConfigModule.forRoot({
          load: [
            () => ({
              STRIPE_CLIENT_ID: mockStripeClientId,
              STRIPE_EXCLUSIVE_TAX_RATES: 'txr_1Iv1J2J2eZvKYlo2',
            }),
          ],
        }),
      ],
      providers: [
        ConfigService,
        GroupsService,
        PrismaHealthIndicator,
        StatementsPrismaClient,
        StripeAccountService,
        StripeInvoiceService,
        StripeCustomerService,
        StripePricesService,
        StripeService,
        StripeSubscriptionService,
      ],
    }).compile();

    service = module.get<StripeService>(StripeService);
    groupsService = module.get<GroupsService>(GroupsService);
    stripeAccountService =
      module.get<StripeAccountService>(StripeAccountService);
    stripeInvoiceService =
      module.get<StripeInvoiceService>(StripeInvoiceService);
    stripeCustomerService = module.get<StripeCustomerService>(
      StripeCustomerService
    );
    stripePricesService = module.get<StripePricesService>(StripePricesService);
    stripeSubscriptionService = module.get<StripeSubscriptionService>(
      StripeSubscriptionService
    );
    prismaClient = module.get<StatementsPrismaClient>(StatementsPrismaClient);
  });

  it('should be defined', () => {
    expect(service).toBeDefined();
    expect(groupsService).toBeDefined();
    expect(stripeAccountService).toBeDefined();
    expect(stripeInvoiceService).toBeDefined();
    expect(stripeCustomerService).toBeDefined();
    expect(prismaClient).toBeDefined();
  });

  describe('stripe customer', () => {
    it('should create a stripe customer for a group', async () => {
      const mockGroupFind = jest
        .spyOn(groupsService, 'findGroupByGroupId')
        .mockResolvedValueOnce(TEST_GROUP);
      const mockStripeCustomerCreate = jest
        .spyOn(stripeCustomerService, 'create')
        .mockResolvedValueOnce({ customerId: STRIPE_CUSTOMER_ID });
      const mockGroupUpdate = (prismaClient.groupConfig.update = jest.fn());

      await service.createStripeCustomerForGroup(
        MOCK_CREATE_STRIPE_CUSTOMER_REQUEST
      );

      expect(mockGroupFind).toHaveBeenCalledWith(TEST_GROUP.groupId);
      expect(mockStripeCustomerCreate).toHaveBeenCalledWith({
        name: TEST_GROUP.businessName,
        email: TEST_GROUP.businessEmail,
        address: { country: 'GB' },
      });
      expect(mockGroupUpdate).toHaveBeenCalledWith({
        data: { stripeCustomerId: 'cus_NffrFeUfNV2Hib' },
        where: { groupId: '036c0720-f908-45fc-ae7c-031a47c2e278' },
      });
    });

    it('should throw an error if a stripe customer already exists', async () => {
      jest.spyOn(groupsService, 'findGroupByGroupId').mockResolvedValueOnce({
        ...TEST_GROUP,
        stripeCustomerId: STRIPE_CUSTOMER_ID,
      });

      await expect(
        service.createStripeCustomerForGroup(
          MOCK_CREATE_STRIPE_CUSTOMER_REQUEST
        )
      ).rejects.toThrow(new StripeCustomerAlreadyExistsException());
    });
  });

  describe('stripe connected account', () => {
    it('should create a stripe connected account for a group', async () => {
      const mockGroupFind = jest
        .spyOn(groupsService, 'findGroupByGroupId')
        .mockResolvedValueOnce(TEST_GROUP);
      const mockStripeAccountCreate = jest
        .spyOn(stripeAccountService, 'create')
        .mockResolvedValue({ accountId: STRIPE_CONNECTED_ACCOUNT_ID });
      const mockGroupUpdate = (prismaClient.groupConfig.update = jest.fn());

      const response = await service.createStripeConnectedAccountForGroup(
        MOCK_CREATE_STRIPE_CONNECTED_ACCOUNT_REQUEST
      );

      expect(response).toEqual(STRIPE_CONNECTED_ACCOUNT_ID);
      expect(mockGroupFind).toHaveBeenCalledWith(TEST_GROUP.groupId);
      expect(mockStripeAccountCreate).toHaveBeenCalledWith({
        businessName: TEST_GROUP.businessName,
        country: 'GB',
        email: TEST_GROUP.businessEmail,
        type: 'standard',
      });
      expect(mockGroupUpdate).toHaveBeenCalledWith({
        data: { stripeConnectedAccountId: STRIPE_CONNECTED_ACCOUNT_ID },
        where: {
          groupId: TEST_GROUP.groupId,
        },
      });
    });

    it('should throw an error if a stripe connected account already exists', async () => {
      jest.spyOn(groupsService, 'findGroupByGroupId').mockResolvedValueOnce({
        ...TEST_GROUP,
        stripeConnectedAccountId: STRIPE_CONNECTED_ACCOUNT_ID,
      });

      await expect(
        service.createStripeConnectedAccountForGroup(
          MOCK_CREATE_STRIPE_CONNECTED_ACCOUNT_REQUEST
        )
      ).rejects.toThrow(new StripeConnectedAccountAlreadyExistsException());
    });

    it.each([
      { ...TEST_GROUP, businessName: undefined },
      { ...TEST_GROUP, businessEmail: undefined },
      { ...TEST_GROUP, accountRef: undefined },
    ])(
      'should throw an error when creating a connected account if the group config is missing business details',
      async (config) => {
        jest
          .spyOn(groupsService, 'findGroupByGroupId')
          .mockResolvedValueOnce(config);

        await expect(
          service.createStripeConnectedAccountForGroup(
            MOCK_CREATE_STRIPE_CONNECTED_ACCOUNT_REQUEST
          )
        ).rejects.toThrow(new StripeCustomerDetailsMissingException());
      }
    );

    it('should remove a stripe connected account for a group', async () => {
      const mockGroupFind = jest
        .spyOn(groupsService, 'findGroupByGroupId')
        .mockResolvedValueOnce(TEST_GROUP_WITH_STRIPE_CONNECTED_ACCOUNT);
      const mockStripeAccountRemove = jest
        .spyOn(stripeAccountService, 'remove')
        .mockResolvedValueOnce();
      const mockGroupUpdate = (prismaClient.groupConfig.update = jest.fn());

      await service.removeStripeConnectedAccountFromGroup(
        MOCK_REMOVE_STRIPE_CONNECTED_ACCOUNT_REQUEST
      );

      expect(mockGroupFind).toHaveBeenCalledWith(TEST_GROUP.groupId);
      expect(mockStripeAccountRemove).toHaveBeenCalledWith(
        mockStripeClientId,
        TEST_GROUP_WITH_STRIPE_CONNECTED_ACCOUNT.stripeConnectedAccountId
      );
      expect(mockGroupUpdate).toHaveBeenCalledWith({
        data: { stripeConnectedAccountId: null },
        where: { groupId: TEST_GROUP.groupId },
      });
    });

    it('should throw an error if a stripe connected account is not found when removing', async () => {
      jest.spyOn(groupsService, 'findGroupByGroupId').mockResolvedValueOnce({
        ...TEST_GROUP,
        stripeConnectedAccountId: undefined,
      });

      await expect(() =>
        service.removeStripeConnectedAccountFromGroup(
          MOCK_REMOVE_STRIPE_CONNECTED_ACCOUNT_REQUEST
        )
      ).rejects.toThrow(new StripeConnectedAccountNotFoundException());
    });

    it('should create a stripe connected account login link', async () => {
      const mockGroupFind = jest
        .spyOn(groupsService, 'findGroupByGroupId')
        .mockResolvedValueOnce(TEST_GROUP_WITH_STRIPE_CONNECTED_ACCOUNT);
      const mockCreateStripeAccountLink = jest
        .spyOn(stripeAccountService, 'createAccountOnboardingLink')
        .mockResolvedValue(TEST_STRIPE_ACCOUNT_ONBOARDING_LINK);

      expect(
        await service.createStripeConnectedAccountLink(
          MOCK_CREATE_STRIPE_CONNECTED_ACCOUNT_LINK_REQUEST
        )
      ).toEqual(TEST_STRIPE_ACCOUNT_ONBOARDING_LINK);
      expect(mockGroupFind).toHaveBeenCalledWith(
        TEST_GROUP_WITH_STRIPE_CONNECTED_ACCOUNT.groupId
      );
      expect(mockCreateStripeAccountLink).toHaveBeenCalledWith(
        TEST_GROUP_WITH_STRIPE_CONNECTED_ACCOUNT.stripeConnectedAccountId,
        'https://sites.podenergy.com/billing'
      );
    });

    it('should throw an error if a stripe connected account is not found when creating login link', async () => {
      jest.spyOn(groupsService, 'findGroupByGroupId').mockResolvedValueOnce({
        ...TEST_GROUP_WITH_STRIPE_CONNECTED_ACCOUNT,
        stripeConnectedAccountId: undefined,
      });

      await expect(() =>
        service.createStripeConnectedAccountLink(
          MOCK_CREATE_STRIPE_CONNECTED_ACCOUNT_LINK_REQUEST
        )
      ).rejects.toThrow(new StripeConnectedAccountNotFoundException());
    });

    it('should refresh onboarding link', async () => {
      const stripeConnectedAccountLink =
        'https://stripe.com/connected-account-link';
      const httpRedirectResponse = {
        url: stripeConnectedAccountLink,
        statusCode: 307,
      };

      jest
        .spyOn(service, 'createStripeConnectedAccountLink')
        .mockResolvedValueOnce(stripeConnectedAccountLink);

      expect(
        await service.refreshOnboardingLink(
          '45c9ef22-b44b-4edd-bb66-392e923950af'
        )
      ).toEqual(httpRedirectResponse);
    });
  });

  describe('stripe subscription', () => {
    it('should create a stripe subscription', async () => {
      const mockGroupFind = jest
        .spyOn(groupsService, 'findGroupByGroupId')
        .mockResolvedValueOnce(TEST_GROUP_WITH_STRIPE_CONNECTED_ACCOUNT);
      const mockPricesFind = jest
        .spyOn(stripePricesService, 'getPriceByLookupKey')
        .mockResolvedValueOnce(TEST_PRICE as Stripe.Price);
      const mockCreateSubscription = jest
        .spyOn(stripeSubscriptionService, 'create')
        .mockResolvedValueOnce(TEST_SUBSCRIPTION);
      const mockGroupUpdate = (prismaClient.groupConfig.update = jest.fn());

      await service.createStripeSubscriptionForGroup(
        MOCK_CREATE_OR_UPDATE_STRIPE_SUBSCRIPTION_REQUEST
      );

      expect(mockGroupFind).toHaveBeenCalledWith(
        TEST_GROUP_WITH_STRIPE_CONNECTED_ACCOUNT.groupId
      );
      expect(mockPricesFind).toHaveBeenCalledWith(
        'site_management_service_fee_monthly'
      );
      expect(mockCreateSubscription).toHaveBeenCalledWith({
        customerId: TEST_GROUP_WITH_STRIPE_CONNECTED_ACCOUNT.stripeCustomerId,
        prices: [
          {
            id: 'price_1',
            quantity: 10,
            taxRates: ['txr_1Iv1J2J2eZvKYlo2'],
          },
        ],
      });
      expect(mockGroupUpdate).toHaveBeenCalledWith({
        data: { stripeSubscriptionId: 'sub_1MowQVLkdIwHu7ixeRlqHVzs' },
        where: { groupId: '036c0720-f908-45fc-ae7c-031a47c2e278' },
      });
    });

    it('should throw an error if creating a subscription fails', async () => {
      jest
        .spyOn(groupsService, 'findGroupByGroupId')
        .mockResolvedValueOnce(TEST_GROUP_WITH_STRIPE_CONNECTED_ACCOUNT);
      jest
        .spyOn(stripePricesService, 'getPriceByLookupKey')
        .mockResolvedValueOnce(TEST_PRICE as Stripe.Price);
      jest.spyOn(stripeSubscriptionService, 'create').mockResolvedValueOnce({
        id: TEST_SUBSCRIPTION.id,
        status: 'incomplete',
      });
      prismaClient.groupConfig.update = jest.fn();

      await expect(
        service.createStripeSubscriptionForGroup(
          MOCK_CREATE_OR_UPDATE_STRIPE_SUBSCRIPTION_REQUEST
        )
      ).rejects.toThrow(new CreateStripeSubscriptionException());
    });

    it('should throw an error if a stripe customer is not found when creating a subscription', async () => {
      jest
        .spyOn(groupsService, 'findGroupByGroupId')
        .mockResolvedValueOnce(TEST_GROUP_WITH_ADDRESS);

      await expect(() =>
        service.createStripeSubscriptionForGroup(
          MOCK_CREATE_OR_UPDATE_STRIPE_SUBSCRIPTION_REQUEST
        )
      ).rejects.toThrow(new StripeCustomerNotFoundException());
    });

    it('should throw an error if a stripe price is not found when creating a subscription', async () => {
      jest
        .spyOn(groupsService, 'findGroupByGroupId')
        .mockResolvedValueOnce(TEST_GROUP_WITH_STRIPE_CONNECTED_ACCOUNT);
      jest.spyOn(stripePricesService, 'getPriceByLookupKey');

      await expect(() =>
        service.createStripeSubscriptionForGroup({
          groupId: '036c0720-f908-45fc-ae7c-031a47c2e278',
          socketQuantity: 10,
        })
      ).rejects.toThrow(new StripePriceNotFoundException());
    });

    it('should throw an error if a stripe subscription already exists', async () => {
      jest
        .spyOn(groupsService, 'findGroupByGroupId')
        .mockResolvedValueOnce(TEST_GROUP_WITH_STRIPE_SUBSCRIPTION);

      await expect(() =>
        service.createStripeSubscriptionForGroup(
          MOCK_CREATE_OR_UPDATE_STRIPE_SUBSCRIPTION_REQUEST
        )
      ).rejects.toThrow(new StripeSubscriptionAlreadyExistsException());
    });

    it('should retrieve a stripe subscription', async () => {
      jest
        .spyOn(stripeSubscriptionService, 'retrieve')
        .mockResolvedValueOnce(TEST_SUBSCRIPTION);

      const response = await service.retrieveSubscription(TEST_SUBSCRIPTION.id);

      expect(response).toEqual(TEST_SUBSCRIPTION);
    });

    it('should update a stripe subscription', async () => {
      jest
        .spyOn(groupsService, 'findGroupByGroupId')
        .mockResolvedValueOnce(TEST_GROUP_WITH_STRIPE_SUBSCRIPTION);
      jest.spyOn(stripeSubscriptionService, 'retrieve').mockResolvedValueOnce({
        id: TEST_GROUP_WITH_STRIPE_SUBSCRIPTION.stripeSubscriptionId as string,
        items: [
          {
            id: 'si_1MowQVLkdIwHu7ixb2lqHVzs',
            price: {
              id: 'price_1',
              lookup_key: 'site_management_service_fee_monthly',
            },
            quantity: 10,
          },
        ],
        status: 'active',
      });

      await service.updateStripeSubscriptionForGroup({
        groupId: '036c0720-f908-45fc-ae7c-031a47c2e278',
        socketQuantity: 15,
      });

      expect(stripeSubscriptionService.update).toHaveBeenCalledWith(
        TEST_GROUP_WITH_STRIPE_SUBSCRIPTION.stripeSubscriptionId,
        {
          items: [
            {
              id: 'si_1MowQVLkdIwHu7ixb2lqHVzs',
              quantity: 15,
            },
          ],
        }
      );
    });

    it('should throw an error if a stripe subscription is not found when updating a subscription', async () => {
      jest
        .spyOn(groupsService, 'findGroupByGroupId')
        .mockResolvedValueOnce(TEST_GROUP_WITH_ADDRESS);

      await expect(() =>
        service.createStripeSubscriptionForGroup(
          MOCK_CREATE_OR_UPDATE_STRIPE_SUBSCRIPTION_REQUEST
        )
      ).rejects.toThrow(new StripeCustomerNotFoundException());
    });

    it('should throw an error if there is no subscription when updating a subscription', async () => {
      jest
        .spyOn(groupsService, 'findGroupByGroupId')
        .mockResolvedValueOnce(TEST_GROUP_WITH_STRIPE_SUBSCRIPTION);
      jest.spyOn(stripeSubscriptionService, 'retrieve');

      await expect(() =>
        service.updateStripeSubscriptionForGroup({
          groupId: '036c0720-f908-45fc-ae7c-031a47c2e278',
          socketQuantity: 10,
        })
      ).rejects.toThrow(new StripeSubscriptionNotFoundException());
    });

    it('should throw an error if no subscription item is found when updating a subscription', async () => {
      jest
        .spyOn(groupsService, 'findGroupByGroupId')
        .mockResolvedValueOnce(TEST_GROUP_WITH_STRIPE_SUBSCRIPTION);
      jest.spyOn(stripeSubscriptionService, 'retrieve').mockResolvedValueOnce({
        id: TEST_GROUP_WITH_STRIPE_SUBSCRIPTION.stripeSubscriptionId as string,
        items: [],
        status: 'active',
      });

      await expect(() =>
        service.updateStripeSubscriptionForGroup(
          MOCK_CREATE_OR_UPDATE_STRIPE_SUBSCRIPTION_REQUEST
        )
      ).rejects.toThrow(new StripePriceNotFoundException());
    });

    it('should get a subscription invoice', async () => {
      const mockGetInvoices = jest
        .spyOn(stripeInvoiceService, 'getInvoicesBySubscriptionId')
        .mockResolvedValue([TEST_STRIPE_INVOICE_RESPONSE]);

      const invoice = await service.getSubscriptionInvoice(
        TEST_STRIPE_INVOICE_RESPONSE.id as string,
        TEST_SUBSCRIPTION.id
      );

      expect(invoice).toEqual(TEST_SUBSCRIPTION_INVOICE);
      expect(mockGetInvoices).toHaveBeenCalledWith(TEST_SUBSCRIPTION.id);
    });

    it('should throw an error if a subscription invoice is not found', async () => {
      jest
        .spyOn(stripeInvoiceService, 'getInvoicesBySubscriptionId')
        .mockResolvedValue([]);

      await expect(() =>
        service.getSubscriptionInvoice(
          TEST_STRIPE_INVOICE_RESPONSE.id as string,
          TEST_SUBSCRIPTION.id
        )
      ).rejects.toThrow(new StripeSubscriptionInvoiceNotFoundException());
    });

    it('should get a subscription invoice PDF', async () => {
      mockAxiosGet.mockResolvedValueOnce({
        data: Buffer.from('test.pdf'),
        status: 200,
      });
      jest
        .spyOn(stripeInvoiceService, 'getInvoicesBySubscriptionId')
        .mockResolvedValue([TEST_STRIPE_INVOICE_RESPONSE]);

      const response = await service.getSubscriptionInvoicePdf(
        TEST_STRIPE_INVOICE_RESPONSE.id as string,
        TEST_SUBSCRIPTION.id
      );

      expect(mockAxiosGet).toHaveBeenCalledWith(
        TEST_STRIPE_INVOICE_RESPONSE.invoice_pdf,
        { responseType: 'arraybuffer' }
      );
      expect(response).toEqual({
        buffer: Buffer.from('test.pdf'),
        filename: `Invoice-${TEST_STRIPE_INVOICE_RESPONSE.number}.pdf`,
      });
    });

    it('should throw an error when getting an invoice pdf if the invoice is not finalised', async () => {
      jest
        .spyOn(stripeInvoiceService, 'getInvoicesBySubscriptionId')
        .mockResolvedValue([
          { ...TEST_STRIPE_INVOICE_RESPONSE, invoice_pdf: undefined },
        ]);

      await expect(() =>
        service.getSubscriptionInvoicePdf(
          TEST_STRIPE_INVOICE_RESPONSE.id as string,
          TEST_SUBSCRIPTION.id
        )
      ).rejects.toThrow(new StripeSubscriptionInvoiceNotFinalisedException());
    });

    it('should get list of invoices for a subscription ID', async () => {
      const mockListInvoices = jest
        .spyOn(stripeInvoiceService, 'getInvoicesBySubscriptionId')
        .mockResolvedValue([TEST_STRIPE_INVOICE_RESPONSE]);

      const invoices = await service.getInvoicesBySubscriptionId(
        'sub_123456789'
      );

      expect(invoices).toEqual([TEST_SUBSCRIPTION_INVOICE]);
      expect(mockListInvoices).toHaveBeenCalledWith('sub_123456789');
    });

    it('should throw an error if there is no invoice ID', async () => {
      jest
        .spyOn(stripeInvoiceService, 'getInvoicesBySubscriptionId')
        .mockResolvedValue([
          { ...TEST_STRIPE_INVOICE_RESPONSE, id: undefined },
        ]);

      await expect(() =>
        service.getInvoicesBySubscriptionId(TEST_SUBSCRIPTION.id)
      ).rejects.toThrow('Invoice ID is not defined');
    });
  });
});
