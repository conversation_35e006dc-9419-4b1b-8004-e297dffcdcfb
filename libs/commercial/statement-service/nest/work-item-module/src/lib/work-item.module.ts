import {
  ConfigService,
  ConfigModule as NestConfigModule,
} from '@nestjs/config';
import {
  Configuration,
  SitesApi,
} from '@experience/shared/axios/data-platform-api-client';
import { GenerateWorkItemsCommand } from './commands/generate-work-items.command';
import { MarkWorkItemsReadyCommand } from './commands/mark-work-items-ready.command';
import { Module } from '@nestjs/common';
import { SitesModule } from '@experience/commercial/statement-service/nest/sites-module';
import { StatementModule } from '@experience/commercial/statement-service/nest/statement-module';
import { StatementsPrismaClientModule } from '@experience/commercial/statement-service/prisma/statements/client';
import { WorkItemController } from './work-item.controller';
import { WorkItemService } from './work-item.service';

@Module({
  imports: [
    NestConfigModule,
    StatementsPrismaClientModule,
    StatementModule,
    SitesModule,
  ],
  controllers: [WorkItemController],
  providers: [
    {
      inject: [ConfigService],
      provide: SitesApi,
      useFactory: async (configService: ConfigService) =>
        new SitesApi(
          new Configuration({
            basePath: configService.get('DATA_PLATFORM_API_BASE_URL'),
          })
        ),
    },
    WorkItemService,
    GenerateWorkItemsCommand,
    MarkWorkItemsReadyCommand,
  ],
  exports: [WorkItemService],
})
export class WorkItemModule {}
