import { ConfigModule } from '@nestjs/config';
import { PrismaHealthIndicator } from '@nestjs/terminus';
import { SqsClientService } from '@experience/shared/nest/aws/sqs-module';
import {
  StatementsPrismaClient,
  TEST_SITE_CONFIG_ENTITY,
  TEST_WORK_ITEM_ENTITY,
} from '@experience/commercial/statement-service/prisma/statements/client';
import { StatementsToSendProducerService } from './statements-to-send.producer.service';
import { Status } from '@experience/commercial/statement-service/prisma/statements/client';
import { TEST_WORK_ITEM } from '@experience/commercial/statement-service/shared';
import { Test, TestingModule } from '@nestjs/testing';

jest.mock('@experience/shared/nest/aws/sqs-module', () => ({
  ...jest.requireActual('@experience/shared/nest/aws/sqs-module'),
  SqsClientService: jest.fn().mockImplementation(() => ({
    sendBatchMessages: jest.fn(),
  })),
}));
jest.mock('uuid', () => ({ v4: () => 'e3e92d8a-966e-4e0d-8895-6e4d8d75dc49' }));

describe('Statements to send producer service', () => {
  let service: StatementsToSendProducerService;
  let sqsClientService: SqsClientService;
  let prismaClient: StatementsPrismaClient;

  beforeAll(async () => {
    const module: TestingModule = await Test.createTestingModule({
      imports: [
        await ConfigModule.forRoot({
          load: [
            () => ({
              STATEMENTS_TO_SEND_QUEUE_URL:
                'http://localhost/000000/statements-to-send',
            }),
          ],
        }),
      ],
      providers: [
        PrismaHealthIndicator,
        SqsClientService,
        StatementsPrismaClient,
        StatementsToSendProducerService,
      ],
    }).compile();

    service = module.get<StatementsToSendProducerService>(
      StatementsToSendProducerService
    );
    sqsClientService = module.get<SqsClientService>(SqsClientService);
    prismaClient = module.get<StatementsPrismaClient>(StatementsPrismaClient);
  });

  it('should should send automated work items messages to the send email queue', async () => {
    const mockFindMany = (prismaClient.workItem.findMany = jest
      .fn()
      .mockResolvedValueOnce([TEST_WORK_ITEM_ENTITY]));
    const mockFindSiteConfig = (prismaClient.siteConfig.findMany =
      jest.fn()).mockReturnValue([TEST_SITE_CONFIG_ENTITY]);

    const mockSendBatchMessages = jest.spyOn(
      sqsClientService,
      'sendBatchMessages'
    );
    const mockUpdateStatusToSending = (prismaClient.workItem.updateMany =
      jest.fn());

    await service.queueStatementsToSend();

    expect(mockFindMany).toHaveBeenCalledWith({
      where: {
        status: Status.GENERATED,
        deletedAt: null,
      },
    });
    expect(mockFindSiteConfig).toHaveBeenCalledTimes(1);
    expect(mockUpdateStatusToSending).toHaveBeenCalledWith({
      where: {
        id: {
          in: [TEST_WORK_ITEM.id],
        },
      },
      data: {
        status: Status.SENDING,
      },
    });
    expect(mockSendBatchMessages).toHaveBeenCalledWith(
      'http://localhost/000000/statements-to-send',
      [
        {
          messageId: 'e3e92d8a-966e-4e0d-8895-6e4d8d75dc49',
          messageBody: JSON.stringify({
            workItemId: TEST_WORK_ITEM.id,
          }),
        },
      ]
    );

    expect(mockSendBatchMessages.mock.invocationCallOrder[0]).toBeGreaterThan(
      mockUpdateStatusToSending.mock.invocationCallOrder[0]
    );
  });
});
