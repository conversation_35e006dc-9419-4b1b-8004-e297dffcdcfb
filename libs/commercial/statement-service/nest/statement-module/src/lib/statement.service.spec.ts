import * as MockDate from 'mockdate';
import { ConfigModule } from '@nestjs/config';
import { GroupsService } from '@experience/commercial/statement-service/nest/groups-module';
import {
  InvalidStatusTransitionException,
  PDFGenerationFailedException,
  StatementAlreadyPaidOutException,
} from '@experience/commercial/statement-service/nest/shared';
import { InvoiceService } from '@experience/commercial/statement-service/nest/invoice-module';
import { Logger } from '@nestjs/common';
import {
  PayoutStatus,
  StatementsPrismaClient,
  Status,
  TEST_CHARGER_CONFIG_ENTITY,
  TEST_GROUP_CONFIG_ENTITY,
  TEST_STATEMENT_ENTITY,
  TEST_STATEMENT_ENTITY_DEEP,
  TEST_WORK_ITEM_ENTITY,
  statementDeepIncludeOptions,
} from '@experience/commercial/statement-service/prisma/statements/client';
import { PrismaHealthIndicator } from '@nestjs/terminus';
import { S3Service } from '@experience/shared/nest/aws/s3-module';
import {
  SITE_ADMIN_API_URL,
  TEST_CREATE_STATEMENT_RESPONSE,
  TEST_GROUP,
  TEST_GROUP_WITH_STRIPE_CONNECTED_ACCOUNT,
  TEST_GROUP_WITH_STRIPE_CUSTOMER,
  TEST_INVOICE,
  TEST_PARTIAL_INVOICE_WITH_STRIPE_DATA,
  TEST_STATEMENT,
  TEST_WORK_ITEM,
  WorkItemStatus,
  getTestStatement,
} from '@experience/commercial/statement-service/shared';
import { SitesApi } from '@experience/shared/axios/data-platform-api-client';
import {
  StatementNotFoundException,
  StatementWorkItemNotFoundException,
} from './statement.exception';
import { StatementService } from './statement.service';
import { StripeConnectService } from '@experience/shared/nest/stripe';
import { TEST_MONTHLY_SITE_STATEMENT } from '@experience/commercial/site-admin/typescript/domain-model';
import { Test, TestingModule } from '@nestjs/testing';
import {
  firstOfLastMonth,
  sanitiseString,
} from '@experience/shared/typescript/utils';
import { mockDeep } from 'jest-mock-extended';
import dayjs from 'dayjs';

const mockGeneratePdfFromWebpage = jest.fn();
const mockValidatePdf = jest.fn().mockResolvedValue(true);

jest.mock('@experience/commercial/statement-service/nest/groups-module');
jest.mock('@experience/commercial/statement-service/nest/invoice-module');
jest.mock('@experience/shared/nest/aws/s3-module');
jest.mock('@experience/shared/nest/stripe');
jest.mock('@experience/commercial/statement-service/nest/shared', () => ({
  ...jest.requireActual('@experience/commercial/statement-service/nest/shared'),
  generatePdfFromWebpage: (url: string) => mockGeneratePdfFromWebpage(url),
  validatePdfContent: (data: Buffer, expectedText: string[]) =>
    mockValidatePdf(data, expectedText),
}));

const TEST_STATEMENT_WITH_INVOICE = getTestStatement(
  TEST_PARTIAL_INVOICE_WITH_STRIPE_DATA,
  TEST_WORK_ITEM
);

describe('Statement service', () => {
  let service: StatementService;
  let s3Service: S3Service;
  let prismaClient: StatementsPrismaClient;
  let invoiceService: InvoiceService;
  let groupsService: GroupsService;
  const logger = mockDeep<Logger>();

  const expectedStatement = {
    data: {
      automaticPayout: false,
      claimedEnergyDelivered:
        TEST_MONTHLY_SITE_STATEMENT.claimedEnergyDelivered,
      emails: '-',
      energyDelivered: TEST_MONTHLY_SITE_STATEMENT.energyDelivered,
      feesGross: TEST_MONTHLY_SITE_STATEMENT.fees.gross,
      feesNet: TEST_MONTHLY_SITE_STATEMENT.fees.net,
      feesVat: TEST_MONTHLY_SITE_STATEMENT.fees.vat,
      numberOfCharges: TEST_MONTHLY_SITE_STATEMENT.numberOfCharges,
      paidEnergyDelivered: TEST_MONTHLY_SITE_STATEMENT.paidEnergyDelivered,
      reference: TEST_MONTHLY_SITE_STATEMENT.reference,
      revenueGross: TEST_MONTHLY_SITE_STATEMENT.revenue.gross,
      revenueNet: TEST_MONTHLY_SITE_STATEMENT.revenue.net,
      revenueVat: TEST_MONTHLY_SITE_STATEMENT.revenue.vat,
      siteAddress: TEST_MONTHLY_SITE_STATEMENT.siteAddress,
      workItem: {
        connect: { id: TEST_WORK_ITEM_ENTITY.id },
      },
      groupConfig: {
        connect: { groupId: TEST_GROUP_CONFIG_ENTITY.groupId },
      },
    },
  };

  const testBuffer = Buffer.alloc(20000);
  const expectedFilename = `${sanitiseString(
    `${TEST_WORK_ITEM_ENTITY.groupName}}/${
      TEST_WORK_ITEM_ENTITY.siteName
    }/${dayjs(TEST_WORK_ITEM_ENTITY.month).format('YYYY')}/${dayjs(
      TEST_WORK_ITEM_ENTITY.month
    ).format('MMMM')}`,
    true
  )}/Statement.pdf`;

  const adjustedFees = [
    {
      fee: 100,
      ppid: 'ppid',
    },
  ];

  beforeAll(async () => {
    const module: TestingModule = await Test.createTestingModule({
      imports: [
        await ConfigModule.forRoot({
          load: [
            () => ({
              S3_GENERATED_DOCUMENTS_BUCKET_NAME:
                'statements-service-generated-documents',
              STATEMENT_SERVICE_WEBAPP_URL: 'http://localhost:5101',
            }),
          ],
        }),
      ],
      providers: [
        GroupsService,
        InvoiceService,
        PrismaHealthIndicator,
        S3Service,
        StatementService,
        StatementsPrismaClient,
        StripeConnectService,
        { provide: SitesApi, useValue: new SitesApi() },
      ],
    }).compile();

    module.useLogger(logger);
    service = module.get<StatementService>(StatementService);
    invoiceService = module.get<InvoiceService>(InvoiceService);
    prismaClient = module.get<StatementsPrismaClient>(StatementsPrismaClient);
    s3Service = module.get<S3Service>(S3Service);
    groupsService = module.get<GroupsService>(GroupsService);
    MockDate.set(new Date(2022, 0, 15, 12, 0, 0, 0));
  });

  afterAll(() => {
    MockDate.reset();
  });

  it('should be defined', () => {
    expect(service).toBeDefined();
    expect(invoiceService).toBeDefined();
  });

  it.each([
    [
      'for a customer with a stripe connected account and transfers enabled',
      TEST_GROUP_WITH_STRIPE_CONNECTED_ACCOUNT,
      'PENDING',
      true,
    ],
    [
      'for a customer with a stripe connected account and transfers disabled',
      {
        ...TEST_GROUP_WITH_STRIPE_CONNECTED_ACCOUNT,
        transfersEnabled: false,
      },
      'DEFERRED',
      true,
    ],
    [
      'for a customer without a stripe connected account',
      TEST_GROUP,
      undefined,
      false,
    ],
  ])(
    'should create a new statement %s',
    async (_, groupConfig, payoutStatus, automaticPayout) => {
      const mockCreateSiteStatement = jest
        .spyOn(global, 'fetch')
        .mockResolvedValueOnce({
          json: () => TEST_MONTHLY_SITE_STATEMENT,
        } as unknown as Response);
      const mockFindWorkItem = (prismaClient.workItem.findUnique = jest
        .fn()
        .mockResolvedValueOnce(TEST_WORK_ITEM_ENTITY));
      const mockFindStatement = (prismaClient.statement.findFirst = jest
        .fn()
        .mockResolvedValue(null));
      const mockCreateStatement = (prismaClient.statement.create = jest
        .fn()
        .mockResolvedValue({ id: TEST_STATEMENT_ENTITY_DEEP.id }));
      const mockFindGroupConfig = jest
        .spyOn(groupsService, 'findGroupByGroupId')
        .mockResolvedValueOnce(groupConfig);
      const mockCreateStatementChargerConfigs =
        (prismaClient.statementChargerConfig.createMany = jest.fn());
      const mockFindChargerConfig = (prismaClient.chargerConfig.findMany =
        jest.fn()).mockResolvedValueOnce([]);
      const mockCreateChargerConfigs = (prismaClient.chargerConfig.createMany =
        jest.fn());
      const mockUpdateWorkItem = (prismaClient.workItem.update = jest.fn());
      const mockPdfGeneration =
        mockGeneratePdfFromWebpage.mockResolvedValueOnce(testBuffer);
      const mockS3Upload = (s3Service.uploadFile = jest.fn());

      const response = await service.createStatement({
        adjustedFees,
        date: '2023-01-01',
        groupUid: TEST_WORK_ITEM_ENTITY.groupId,
        siteId: TEST_WORK_ITEM_ENTITY.siteId,
        workItemId: TEST_WORK_ITEM_ENTITY.id,
      });

      expect(mockCreateSiteStatement).toHaveBeenCalledWith(
        `${SITE_ADMIN_API_URL}/sites/${TEST_WORK_ITEM_ENTITY.siteId}/statement?period=1&groupUid=${TEST_WORK_ITEM_ENTITY.groupId}&dateOverride=2023-01-01`,
        {
          headers: {
            'content-type': 'application/json',
          },
          method: 'POST',
          body: JSON.stringify({ adjustedFees }),
        }
      );
      expect(mockFindWorkItem).toHaveBeenCalledWith({
        include: { user: true },
        where: { id: TEST_WORK_ITEM_ENTITY.id, deletedAt: null },
      });
      expect(mockFindStatement).toHaveBeenCalledWith({
        ...statementDeepIncludeOptions,
        where: { workItemId: TEST_WORK_ITEM_ENTITY.id },
      });
      expect(mockFindChargerConfig).toHaveBeenCalledWith({
        where: { ppid: { in: [adjustedFees[0].ppid] } },
      });
      expect(mockCreateStatement).toHaveBeenCalledWith({
        ...expectedStatement,
        data: {
          ...expectedStatement.data,
          automaticPayout,
          payoutStatus,
        },
      });
      expect(mockFindGroupConfig).toHaveBeenCalledWith(
        TEST_WORK_ITEM_ENTITY.groupId
      );
      expect(mockCreateStatementChargerConfigs).toHaveBeenCalledWith({
        data: [
          {
            fee: 100,
            ppid: 'ppid',
            statementId: expect.anything(),
          },
        ],
      });
      expect(mockCreateChargerConfigs).toHaveBeenCalledWith({
        data: [
          {
            fee: 100,
            ppid: 'ppid',
            siteId: TEST_WORK_ITEM_ENTITY.siteId,
          },
        ],
      });
      expect(mockPdfGeneration).toHaveBeenCalledWith(
        `http://localhost:5101/statements/${TEST_STATEMENT_ENTITY_DEEP.id}`
      );
      expect(mockValidatePdf).toHaveBeenCalledWith(testBuffer, [
        '£0.87',
        '£3,813.33',
      ]);
      expect(mockS3Upload).toHaveBeenCalledWith(
        'statements-service-generated-documents',
        testBuffer,
        'Registers-of-Scotland/Discovery-House/2023/January/Statement.pdf'
      );
      expect(mockUpdateWorkItem).toHaveBeenCalledWith({
        where: { id: TEST_WORK_ITEM_ENTITY.id },
        data: {
          groupName: TEST_MONTHLY_SITE_STATEMENT.groupName,
          siteName: TEST_MONTHLY_SITE_STATEMENT.siteName,
          status: 'GENERATED',
        },
      });
      expect(response).toEqual(TEST_CREATE_STATEMENT_RESPONSE);
    }
  );

  it.each([
    ['for a VAT registered customer', true, expectedStatement],
    [
      'for a non VAT registered customer',
      false,
      {
        ...expectedStatement,
        data: {
          ...expectedStatement.data,
          revenueGross: expectedStatement.data.revenueNet,
          revenueVat: 0,
        },
      },
    ],
  ])('should create a statement %s', async (_, vatRegistered, expected) => {
    jest.spyOn(global, 'fetch').mockResolvedValueOnce({
      json: () => TEST_MONTHLY_SITE_STATEMENT,
    } as unknown as Response);
    prismaClient.workItem.findUnique = jest
      .fn()
      .mockResolvedValueOnce(TEST_WORK_ITEM_ENTITY);
    prismaClient.statement.findFirst = jest.fn().mockResolvedValue(null);
    jest
      .spyOn(groupsService, 'findGroupByGroupId')
      .mockResolvedValueOnce({ ...TEST_GROUP, vatRegistered });
    prismaClient.statementChargerConfig.createMany = jest.fn();
    (prismaClient.chargerConfig.findMany = jest.fn()).mockResolvedValueOnce([]);
    prismaClient.chargerConfig.createMany = jest.fn();
    prismaClient.workItem.update = jest.fn();
    mockGeneratePdfFromWebpage.mockResolvedValueOnce(testBuffer);
    s3Service.uploadFile = jest.fn();

    const mockStatementCreate = (prismaClient.statement.create = jest
      .fn()
      .mockResolvedValue({ id: TEST_STATEMENT_ENTITY_DEEP.id }));

    await service.createStatement({
      adjustedFees,
      date: '2023-01-01',
      groupUid: TEST_WORK_ITEM_ENTITY.groupId,
      siteId: TEST_WORK_ITEM_ENTITY.siteId,
      workItemId: TEST_WORK_ITEM_ENTITY.id,
    });

    expect(mockStatementCreate).toHaveBeenCalledWith(expected);
  });

  it('should create an admin fee invoice for a non-stripe customer', async () => {
    const mockCreateSiteStatement = jest
      .spyOn(global, 'fetch')
      .mockResolvedValueOnce({
        json: () => TEST_MONTHLY_SITE_STATEMENT,
      } as unknown as Response);
    const mockFindWorkItem = (prismaClient.workItem.findUnique = jest
      .fn()
      .mockResolvedValueOnce(TEST_WORK_ITEM_ENTITY));
    const mockFindStatement = (prismaClient.statement.findFirst = jest
      .fn()
      .mockResolvedValue(null));
    const mockCreateStatement = (prismaClient.statement.create = jest
      .fn()
      .mockResolvedValue({ id: TEST_STATEMENT_ENTITY_DEEP.id }));
    const mockFindGroupConfig = jest
      .spyOn(groupsService, 'findGroupByGroupId')
      .mockResolvedValueOnce(TEST_GROUP);
    const mockCreateStatementChargerConfigs =
      (prismaClient.statementChargerConfig.createMany = jest.fn());
    const mockFindChargerConfig = (prismaClient.chargerConfig.findMany =
      jest.fn()).mockResolvedValueOnce([]);
    const mockCreateChargerConfigs = (prismaClient.chargerConfig.createMany =
      jest.fn());
    const mockCreateInvoice = jest
      .spyOn(invoiceService, 'createInvoice')
      .mockResolvedValueOnce(TEST_INVOICE.id);
    const mockUploadInvoicePdfToS3 = jest.spyOn(
      invoiceService,
      'uploadInvoicePdfToS3'
    );
    const mockUpdateWorkItem = (prismaClient.workItem.update = jest.fn());
    const mockPdfGeneration =
      mockGeneratePdfFromWebpage.mockResolvedValueOnce(testBuffer);
    const mockS3Upload = (s3Service.uploadFile = jest.fn());

    const response = await service.createStatement({
      adjustedFees,
      date: '2023-01-01',
      groupUid: TEST_WORK_ITEM_ENTITY.groupId,
      siteId: TEST_WORK_ITEM_ENTITY.siteId,
      workItemId: TEST_WORK_ITEM_ENTITY.id,
    });

    expect(mockCreateSiteStatement).toHaveBeenCalledWith(
      `${SITE_ADMIN_API_URL}/sites/${TEST_WORK_ITEM_ENTITY.siteId}/statement?period=1&groupUid=${TEST_WORK_ITEM_ENTITY.groupId}&dateOverride=2023-01-01`,
      {
        headers: {
          'content-type': 'application/json',
        },
        method: 'POST',
        body: JSON.stringify({ adjustedFees }),
      }
    );
    expect(mockFindWorkItem).toHaveBeenCalledWith({
      include: { user: true },
      where: { id: TEST_WORK_ITEM_ENTITY.id, deletedAt: null },
    });
    expect(mockFindStatement).toHaveBeenCalledWith({
      ...statementDeepIncludeOptions,
      where: { workItemId: TEST_WORK_ITEM_ENTITY.id },
    });
    expect(mockCreateStatement).toHaveBeenCalledWith(expectedStatement);
    expect(mockFindGroupConfig).toHaveBeenCalledWith(
      TEST_WORK_ITEM_ENTITY.groupId
    );
    expect(mockCreateStatementChargerConfigs).toHaveBeenCalledWith({
      data: [
        {
          fee: 100,
          ppid: 'ppid',
          statementId: expect.anything(),
        },
      ],
    });
    expect(mockFindChargerConfig).toHaveBeenCalledWith({
      where: { ppid: { in: [adjustedFees[0].ppid] } },
    });
    expect(mockCreateChargerConfigs).toHaveBeenCalledWith({
      data: [
        {
          fee: 100,
          ppid: 'ppid',
          siteId: TEST_WORK_ITEM_ENTITY.siteId,
        },
      ],
    });
    expect(mockCreateInvoice).toHaveBeenCalledWith(
      TEST_STATEMENT_ENTITY_DEEP.id
    );
    expect(mockUploadInvoicePdfToS3).toHaveBeenCalledWith(
      TEST_INVOICE.id,
      {
        date: '2023-02-13',
        groupName: 'Registers of Scotland',
        siteName: 'Discovery House',
      },
      TEST_MONTHLY_SITE_STATEMENT.fees.net
    );
    expect(
      jest.spyOn(invoiceService, 'createAndUploadStripeInvoice')
    ).not.toHaveBeenCalled();
    expect(mockPdfGeneration).toHaveBeenCalledWith(
      `http://localhost:5101/statements/${TEST_STATEMENT_ENTITY_DEEP.id}`
    );
    expect(mockS3Upload).toHaveBeenCalledWith(
      'statements-service-generated-documents',
      testBuffer,
      'Registers-of-Scotland/Discovery-House/2023/January/Statement.pdf'
    );
    expect(mockUpdateWorkItem).toHaveBeenCalledWith({
      where: { id: TEST_WORK_ITEM_ENTITY.id },
      data: {
        groupName: TEST_MONTHLY_SITE_STATEMENT.groupName,
        siteName: TEST_MONTHLY_SITE_STATEMENT.siteName,
        status: 'GENERATED',
      },
    });
    expect(response).toEqual(TEST_CREATE_STATEMENT_RESPONSE);
  });

  it('should not create an admin fee invoice if there is no account reference number', async () => {
    const mockCreateSiteStatement = jest
      .spyOn(global, 'fetch')
      .mockResolvedValueOnce({
        json: () => TEST_MONTHLY_SITE_STATEMENT,
      } as unknown as Response);
    const mockCreateAdminFee = jest.spyOn(invoiceService, 'createInvoice');
    const mockCreateStatement = (prismaClient.statement.create = jest
      .fn()
      .mockResolvedValue({ id: TEST_STATEMENT_ENTITY_DEEP.id }));
    const mockFindGroupConfig = jest
      .spyOn(groupsService, 'findGroupByGroupId')
      .mockResolvedValueOnce({ ...TEST_GROUP, accountRef: undefined });

    prismaClient.workItem.findUnique = jest
      .fn()
      .mockResolvedValueOnce(TEST_WORK_ITEM_ENTITY);
    prismaClient.statement.findFirst = jest.fn().mockResolvedValue(null);
    prismaClient.statementChargerConfig.createMany = jest.fn();
    prismaClient.chargerConfig.findMany = jest.fn().mockResolvedValueOnce([]);
    prismaClient.chargerConfig.createMany = jest.fn();
    prismaClient.workItem.update = jest.fn();
    mockGeneratePdfFromWebpage.mockResolvedValueOnce(testBuffer);
    s3Service.uploadFile = jest.fn();

    await service.createStatement({
      adjustedFees,
      date: '2023-01-01',
      groupUid: TEST_WORK_ITEM_ENTITY.groupId,
      siteId: TEST_WORK_ITEM_ENTITY.siteId,
      workItemId: TEST_WORK_ITEM_ENTITY.id,
    });

    expect(mockCreateSiteStatement).toHaveBeenCalledWith(
      `${SITE_ADMIN_API_URL}/sites/${TEST_WORK_ITEM_ENTITY.siteId}/statement?period=1&groupUid=${TEST_WORK_ITEM_ENTITY.groupId}&dateOverride=2023-01-01`,
      {
        headers: {
          'content-type': 'application/json',
        },
        method: 'POST',
        body: JSON.stringify({ adjustedFees }),
      }
    );
    expect(mockCreateStatement).toHaveBeenCalledWith(expectedStatement);
    expect(mockFindGroupConfig).toHaveBeenCalledWith(
      TEST_WORK_ITEM_ENTITY.groupId
    );
    expect(mockCreateAdminFee).not.toHaveBeenCalled();
    expect(
      jest.spyOn(invoiceService, 'createAndUploadStripeInvoice')
    ).not.toHaveBeenCalled();
  });

  it('should create an admin fee invoice for a stripe customer', async () => {
    const mockCreateSiteStatement = jest
      .spyOn(global, 'fetch')
      .mockResolvedValueOnce({
        json: () => TEST_MONTHLY_SITE_STATEMENT,
      } as unknown as Response);
    const mockFindWorkItem = (prismaClient.workItem.findUnique = jest
      .fn()
      .mockResolvedValueOnce(TEST_WORK_ITEM_ENTITY));
    const mockFindStatement = (prismaClient.statement.findFirst = jest
      .fn()
      .mockResolvedValue(null));
    const mockCreateStatement = (prismaClient.statement.create = jest
      .fn()
      .mockResolvedValue({ id: TEST_STATEMENT_ENTITY_DEEP.id }));
    const mockFindGroupConfig = jest
      .spyOn(groupsService, 'findGroupByGroupId')
      .mockResolvedValue(TEST_GROUP_WITH_STRIPE_CUSTOMER);
    const mockCreateStatementChargerConfigs =
      (prismaClient.statementChargerConfig.createMany = jest.fn());
    const mockFindChargerConfig = (prismaClient.chargerConfig.findMany =
      jest.fn()).mockResolvedValueOnce([]);
    const mockCreateChargerConfigs = (prismaClient.chargerConfig.createMany =
      jest.fn());
    const mockCreateInvoice = jest
      .spyOn(invoiceService, 'createInvoice')
      .mockResolvedValueOnce(TEST_INVOICE.id);
    const mockUploadInvoicePdfToS3 = jest.spyOn(
      invoiceService,
      'uploadInvoicePdfToS3'
    );
    const mockCreateStripeInvoice = jest.spyOn(
      invoiceService,
      'createAndUploadStripeInvoice'
    );
    const mockUpdateWorkItem = (prismaClient.workItem.update = jest.fn());
    const mockPdfGeneration =
      mockGeneratePdfFromWebpage.mockResolvedValueOnce(testBuffer);
    const mockS3Upload = (s3Service.uploadFile = jest.fn());

    const response = await service.createStatement({
      adjustedFees,
      date: '2023-01-01',
      groupUid: TEST_WORK_ITEM_ENTITY.groupId,
      siteId: TEST_WORK_ITEM_ENTITY.siteId,
      workItemId: TEST_WORK_ITEM_ENTITY.id,
    });

    expect(mockCreateSiteStatement).toHaveBeenCalledWith(
      `${SITE_ADMIN_API_URL}/sites/${TEST_WORK_ITEM_ENTITY.siteId}/statement?period=1&groupUid=${TEST_WORK_ITEM_ENTITY.groupId}&dateOverride=2023-01-01`,
      {
        headers: {
          'content-type': 'application/json',
        },
        method: 'POST',
        body: JSON.stringify({ adjustedFees }),
      }
    );
    expect(mockFindWorkItem).toHaveBeenCalledWith({
      include: { user: true },
      where: { id: TEST_WORK_ITEM_ENTITY.id, deletedAt: null },
    });
    expect(mockFindStatement).toHaveBeenCalledWith({
      ...statementDeepIncludeOptions,
      where: { workItemId: TEST_WORK_ITEM_ENTITY.id },
    });
    expect(mockCreateStatement).toHaveBeenCalledWith(expectedStatement);
    expect(mockFindGroupConfig).toHaveBeenCalledWith(
      TEST_WORK_ITEM_ENTITY.groupId
    );
    expect(mockCreateStatementChargerConfigs).toHaveBeenCalledWith({
      data: [
        {
          fee: 100,
          ppid: 'ppid',
          statementId: expect.anything(),
        },
      ],
    });
    expect(mockFindChargerConfig).toHaveBeenCalledWith({
      where: { ppid: { in: [adjustedFees[0].ppid] } },
    });
    expect(mockCreateChargerConfigs).toHaveBeenCalledWith({
      data: [
        {
          fee: 100,
          ppid: 'ppid',
          siteId: TEST_WORK_ITEM_ENTITY.siteId,
        },
      ],
    });
    expect(mockCreateInvoice).toHaveBeenCalledWith(
      TEST_STATEMENT_ENTITY_DEEP.id
    );
    expect(mockCreateStripeInvoice).toHaveBeenCalledWith(
      TEST_STATEMENT_ENTITY_DEEP.workItem.groupId,
      TEST_INVOICE.id
    );
    expect(mockUploadInvoicePdfToS3).not.toHaveBeenCalled();
    expect(mockPdfGeneration).toHaveBeenCalledWith(
      `http://localhost:5101/statements/${TEST_STATEMENT_ENTITY_DEEP.id}`
    );
    expect(mockS3Upload).toHaveBeenCalledWith(
      'statements-service-generated-documents',
      testBuffer,
      'Registers-of-Scotland/Discovery-House/2023/January/Statement.pdf'
    );
    expect(mockUpdateWorkItem).toHaveBeenCalledWith({
      where: { id: TEST_WORK_ITEM_ENTITY.id },
      data: {
        groupName: TEST_MONTHLY_SITE_STATEMENT.groupName,
        siteName: TEST_MONTHLY_SITE_STATEMENT.siteName,
        status: 'GENERATED',
      },
    });
    expect(response).toEqual(TEST_CREATE_STATEMENT_RESPONSE);
  });

  it('should update existing charger configs when creating a statement', async () => {
    const mockCreateSiteStatement = jest
      .spyOn(global, 'fetch')
      .mockResolvedValueOnce({
        json: () => TEST_MONTHLY_SITE_STATEMENT,
      } as unknown as Response);
    const mockFindWorkItem = (prismaClient.workItem.findUnique = jest
      .fn()
      .mockResolvedValueOnce(TEST_WORK_ITEM_ENTITY));
    const mockFindStatement = (prismaClient.statement.findFirst = jest
      .fn()
      .mockResolvedValue(null));
    const mockFindChargerConfig = (prismaClient.chargerConfig.findMany =
      jest.fn()).mockResolvedValueOnce([TEST_CHARGER_CONFIG_ENTITY]);
    const mockDeleteChargerConfigs = (prismaClient.chargerConfig.deleteMany =
      jest.fn());
    const mockCreateStatement = (prismaClient.statement.create = jest
      .fn()
      .mockResolvedValue({ id: TEST_STATEMENT_ENTITY_DEEP.id }));
    const mockFindGroupConfig = jest
      .spyOn(groupsService, 'findGroupByGroupId')
      .mockResolvedValueOnce(TEST_GROUP);
    const mockCreateStatementChargerConfigs =
      (prismaClient.statementChargerConfig.createMany = jest.fn());
    const mockCreateChargerConfigs = (prismaClient.chargerConfig.createMany =
      jest.fn());
    const mockUpdateWorkItem = (prismaClient.workItem.update = jest.fn());
    const mockPdfGeneration =
      mockGeneratePdfFromWebpage.mockResolvedValueOnce(testBuffer);
    const mockS3Upload = (s3Service.uploadFile = jest.fn());

    const response = await service.createStatement({
      adjustedFees,
      date: '2023-01-01',
      groupUid: TEST_WORK_ITEM_ENTITY.groupId,
      siteId: TEST_WORK_ITEM_ENTITY.siteId,
      workItemId: TEST_WORK_ITEM_ENTITY.id,
    });

    expect(mockCreateSiteStatement).toHaveBeenCalledWith(
      `${SITE_ADMIN_API_URL}/sites/${TEST_WORK_ITEM_ENTITY.siteId}/statement?period=1&groupUid=${TEST_WORK_ITEM_ENTITY.groupId}&dateOverride=2023-01-01`,
      {
        headers: {
          'content-type': 'application/json',
        },
        method: 'POST',
        body: JSON.stringify({ adjustedFees }),
      }
    );
    expect(mockFindWorkItem).toHaveBeenCalledWith({
      include: { user: true },
      where: { id: TEST_WORK_ITEM_ENTITY.id, deletedAt: null },
    });
    expect(mockFindStatement).toHaveBeenCalledWith({
      ...statementDeepIncludeOptions,
      where: { workItemId: TEST_WORK_ITEM_ENTITY.id },
    });
    expect(mockFindChargerConfig).toHaveBeenCalledWith({
      where: { ppid: { in: [adjustedFees[0].ppid] } },
    });
    expect(mockDeleteChargerConfigs).toHaveBeenCalledWith({
      where: {
        ppid: { in: [TEST_CHARGER_CONFIG_ENTITY.ppid] },
      },
    });

    expect(mockCreateSiteStatement).toHaveBeenCalledWith(
      `${SITE_ADMIN_API_URL}/sites/${TEST_WORK_ITEM_ENTITY.siteId}/statement?period=1&groupUid=${TEST_WORK_ITEM_ENTITY.groupId}&dateOverride=2023-01-01`,
      {
        headers: {
          'content-type': 'application/json',
        },
        method: 'POST',
        body: JSON.stringify({ adjustedFees }),
      }
    );
    expect(mockCreateStatement).toHaveBeenCalledWith(expectedStatement);
    expect(mockFindGroupConfig).toHaveBeenCalledWith(
      TEST_WORK_ITEM_ENTITY.groupId
    );
    expect(mockCreateStatementChargerConfigs).toHaveBeenCalledWith({
      data: [
        {
          fee: 100,
          ppid: 'ppid',
          statementId: expect.anything(),
        },
      ],
    });
    expect(mockCreateChargerConfigs).toHaveBeenCalledWith({
      data: [
        {
          fee: 100,
          ppid: 'ppid',
          siteId: TEST_WORK_ITEM_ENTITY.siteId,
        },
      ],
    });
    expect(mockPdfGeneration).toHaveBeenCalledWith(
      `http://localhost:5101/statements/${TEST_STATEMENT_ENTITY_DEEP.id}`
    );
    expect(mockS3Upload).toHaveBeenCalledWith(
      'statements-service-generated-documents',
      testBuffer,
      'Registers-of-Scotland/Discovery-House/2023/January/Statement.pdf'
    );
    expect(mockUpdateWorkItem).toHaveBeenCalledWith({
      where: { id: TEST_WORK_ITEM_ENTITY.id },
      data: {
        groupName: TEST_MONTHLY_SITE_STATEMENT.groupName,
        siteName: TEST_MONTHLY_SITE_STATEMENT.siteName,
        status: 'GENERATED',
      },
    });
    expect(response).toEqual(TEST_CREATE_STATEMENT_RESPONSE);
  });

  it('should throw an exception if the work item is not found when creating a statement', async () => {
    prismaClient.workItem.findUnique = jest.fn().mockResolvedValue(null);

    await expect(
      service.createStatement({
        adjustedFees,
        date: '2023-01-01',
        groupUid: TEST_WORK_ITEM_ENTITY.groupId,
        siteId: TEST_WORK_ITEM_ENTITY.siteId,
        workItemId: TEST_WORK_ITEM_ENTITY.id,
      })
    ).rejects.toThrow(StatementWorkItemNotFoundException);
  });

  it('should overwrite an existing statement', async () => {
    const mockCreateSiteStatement = jest
      .spyOn(global, 'fetch')
      .mockResolvedValueOnce({
        json: () => TEST_MONTHLY_SITE_STATEMENT,
      } as unknown as Response);
    const mockFindWorkItem = (prismaClient.workItem.findUnique = jest
      .fn()
      .mockResolvedValueOnce(TEST_WORK_ITEM_ENTITY));
    const mockFindStatement = (prismaClient.statement.findFirst = jest
      .fn()
      .mockResolvedValue({
        ...TEST_STATEMENT_ENTITY_DEEP,
        workItem: {
          ...TEST_WORK_ITEM_ENTITY,
          status: Status.GENERATED,
        },
      }));
    const mockDeleteStatement = (prismaClient.statement.delete = jest.fn());
    const mockCreateStatement = (prismaClient.statement.create = jest
      .fn()
      .mockResolvedValue({ id: TEST_STATEMENT_ENTITY_DEEP.id }));
    const mockFindGroupConfig = jest
      .spyOn(groupsService, 'findGroupByGroupId')
      .mockResolvedValueOnce(TEST_GROUP);
    const mockCreateStatementChargerConfigs =
      (prismaClient.statementChargerConfig.createMany = jest.fn());
    const mockFindChargerConfig = (prismaClient.chargerConfig.findMany =
      jest.fn()).mockResolvedValue([]);
    const mockCreateChargerConfigs = (prismaClient.chargerConfig.createMany =
      jest.fn());
    const mockUpdateWorkItem = (prismaClient.workItem.update = jest.fn());
    const mockPdfGeneration =
      mockGeneratePdfFromWebpage.mockResolvedValueOnce(testBuffer);

    const response = await service.createStatement({
      adjustedFees,
      date: '2023-01-01',
      groupUid: TEST_WORK_ITEM_ENTITY.groupId,
      siteId: TEST_WORK_ITEM_ENTITY.siteId,
      workItemId: TEST_WORK_ITEM_ENTITY.id,
    });

    expect(mockCreateSiteStatement).toHaveBeenCalledWith(
      `${SITE_ADMIN_API_URL}/sites/${TEST_WORK_ITEM_ENTITY.siteId}/statement?period=1&groupUid=${TEST_WORK_ITEM_ENTITY.groupId}&dateOverride=2023-01-01`,
      {
        headers: {
          'content-type': 'application/json',
        },
        method: 'POST',
        body: JSON.stringify({ adjustedFees }),
      }
    );
    expect(mockFindWorkItem).toHaveBeenCalledWith({
      include: { user: true },
      where: { id: TEST_WORK_ITEM_ENTITY.id, deletedAt: null },
    });
    expect(mockFindStatement).toHaveBeenCalledWith({
      ...statementDeepIncludeOptions,
      where: { workItemId: TEST_WORK_ITEM_ENTITY.id },
    });
    expect(mockDeleteStatement).toHaveBeenCalledWith({
      where: { id: TEST_STATEMENT_ENTITY_DEEP.id },
    });
    expect(mockCreateStatement).toHaveBeenCalledWith(expectedStatement);
    expect(mockFindGroupConfig).toHaveBeenCalledWith(
      TEST_WORK_ITEM_ENTITY.groupId
    );
    expect(mockCreateStatementChargerConfigs).toHaveBeenCalledWith({
      data: [
        {
          fee: 100,
          ppid: 'ppid',
          statementId: expect.anything(),
        },
      ],
    });
    expect(mockFindChargerConfig).toHaveBeenCalledWith({
      where: { ppid: { in: [adjustedFees[0].ppid] } },
    });
    expect(mockCreateChargerConfigs).toHaveBeenCalledWith({
      data: [
        {
          fee: 100,
          ppid: 'ppid',
          siteId: TEST_WORK_ITEM_ENTITY.siteId,
        },
      ],
    });
    expect(mockPdfGeneration).toHaveBeenCalledWith(
      `http://localhost:5101/statements/${TEST_STATEMENT_ENTITY_DEEP.id}`
    );
    expect(mockUpdateWorkItem).toHaveBeenCalledWith({
      where: { id: TEST_WORK_ITEM_ENTITY.id },
      data: {
        groupName: TEST_MONTHLY_SITE_STATEMENT.groupName,
        siteName: TEST_MONTHLY_SITE_STATEMENT.siteName,
        status: Status.GENERATED,
      },
    });
    expect(response).toEqual(TEST_CREATE_STATEMENT_RESPONSE);
  });

  it('should throw an error if the pdf does not contain the expected content', async () => {
    mockValidatePdf.mockResolvedValueOnce(false);
    jest.spyOn(global, 'fetch').mockResolvedValueOnce({
      json: () => TEST_MONTHLY_SITE_STATEMENT,
    } as unknown as Response);
    prismaClient.workItem.findUnique = jest
      .fn()
      .mockResolvedValue(TEST_WORK_ITEM_ENTITY);
    prismaClient.statement.findFirst = jest.fn().mockResolvedValue(null);
    prismaClient.statement.create = jest
      .fn()
      .mockResolvedValue({ id: TEST_STATEMENT_ENTITY_DEEP.id });
    prismaClient.chargerConfig.findMany = jest.fn().mockResolvedValue([]);
    prismaClient.statementChargerConfig.createMany = jest.fn();
    prismaClient.chargerConfig.createMany = jest.fn();
    jest
      .spyOn(groupsService, 'findGroupByGroupId')
      .mockResolvedValueOnce(TEST_GROUP);
    const mockUpdateWorkItem = (prismaClient.workItem.update = jest.fn());
    const mockPdfGeneration = mockGeneratePdfFromWebpage.mockResolvedValueOnce(
      Buffer.alloc(20000)
    );
    const mockS3Upload = (s3Service.uploadFile = jest.fn());

    await expect(
      service.createStatement({
        adjustedFees,
        date: '2023-01-01',
        groupUid: TEST_WORK_ITEM_ENTITY.groupId,
        siteId: TEST_WORK_ITEM_ENTITY.siteId,
        workItemId: TEST_WORK_ITEM_ENTITY.id,
      })
    ).rejects.toThrow(PDFGenerationFailedException);

    expect(mockPdfGeneration).toHaveBeenCalledWith(
      'http://localhost:5101/statements/0a69aa8f-fa98-4488-a113-4ea8aca29e5d'
    );
    expect(mockS3Upload).not.toHaveBeenCalled();
    expect(mockUpdateWorkItem).not.toHaveBeenCalled();
  });

  it('should throw an error if the pdf fails to generate', async () => {
    mockValidatePdf.mockRejectedValueOnce(new PDFGenerationFailedException());
    jest.spyOn(global, 'fetch').mockResolvedValueOnce({
      json: () => TEST_MONTHLY_SITE_STATEMENT,
    } as unknown as Response);
    prismaClient.workItem.findUnique = jest
      .fn()
      .mockResolvedValue(TEST_WORK_ITEM_ENTITY);
    prismaClient.statement.findFirst = jest.fn().mockResolvedValue(null);
    prismaClient.statement.create = jest
      .fn()
      .mockResolvedValue({ id: TEST_STATEMENT_ENTITY_DEEP.id });
    prismaClient.chargerConfig.findMany = jest.fn().mockResolvedValue([]);
    prismaClient.statementChargerConfig.createMany = jest.fn();
    prismaClient.chargerConfig.createMany = jest.fn();
    jest
      .spyOn(groupsService, 'findGroupByGroupId')
      .mockResolvedValueOnce(TEST_GROUP);
    const mockUpdateWorkItem = (prismaClient.workItem.update = jest.fn());
    const mockPdfGeneration = mockGeneratePdfFromWebpage.mockResolvedValueOnce(
      Buffer.alloc(20_000)
    );
    const mockS3Upload = (s3Service.uploadFile = jest.fn());

    await expect(
      service.createStatement({
        adjustedFees,
        date: '2023-01-01',
        groupUid: TEST_WORK_ITEM_ENTITY.groupId,
        siteId: TEST_WORK_ITEM_ENTITY.siteId,
        workItemId: TEST_WORK_ITEM_ENTITY.id,
      })
    ).rejects.toThrow(PDFGenerationFailedException);

    expect(mockPdfGeneration).toHaveBeenCalledWith(
      'http://localhost:5101/statements/0a69aa8f-fa98-4488-a113-4ea8aca29e5d'
    );
    expect(mockS3Upload).not.toHaveBeenCalled();
    expect(mockUpdateWorkItem).not.toHaveBeenCalled();
  });

  it('should throw an invalid status transition exception if "GENERATED" is not a valid transition', async () => {
    jest.spyOn(global, 'fetch').mockResolvedValueOnce({
      json: () => TEST_MONTHLY_SITE_STATEMENT,
    } as unknown as Response);
    prismaClient.workItem.findUnique = jest
      .fn()
      .mockResolvedValue(TEST_WORK_ITEM_ENTITY);
    const mockFindStatement = (prismaClient.statement.findFirst = jest
      .fn()
      .mockResolvedValueOnce({
        ...TEST_STATEMENT_ENTITY_DEEP,
        workItem: {
          ...TEST_WORK_ITEM_ENTITY,
          status: Status.SENT,
        },
      }));

    await expect(
      service.createStatement({
        adjustedFees,
        date: '2023-01-01',
        groupUid: TEST_WORK_ITEM_ENTITY.groupId,
        siteId: TEST_WORK_ITEM_ENTITY.siteId,
        workItemId: TEST_WORK_ITEM_ENTITY.id,
      })
    ).rejects.toThrow(InvalidStatusTransitionException);
    expect(mockFindStatement).toHaveBeenCalledWith({
      ...statementDeepIncludeOptions,
      where: { workItemId: TEST_WORK_ITEM_ENTITY.id },
    });
  });

  it.each(['PAID_OUT', 'TRANSFERRED'])(
    'should throw an exception if statement payout status is %s',
    async (payoutStatus) => {
      prismaClient.workItem.findUnique = jest
        .fn()
        .mockResolvedValue(TEST_WORK_ITEM_ENTITY);
      prismaClient.statement.findFirst = jest.fn().mockResolvedValueOnce({
        ...TEST_STATEMENT_ENTITY_DEEP,
        payoutStatus: payoutStatus,
        workItem: {
          ...TEST_WORK_ITEM_ENTITY,
          status: Status.READY,
        },
      });

      await expect(
        service.createStatement({
          adjustedFees,
          date: '2023-01-01',
          groupUid: TEST_WORK_ITEM_ENTITY.groupId,
          siteId: TEST_WORK_ITEM_ENTITY.siteId,
          workItemId: TEST_WORK_ITEM_ENTITY.id,
        })
      ).rejects.toThrow(StatementAlreadyPaidOutException);

      expect(prismaClient.statement.findFirst).toHaveBeenCalledWith({
        ...statementDeepIncludeOptions,
        where: { workItemId: TEST_WORK_ITEM_ENTITY.id },
      });
    }
  );

  it('should fetch all the statements', async () => {
    const mockFindMany = (prismaClient.statement.findMany = jest
      .fn()
      .mockReturnValueOnce([TEST_STATEMENT_ENTITY_DEEP]));

    const statements = await service.findAllStatements(
      '2023-01-01',
      undefined,
      WorkItemStatus.NEW
    );

    expect(mockFindMany).toHaveBeenCalledWith({
      where: {
        workItem: {
          month: new Date('2023-01-01T00:00:00.000Z'),
          status: 'NEW',
        },
      },
      include: {
        invoice: true,
        workItem: {
          include: {
            user: true,
          },
        },
        statementChargerConfig: true,
      },
      orderBy: [
        {
          workItem: {
            month: 'desc',
          },
        },
        {
          workItem: {
            groupName: 'asc',
          },
        },
        {
          workItem: {
            siteName: 'asc',
          },
        },
      ],
    });

    expect(statements).toEqual([TEST_STATEMENT_WITH_INVOICE]);
  });

  it('should fetch all the statements for a given group', async () => {
    const mockFindMany = (prismaClient.statement.findMany = jest
      .fn()
      .mockReturnValueOnce([TEST_STATEMENT_ENTITY_DEEP]));

    const statements = await service.findAllStatements(
      '2023-01-01',
      '725fead1-a43b-468b-a500-a279d8a47f95',
      WorkItemStatus.NEW
    );

    expect(mockFindMany).toHaveBeenCalledWith({
      where: {
        workItem: {
          month: new Date('2023-01-01T00:00:00.000Z'),
          status: 'NEW',
          groupId: '725fead1-a43b-468b-a500-a279d8a47f95',
        },
      },
      include: {
        invoice: true,
        workItem: {
          include: {
            user: true,
          },
        },
        statementChargerConfig: true,
      },
      orderBy: [
        {
          workItem: {
            month: 'desc',
          },
        },
        {
          workItem: {
            groupName: 'asc',
          },
        },
        {
          workItem: {
            siteName: 'asc',
          },
        },
      ],
    });

    expect(statements).toEqual([TEST_STATEMENT_WITH_INVOICE]);
  });

  it('should fetch a statement', async () => {
    const mockFindUnique = (prismaClient.statement.findUnique = jest
      .fn()
      .mockReturnValueOnce(TEST_STATEMENT_ENTITY_DEEP));

    const statement = await service.findStatement(TEST_STATEMENT.id);

    expect(mockFindUnique).toHaveBeenCalledWith({
      include: {
        invoice: true,
        statementChargerConfig: true,
        workItem: {
          include: {
            user: true,
          },
        },
      },
      where: {
        deletedAt: null,
        id: '0a69aa8f-fa98-4488-a113-4ea8aca29e5d',
      },
    });

    expect(statement).toEqual(TEST_STATEMENT_WITH_INVOICE);
  });

  it('should throw an exception if the statement is not found', async () => {
    prismaClient.statement.findUnique = jest.fn().mockReturnValueOnce(null);

    await expect(service.findStatement(TEST_STATEMENT.id)).rejects.toThrow(
      StatementNotFoundException
    );
  });

  it('should get the most recent sent statement for a given site', async () => {
    const mockFindStatement = (prismaClient.statement.findFirst = jest
      .fn()
      .mockResolvedValue(TEST_STATEMENT_ENTITY_DEEP));

    const statement = await service.getMostRecentSentStatementBySiteId(
      TEST_WORK_ITEM_ENTITY.siteId
    );

    expect(statement).toEqual(TEST_STATEMENT_WITH_INVOICE);
    expect(mockFindStatement).toHaveBeenCalledWith({
      ...statementDeepIncludeOptions,
      where: {
        workItem: { siteId: TEST_WORK_ITEM_ENTITY.siteId, status: Status.SENT },
      },
      orderBy: { createdAt: 'desc' },
    });
  });

  it('should return undefined if there is no recent statement', async () => {
    prismaClient.statement.findFirst = jest.fn().mockResolvedValue(null);

    expect(
      await service.getMostRecentSentStatementBySiteId(
        TEST_WORK_ITEM_ENTITY.siteId
      )
    ).toEqual(undefined);
  });

  it('should download an existing statement from s3', async () => {
    const mockFind = (prismaClient.statement.findUnique = jest
      .fn()
      .mockReturnValueOnce(TEST_STATEMENT_ENTITY_DEEP));
    const mocks3Download = (s3Service.getFile = jest
      .fn()
      .mockReturnValueOnce(testBuffer));

    const result = await service.getStatementPdf(TEST_STATEMENT.id);

    expect(mockFind).toHaveBeenCalledWith({
      include: {
        invoice: true,
        statementChargerConfig: true,
        workItem: {
          include: {
            user: true,
          },
        },
      },
      where: {
        deletedAt: null,
        id: TEST_STATEMENT.id,
      },
    });
    expect(mocks3Download).toHaveBeenCalledWith(
      'statements-service-generated-documents',
      expectedFilename
    );

    expect(result).toEqual({ filename: expectedFilename, buffer: testBuffer });
  });

  it('should throw an exception when attempting to download if there is no workitem present on a statement', async () => {
    const mockFind = (prismaClient.statement.findUnique = jest
      .fn()
      .mockReturnValueOnce(TEST_STATEMENT_ENTITY));

    await expect(service.getStatementPdf(TEST_STATEMENT.id)).rejects.toThrow(
      StatementWorkItemNotFoundException
    );

    expect(mockFind).toHaveBeenCalledWith({
      include: {
        invoice: true,
        statementChargerConfig: true,
        workItem: {
          include: {
            user: true,
          },
        },
      },
      where: {
        deletedAt: null,
        id: TEST_STATEMENT.id,
      },
    });
  });

  it('should update deferred statements if transfers enabled is true', async () => {
    const mockFindStatements = (prismaClient.statement.findMany = jest
      .fn()
      .mockResolvedValueOnce([
        {
          ...TEST_STATEMENT_ENTITY_DEEP,
          groupConfig: { ...TEST_GROUP_CONFIG_ENTITY, transfersEnabled: true },
        },
      ]));
    const mockUpdateStatements = (prismaClient.statement.updateMany =
      jest.fn());

    await service.updateDeferredStatements();

    expect(mockFindStatements).toHaveBeenCalledWith({
      include: {
        groupConfig: true,
        invoice: true,
        statementChargerConfig: true,
        workItem: {
          include: {
            user: true,
          },
        },
      },
      where: {
        deletedAt: null,
        payoutStatus: PayoutStatus.DEFERRED,
        workItem: {
          month: {
            lt: dayjs('2021-12-01').toDate(),
          },
        },
      },
    });

    expect(mockUpdateStatements).toHaveBeenCalledWith({
      data: {
        payoutStatus: PayoutStatus.PENDING,
      },
      where: {
        id: {
          in: [TEST_STATEMENT_ENTITY_DEEP.id],
        },
      },
    });
  });

  it('should not update deferred statements if transfers enabled is false', async () => {
    const mockFindStatements = (prismaClient.statement.findMany = jest
      .fn()
      .mockResolvedValueOnce([
        {
          ...TEST_STATEMENT_ENTITY_DEEP,
          groupConfig: { ...TEST_GROUP_CONFIG_ENTITY, transfersEnabled: false },
        },
      ]));
    const mockUpdateStatements = (prismaClient.statement.updateMany =
      jest.fn());

    await service.updateDeferredStatements();

    expect(mockFindStatements).toHaveBeenCalledWith({
      include: {
        groupConfig: true,
        invoice: true,
        statementChargerConfig: true,
        workItem: {
          include: {
            user: true,
          },
        },
      },
      where: {
        deletedAt: null,
        payoutStatus: PayoutStatus.DEFERRED,
        workItem: {
          month: {
            lt: dayjs('2021-12-01').toDate(),
          },
        },
      },
    });

    expect(mockUpdateStatements).not.toHaveBeenCalled();
  });

  it.each([
    ['PENDING', ['WARNING']],
    ['WARNING', ['PENDING']],
    ['TRANSFERRED', ['PENDING', 'WARNING']],
    ['PAID_OUT', ['TRANSFERRED']],
  ])(
    'should update statement payout status to %s by connected account id',
    async (payoutStatus, previousStatuses) => {
      const mockFindGroup = (prismaClient.groupConfig.findMany = jest
        .fn()
        .mockResolvedValueOnce([TEST_GROUP]));
      const mockFindMany = (prismaClient.statement.findMany = jest
        .fn()
        .mockResolvedValueOnce([TEST_STATEMENT_ENTITY_DEEP]));
      const mockUpdate = (prismaClient.statement.updateMany = jest.fn());

      await service.updateCurrentStatementsPayoutStatusByConnectedAccountId(
        'acct_123456789',
        payoutStatus as PayoutStatus
      );

      expect(mockFindGroup).toHaveBeenCalledWith({
        where: {
          stripeConnectedAccountId: 'acct_123456789',
        },
      });
      expect(mockFindMany).toHaveBeenCalledWith({
        include: {
          invoice: true,
          statementChargerConfig: true,
          workItem: {
            include: {
              user: true,
            },
          },
        },
        where: {
          workItem: {
            deletedAt: null,
            groupId: { in: [TEST_GROUP.groupId] },
          },
          payoutStatus: {
            in: previousStatuses,
          },
        },
      });

      expect(mockUpdate).toHaveBeenCalledWith({
        data: {
          payoutStatus,
        },
        where: {
          id: {
            in: [TEST_STATEMENT_ENTITY_DEEP.id],
          },
        },
      });
    }
  );

  it('should get a list of stripe connected accounts', async () => {
    const mockFindMany = (prismaClient.groupConfig.findMany = jest
      .fn()
      .mockReturnValueOnce([TEST_GROUP_WITH_STRIPE_CONNECTED_ACCOUNT]));

    const connectedAccounts = await service.getStripeConnectedAccountGroups();

    expect(mockFindMany).toHaveBeenCalledWith({
      where: {
        stripeConnectedAccountId: {
          not: null,
        },
      },
    });

    expect(connectedAccounts).toEqual([
      TEST_GROUP_WITH_STRIPE_CONNECTED_ACCOUNT,
    ]);
  });

  it('should get a list of statements to payout', async () => {
    const mockFindMany = (prismaClient.statement.findMany = jest
      .fn()
      .mockReturnValueOnce([TEST_STATEMENT_ENTITY_DEEP]));
    const groupId = TEST_GROUP_CONFIG_ENTITY.id;

    const statements = await service.getStatementsToPayout(groupId);

    expect(mockFindMany).toHaveBeenCalledWith({
      include: {
        invoice: true,
        statementChargerConfig: true,
        workItem: {
          include: {
            user: true,
          },
        },
      },
      where: {
        OR: [
          {
            automaticPayout: true,
            deletedAt: null,
            payoutStatus: 'PENDING',
            stripeTransferId: null,
            workItem: {
              deletedAt: null,
              groupId,
              month: {
                lte: firstOfLastMonth(),
              },
            },
          },
          {
            automaticPayout: true,
            deletedAt: null,
            payoutStatus: 'WARNING',
            stripeTransferId: null,
            workItem: {
              deletedAt: null,
              groupId,
              month: {
                lte: firstOfLastMonth(),
              },
            },
          },
        ],
      },
    });
    expect(statements).toEqual([TEST_STATEMENT_WITH_INVOICE]);
  });

  it('should update statements payout status when the payout is transferred', async () => {
    const mockUpdate = (prismaClient.statement.updateMany = jest.fn());

    await service.markPayoutsAsTransferred(
      [TEST_STATEMENT.id],
      'stripe-transfer-id'
    );

    expect(mockUpdate).toHaveBeenCalledWith({
      data: {
        payoutStatus: 'TRANSFERRED',
        stripeTransferId: 'stripe-transfer-id',
      },
      where: {
        id: {
          in: [TEST_STATEMENT.id],
        },
      },
    });
  });
});
