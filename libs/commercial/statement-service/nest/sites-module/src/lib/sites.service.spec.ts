import * as MockDate from 'mockdate';
import { ConfigService } from '@nestjs/config';
import { Logger } from '@nestjs/common';
import { PrismaHealthIndicator } from '@nestjs/terminus';
import { SiteNotFoundException } from '@experience/commercial/statement-service/nest/shared';
import { SitesService } from './sites.service';
import {
  StatementsPrismaClient,
  TEST_CHARGER_CONFIG_ENTITY,
  TEST_SITE_CONFIG_ENTITY,
} from '@experience/commercial/statement-service/prisma/statements/client';
import {
  TEST_CHARGER_WITH_POD,
  TEST_GROUP,
  TEST_SITE,
  TEST_UPDATE_CHARGER_REQUEST,
  TEST_UPDATE_SITE_REQUEST,
} from '@experience/commercial/statement-service/shared';
import { Test, TestingModule } from '@nestjs/testing';
import { createTestPod } from '@experience/commercial/site-admin/typescript/domain-model';
import { mockDeep } from 'jest-mock-extended';

describe('Site service', () => {
  let service: SitesService;
  let prismaClient: StatementsPrismaClient;
  const logger = mockDeep<Logger>();

  beforeAll(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        ConfigService,
        PrismaHealthIndicator,
        SitesService,
        StatementsPrismaClient,
      ],
    }).compile();

    module.useLogger(logger);
    service = module.get<SitesService>(SitesService);
    prismaClient = module.get<StatementsPrismaClient>(StatementsPrismaClient);
    MockDate.set(new Date(2022, 0, 15, 12, 0, 0, 0));
  });

  afterAll(() => {
    MockDate.reset();
  });

  it('should be defined', () => {
    expect(service).toBeDefined();
  });

  it('should fetch a site by site id', async () => {
    const mockFind = (prismaClient.siteConfig.findUnique = jest
      .fn()
      .mockReturnValueOnce(TEST_SITE_CONFIG_ENTITY));

    const siteConfig = await service.findSite(TEST_SITE.siteId);

    expect(mockFind).toHaveBeenCalledWith({
      where: {
        siteId: 'a531e4e4-1486-4b4c-b1ec-f82aa8a8c346',
      },
    });

    expect(siteConfig).toEqual(TEST_SITE);
  });

  it('should throw an exception if the site is not found', async () => {
    prismaClient.siteConfig.findUnique = jest.fn().mockReturnValueOnce(null);

    await expect(service.findSite(TEST_SITE.siteId)).rejects.toThrow(
      SiteNotFoundException
    );
  });

  it('should fetch site by group id', async () => {
    const mockFindMany = (prismaClient.siteConfig.findMany = jest
      .fn()
      .mockReturnValueOnce([TEST_SITE_CONFIG_ENTITY]));

    const siteConfigs = await service.findSiteByGroupId(TEST_GROUP.groupId);
    expect(mockFindMany).toHaveBeenCalledWith({
      where: {
        groupId: TEST_GROUP.groupId,
      },
    });
    expect(siteConfigs).toEqual([TEST_SITE]);
  });

  it('should find or create a site', async () => {
    const mockUpsert = (prismaClient.siteConfig.upsert = jest.fn());

    await service.findOrUpsertSite({
      groupId: TEST_SITE.groupId,
      siteId: TEST_SITE.siteId,
      siteName: TEST_SITE.siteName,
    });

    expect(mockUpsert).toHaveBeenCalledWith({
      where: { siteId: TEST_SITE.siteId },
      create: {
        siteId: TEST_SITE.siteId,
        groupId: TEST_SITE.groupId,
        siteName: TEST_SITE.siteName,
        emails: '',
        automated: false,
      },
      update: {
        groupId: TEST_SITE.groupId,
        siteName: TEST_SITE.siteName,
      },
    });
  });

  it('should update a site', async () => {
    const mockFind = (prismaClient.siteConfig.findUnique = jest
      .fn()
      .mockReturnValueOnce(TEST_SITE_CONFIG_ENTITY));

    const mockUpdate = (prismaClient.siteConfig.update = jest.fn());

    await service.updateSite(TEST_SITE.siteId, TEST_UPDATE_SITE_REQUEST);

    expect(mockFind).toHaveBeenCalledWith({
      where: { siteId: TEST_SITE_CONFIG_ENTITY.siteId },
    });
    expect(mockUpdate).toHaveBeenCalledWith({
      where: { siteId: TEST_SITE_CONFIG_ENTITY.siteId },
      data: {
        automated: false,
        emails: '<EMAIL>,<EMAIL>',
      },
    });
  });

  it('should throw an exception if the site is not found', async () => {
    prismaClient.siteConfig.findUnique = jest.fn().mockReturnValueOnce(null);

    await expect(
      service.updateSite(TEST_SITE.siteId, TEST_UPDATE_SITE_REQUEST)
    ).rejects.toThrow(SiteNotFoundException);
  });

  it('should fetch the all chargers by site id', async () => {
    const TEST_POD_1 = createTestPod({ ppid: 'PG-70500' });
    const TEST_POD_2 = createTestPod({ ppid: 'PP-734676' });
    const mockFindSiteConfig = (service.findSite = jest
      .fn()
      .mockReturnValueOnce(TEST_SITE));
    const mockSite = {
      ...TEST_SITE,
      pods: [TEST_POD_1, TEST_POD_2],
    };
    const mockGetPods = jest.spyOn(global, 'fetch').mockResolvedValueOnce({
      ok: true,
      json: jest.fn().mockResolvedValueOnce(mockSite),
      status: 200,
    } as unknown as Response);
    const mockFindMany = (prismaClient.chargerConfig.findMany = jest
      .fn()
      .mockReturnValueOnce([TEST_CHARGER_CONFIG_ENTITY]));

    const chargerConfigs = await service.getAllChargersWithPods(
      TEST_SITE.siteId
    );

    expect(mockFindSiteConfig).toHaveBeenCalledWith(
      'a531e4e4-1486-4b4c-b1ec-f82aa8a8c346'
    );
    expect(mockGetPods).toHaveBeenCalledWith(
      'http://localhost:4102/sites/a531e4e4-1486-4b4c-b1ec-f82aa8a8c346?groupUid=036c0720-f908-45fc-ae7c-031a47c2e278',
      { headers: { 'content-type': 'application/json' }, method: 'GET' }
    );
    expect(mockFindMany).toHaveBeenCalledWith({
      where: { siteId: 'a531e4e4-1486-4b4c-b1ec-f82aa8a8c346' },
    });
    expect(chargerConfigs).toEqual([
      {
        fee: 0.29,
        pod: TEST_POD_1,
        ppid: 'PG-70500',
        siteId: 'a531e4e4-1486-4b4c-b1ec-f82aa8a8c346',
      },
      {
        fee: null,
        pod: TEST_POD_2,
        ppid: 'PP-734676',
        siteId: 'a531e4e4-1486-4b4c-b1ec-f82aa8a8c346',
      },
    ]);
  });

  it('should update the chargers by ppid', async () => {
    const mockFindChargerConfig = (prismaClient.chargerConfig.findMany =
      jest.fn()).mockResolvedValueOnce([TEST_CHARGER_CONFIG_ENTITY]);
    const mockDeleteMany = (prismaClient.chargerConfig.deleteMany = jest.fn());
    const mockCreateMany = (prismaClient.chargerConfig.createMany = jest.fn());

    await service.updateChargerConfig(
      TEST_SITE.siteId,
      TEST_UPDATE_CHARGER_REQUEST
    );

    expect(mockFindChargerConfig).toHaveBeenCalledWith({
      where: {
        ppid: {
          in: ['PG-70500', 'PG-70501'],
        },
      },
    });
    expect(mockDeleteMany).toHaveBeenCalledWith({
      where: { ppid: { in: [TEST_CHARGER_WITH_POD.ppid] } },
    });
    expect(mockCreateMany).toHaveBeenCalledWith({
      data: [{ fee: 0.29, ppid: 'PG-70500', siteId: '75710' }],
    });
  });
});
