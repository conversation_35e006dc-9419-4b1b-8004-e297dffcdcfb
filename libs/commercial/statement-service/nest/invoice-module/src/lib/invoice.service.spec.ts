import * as MockDate from 'mockdate';
import { ConfigModule } from '@nestjs/config';
import { GroupsService } from '@experience/commercial/statement-service/nest/groups-module';
import {
  Invoice,
  StatementEntityNoInvoice,
  StatementsPrismaClient,
  TEST_GROUP_CONFIG_ENTITY_WITH_PO_NUMBER,
  TEST_INVOICE_ENTITY,
  TEST_INVOICE_ENTITY_WITH_STRIPE_DATA,
  TEST_STATEMENT_ENTITY_DEEP,
  invoiceDeepIncludeOptions,
  statementNoInvoiceIncludeOptions,
} from '@experience/commercial/statement-service/prisma/statements/client';
import {
  InvoiceNotUpdatableException,
  InvoiceStatusInvalidException,
} from './invoice.exception';
import { InvoiceService } from './invoice.service';
import {
  MOCK_UPDATE_INVOICE_STATUS_REQUEST_PAID,
  MOCK_UPDATE_INVOICE_STATUS_REQUEST_VOID,
  TEST_GROUP,
  TEST_GROUP_WITH_STRIPE_CUSTOMER,
  TEST_INVOICE,
  TEST_INVOICE_WITH_STRIPE_DATA,
  TEST_REISSUE_STRIPE_INVOICE_REQUEST,
  TEST_SITE,
  TEST_STATEMENT,
  TEST_WORK_ITEM,
} from '@experience/commercial/statement-service/shared';
import { PDFGenerationFailedException } from '@experience/commercial/statement-service/nest/shared';
import { PrismaHealthIndicator } from '@nestjs/terminus';
import { S3Service } from '@experience/shared/nest/aws/s3-module';
import { SitesService } from '@experience/commercial/statement-service/nest/sites-module';
import { Stripe } from 'stripe';
import {
  StripeCustomerService,
  StripeInvoiceService,
  TEST_STRIPE_INVOICE_PDF_URL,
} from '@experience/shared/nest/stripe';
import { TEST_MONTHLY_SITE_STATEMENT } from '@experience/commercial/site-admin/typescript/domain-model';
import { Test } from '@nestjs/testing';
import { sanitiseString } from '@experience/shared/typescript/utils';
import { setIn } from 'immutable';
import dayjs from 'dayjs';

const mockPdfGenerate = jest.fn();
const mockValidatePdf = jest.fn().mockResolvedValue(true);

jest.mock('@experience/shared/nest/aws/s3-module');
jest.mock('@experience/commercial/statement-service/nest/groups-module');
jest.mock('@experience/commercial/statement-service/nest/sites-module');
jest.mock('@experience/shared/nest/stripe');
jest.mock('@experience/commercial/statement-service/nest/shared', () => ({
  ...jest.requireActual('@experience/commercial/statement-service/nest/shared'),
  generatePdfFromWebpage: (url: string) => mockPdfGenerate(url),
  validatePdfContent: (data: Buffer, expectedText: string[]) =>
    mockValidatePdf(data, expectedText),
}));

const TEST_FILENAME_DETAILS = {
  date: TEST_WORK_ITEM.month,
  groupName: TEST_GROUP.groupName,
  siteName: TEST_SITE.siteName,
};

const TEST_STRIPE_INVOICE_RESPONSE = {
  invoiceId: 'in_1MtHbELkdIwHu7ixl4OzzPMv',
};

export const TEST_INVOICE_ENTITY_WITH_STATEMENT: Invoice & {
  statement: StatementEntityNoInvoice;
} = {
  ...TEST_INVOICE_ENTITY,
  statementId: TEST_STATEMENT_ENTITY_DEEP.id,
  statement: setIn(TEST_STATEMENT_ENTITY_DEEP, ['invoice'], null),
};

describe('Invoice service', () => {
  let service: InvoiceService;

  let groupsService: GroupsService;
  let s3Service: S3Service;
  let sitesService: SitesService;
  let statementsDatabase: StatementsPrismaClient;
  let stripeCustomerService: StripeCustomerService;
  let stripeInvoiceService: StripeInvoiceService;

  const testBuffer = Buffer.alloc(20_000);
  const expectedFilename = `${sanitiseString(
    `${TEST_INVOICE.group.groupName}}/${TEST_INVOICE.site.siteName}/${dayjs(
      TEST_INVOICE.statement.workItem?.month
    ).format('YYYY')}/${dayjs(TEST_INVOICE.statement.workItem?.month).format(
      'MMMM'
    )}`,
    true
  )}/Invoice.pdf`;

  beforeEach(async () => {
    const module = await Test.createTestingModule({
      imports: [
        ConfigModule.forRoot({
          load: [
            () => ({
              S3_GENERATED_DOCUMENTS_BUCKET_NAME:
                'statements-service-generated-documents',
              STATEMENT_SERVICE_WEBAPP_URL: 'http://localhost:5101',
              STRIPE_INCLUSIVE_TAX_RATE_IDS: 'txr_1Iv1J2J2eZvKYlo2',
            }),
          ],
        }),
      ],
      providers: [
        GroupsService,
        InvoiceService,
        PrismaHealthIndicator,
        S3Service,
        SitesService,
        StatementsPrismaClient,
        StripeCustomerService,
        StripeInvoiceService,
      ],
    }).compile();

    service = module.get<InvoiceService>(InvoiceService);

    groupsService = module.get<GroupsService>(GroupsService);
    s3Service = module.get<S3Service>(S3Service);
    sitesService = module.get<SitesService>(SitesService);
    statementsDatabase = module.get<StatementsPrismaClient>(
      StatementsPrismaClient
    );
    stripeCustomerService = module.get<StripeCustomerService>(
      StripeCustomerService
    );
    stripeInvoiceService =
      module.get<StripeInvoiceService>(StripeInvoiceService);

    MockDate.set(new Date(2022, 0, 15, 12, 0, 0, 0));
  });

  afterEach(() => {
    MockDate.reset();
  });

  it('should be defined', () => {
    expect(service).toBeDefined();
  });

  it('should find an invoice', async () => {
    const mockFindInvoice = (statementsDatabase.invoice.findUnique = jest
      .fn()
      .mockResolvedValueOnce(TEST_INVOICE_ENTITY_WITH_STATEMENT));
    const mockFindGroup = jest
      .spyOn(groupsService, 'findGroupByGroupId')
      .mockResolvedValueOnce(TEST_GROUP);
    const mockFindSite = jest
      .spyOn(sitesService, 'findSite')
      .mockResolvedValueOnce(TEST_SITE);

    const invoice = await service.findInvoiceById(TEST_INVOICE.id);

    expect(invoice).toEqual(TEST_INVOICE);
    expect(mockFindInvoice).toHaveBeenCalledWith({
      include: { statement: statementNoInvoiceIncludeOptions },
      where: { id: TEST_INVOICE.id, deletedAt: null },
    });
    expect(mockFindSite).toHaveBeenCalledWith(
      TEST_INVOICE.statement.workItem?.siteId
    );
    expect(mockFindGroup).toHaveBeenCalledWith(
      TEST_INVOICE.statement.workItem?.groupUid
    );
  });

  it('should create an invoice', async () => {
    const mockCreateInvoice = (statementsDatabase.invoice.create = jest
      .fn()
      .mockResolvedValueOnce(TEST_INVOICE_ENTITY));

    const invoiceId = await service.createInvoice(TEST_STATEMENT.id);

    expect(invoiceId).toEqual(TEST_INVOICE.id);
    expect(mockCreateInvoice).toHaveBeenCalledWith({
      data: { statementId: TEST_STATEMENT.id },
    });
  });

  it('should upload an invoice to s3', async () => {
    const mockUploadInvoicePdfToS3 = jest.spyOn(s3Service, 'uploadFile');
    mockPdfGenerate.mockResolvedValueOnce(testBuffer);

    await service.uploadInvoicePdfToS3(
      TEST_INVOICE.id,
      TEST_FILENAME_DETAILS,
      TEST_MONTHLY_SITE_STATEMENT.fees.net
    );

    expect(mockPdfGenerate).toHaveBeenCalledWith(
      `http://localhost:5101/statements/invoices/${TEST_INVOICE.id}`
    );
    expect(mockValidatePdf).toHaveBeenCalledWith(testBuffer, ['£0.87']);
    expect(mockUploadInvoicePdfToS3).toHaveBeenCalledWith(
      'statements-service-generated-documents',
      testBuffer,
      expectedFilename
    );
  });

  it('should throw an error if the pdf does not contain the expected content', async () => {
    jest
      .spyOn(groupsService, 'findGroupByGroupId')
      .mockResolvedValueOnce(TEST_GROUP);
    jest.spyOn(sitesService, 'findSite').mockResolvedValueOnce(TEST_SITE);
    mockPdfGenerate.mockResolvedValueOnce(testBuffer);
    mockValidatePdf.mockResolvedValueOnce(false);

    await expect(
      service.uploadInvoicePdfToS3(
        TEST_INVOICE.id,
        TEST_FILENAME_DETAILS,
        TEST_MONTHLY_SITE_STATEMENT.fees.net
      )
    ).rejects.toThrow(new PDFGenerationFailedException());
  });

  it('should throw an error if the pdf fails to generate', async () => {
    jest
      .spyOn(groupsService, 'findGroupByGroupId')
      .mockResolvedValueOnce(TEST_GROUP);
    jest.spyOn(sitesService, 'findSite').mockResolvedValueOnce(TEST_SITE);
    mockPdfGenerate.mockResolvedValueOnce(testBuffer);
    mockValidatePdf.mockRejectedValueOnce(new PDFGenerationFailedException());

    await expect(
      service.uploadInvoicePdfToS3(
        TEST_INVOICE.id,
        TEST_FILENAME_DETAILS,
        TEST_MONTHLY_SITE_STATEMENT.fees.net
      )
    ).rejects.toThrow(new PDFGenerationFailedException());
  });

  it('should get an invoice pdf', async () => {
    const mockFindInvoice = (statementsDatabase.invoice.findUnique = jest
      .fn()
      .mockResolvedValueOnce(TEST_INVOICE_ENTITY_WITH_STATEMENT));
    const mockFindGroup = jest
      .spyOn(groupsService, 'findGroupByGroupId')
      .mockResolvedValueOnce(TEST_GROUP);
    const mockFindSite = jest
      .spyOn(sitesService, 'findSite')
      .mockResolvedValueOnce(TEST_SITE);
    const mockPdfDownload = jest
      .spyOn(s3Service, 'getFile')
      .mockResolvedValueOnce(testBuffer);

    const response = await service.getInvoicePdf(TEST_INVOICE.id);

    expect(mockFindInvoice).toHaveBeenCalledWith({
      include: { statement: statementNoInvoiceIncludeOptions },
      where: { id: TEST_INVOICE.id, deletedAt: null },
    });
    expect(mockFindSite).toHaveBeenCalledWith(
      TEST_INVOICE.statement.workItem?.siteId
    );
    expect(mockFindGroup).toHaveBeenCalledWith(
      TEST_INVOICE.statement.workItem?.groupUid
    );
    expect(mockPdfDownload).toHaveBeenCalledWith(
      'statements-service-generated-documents',
      expectedFilename
    );

    expect(response.buffer).toEqual(testBuffer);
    expect(response.filename).toEqual(expectedFilename);
  });

  it.each([
    [
      'and charge automatically if direct debit is the default payment',
      'bacs_debit',
      'charge_automatically',
      TEST_GROUP_WITH_STRIPE_CUSTOMER,
      [
        { name: 'Account reference', value: TEST_GROUP.accountRef },
        { name: 'Site name', value: TEST_STATEMENT.workItem?.siteName },
        { name: 'Site address', value: TEST_STATEMENT.siteAddress },
        { name: 'PO number', value: TEST_GROUP.poNumber },
      ],
    ],
    [
      'and charge manually if direct debit is not the default payment method',
      'customer_balance',
      'send_invoice',
      TEST_GROUP_WITH_STRIPE_CUSTOMER,
      [
        { name: 'Account reference', value: TEST_GROUP.accountRef },
        { name: 'Site name', value: TEST_STATEMENT.workItem?.siteName },
        { name: 'Site address', value: TEST_STATEMENT.siteAddress },
        { name: 'PO number', value: TEST_GROUP.poNumber },
      ],
    ],
    [
      'and omit the PO number custom field if it is not included in the group config',
      'bacs_debit',
      'charge_automatically',
      { ...TEST_GROUP_WITH_STRIPE_CUSTOMER, poNumber: undefined },
      [
        { name: 'Account reference', value: TEST_GROUP.accountRef },
        { name: 'Site name', value: TEST_STATEMENT.workItem?.siteName },
        { name: 'Site address', value: TEST_STATEMENT.siteAddress },
      ],
    ],
  ])(
    'should create and upload a stripe invoice if the group is setup for stripe %s',
    async (
      _,
      defaultPaymentMethod,
      collectionMethod,
      groupConfig,
      customFields
    ) => {
      const TEST_CUSTOMER = {
        customerId: TEST_GROUP_WITH_STRIPE_CUSTOMER.stripeCustomerId as string,
        name: TEST_GROUP_WITH_STRIPE_CUSTOMER.businessName,
        defaultPaymentMethod,
      };

      const mockArrayBuffer = new ArrayBuffer(8);

      const mockFindInvoice = (statementsDatabase.invoice.findUnique = jest
        .fn()
        .mockResolvedValueOnce(
          setIn(
            TEST_INVOICE_ENTITY_WITH_STATEMENT,
            ['statement', 'workItem', 'automated'],
            true
          )
        ));
      const mockFindGroupConfig = jest
        .spyOn(groupsService, 'findGroupByGroupId')
        .mockResolvedValueOnce(groupConfig);
      const mockFindSiteConfig = jest
        .spyOn(sitesService, 'findSite')
        .mockResolvedValueOnce(TEST_SITE);
      const mockRetrieveCustomer = jest
        .spyOn(stripeCustomerService, 'retrieve')
        .mockResolvedValueOnce(TEST_CUSTOMER);
      const mockCreateStripeInvoice = jest
        .spyOn(stripeInvoiceService, 'createInvoice')
        .mockResolvedValueOnce(TEST_STRIPE_INVOICE_RESPONSE);
      const mockFinaliseInvoice = jest
        .spyOn(stripeInvoiceService, 'finaliseInvoice')
        .mockResolvedValueOnce({
          invoice_pdf: 'https://stripe.com/invoice.pdf',
          status: 'open',
        } as Stripe.Response<Stripe.Invoice>);
      const mockFetch = jest.spyOn(global, 'fetch').mockResolvedValueOnce({
        arrayBuffer: async () => mockArrayBuffer,
      } as Response);
      const mockUploadFile = jest.spyOn(s3Service, 'uploadFile');
      const mockUpdateInvoice = (statementsDatabase.invoice.update = jest.fn());

      await service.createAndUploadStripeInvoice(
        TEST_GROUP.groupId,
        TEST_INVOICE.id
      );

      expect(mockFindInvoice).toHaveBeenCalledWith({
        include: { statement: statementNoInvoiceIncludeOptions },
        where: { id: TEST_INVOICE.id, deletedAt: null },
      });
      expect(mockRetrieveCustomer).toHaveBeenCalledWith(
        TEST_CUSTOMER.customerId
      );
      expect(mockCreateStripeInvoice).toHaveBeenCalledWith({
        amount: 2700,
        collectionMethod,
        customFields,
        customerId: TEST_GROUP_WITH_STRIPE_CUSTOMER.stripeCustomerId,
        daysUntilDue: 30,
        description: `Admin fees | ${TEST_STATEMENT.reference}`,
        endPeriod: '2023-02-28T00:00:00.000Z',
        footer:
          'Where required, your charger will comply with The Electric Vehicles (Smart Charge Points) Regulations 2021. Our statement of Compliance can be found at https://pod-point.com/technical/hardware.',
        startPeriod: '2023-02-01T00:00:00.000Z',
        taxBehaviour: 'inclusive',
        taxRates: ['txr_1Iv1J2J2eZvKYlo2'],
      });
      expect(mockFinaliseInvoice).toHaveBeenCalledWith(
        TEST_STRIPE_INVOICE_RESPONSE.invoiceId
      );
      expect(mockFindGroupConfig).toHaveBeenCalledWith(TEST_GROUP.groupId);
      expect(mockFindSiteConfig).toHaveBeenCalledWith(TEST_SITE.siteId);
      expect(mockFetch).toHaveBeenCalledWith('https://stripe.com/invoice.pdf');
      expect(mockUploadFile).toHaveBeenCalledWith(
        'statements-service-generated-documents',
        Buffer.from(mockArrayBuffer),
        'Test-Group-Inc/Site-1/2023/February/Invoice.pdf'
      );
      expect(mockUpdateInvoice).toHaveBeenCalledWith({
        data: {
          stripeInvoiceId: TEST_STRIPE_INVOICE_RESPONSE.invoiceId,
          stripeInvoiceNumber: undefined,
          stripeInvoiceStatus: 'open',
        },
        where: { id: TEST_INVOICE.id },
      });
    }
  );

  it('should not create a stripe invoice if group is not setup for stripe', async () => {
    statementsDatabase.invoice.findUnique = jest
      .fn()
      .mockResolvedValueOnce(
        setIn(
          TEST_INVOICE_ENTITY_WITH_STATEMENT,
          ['statement', 'workItem', 'automated'],
          true
        )
      )
      .mockResolvedValueOnce(TEST_INVOICE_ENTITY_WITH_STATEMENT);
    jest
      .spyOn(groupsService, 'findGroupByGroupId')
      .mockResolvedValueOnce(TEST_GROUP);

    const mockCreateInvoice = jest.spyOn(stripeInvoiceService, 'createInvoice');

    await service.createAndUploadStripeInvoice(
      TEST_GROUP.groupId,
      TEST_STATEMENT.id
    );

    expect(mockCreateInvoice).not.toHaveBeenCalled();
  });

  it('should throw an exception if there is no invoice pdf url', async () => {
    statementsDatabase.invoice.findUnique = jest
      .fn()
      .mockResolvedValueOnce(
        setIn(
          TEST_INVOICE_ENTITY_WITH_STATEMENT,
          ['statement', 'workItem', 'automated'],
          true
        )
      )
      .mockResolvedValueOnce(TEST_INVOICE_ENTITY_WITH_STATEMENT);
    jest
      .spyOn(groupsService, 'findGroupByGroupId')
      .mockResolvedValueOnce(TEST_GROUP_WITH_STRIPE_CUSTOMER);
    jest.spyOn(sitesService, 'findSite').mockResolvedValueOnce(TEST_SITE);
    jest.spyOn(stripeCustomerService, 'retrieve').mockResolvedValueOnce({
      customerId: TEST_GROUP_WITH_STRIPE_CUSTOMER.stripeCustomerId as string,
      name: TEST_GROUP_WITH_STRIPE_CUSTOMER.businessName,
    });
    const mockCreateInvoice = jest
      .spyOn(stripeInvoiceService, 'createInvoice')
      .mockResolvedValueOnce(TEST_STRIPE_INVOICE_RESPONSE);
    const mockFinaliseInvoice = jest
      .spyOn(stripeInvoiceService, 'finaliseInvoice')
      .mockResolvedValueOnce({
        invoice_pdf: null,
        number: 'ADF-4567',
        status: 'open',
      } as Stripe.Response<Stripe.Invoice>);

    await expect(
      service.createAndUploadStripeInvoice(
        TEST_GROUP.groupId,
        TEST_STATEMENT.id
      )
    ).rejects.toThrow(PDFGenerationFailedException);

    expect(mockCreateInvoice).toHaveBeenCalledWith({
      amount: 2700,
      collectionMethod: 'send_invoice',
      customFields: [
        { name: 'Account reference', value: '*********' },
        { name: 'Site name', value: 'Site 1' },
        {
          name: 'Site address',
          value: '28 - 42 Banner Street, Islington, London, EC1Y 8QE, UK',
        },
        { name: 'PO number', value: 'UK10010001' },
      ],
      customerId: 'cus_*********',
      daysUntilDue: 30,
      description: 'Admin fees | ROSDH012023',
      endPeriod: '2023-02-28T00:00:00.000Z',
      footer:
        'Where required, your charger will comply with The Electric Vehicles (Smart Charge Points) Regulations 2021. Our statement of Compliance can be found at https://pod-point.com/technical/hardware.',
      startPeriod: '2023-02-01T00:00:00.000Z',
      taxBehaviour: 'inclusive',
      taxRates: ['txr_1Iv1J2J2eZvKYlo2'],
    });
    expect(mockFinaliseInvoice).toHaveBeenCalledWith(
      'in_1MtHbELkdIwHu7ixl4OzzPMv'
    );
  });

  it('should generate ifs export for month', async () => {
    const mockFindMany = (statementsDatabase.invoice.findMany = jest
      .fn()
      .mockResolvedValueOnce([TEST_INVOICE_ENTITY]));

    const { file, filename } = await service.generateIfsInvoicesExport(
      '2023-02-05'
    );

    expect(file).toBeDefined();
    expect(filename).toEqual('ifs-invoices-export_February-2023.csv');
    expect(mockFindMany).toHaveBeenCalledWith({
      orderBy: {
        invoiceNumber: 'asc',
      },
      where: {
        createdAt: {
          gte: new Date('2023-02-01T00:00:00.000Z'),
          lt: new Date('2023-02-28T23:59:59.999Z'),
        },
        deletedAt: null,
      },
      include: {
        statement: { include: { workItem: true, groupConfig: true } },
      },
    });
  });

  it('should update the stripe invoice status for an invoice', async () => {
    const mockStatus = jest
      .spyOn(stripeInvoiceService, 'calculateInvoiceStatus')
      .mockReturnValueOnce('void');
    const mockUpdate = (statementsDatabase.invoice.update = jest.fn());

    await service.updateStripeInvoiceStatusByStripeInvoiceId(
      TEST_STRIPE_INVOICE_RESPONSE.invoiceId,
      'void',
      null
    );

    expect(mockStatus).toHaveBeenCalledWith('void', null);
    expect(mockUpdate).toHaveBeenCalledWith({
      where: { stripeInvoiceId: TEST_STRIPE_INVOICE_RESPONSE.invoiceId },
      data: {
        stripeInvoiceStatus: 'void',
      },
    });
  });

  it('should update the stripe invoice data with the new invoice data', async () => {
    const mockArrayBuffer = new ArrayBuffer(8);
    const mockUpdate = (statementsDatabase.invoice.update = jest
      .fn()
      .mockResolvedValueOnce({ id: TEST_INVOICE.id }));
    const mockFindInvoice = jest
      .spyOn(service, 'findInvoiceById')
      .mockResolvedValueOnce(TEST_INVOICE);
    const mockFetch = jest.spyOn(global, 'fetch').mockResolvedValueOnce({
      arrayBuffer: async () => mockArrayBuffer,
    } as Response);
    const mockUploadFile = jest.spyOn(s3Service, 'uploadFile');

    await service.replaceStripeInvoiceData(
      'in_1Mold',
      'https://stripe.com/invoice-page.com',
      'in_2Mnew',
      'ADF-0224',
      TEST_STRIPE_INVOICE_PDF_URL,
      'open'
    );

    expect(mockUpdate).toHaveBeenCalledWith({
      where: { stripeInvoiceId: 'in_1Mold' },
      data: {
        hostedInvoiceUrl: 'https://stripe.com/invoice-page.com',
        stripeInvoiceId: 'in_2Mnew',
        stripeInvoiceNumber: 'ADF-0224',
        stripeInvoiceStatus: 'open',
      },
    });
    expect(mockFindInvoice).toHaveBeenCalledWith(TEST_INVOICE.id);
    expect(mockFetch).toHaveBeenCalledWith(TEST_STRIPE_INVOICE_PDF_URL);
    expect(mockUploadFile).toHaveBeenCalledWith(
      'statements-service-generated-documents',
      Buffer.from(mockArrayBuffer),
      'Test-Group-Inc/Site-1/2023/February/Invoice.pdf'
    );
  });

  it('should reissue existing stripe invoices', async () => {
    const invoiceEntity = {
      ...TEST_INVOICE_ENTITY_WITH_STRIPE_DATA,
      statement: {
        ...TEST_STATEMENT_ENTITY_DEEP,
        groupConfig: TEST_GROUP_CONFIG_ENTITY_WITH_PO_NUMBER,
      },
    };
    const mockFindInvoices = (statementsDatabase.invoice.findMany = jest
      .fn()
      .mockResolvedValue([invoiceEntity]));
    const mockReviseStripeInvoice = jest
      .spyOn(stripeInvoiceService, 'reviseInvoice')
      .mockResolvedValueOnce(TEST_STRIPE_INVOICE_RESPONSE);
    const mockFinaliseStripeInvoice = jest.spyOn(
      stripeInvoiceService,
      'finaliseInvoice'
    );

    await service.reissueStripeInvoices(TEST_REISSUE_STRIPE_INVOICE_REQUEST);

    expect(mockFindInvoices).toHaveBeenCalledWith({
      ...invoiceDeepIncludeOptions,
      where: {
        deletedAt: null,
        statement: { groupId: TEST_GROUP.groupId },
        stripeInvoiceStatus: { in: ['open', 'overdue'] },
      },
    });
    expect(mockReviseStripeInvoice).toHaveBeenCalledWith({
      customFields: [
        {
          name: 'Account reference',
          value: invoiceEntity.statement.groupConfig.accountRef,
        },
        {
          name: 'Site name',
          value: invoiceEntity.statement.workItem.siteName,
        },
        {
          name: 'Site address',
          value: invoiceEntity.statement.siteAddress,
        },
        {
          name: 'PO number',
          value: invoiceEntity.statement.groupConfig.poNumber,
        },
      ],
      fromInvoiceId: invoiceEntity.stripeInvoiceId,
    });
    expect(mockFinaliseStripeInvoice).toHaveBeenCalledWith(
      TEST_STRIPE_INVOICE_RESPONSE.invoiceId
    );
  });

  it.each([
    ['open', TEST_INVOICE_WITH_STRIPE_DATA],
    [
      'overdue',
      {
        ...TEST_INVOICE_WITH_STRIPE_DATA,
        stripeInvoiceStatus: 'overdue',
      },
    ],
  ])(
    'should update an %s stripe invoice to paid status',
    async (_, invoice) => {
      const mockUpdate = (statementsDatabase.invoice.update = jest.fn());
      const mockFindInvoice = jest
        .spyOn(service, 'findInvoiceById')
        .mockResolvedValueOnce(invoice);
      const mockUpdateStripeInvoice = jest
        .spyOn(stripeInvoiceService, 'markInvoiceAsPaid')
        .mockResolvedValueOnce();

      await service.updateInvoiceStatus(
        TEST_INVOICE.id,
        MOCK_UPDATE_INVOICE_STATUS_REQUEST_PAID
      );

      expect(mockFindInvoice).toHaveBeenCalledWith(
        TEST_INVOICE_WITH_STRIPE_DATA.id
      );
      expect(mockUpdateStripeInvoice).toHaveBeenCalledWith(
        TEST_INVOICE_WITH_STRIPE_DATA.stripeInvoiceId
      );
      expect(mockUpdate).toHaveBeenCalledWith({
        where: { id: TEST_INVOICE_WITH_STRIPE_DATA.id },
        data: { stripeInvoiceStatus: 'paid' },
      });
    }
  );

  it.each([
    ['open', TEST_INVOICE_WITH_STRIPE_DATA],
    [
      'overdue',
      {
        ...TEST_INVOICE_WITH_STRIPE_DATA,
        stripeInvoiceStatus: 'overdue',
      },
    ],
  ])(
    'should update an %s stripe invoice to void status',
    async (_, invoice) => {
      const mockUpdate = (statementsDatabase.invoice.update = jest.fn());
      const mockFindInvoice = jest
        .spyOn(service, 'findInvoiceById')
        .mockResolvedValueOnce(invoice);
      const mockUpdateStripeInvoice = jest
        .spyOn(stripeInvoiceService, 'voidInvoice')
        .mockResolvedValueOnce();

      await service.updateInvoiceStatus(
        TEST_INVOICE_WITH_STRIPE_DATA.id,
        MOCK_UPDATE_INVOICE_STATUS_REQUEST_VOID
      );

      expect(mockFindInvoice).toHaveBeenCalledWith(
        TEST_INVOICE_WITH_STRIPE_DATA.id
      );
      expect(mockUpdateStripeInvoice).toHaveBeenCalledWith(
        TEST_INVOICE_WITH_STRIPE_DATA.stripeInvoiceId
      );
      expect(mockUpdate).toHaveBeenCalledWith({
        where: { id: TEST_INVOICE_WITH_STRIPE_DATA.id },
        data: { stripeInvoiceStatus: 'void' },
      });
    }
  );

  it.each([
    [
      'does not have a stripe invoice ID',
      { ...TEST_INVOICE_WITH_STRIPE_DATA, stripeInvoiceId: undefined },
    ],
    [
      'is already paid',
      { ...TEST_INVOICE_WITH_STRIPE_DATA, stripeInvoiceStatus: 'paid' },
    ],
    [
      'has been voided',
      { ...TEST_INVOICE_WITH_STRIPE_DATA, stripeInvoiceStatus: 'void' },
    ],
  ])(
    'should throw an error when updating status if the invoice %s',
    async (_, invoice) => {
      jest.spyOn(service, 'findInvoiceById').mockResolvedValueOnce(invoice);

      await expect(
        service.updateInvoiceStatus(
          TEST_INVOICE.id,
          MOCK_UPDATE_INVOICE_STATUS_REQUEST_PAID
        )
      ).rejects.toThrow(new InvoiceNotUpdatableException());
    }
  );

  it('should throw an error when updating status if the status is invalid', async () => {
    jest
      .spyOn(service, 'findInvoiceById')
      .mockResolvedValueOnce(TEST_INVOICE_WITH_STRIPE_DATA);

    await expect(
      service.updateInvoiceStatus(TEST_INVOICE_WITH_STRIPE_DATA.id, {
        status: 'invalid',
      })
    ).rejects.toThrow(new InvoiceStatusInvalidException());
  });

  describe('refresh invoice urls', () => {
    it('should successfully refresh hosted invoice URLs', async () => {
      const mockInvoices = [
        { id: 'invoice-1', stripeInvoiceId: 'in_test1' },
        { id: 'invoice-2', stripeInvoiceId: 'in_test2' },
      ];

      const mockStripeInvoice = {
        hosted_invoice_url: 'https://invoice.stripe.com/i/test',
      } as Stripe.Invoice;

      const mockFindMany = (statementsDatabase.invoice.findMany = jest
        .fn()
        .mockResolvedValueOnce(mockInvoices)
        .mockResolvedValueOnce([]));

      const mockGetInvoice = jest
        .spyOn(stripeInvoiceService, 'getInvoice')
        .mockResolvedValue(mockStripeInvoice);

      const mockUpdate = (statementsDatabase.invoice.update = jest
        .fn()
        .mockResolvedValue({ id: 'updated-invoice' }));

      const result = await service.refreshHostedInvoiceUrls();

      expect(mockFindMany).toHaveBeenCalledWith({
        where: {
          stripeInvoiceId: { not: null },
          stripeInvoiceStatus: {
            in: ['overdue'],
          },
          updatedAt: {
            lt: new Date('2021-12-16T12:00:00.000Z'),
          },
          deletedAt: null,
        },
        select: {
          id: true,
          stripeInvoiceId: true,
        },
        take: 10,
        skip: 0,
        orderBy: { createdAt: 'asc' },
      });

      expect(mockGetInvoice).toHaveBeenCalledTimes(2);
      expect(mockGetInvoice).toHaveBeenCalledWith('in_test1');
      expect(mockGetInvoice).toHaveBeenCalledWith('in_test2');

      expect(mockUpdate).toHaveBeenCalledTimes(2);
      expect(mockUpdate).toHaveBeenCalledWith({
        where: { stripeInvoiceId: 'in_test1' },
        data: { hostedInvoiceUrl: 'https://invoice.stripe.com/i/test' },
      });
      expect(mockUpdate).toHaveBeenCalledWith({
        where: { stripeInvoiceId: 'in_test2' },
        data: { hostedInvoiceUrl: 'https://invoice.stripe.com/i/test' },
      });

      expect(result).toEqual({
        totalInvoices: 2,
        successfulUpdates: 2,
        failedUpdates: 0,
        skippedInvoices: 0,
      });
    });

    it('should return early when no invoices need refreshed', async () => {
      const mockFindMany = (statementsDatabase.invoice.findMany =
        jest.fn()).mockResolvedValueOnce(() => []);
      const mockGetInvoice = jest.spyOn(stripeInvoiceService, 'getInvoice');

      const result = await service.refreshHostedInvoiceUrls();

      expect(mockFindMany).toHaveBeenCalled();
      expect(mockGetInvoice).not.toHaveBeenCalled();

      expect(result).toEqual({
        totalInvoices: 0,
        successfulUpdates: 0,
        failedUpdates: 0,
        skippedInvoices: 0,
      });
    });

    it('should skip invoices without stripe invoice IDs', async () => {
      const mockInvoices = [
        { id: 'invoice-1', stripeInvoiceId: 'in_test1' },
        { id: 'invoice-2', stripeInvoiceId: null },
      ];

      const mockStripeInvoice = {
        hosted_invoice_url: 'https://invoice.stripe.com/i/test',
      } as Stripe.Invoice;

      statementsDatabase.invoice.findMany = jest
        .fn()
        .mockResolvedValueOnce(mockInvoices)
        .mockResolvedValueOnce([]);

      const mockGetInvoice = jest
        .spyOn(stripeInvoiceService, 'getInvoice')
        .mockResolvedValue(mockStripeInvoice);

      const mockUpdate = (statementsDatabase.invoice.update = jest
        .fn()
        .mockResolvedValue({ id: 'updated-invoice' }));

      const result = await service.refreshHostedInvoiceUrls();

      expect(mockGetInvoice).toHaveBeenCalledTimes(1);
      expect(mockGetInvoice).toHaveBeenCalledWith('in_test1');

      expect(mockUpdate).toHaveBeenCalledTimes(1);
      expect(mockUpdate).toHaveBeenCalledWith({
        where: { stripeInvoiceId: 'in_test1' },
        data: { hostedInvoiceUrl: 'https://invoice.stripe.com/i/test' },
      });

      expect(result).toEqual({
        totalInvoices: 2,
        successfulUpdates: 1,
        failedUpdates: 0,
        skippedInvoices: 1,
      });
    });

    it('should skip invoices without hosted invoice URLs from Stripe', async () => {
      const mockInvoices = [{ id: 'invoice-1', stripeInvoiceId: 'in_test1' }];

      const mockStripeInvoice = {
        hosted_invoice_url: null,
      } as Stripe.Invoice;

      statementsDatabase.invoice.findMany = jest
        .fn()
        .mockResolvedValueOnce(mockInvoices)
        .mockResolvedValueOnce([]);

      const mockGetInvoice = jest
        .spyOn(stripeInvoiceService, 'getInvoice')
        .mockResolvedValue(mockStripeInvoice);

      const mockUpdate = (statementsDatabase.invoice.update = jest.fn());

      const result = await service.refreshHostedInvoiceUrls();

      expect(mockGetInvoice).toHaveBeenCalledWith('in_test1');
      expect(mockUpdate).not.toHaveBeenCalled();

      expect(result).toEqual({
        totalInvoices: 1,
        successfulUpdates: 0,
        failedUpdates: 0,
        skippedInvoices: 1,
      });
    });

    it('should handle Stripe API errors gracefully', async () => {
      const mockInvoices = [
        { id: 'invoice-1', stripeInvoiceId: 'in_test1' },
        { id: 'invoice-2', stripeInvoiceId: 'in_test2' },
      ];

      const mockStripeInvoice = {
        hosted_invoice_url: 'https://invoice.stripe.com/i/test',
      } as Stripe.Invoice;

      statementsDatabase.invoice.findMany = jest
        .fn()
        .mockResolvedValueOnce(mockInvoices)
        .mockResolvedValueOnce([]);

      const mockGetInvoice = jest
        .spyOn(stripeInvoiceService, 'getInvoice')
        .mockRejectedValueOnce(new Error('Stripe API error'))
        .mockResolvedValueOnce(mockStripeInvoice);

      const mockUpdate = (statementsDatabase.invoice.update = jest
        .fn()
        .mockResolvedValue({ id: 'updated-invoice' }));

      const result = await service.refreshHostedInvoiceUrls();

      expect(mockGetInvoice).toHaveBeenCalledTimes(2);
      expect(mockUpdate).toHaveBeenCalledTimes(1);

      expect(result).toEqual({
        totalInvoices: 2,
        successfulUpdates: 1,
        failedUpdates: 1,
        skippedInvoices: 0,
      });
    });
  });
});
