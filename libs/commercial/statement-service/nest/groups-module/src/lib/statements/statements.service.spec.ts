import { ConfigService } from '@nestjs/config';
import { GroupsService } from '../groups.service';
import { GroupsStatementsService } from './statements.service';
import { PrismaHealthIndicator } from '@nestjs/terminus';
import {
  StatementsPrismaClient,
  TEST_STATEMENT_ENTITY_DEEP,
} from '@experience/commercial/statement-service/prisma/statements/client';
import {
  TEST_GROUP,
  TEST_PARTIAL_INVOICE_WITH_STRIPE_DATA,
  TEST_WORK_ITEM,
  getTestStatement,
} from '@experience/commercial/statement-service/shared';
import { Test } from '@nestjs/testing';

jest.mock('../groups.service');

const TEST_STATEMENT_WITH_INVOICE = getTestStatement(
  TEST_PARTIAL_INVOICE_WITH_STRIPE_DATA,
  TEST_WORK_ITEM
);

describe('groups statements service', () => {
  let service: GroupsStatementsService;
  let groupsService: GroupsService;
  let database: StatementsPrismaClient;

  beforeEach(async () => {
    const module = await Test.createTestingModule({
      providers: [
        ConfigService,
        GroupsStatementsService,
        GroupsService,
        PrismaHealthIndicator,
        StatementsPrismaClient,
      ],
    }).compile();

    service = module.get(GroupsStatementsService);
    groupsService = module.get(GroupsService);
    database = module.get(StatementsPrismaClient);
  });

  it('should be defined', () => {
    expect(service).toBeDefined();
  });

  it('should find a list of statements for a group', async () => {
    const mockFindGroupByGroupId = jest
      .spyOn(groupsService, 'findGroupByGroupId')
      .mockResolvedValueOnce(TEST_GROUP);
    const mockFindMany = (database.statement.findMany = jest
      .fn()
      .mockResolvedValueOnce([TEST_STATEMENT_ENTITY_DEEP]));

    const statements = await service.findStatementsByGroupId(
      TEST_GROUP.groupId
    );

    expect(statements).toEqual([TEST_STATEMENT_WITH_INVOICE]);
    expect(mockFindGroupByGroupId).toHaveBeenCalledWith(TEST_GROUP.groupId);
    expect(mockFindMany).toHaveBeenCalledWith({
      include: {
        invoice: true,
        statementChargerConfig: true,
        workItem: true,
      },
      where: {
        deletedAt: null,
        groupId: TEST_GROUP.groupId,
        workItem: {
          status: 'SENT',
        },
      },
    });
  });
});
