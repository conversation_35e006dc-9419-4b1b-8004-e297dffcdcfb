import * as MockDate from 'mockdate';
import { ConfigModule, ConfigService } from '@nestjs/config';
import { DocumentUploadFailedException } from '@experience/commercial/statement-service/nest/shared';
import { DocumentsService } from './documents.service';
import { GroupsService } from '../groups.service';
import { PrismaHealthIndicator } from '@nestjs/terminus';
import { S3Service } from '@experience/shared/nest/aws/s3-module';
import {
  StatementsPrismaClient,
  TEST_DOCUMENT_ENTITY,
} from '@experience/commercial/statement-service/prisma/statements/client';
import {
  TEST_CREATE_DOCUMENT_REQUEST,
  TEST_DOCUMENT,
  TEST_GROUP,
} from '@experience/commercial/statement-service/shared';
import { Test } from '@nestjs/testing';
import dayjs from 'dayjs';

jest.mock('../groups.service');
jest.mock('@experience/shared/nest/aws/s3-module');

describe('groups documents service', () => {
  let service: DocumentsService;
  let groupService: GroupsService;
  let s3Service: S3Service;
  let database: StatementsPrismaClient;

  beforeEach(async () => {
    const module = await Test.createTestingModule({
      imports: [
        await ConfigModule.forRoot({
          load: [
            () => ({
              S3_GROUP_DOCS_BUCKET_NAME: 'statements-service-group-docs',
            }),
          ],
        }),
      ],
      providers: [
        ConfigService,
        GroupsService,
        DocumentsService,
        S3Service,
        {
          provide: StatementsPrismaClient,
          useValue: {
            groupDocuments: {
              findMany: jest.fn(),
            },
          },
        },
        PrismaHealthIndicator,
        StatementsPrismaClient,
      ],
    }).compile();

    service = module.get(DocumentsService);
    database = module.get(StatementsPrismaClient);
    groupService = module.get(GroupsService);
    s3Service = module.get(S3Service);
    MockDate.set(new Date(2022, 0, 15, 12, 0, 0, 0));
  });

  it('should be defined', () => {
    expect(service).toBeDefined();
    expect(database).toBeDefined();
    expect(groupService).toBeDefined();
    expect(s3Service).toBeDefined();
  });

  it('should find a list of documents for a group', async () => {
    const mockFindMany = (database.groupDocuments.findMany = jest
      .fn()
      .mockResolvedValueOnce([TEST_DOCUMENT_ENTITY]));

    const documents = await service.findDocumentsByGroupId(TEST_GROUP.groupId);

    expect(documents).toEqual([TEST_DOCUMENT]);
    expect(mockFindMany).toHaveBeenCalledWith({
      where: {
        groupId: TEST_GROUP.groupId,
      },
    });
  });

  it('should upload a document', async () => {
    const mockFindGroupByGroupId = jest
      .spyOn(groupService, 'findGroupByGroupId')
      .mockResolvedValueOnce(TEST_GROUP);
    const mockUploadFile = jest
      .spyOn(s3Service, 'uploadFile')
      .mockResolvedValueOnce(true);
    const testBuffer = Buffer.alloc(20000);
    const mockCreate = (database.groupDocuments.create = jest
      .fn()
      .mockResolvedValueOnce(1));

    await service.uploadDocument(
      TEST_CREATE_DOCUMENT_REQUEST,
      testBuffer,
      'application/pdf',
      TEST_GROUP.groupId
    );

    expect(mockFindGroupByGroupId).toHaveBeenCalledWith(TEST_GROUP.groupId);
    expect(mockUploadFile).toHaveBeenCalledWith(
      'statements-service-group-docs',
      testBuffer,
      `${TEST_GROUP.groupName}/${TEST_DOCUMENT.name}`
    );
    expect(mockCreate).toHaveBeenCalledWith({
      data: {
        groupId: TEST_GROUP.groupId,
        name: `${TEST_CREATE_DOCUMENT_REQUEST.name}.pdf`,
        type: TEST_CREATE_DOCUMENT_REQUEST.type,
        s3Location: `https://statements-service-group-docs.s3.amazonaws.com/${TEST_GROUP.groupName}/${TEST_DOCUMENT.name}`,
        startDate: dayjs(TEST_CREATE_DOCUMENT_REQUEST.startDate).toDate(),
        uploadDate: new Date(),
      },
    });
  });

  it('should throw an error if document upload fails', async () => {
    jest
      .spyOn(groupService, 'findGroupByGroupId')
      .mockResolvedValueOnce(TEST_GROUP);
    jest
      .spyOn(s3Service, 'uploadFile')
      .mockRejectedValueOnce(new Error('Upload failed'));
    const mockCreate = (database.groupDocuments.create = jest.fn());

    await expect(
      service.uploadDocument(
        TEST_CREATE_DOCUMENT_REQUEST,
        Buffer.alloc(20000),
        'application/pdf',
        TEST_GROUP.groupId
      )
    ).rejects.toThrow(new DocumentUploadFailedException());

    expect(mockCreate).not.toHaveBeenCalled();
  });

  it('should download a document', async () => {
    const mockFindGroupByGroupId = jest
      .spyOn(groupService, 'findGroupByGroupId')
      .mockResolvedValueOnce(TEST_GROUP);
    const mockFindDocument = (database.groupDocuments.findUniqueOrThrow = jest
      .fn()
      .mockResolvedValueOnce(TEST_DOCUMENT_ENTITY));
    const mockGetFile = jest
      .spyOn(s3Service, 'getFile')
      .mockResolvedValueOnce(Buffer.from('test file content'));

    const document = await service.downloadDocument(
      TEST_DOCUMENT.id,
      TEST_GROUP.groupId
    );

    expect(document).toEqual({
      filename: TEST_DOCUMENT.name,
      mimetype: 'application/pdf',
      buffer: Buffer.from('test file content'),
    });
    expect(mockFindDocument).toHaveBeenCalledWith({
      where: {
        id: TEST_DOCUMENT.id,
      },
    });
    expect(mockFindGroupByGroupId).toHaveBeenCalledWith(TEST_GROUP.groupId);
    expect(mockGetFile).toHaveBeenCalledWith(
      'statements-service-group-docs',
      `${TEST_GROUP.groupName}/${TEST_DOCUMENT.name}`
    );
  });
});
