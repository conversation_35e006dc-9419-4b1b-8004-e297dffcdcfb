import { APP_GUARD } from '@nestjs/core';
import {
  DocumentErrorCodes,
  DocumentNotFoundException,
} from '@experience/commercial/statement-service/nest/shared';
import { DocumentsController } from './documents.controller';
import { DocumentsService } from './documents.service';
import { INestApplication } from '@nestjs/common';
import {
  OidcRoles,
  getOidcUser,
} from '@experience/shared/typescript/oidc-utils';
import { RolesGuard } from '@experience/shared/nest/utils';
import {
  TEST_CREATE_DOCUMENT_REQUEST,
  TEST_DOCUMENT,
  TEST_GROUP,
} from '@experience/commercial/statement-service/shared';
import { TEST_OIDC_USER } from '@experience/shared/typescript/oidc-utils/specs';
import { Test } from '@nestjs/testing';
import mime from 'mime-types';
import request from 'supertest';

jest.mock('./documents.service');
jest.mock('@experience/shared/typescript/oidc-utils');
const mockGetOidcUser = jest.mocked(getOidcUser);

const TEST_OIDC_USER_WITH_ROLE = {
  ...TEST_OIDC_USER,
  roles: [OidcRoles.GROUP_MANAGE_DOCUMENTS],
};

describe('Groups documents controller', () => {
  let app: INestApplication;
  let controller: DocumentsController;
  let service: DocumentsService;

  beforeAll(async () => {
    const module = await Test.createTestingModule({
      controllers: [DocumentsController],
      providers: [
        DocumentsService,
        { provide: APP_GUARD, useClass: RolesGuard },
      ],
    }).compile();

    controller = module.get(DocumentsController);
    service = module.get(DocumentsService);

    app = module.createNestApplication();
    await app.init();
  });

  beforeEach(() => {
    mockGetOidcUser.mockReturnValue(TEST_OIDC_USER_WITH_ROLE);
  });

  afterAll(async () => {
    await app.close();
  });

  it('should be defined', () => {
    expect(app).toBeDefined();
    expect(controller).toBeDefined();
    expect(service).toBeDefined();
  });

  it('should find a list of documents for a group', async () => {
    const mockFindDocumentsByGroupId = jest
      .spyOn(service, 'findDocumentsByGroupId')
      .mockResolvedValueOnce([TEST_DOCUMENT]);

    const response = await request(app.getHttpServer())
      .get(`/groups/${TEST_GROUP.groupId}/documents`)
      .expect(200);

    expect(response.body).toEqual([TEST_DOCUMENT]);
    expect(mockFindDocumentsByGroupId).toHaveBeenCalledWith(TEST_GROUP.groupId);
  });

  it.each(['.pdf', '.docx'])(
    'should upload a document with correct file type - %s',
    async (ext) => {
      const mockUploadDocument = jest
        .spyOn(service, 'uploadDocument')
        .mockResolvedValueOnce();

      const response = await request(app.getHttpServer())
        .post(`/groups/${TEST_GROUP.groupId}/documents`)
        .attach('file', Buffer.alloc(20000), `test${ext}`)
        .field({
          name: TEST_CREATE_DOCUMENT_REQUEST.name,
          type: TEST_CREATE_DOCUMENT_REQUEST.type,
          startDate: TEST_CREATE_DOCUMENT_REQUEST.startDate,
        });

      expect(response.status).toEqual(201);
      expect(mockUploadDocument).toHaveBeenCalledWith(
        TEST_CREATE_DOCUMENT_REQUEST,
        expect.any(Buffer),
        mime.lookup(ext),
        TEST_GROUP.groupId
      );
    }
  );

  it('should throw a validation error when file type is wrong when uploading a document', async () => {
    const response = await request(app.getHttpServer())
      .post(`/groups/${TEST_GROUP.groupId}/documents`)
      .attach('file', Buffer.alloc(20000), 'test.txt')
      .field({
        name: TEST_CREATE_DOCUMENT_REQUEST.name,
        type: TEST_CREATE_DOCUMENT_REQUEST.type,
        startDate: TEST_CREATE_DOCUMENT_REQUEST.startDate,
      });

    expect(response.status).toEqual(400);
    expect(response.body).toEqual({
      error: 'Bad Request',
      message:
        'Validation failed (current file type is text/plain, expected type is /pdf|word/)',
      statusCode: 400,
    });
  });

  it('should throw a validation error when file size is too large when uploading a document', async () => {
    const response = await request(app.getHttpServer())
      .post(`/groups/${TEST_GROUP.groupId}/documents`)
      .attach('file', Buffer.alloc(11_000_000), 'test.pdf')
      .field({
        name: TEST_CREATE_DOCUMENT_REQUEST.name,
        type: TEST_CREATE_DOCUMENT_REQUEST.type,
        startDate: TEST_CREATE_DOCUMENT_REQUEST.startDate,
      });

    expect(response.status).toEqual(400);
    expect(response.body).toEqual({
      error: 'Bad Request',
      message: 'Validation failed (expected size is less than 10000000)',
      statusCode: 400,
    });
  });

  it('should return a 403 error when user does not have the required role when getting documents', async () => {
    mockGetOidcUser.mockReturnValueOnce(TEST_OIDC_USER);
    const response = await request(app.getHttpServer())
      .get(`/groups/${TEST_GROUP.groupId}/documents`)
      .expect(403);

    expect(response.body).toEqual({
      statusCode: 403,
      message: 'Forbidden resource',
      error: 'Forbidden',
    });
  });

  it('should return a 403 error when user does not have the required role when uploading a document', async () => {
    mockGetOidcUser.mockReturnValueOnce(TEST_OIDC_USER);
    const response = await request(app.getHttpServer())
      .post(`/groups/${TEST_GROUP.groupId}/documents`)
      .attach('file', Buffer.alloc(20000), 'test.pdf')
      .field({
        name: TEST_CREATE_DOCUMENT_REQUEST.name,
        type: TEST_CREATE_DOCUMENT_REQUEST.type,
        startDate: TEST_CREATE_DOCUMENT_REQUEST.startDate,
      })
      .expect(403);

    expect(response.body).toEqual({
      statusCode: 403,
      message: 'Forbidden resource',
      error: 'Forbidden',
    });
  });

  it('should download a document', async () => {
    const mockDownloadDocument = jest
      .spyOn(service, 'downloadDocument')
      .mockResolvedValueOnce({
        filename: TEST_DOCUMENT.name,
        mimetype: 'application/pdf',
        buffer: Buffer.from('test content'),
      });

    const response = await request(app.getHttpServer()).get(
      `/groups/${TEST_GROUP.groupId}/documents/${TEST_DOCUMENT.id}/download`
    );

    expect(response.headers['content-disposition']).toContain(
      `attachment; filename=${TEST_DOCUMENT.name}`
    );
    expect(response.headers['content-type']).toEqual('application/pdf');
    expect(response.body).toEqual(Buffer.from('test content'));
    expect(mockDownloadDocument).toHaveBeenCalledWith(
      TEST_DOCUMENT.id,
      TEST_GROUP.groupId
    );
  });

  it('should throw an exception when trying to download if no document is found', async () => {
    jest
      .spyOn(service, 'downloadDocument')
      .mockRejectedValueOnce(new DocumentNotFoundException());

    const response = await request(app.getHttpServer()).get(
      `/groups/${TEST_GROUP.groupId}/documents/${TEST_DOCUMENT.id}/download`
    );

    expect(response.status).toEqual(404);
    expect(response.body).toEqual({
      statusCode: 404,
      message: 'Document not found',
      error: DocumentErrorCodes.DOCUMENT_NOT_FOUND,
    });
  });
});
