import { ConfigService } from '@nestjs/config';
import {
  GroupConfigEntityShallow,
  StatementsPrismaClient,
  TEST_GROUP_CONFIG_ENTITY,
  TEST_GROUP_CONFIG_ENTITY_WITH_ADDRESS,
  TEST_GROUP_CONFIG_ENTITY_WITH_DOCUMENT,
} from '@experience/commercial/statement-service/prisma/statements/client';
import { GroupNotFoundException } from '@experience/commercial/statement-service/nest/shared';
import { GroupsService } from './groups.service';
import { PrismaHealthIndicator } from '@nestjs/terminus';
import {
  SITE_ADMIN_API_URL,
  TEST_CREATE_GROUP_REQUEST,
  TEST_GROUP,
  TEST_GROUP_WITH_ADDRESS,
  TEST_SITE,
  TEST_UPDATE_GROUP_REQUEST,
} from '@experience/commercial/statement-service/shared';
import { SitesService } from '@experience/commercial/statement-service/nest/sites-module';
import { StripeCustomerService } from '@experience/shared/nest/stripe';
import { TEST_GROUP_WITH_TYPE } from '@experience/commercial/site-admin/typescript/domain-model';
import { Test, TestingModule } from '@nestjs/testing';

jest.mock('@experience/shared/nest/stripe');
jest.mock('./documents/documents.service');

describe('Groups service', () => {
  let service: GroupsService;
  let sitesService: SitesService;
  let stripeCustomerService: StripeCustomerService;
  let prismaClient: StatementsPrismaClient;

  beforeAll(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        ConfigService,
        GroupsService,
        PrismaHealthIndicator,
        SitesService,
        StatementsPrismaClient,
        StripeCustomerService,
      ],
    }).compile();

    service = module.get<GroupsService>(GroupsService);
    sitesService = module.get<SitesService>(SitesService);
    stripeCustomerService = module.get<StripeCustomerService>(
      StripeCustomerService
    );
    prismaClient = module.get<StatementsPrismaClient>(StatementsPrismaClient);
  });

  it('should be defined', () => {
    expect(service).toBeDefined();
    expect(sitesService).toBeDefined();
    expect(stripeCustomerService).toBeDefined();
  });

  it('should find all groups', async () => {
    const mockFindMany = (prismaClient.groupConfig.findMany = jest
      .fn()
      .mockReturnValueOnce([
        TEST_GROUP_CONFIG_ENTITY,
        TEST_GROUP_CONFIG_ENTITY_WITH_ADDRESS,
      ]));

    const groups = await service.findAllGroups();

    expect(mockFindMany).toHaveBeenCalledWith();
    expect(groups).toEqual([TEST_GROUP, TEST_GROUP_WITH_ADDRESS]);
  });

  it('should find all site admin groups', async () => {
    const mockFindGroups = jest.spyOn(global, 'fetch').mockResolvedValueOnce({
      json: () => [TEST_GROUP_WITH_TYPE],
    } as unknown as Response);

    const siteAdminGroups = await service.findSiteAdminGroups();

    expect(siteAdminGroups).toEqual([TEST_GROUP_WITH_TYPE]);
    expect(mockFindGroups).toHaveBeenCalledWith(
      `${SITE_ADMIN_API_URL}/user/groups?groupId=1`
    );
  });

  it('should filter out schemes when finding site admin groups', async () => {
    const mockFindGroups = jest.spyOn(global, 'fetch').mockResolvedValueOnce({
      json: () => [
        TEST_GROUP_WITH_TYPE,
        { ...TEST_GROUP_WITH_TYPE, type: 'scheme' },
      ],
    } as unknown as Response);

    const siteAdminGroups = await service.findSiteAdminGroups();

    expect(siteAdminGroups).toEqual([TEST_GROUP_WITH_TYPE]);
    expect(mockFindGroups).toHaveBeenCalledWith(
      `${SITE_ADMIN_API_URL}/user/groups?groupId=1`
    );
  });

  it('should fetch group config by groupId', async () => {
    const mockFindUnique = (prismaClient.groupConfig.findUnique = jest
      .fn()
      .mockReturnValueOnce(TEST_GROUP_CONFIG_ENTITY_WITH_DOCUMENT));

    const group = await service.findGroupByGroupId(
      TEST_GROUP_CONFIG_ENTITY_WITH_DOCUMENT.groupId
    );

    expect(mockFindUnique).toHaveBeenCalledWith({
      include: {
        GroupDocument: true,
      },
      where: {
        groupId: TEST_GROUP_CONFIG_ENTITY_WITH_DOCUMENT.groupId,
      },
    });
    expect(group).toEqual({ ...TEST_GROUP, hasContractDocument: true });
  });

  it('should throw an exception if the group config is not found', async () => {
    prismaClient.groupConfig.findUnique = jest.fn().mockReturnValueOnce(null);

    await expect(
      service.findGroupByGroupId(TEST_GROUP_CONFIG_ENTITY.groupId)
    ).rejects.toThrow(GroupNotFoundException);
  });

  it('should fetch group config by groupId including sites', async () => {
    const mockFindUnique = (prismaClient.groupConfig.findUnique = jest
      .fn()
      .mockReturnValueOnce(TEST_GROUP_CONFIG_ENTITY_WITH_DOCUMENT));
    const mockFindSites = (sitesService.findSiteByGroupId = jest
      .fn()
      .mockReturnValueOnce([TEST_SITE]));

    const group = await service.findGroupByGroupId(
      TEST_GROUP_CONFIG_ENTITY_WITH_DOCUMENT.groupId,
      true
    );

    expect(mockFindUnique).toHaveBeenCalledWith({
      include: {
        GroupDocument: true,
      },
      where: { groupId: TEST_GROUP_CONFIG_ENTITY_WITH_DOCUMENT.groupId },
    });
    expect(mockFindSites).toHaveBeenCalledWith(
      TEST_GROUP_CONFIG_ENTITY_WITH_DOCUMENT.groupId
    );

    expect(group).toEqual({
      ...TEST_GROUP,
      sites: [TEST_SITE],
      hasContractDocument: true,
    });
  });

  it('should update group config by groupId', async () => {
    const mockFindUnique = (prismaClient.groupConfig.findUnique = jest
      .fn()
      .mockReturnValueOnce(TEST_GROUP_CONFIG_ENTITY_WITH_ADDRESS));
    const mockUpdateGroupConfig = (prismaClient.groupConfig.update = jest.fn());

    await service.updateGroupByGroupId(
      TEST_GROUP_WITH_ADDRESS.groupId,
      TEST_UPDATE_GROUP_REQUEST
    );

    expect(mockFindUnique).toHaveBeenCalledWith({
      where: { groupId: TEST_GROUP_WITH_ADDRESS.groupId },
    });
    expect(mockUpdateGroupConfig).toHaveBeenCalledWith({
      data: TEST_UPDATE_GROUP_REQUEST,
      where: { groupId: TEST_GROUP_WITH_ADDRESS.groupId },
    });
  });

  it('should update Stripe when updating a group config', async () => {
    const mockFindUnique = (prismaClient.groupConfig.findUnique = jest
      .fn()
      .mockReturnValueOnce({
        ...TEST_GROUP_CONFIG_ENTITY_WITH_ADDRESS,
        stripeCustomerId: '12345',
      } as GroupConfigEntityShallow));
    const mockUpdateGroupConfig = (prismaClient.groupConfig.update = jest.fn());
    const mockUpdateManyGroupConfig = (prismaClient.groupConfig.updateMany =
      jest.fn());
    const mockUpdate = jest.spyOn(stripeCustomerService, 'update');

    await service.updateGroupByGroupId(
      TEST_GROUP_WITH_ADDRESS.groupId,
      TEST_UPDATE_GROUP_REQUEST
    );

    expect(mockFindUnique).toHaveBeenCalledWith({
      where: { groupId: TEST_GROUP_WITH_ADDRESS.groupId },
    });
    expect(mockUpdate).toHaveBeenCalledWith('12345', {
      email: TEST_UPDATE_GROUP_REQUEST.businessEmail,
      name: TEST_UPDATE_GROUP_REQUEST.businessName,
      address: {
        line1: TEST_UPDATE_GROUP_REQUEST.addressLine1,
        line2: TEST_UPDATE_GROUP_REQUEST.addressLine2,
        city: TEST_UPDATE_GROUP_REQUEST.town,
        postalCode: TEST_UPDATE_GROUP_REQUEST.postcode,
        country: 'GB',
      },
    });
    expect(mockUpdateGroupConfig).toHaveBeenCalledWith({
      data: TEST_UPDATE_GROUP_REQUEST,
      where: { groupId: TEST_GROUP_WITH_ADDRESS.groupId },
    });
    expect(mockUpdateManyGroupConfig).toHaveBeenCalledWith({
      data: {
        businessName: TEST_UPDATE_GROUP_REQUEST.businessName,
        businessEmail: TEST_UPDATE_GROUP_REQUEST.businessEmail,
        addressLine1: TEST_UPDATE_GROUP_REQUEST.addressLine1,
        addressLine2: TEST_UPDATE_GROUP_REQUEST.addressLine2,
        town: TEST_UPDATE_GROUP_REQUEST.town,
        postcode: TEST_UPDATE_GROUP_REQUEST.postcode,
        county: TEST_UPDATE_GROUP_REQUEST.county,
      },
      where: { stripeCustomerId: '12345' },
    });
  });

  it('should return an error and not call update if group config is not found', async () => {
    prismaClient.groupConfig.findUnique = jest.fn().mockReturnValueOnce(null);
    const mockUpdateGroupConfig = (prismaClient.groupConfig.update = jest
      .fn()
      .mockReturnValueOnce(TEST_GROUP_CONFIG_ENTITY_WITH_ADDRESS));

    await expect(() =>
      service.updateGroupByGroupId(
        TEST_GROUP_WITH_ADDRESS.groupId,
        TEST_UPDATE_GROUP_REQUEST
      )
    ).rejects.toThrow(GroupNotFoundException);

    expect(mockUpdateGroupConfig).not.toHaveBeenCalled();
  });

  it('should create a group config', async () => {
    const mockCreate = (prismaClient.groupConfig.create = jest.fn());

    await service.createGroup(TEST_CREATE_GROUP_REQUEST);

    expect(mockCreate).toHaveBeenCalledWith({
      data: TEST_CREATE_GROUP_REQUEST,
    });
  });

  it('should update transfers enabled by connected account ID', async () => {
    const mockUpdateMany = (prismaClient.groupConfig.updateMany = jest.fn());

    await service.updateTransfersEnabledByConnectedAccountId(
      TEST_GROUP.stripeConnectedAccountId as string,
      true
    );

    expect(mockUpdateMany).toHaveBeenCalledWith({
      where: {
        stripeConnectedAccountId: TEST_GROUP.stripeConnectedAccountId,
      },
      data: {
        transfersEnabled: true,
      },
    });
  });

  it('should update subscription status by subscription ID', async () => {
    const mockUpdateMany = (prismaClient.groupConfig.updateMany = jest.fn());

    await service.updateSubscriptionStatusBySubscriptionId(
      TEST_GROUP.stripeSubscriptionId as string,
      'active'
    );

    expect(mockUpdateMany).toHaveBeenCalledWith({
      where: {
        stripeSubscriptionId: TEST_GROUP.stripeSubscriptionId,
      },
      data: {
        stripeSubscriptionStatus: 'active',
      },
    });
  });
});
