import { ConfigModule, ConfigService } from '@nestjs/config';
import { Module } from '@nestjs/common';
import {
  QueueConsumerService,
  SqsConsumerModule,
} from '@experience/shared/nest/aws/sqs-module';
import { SqsConsumerModuleOptions } from '@experience/shared/nest/aws/sqs-module';

export abstract class SendQueueConsumerService extends QueueConsumerService {}

@Module({
  imports: [
    ConfigModule,
    SqsConsumerModule.registerAsync({
      imports: [ConfigModule],
      useFactory: (config: ConfigService): SqsConsumerModuleOptions => ({
        queueUrl: config.get<string>('STATEMENTS_TO_SEND_QUEUE_URL'),
      }),
      inject: [ConfigService],
    }),
  ],
  providers: [
    {
      provide: SendQueueConsumerService,
      useExisting: QueueConsumerService,
    },
  ],
  exports: [SendQueueConsumerService],
})
export class StatementsToSendConsumerModule {}
