# Socket

## Properties

| Name                  | Type        | Description | Notes                             |
| --------------------- | ----------- | ----------- | --------------------------------- |
| **door**              | **string**  |             | [default to undefined]            |
| **firmwareVersion**   | **string**  |             | [default to undefined]            |
| **isUpdateAvailable** | **boolean** |             | [default to undefined]            |
| **lastContact**       | **string**  |             | [optional] [default to undefined] |
| **serialNumber**      | **string**  |             | [default to undefined]            |
| **status**            | **string**  |             | [default to undefined]            |

## Example

```typescript
import { Socket } from './api';

const instance: Socket = {
  door,
  firmwareVersion,
  isUpdateAvailable,
  lastContact,
  serialNumber,
  status,
};
```

[[Back to Model list]](../README.md#documentation-for-models) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to README]](../README.md)
