# AdjustedFee

## Properties

| Name     | Type       | Description | Notes                             |
| -------- | ---------- | ----------- | --------------------------------- |
| **fee**  | **number** |             | [optional] [default to undefined] |
| **ppid** | **string** |             | [default to undefined]            |

## Example

```typescript
import { AdjustedFee } from './api';

const instance: AdjustedFee = {
  fee,
  ppid,
};
```

[[Back to Model list]](../README.md#documentation-for-models) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to README]](../README.md)
