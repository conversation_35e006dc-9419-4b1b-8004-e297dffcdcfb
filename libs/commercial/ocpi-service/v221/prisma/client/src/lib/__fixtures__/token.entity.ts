import { TEST_TOKEN_ENTITY_GROUP_ID, TEST_TOKEN_ENTITY_UID } from './common';
import { Token } from '@prisma/clients/ocpi/v221';
import dayjs from 'dayjs';

export const TEST_TOKEN_ENTITY: Token = {
  contractId: 'DE*GEF*001',
  countryCode: 'GB',
  created: dayjs().utc().toDate(),
  groupId: TEST_TOKEN_ENTITY_GROUP_ID,
  id: TEST_TOKEN_ENTITY_UID,
  issuer: 'TST',
  language: 'EN',
  lastUpdated: dayjs().utc().toDate(),
  partyId: 'TST',
  type: 'APP_USER',
  uid: TEST_TOKEN_ENTITY_UID,
  valid: true,
  visualNumber: '1234567890',
  whitelist: 'ALLOWED',
};
