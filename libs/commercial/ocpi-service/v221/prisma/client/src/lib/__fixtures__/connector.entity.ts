import {
  Connector,
  ConnectorFormat,
  ConnectorPowerType,
  ConnectorStandard,
} from '@prisma/clients/ocpi/v221';
import { TEST_CONNECTOR_ENTITY_ID, TEST_EVSE_ENTITY_ID } from './common';
import { TEST_TARIFF_ENTITY } from './tariff.entity';
import dayjs from 'dayjs';

export const TEST_CONNECTOR_ENTITY: Connector = {
  created: dayjs().utc().toDate(),
  evseId: TEST_EVSE_ENTITY_ID,
  format: ConnectorFormat.SOCKET,
  id: TEST_CONNECTOR_ENTITY_ID,
  lastUpdated: dayjs().utc().toDate(),
  lastUpdatedInPodadmin: dayjs().utc().toDate(),
  maxAmperage: 200,
  maxElectricPower: 300000,
  maxVoltage: 400,
  powerType: ConnectorPowerType.DC,
  socket: 'A',
  standard: ConnectorStandard.IEC_62196_T2,
};

export const TEST_CONNECTOR_ENTITY_DEEP = {
  ...TEST_CONNECTOR_ENTITY,
  tariffs: [TEST_TARIFF_ENTITY],
};
