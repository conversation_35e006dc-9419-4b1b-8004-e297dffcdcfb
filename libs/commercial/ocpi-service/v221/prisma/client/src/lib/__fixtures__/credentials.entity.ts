import {
  $Enums,
  Credentials,
  CredentialsRole,
} from '@prisma/clients/ocpi/v221';

type CredentialsEntity = Credentials & { roles: CredentialsRole[] };

export const TEST_EMSP_CREDENTIALS_ROLE_ENTITY: CredentialsRole = {
  businessDetailsName: 'Test EMSP',
  countryCode: 'GB',
  credentialsId: '8b5418f5-e84d-46ad-aed1-c44d34b263b0',
  id: '953b2082-e881-4adb-9e57-da4d02f08b77',
  partyId: 'TST',
  role: $Enums.CredentialsRoleType.EMSP,
};

export const TEST_EMSP_CREDENTIALS_ENTITY: CredentialsEntity = {
  clientCredentialsId: null,
  deletedAt: null,
  enableCdrsModule: false,
  enableCommandsModule: false,
  enableSessionsModule: false,
  enableTokensModule: false,
  id: '8b5418f5-e84d-46ad-aed1-c44d34b263b0',
  object: {},
  pullTokens: false,
  pushCdrs: false,
  pushCdrsReceiver: null,
  pushEvses: false,
  pushEvsesReceiver: null,
  pushLocations: false,
  pushLocationsReceiver: null,
  pushSessions: false,
  pushSessionsReceiver: null,
  pushTariffs: false,
  pushTariffsReceiver: null,
  roles: [TEST_EMSP_CREDENTIALS_ROLE_ENTITY],
  serverCredentialsId: null,
  token: 'c5b2bcd6-d42a-4f71-8169-225e7f31d226',
  tokenHash: '0147b87ec3ec08e29bd888e3542f7bfd',
  url: 'http://localhost:9876/ocpi/emsp/versions',
};

export const TEST_CPO_ROLE_ENTITY: CredentialsRole = {
  businessDetailsName: 'Pod Point',
  countryCode: 'GB',
  credentialsId: 'f4e6b9d4-5f6e-4e9f-9d8d-6b3e6f3d0f8b',
  id: '49ebbf61-0034-4231-9a8d-2e8dc1e1ea3f',
  partyId: 'POD',
  role: $Enums.CredentialsRoleType.CPO,
};

export const TEST_CPO_CREDENTIALS_ENTITY: CredentialsEntity = {
  clientCredentialsId: null,
  deletedAt: null,
  enableCdrsModule: false,
  enableCommandsModule: false,
  enableSessionsModule: false,
  enableTokensModule: false,
  id: 'f4e6b9d4-5f6e-4e9f-9d8d-6b3e6f3d0f8b',
  object: {},
  pullTokens: false,
  pushCdrs: false,
  pushCdrsReceiver: null,
  pushEvses: false,
  pushEvsesReceiver: null,
  pushLocations: false,
  pushLocationsReceiver: null,
  pushSessions: false,
  pushSessionsReceiver: null,
  pushTariffs: false,
  pushTariffsReceiver: null,
  roles: [TEST_CPO_ROLE_ENTITY],
  serverCredentialsId: null,
  token: '2de68551-2587-4722-aceb-38712c78f4e2',
  tokenHash: '0147b87ec3ec08e29bd888e3542f7efc',
  url: 'https://cpo.com/ocpi/cpo/versions',
};
