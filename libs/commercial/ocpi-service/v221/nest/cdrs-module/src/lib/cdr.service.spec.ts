import { AsyncLocalStorage } from 'async_hooks';
import {
  CdrNotFoundException,
  CdrSchema,
  CredentialsStore,
  Session,
} from '@experience/commercial/ocpi-service/v221/shared';
import { CdrService } from './cdr.service';
import { ConfigModule } from '@nestjs/config';
import { LocationService } from '@experience/commercial/ocpi-service/v221/nest/locations-module';
import { OCPIPrismaClient } from '@experience/commercial/ocpi-service/v221/prisma/client';
import { PrismaHealthIndicator } from '@nestjs/terminus';
import { SessionService } from '@experience/commercial/ocpi-service/v221/nest/sessions-module';
import { SnsClientService } from '@experience/shared/nest/aws/sns-module';
import {
  TEST_CDR,
  TEST_CDR_PAGE,
  TEST_COMPLETED_SESSION,
  TEST_CONNECTOR,
  TEST_CREDENTIALS_A,
  TEST_CREDENTIALS_A_ID,
  TEST_CREDENTIALS_B,
  TEST_CREDENTIALS_STORE,
  TEST_CREDENTIALS_STORE_FOR_PUSH_CDRS,
  TEST_EVSE,
  TEST_LOCATION,
  UUID_FORMAT,
} from '@experience/commercial/ocpi-service/v221/shared/specs';
import {
  TEST_CDR_ENTITY,
  TEST_CDR_LOCATION_ENTITY,
  TEST_CHARGING_PERIOD_ENTITY,
  TEST_COMPLETED_SESSION_ENTITY,
  TEST_DEEP_CDR_ENTITY,
  TEST_TARIFF_ENTITY,
} from '@experience/commercial/ocpi-service/v221/prisma/client/specs';
import { Test, TestingModule } from '@nestjs/testing';
import MockDate from 'mockdate';

jest.mock('uuid', () => ({ v4: () => 'dfeef27d-5902-4eae-8407-488ce0d11dde' }));
jest.mock('@experience/shared/nest/aws/sns-module');

describe('CdrService', () => {
  let als: AsyncLocalStorage<CredentialsStore>;
  let cdrService: CdrService;
  let database: OCPIPrismaClient;
  let locationService: LocationService;
  let sessionService: SessionService;
  let snsClientService: SnsClientService;

  const dateFrom = new Date(0);
  const dateTo = new Date();
  const defaultPaginationParams = { dateFrom, dateTo, limit: 100, offset: 0 };

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      imports: [
        await ConfigModule.forRoot({
          load: [
            () => ({
              EVENTS_TOPIC_ARN:
                'arn:aws:sns:us-east-1:************:ocpi-events',
            }),
          ],
        }),
      ],
      providers: [
        AsyncLocalStorage<CredentialsStore>,
        CdrService,
        LocationService,
        OCPIPrismaClient,
        PrismaHealthIndicator,
        SessionService,
        SnsClientService,
        { provide: 'ALS_PRISMA_OCPI_V221', useValue: new AsyncLocalStorage() },
      ],
    }).compile();

    als = module.get(AsyncLocalStorage<CredentialsStore>);
    cdrService = module.get<CdrService>(CdrService);
    database = module.get<OCPIPrismaClient>(OCPIPrismaClient);
    locationService = module.get<LocationService>(LocationService);
    sessionService = module.get<SessionService>(SessionService);
    snsClientService = module.get<SnsClientService>(SnsClientService);

    jest
      .spyOn(database, '$transaction')
      .mockImplementation((operations) => Promise.all(operations));

    MockDate.set(new Date());
  });

  it('should be defined', () => {
    expect(als).toBeDefined();
    expect(cdrService).toBeDefined();
    expect(database).toBeDefined();
    expect(locationService).toBeDefined();
    expect(sessionService).toBeDefined();
    expect(snsClientService).toBeDefined();
  });

  it.each([
    ['session', TEST_COMPLETED_SESSION],
    ['session id', TEST_COMPLETED_SESSION.id],
  ])('should create cdr from a %s', async (_, session: string | Session) => {
    const mockFindSession = (sessionService.find = jest
      .fn()
      .mockResolvedValueOnce(TEST_COMPLETED_SESSION));
    const mockFindLocation = (locationService.findLocation = jest
      .fn()
      .mockResolvedValueOnce(TEST_LOCATION));
    const mockFindEvse = (locationService.findEvse = jest
      .fn()
      .mockResolvedValueOnce(TEST_EVSE));
    const mockFindConnector = (locationService.findConnector = jest
      .fn()
      .mockResolvedValueOnce(TEST_CONNECTOR));
    const mockCreateCdr = (database.cdr.create = jest
      .fn()
      .mockResolvedValueOnce(TEST_DEEP_CDR_ENTITY));
    const mockPublishMessage = (snsClientService.publishMessage = jest.fn());

    const cdr = await cdrService.create(session);

    expect(cdr).toEqual(TEST_CDR);
    typeof session === 'string'
      ? expect(mockFindSession).toHaveBeenCalledWith(TEST_COMPLETED_SESSION.id)
      : expect(mockFindSession).not.toHaveBeenCalled();
    expect(mockFindLocation).toHaveBeenCalledWith(TEST_LOCATION.id);
    expect(mockFindEvse).toHaveBeenCalledWith(TEST_LOCATION.id, TEST_EVSE.uid);
    expect(mockFindConnector).toHaveBeenCalledWith(
      TEST_LOCATION.id,
      TEST_EVSE.uid,
      TEST_CONNECTOR.id
    );
    expect(mockCreateCdr).toHaveBeenCalledWith({
      include: {
        chargingPeriods: true,
        location: true,
        session: { include: { token: true } },
        tariff: { include: { elements: true } },
      },
      data: {
        ...TEST_CDR_ENTITY,
        chargingPeriods: {
          create: {
            ...TEST_CHARGING_PERIOD_ENTITY,
            cdrId: undefined,
            id: undefined,
          },
        },
        created: undefined,
        id: undefined,
        lastUpdated: undefined,
        location: { create: { ...TEST_CDR_LOCATION_ENTITY, id: undefined } },
        locationId: undefined,
        session: { connect: { id: TEST_COMPLETED_SESSION_ENTITY.id } },
        sessionId: undefined,
        tariff: { connect: { id: TEST_TARIFF_ENTITY.id } },
        tariffId: undefined,
      },
    });
    expect(mockPublishMessage).toHaveBeenCalledWith(
      'arn:aws:sns:us-east-1:************:ocpi-events',
      {
        message: JSON.stringify({
          routingKey: `OCPI.CDR.Created.${TEST_EVSE.physical_reference}`,
          type: 'OCPI.CDR.Created',
          eventId: 'dfeef27d-5902-4eae-8407-488ce0d11dde',
          publishedAt: new Date(),
          data: {
            location: TEST_LOCATION,
            evse: TEST_EVSE,
            connector: TEST_CONNECTOR,
            cdr: CdrSchema.parse(TEST_CDR),
          },
        }),
      }
    );
  });

  it('should return a cdr', async () => {
    const mockFindUnique = (database.cdr.findUnique = jest
      .fn()
      .mockResolvedValueOnce(TEST_DEEP_CDR_ENTITY));

    const cdr = await cdrService.find(TEST_DEEP_CDR_ENTITY.id);

    expect(cdr).toEqual(TEST_CDR);
    expect(mockFindUnique).toHaveBeenCalledWith({
      include: {
        chargingPeriods: true,
        location: true,
        session: { include: { token: true } },
        tariff: { include: { elements: true } },
      },
      where: { id: TEST_DEEP_CDR_ENTITY.id },
    });
  });

  it('should return a cdr by session id', async () => {
    const mockFindUnique = (database.cdr.findUnique = jest
      .fn()
      .mockResolvedValueOnce(TEST_DEEP_CDR_ENTITY));

    const cdr = await cdrService.findBySessionId(
      TEST_DEEP_CDR_ENTITY.sessionId
    );

    expect(cdr).toEqual(TEST_CDR);
    expect(mockFindUnique).toHaveBeenCalledWith({
      include: {
        chargingPeriods: true,
        location: true,
        session: { include: { token: true } },
        tariff: { include: { elements: true } },
      },
      where: { sessionId: TEST_DEEP_CDR_ENTITY.sessionId },
    });
  });

  it('should throw an error when cdr is not found', async () => {
    const mockFindUnique = (database.cdr.findUnique = jest
      .fn()
      .mockResolvedValueOnce(null));

    await expect(cdrService.find(TEST_DEEP_CDR_ENTITY.id)).rejects.toThrow(
      CdrNotFoundException
    );

    expect(mockFindUnique).toHaveBeenCalledWith({
      include: {
        chargingPeriods: true,
        location: true,
        session: { include: { token: true } },
        tariff: { include: { elements: true } },
      },
      where: { id: TEST_DEEP_CDR_ENTITY.id },
    });
  });

  it('should throw an error when cdr is not found by session id', async () => {
    const mockFindUnique = (database.cdr.findUnique = jest
      .fn()
      .mockResolvedValueOnce(null));

    await expect(
      cdrService.findBySessionId(TEST_DEEP_CDR_ENTITY.sessionId)
    ).rejects.toThrow(CdrNotFoundException);

    expect(mockFindUnique).toHaveBeenCalledWith({
      include: {
        chargingPeriods: true,
        location: true,
        session: { include: { token: true } },
        tariff: { include: { elements: true } },
      },
      where: { sessionId: TEST_DEEP_CDR_ENTITY.sessionId },
    });
  });

  it('should push cdr to emsp', async () => {
    jest
      .spyOn(als, 'getStore')
      .mockReturnValue(TEST_CREDENTIALS_STORE_FOR_PUSH_CDRS);
    database.cdr.findUnique = jest
      .fn()
      .mockResolvedValueOnce(TEST_DEEP_CDR_ENTITY);
    const mockFetch = jest.spyOn(global, 'fetch').mockResolvedValue({
      headers: {
        get: jest
          .fn()
          .mockImplementation((name) =>
            name === 'location'
              ? `http://localhost:9876/ocpi/emsp/2.2/cdrs/${TEST_CDR.id}`
              : null
          ),
      },
      ok: true,
      json: { success: true },
      status: 200,
    } as unknown as Response);
    const mockUpdateCdr = (database.cdr.update = jest
      .fn()
      .mockResolvedValueOnce(TEST_DEEP_CDR_ENTITY));

    await cdrService.push(TEST_CDR);

    expect(mockFetch).toHaveBeenCalledWith(
      'http://localhost:9876/ocpi/emsp/2.2.1/cdrs',
      {
        body: JSON.stringify(CdrSchema.parse(TEST_CDR)),
        headers: {
          Authorization: `Token ${Buffer.from(
            TEST_CREDENTIALS_B.token
          ).toString('base64')}`,
          'content-type': 'application/json',
          'x-correlation-id': expect.stringMatching(UUID_FORMAT),
          'x-request-id': expect.stringMatching(UUID_FORMAT),
        },
        method: 'POST',
      }
    );
    expect(mockUpdateCdr).toHaveBeenCalledWith({
      data: {
        emspLocation: `http://localhost:9876/ocpi/emsp/2.2/cdrs/${TEST_CDR.id}`,
      },
      where: {
        id: TEST_CDR.id,
      },
    });
  });

  it('should return a list of all cdrs for a given client', async () => {
    jest.spyOn(als, 'getStore').mockReturnValue(TEST_CREDENTIALS_STORE);
    const mockFindMany = (database.cdr.findMany = jest
      .fn()
      .mockResolvedValueOnce([TEST_DEEP_CDR_ENTITY]));
    database.cdr.count = jest.fn().mockResolvedValueOnce(1);

    const cdrs = await cdrService.findAll(defaultPaginationParams);

    expect(mockFindMany).toHaveBeenCalledWith({
      include: {
        chargingPeriods: true,
        location: true,
        session: { include: { token: true } },
        tariff: { include: { elements: true } },
      },
      orderBy: { lastUpdated: 'asc' },
      skip: 0,
      take: 100,
      where: {
        lastUpdated: { gte: dateFrom, lt: dateTo },
        session: {
          token: {
            countryCode: TEST_CREDENTIALS_B.roles[0].country_code,
            partyId: TEST_CREDENTIALS_B.roles[0].party_id,
          },
        },
      },
    });
    expect(cdrs).toEqual(TEST_CDR_PAGE);
  });

  it('should return a empty page if the client does not have the correct role', async () => {
    jest.spyOn(als, 'getStore').mockReturnValue({
      clientCredentials: TEST_CREDENTIALS_A,
      clientCredentialsId: TEST_CREDENTIALS_A_ID,
      serverCredentials: TEST_CREDENTIALS_A,
      serverCredentialsId: TEST_CREDENTIALS_A_ID,
    });
    const mockFindMany = (database.cdr.findMany = jest.fn());
    const mockCount = (database.cdr.count = jest.fn());

    const cdrs = await cdrService.findAll(defaultPaginationParams);

    expect(mockFindMany).not.toHaveBeenCalled();
    expect(mockCount).not.toHaveBeenCalled();
    expect(cdrs).toEqual({
      elements: [],
      number: 1,
      size: 0,
      totalElements: 0,
      totalPages: 0,
    });
  });

  it('should return a list of all cdrs excluding those which have been pushed', async () => {
    jest.spyOn(als, 'getStore').mockReturnValue(TEST_CREDENTIALS_STORE);
    const mockFindMany = (database.cdr.findMany = jest
      .fn()
      .mockResolvedValueOnce([TEST_DEEP_CDR_ENTITY]));
    database.cdr.count = jest.fn().mockResolvedValueOnce(1);

    const cdrs = await cdrService.findAll(defaultPaginationParams, true);

    expect(mockFindMany).toHaveBeenCalledWith({
      include: {
        chargingPeriods: true,
        location: true,
        session: { include: { token: true } },
        tariff: { include: { elements: true } },
      },
      orderBy: { lastUpdated: 'asc' },
      skip: 0,
      take: 100,
      where: {
        emspLocation: { not: null },
        lastUpdated: { gte: dateFrom, lt: dateTo },
        session: {
          token: {
            countryCode: TEST_CREDENTIALS_B.roles[0].country_code,
            partyId: TEST_CREDENTIALS_B.roles[0].party_id,
          },
        },
      },
    });
    expect(cdrs).toEqual(TEST_CDR_PAGE);
  });
});
