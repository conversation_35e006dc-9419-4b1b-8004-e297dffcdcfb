import { SQSClient, SendMessageCommand } from '@aws-sdk/client-sqs';
import {
  TEST_CHARGE_NOTIFICATION_EVENT,
  TEST_CORRELATION_ID,
  TEST_CREDENTIALS_SERVER_POD_EMSP,
  TEST_REQUEST_ID,
} from '@experience/commercial/ocpi-service/v221/shared/specs';
import { cdrArrayPropertyMatchers } from '../cdrs.module.spec';
import { waitFor } from '@experience/shared/typescript/utils';
import axios from 'axios';

const authorization = `Token ${Buffer.from(
  TEST_CREDENTIALS_SERVER_POD_EMSP.token
).toString('base64')}`;

export const describeCdrsConsumerModule = (baseUrl: string) => {
  describe('describeCdrsConsumerModule', () => {
    const sqsClient = new SQSClient({
      region: 'eu-west-1',
      endpoint: 'http://localhost:4566',
      disableHostPrefix: true,
      credentials: {
        accessKeyId: 'test',
        secretAccessKey: 'test',
      },
    });

    it('should handle a charge notification event', async () => {
      const headers = {
        headers: {
          Authorization: authorization,
          'x-correlation-id': TEST_CORRELATION_ID,
          'x-request-id': TEST_REQUEST_ID,
        },
      };
      const messageBody = JSON.stringify({
        Message: JSON.stringify(TEST_CHARGE_NOTIFICATION_EVENT),
      });
      await sqsClient.send(
        new SendMessageCommand({
          QueueUrl: `http://sqs.eu-west-1.localhost.localstack.cloud:4566/000000000000/ocpi-service-charge-notifications`,
          MessageBody: messageBody,
        })
      );

      await waitFor(async () => {
        expect(
          (await axios.get(`${baseUrl}/ocpi/cpo/2.2.1/cdrs`, headers)).headers[
            'x-total-count'
          ]
        ).toEqual('1');
      });

      const response = await axios.get(
        `${baseUrl}/ocpi/cpo/2.2.1/cdrs`,
        headers
      );
      expect(response.data).toMatchSnapshot(
        cdrArrayPropertyMatchers(response.data)
      );
    });
  });
};
