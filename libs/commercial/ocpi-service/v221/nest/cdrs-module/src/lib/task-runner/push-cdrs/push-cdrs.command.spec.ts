import { AsyncLocalStorage } from 'async_hooks';
import { Authorisers } from '@experience/shared/sequelize/podadmin';
import { CdrService } from '../../cdr.service';
import { ConfigModule } from '@nestjs/config';
import { CredentialService } from '@experience/commercial/ocpi-service/v221/nest/credentials-module';
import { CredentialsStore } from '@experience/commercial/ocpi-service/v221/shared';
import { DEFAULT_LIMIT, DEFAULT_OFFSET } from '@experience/shared/nest/utils';
import { OCPIPrismaClient } from '@experience/commercial/ocpi-service/v221/prisma/client';
import { PrismaHealthIndicator } from '@nestjs/terminus';
import { PushCdrsCommand } from './push-cdrs.command';
import { SqsClientService } from '@experience/shared/nest/aws/sqs-module';
import {
  TEST_CDR,
  TEST_CDR_PAGE,
  TEST_CREDENTIALS_STORE_FOR_PUSH_CDRS,
} from '@experience/commercial/ocpi-service/v221/shared/specs';
import { Test, TestingModule } from '@nestjs/testing';
import MockDate from 'mockdate';
import dayjs from 'dayjs';

jest.mock('@experience/shared/nest/aws/sqs-module');
jest.mock('../../cdr.service');

describe('push cdrs command', () => {
  let command: PushCdrsCommand;
  let cdrService: CdrService;
  let credentialService: CredentialService;
  let sqsClientService: SqsClientService;

  MockDate.set(new Date());

  beforeAll(async () => {
    const module: TestingModule = await Test.createTestingModule({
      imports: [
        await ConfigModule.forRoot({
          load: [
            () => ({
              PUSH_CDRS_QUEUE_URL: 'http://localhost/000000/push-cdrs',
            }),
          ],
        }),
      ],
      providers: [
        AsyncLocalStorage<CredentialsStore>,
        CdrService,
        CredentialService,
        OCPIPrismaClient,
        PrismaHealthIndicator,
        PushCdrsCommand,
        SqsClientService,
        { provide: 'ALS_PRISMA_OCPI_V221', useValue: new AsyncLocalStorage() },
        { provide: 'AUTHORISERS_REPOSITORY', useValue: Authorisers },
      ],
    }).compile();

    command = module.get<PushCdrsCommand>(PushCdrsCommand);
    cdrService = module.get<CdrService>(CdrService);
    credentialService = module.get<CredentialService>(CredentialService);
    sqsClientService = module.get<SqsClientService>(SqsClientService);
  });

  it('should be defined', () => {
    expect(command).toBeDefined();
    expect(cdrService).toBeDefined();
    expect(credentialService).toBeDefined();
    expect(sqsClientService).toBeDefined();
  });

  it('should queue a push cdr event', async () => {
    const mockCreateStoresForPushCdrs = jest
      .spyOn(credentialService, 'createStoresForPushCdrs')
      .mockResolvedValueOnce([TEST_CREDENTIALS_STORE_FOR_PUSH_CDRS]);
    const mockFindAll = jest
      .spyOn(cdrService, 'findAll')
      .mockResolvedValueOnce(TEST_CDR_PAGE);
    const mockSendMessage = jest.spyOn(sqsClientService, 'sendMessage');

    await command.run();

    expect(mockCreateStoresForPushCdrs).toHaveBeenCalled();
    expect(mockFindAll).toHaveBeenCalledWith(
      {
        dateFrom: dayjs(new Date()).subtract(10, 'minutes').toDate(),
        dateTo: new Date(),
        limit: DEFAULT_LIMIT,
        offset: DEFAULT_OFFSET,
      },
      true
    );
    expect(mockSendMessage).toHaveBeenCalledWith(
      'http://localhost/000000/push-cdrs',
      { messageBody: TEST_CDR.id }
    );
  });

  it('should queue multiple push cdr events and paginate', async () => {
    const mockCreateStoresForPushCdrs = jest
      .spyOn(credentialService, 'createStoresForPushCdrs')
      .mockResolvedValueOnce([TEST_CREDENTIALS_STORE_FOR_PUSH_CDRS])
      .mockResolvedValueOnce([TEST_CREDENTIALS_STORE_FOR_PUSH_CDRS]);
    const mockFindAll = jest
      .spyOn(cdrService, 'findAll')
      .mockResolvedValueOnce({
        ...TEST_CDR_PAGE,
        number: 1,
        size: DEFAULT_LIMIT,
        totalElements: DEFAULT_LIMIT + 1,
        totalPages: 2,
      })
      .mockResolvedValueOnce({
        ...TEST_CDR_PAGE,
        number: 2,
        size: 1,
        totalElements: DEFAULT_LIMIT + 1,
        totalPages: 2,
      });
    const mockSendMessage = jest.spyOn(sqsClientService, 'sendMessage');

    await command.run();

    expect(mockCreateStoresForPushCdrs).toHaveBeenCalled();
    expect(mockFindAll).toHaveBeenNthCalledWith(
      1,
      {
        dateFrom: dayjs(new Date()).subtract(10, 'minutes').toDate(),
        dateTo: new Date(),
        limit: DEFAULT_LIMIT,
        offset: DEFAULT_OFFSET,
      },
      true
    );
    expect(mockFindAll).toHaveBeenNthCalledWith(
      2,
      {
        dateFrom: dayjs(new Date()).subtract(10, 'minutes').toDate(),
        dateTo: new Date(),
        limit: DEFAULT_LIMIT,
        offset: DEFAULT_OFFSET + DEFAULT_LIMIT,
      },
      true
    );
    expect(mockSendMessage).toHaveBeenNthCalledWith(
      1,
      'http://localhost/000000/push-cdrs',
      { messageBody: TEST_CDR.id }
    );
    expect(mockSendMessage).toHaveBeenNthCalledWith(
      2,
      'http://localhost/000000/push-cdrs',
      { messageBody: TEST_CDR.id }
    );
  });
});
