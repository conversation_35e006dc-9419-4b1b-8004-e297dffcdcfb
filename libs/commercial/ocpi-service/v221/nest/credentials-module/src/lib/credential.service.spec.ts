import { AsyncLocalStorage } from 'async_hooks';
import {
  Authorisers,
  TEST_AUTHORISER_ENTITY_WITH_TYPE_OCPI,
} from '@experience/shared/sequelize/podadmin';
import { ConfigService } from '@nestjs/config';
import {
  CredentialService,
  credentialsDeepIncludeOptions,
} from './credential.service';
import {
  CredentialsNotFoundException,
  CredentialsStore,
  Role,
} from '@experience/commercial/ocpi-service/v221/shared';
import { OCPIPrismaClient } from '@experience/commercial/ocpi-service/v221/prisma/client';
import { PrismaHealthIndicator } from '@nestjs/terminus';
import {
  TEST_CDR_TOKEN,
  TEST_CPO_BASE_URL,
  TEST_CREDENTIALS_B,
  TEST_CREDENTIALS_B_ID,
  TEST_CREDENTIALS_C,
  TEST_CREDENTIALS_C_ID,
  TEST_CREDENTIALS_STORE,
  TEST_TOKEN,
} from '@experience/commercial/ocpi-service/v221/shared/specs';
import {
  TEST_CPO_CREDENTIALS_ENTITY,
  TEST_EMSP_CREDENTIALS_ENTITY,
} from '@experience/commercial/ocpi-service/v221/prisma/client/specs';
import { Test, TestingModule } from '@nestjs/testing';

const mockUuid = jest.fn();

jest.mock('uuid', () => ({
  v4: () => mockUuid(),
}));

describe('CredentialService', () => {
  let als: AsyncLocalStorage<CredentialsStore>;
  let authorisers: typeof Authorisers;
  let database: OCPIPrismaClient;
  let service: CredentialService;

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        AsyncLocalStorage<CredentialsStore>,
        ConfigService,
        CredentialService,
        OCPIPrismaClient,
        PrismaHealthIndicator,
        { provide: 'ALS_PRISMA_OCPI_V221', useValue: new AsyncLocalStorage() },
        { provide: 'AUTHORISERS_REPOSITORY', useValue: Authorisers },
      ],
    }).compile();

    als = module.get(AsyncLocalStorage<CredentialsStore>);
    authorisers = module.get<typeof Authorisers>('AUTHORISERS_REPOSITORY');
    database = module.get<OCPIPrismaClient>(OCPIPrismaClient);
    service = module.get<CredentialService>(CredentialService);

    jest.spyOn(als, 'getStore').mockReturnValue(TEST_CREDENTIALS_STORE);
  });

  it('should be defined', () => {
    expect(als).toBeDefined();
    expect(authorisers).toBeDefined();
    expect(database).toBeDefined();
    expect(service).toBeDefined();
  });

  it('should create new client credentials in our database and return our credentials for the client', async () => {
    const mockCreate = (database.credentials.create = jest
      .fn()
      .mockResolvedValueOnce(TEST_EMSP_CREDENTIALS_ENTITY)
      .mockResolvedValueOnce(TEST_CPO_CREDENTIALS_ENTITY));
    const mockUpdate = (database.credentials.update = jest
      .fn()
      .mockResolvedValueOnce(TEST_EMSP_CREDENTIALS_ENTITY)
      .mockResolvedValueOnce(TEST_CPO_CREDENTIALS_ENTITY));
    const mockFindOneAuthorisers = (authorisers.findOne = jest.fn());
    const mockCreateAuthorisers = (authorisers.create = jest.fn());
    const mockDestroyAuthorisers = (authorisers.destroy = jest.fn());
    jest.spyOn(als, 'getStore').mockReturnValueOnce({
      ...TEST_CREDENTIALS_STORE,
      clientCredentials: null,
    });
    mockUuid.mockReturnValueOnce(TEST_CREDENTIALS_C.token);

    expect(await service.create(TEST_CPO_BASE_URL, TEST_CREDENTIALS_B)).toEqual(
      TEST_CREDENTIALS_C
    );

    expect(mockCreate).toHaveBeenNthCalledWith(1, {
      data: {
        token: TEST_CREDENTIALS_B.token,
        url: TEST_CREDENTIALS_B.url,
        object: TEST_CREDENTIALS_B,
        roles: {
          create: [
            {
              partyId: TEST_CREDENTIALS_B.roles[0].party_id,
              countryCode: TEST_CREDENTIALS_B.roles[0].country_code,
              role: TEST_CREDENTIALS_B.roles[0].role,
              businessDetailsName:
                TEST_CREDENTIALS_B.roles[0].business_details.name,
            },
          ],
        },
      },
    });
    expect(mockCreate).toHaveBeenNthCalledWith(2, {
      data: {
        clientCredentialsId: TEST_EMSP_CREDENTIALS_ENTITY.id,
        token: TEST_CREDENTIALS_C.token,
        url: TEST_CREDENTIALS_C.url,
        object: TEST_CREDENTIALS_C,
        roles: {
          create: [
            {
              partyId: TEST_CREDENTIALS_C.roles[0].party_id,
              countryCode: TEST_CREDENTIALS_C.roles[0].country_code,
              role: TEST_CREDENTIALS_C.roles[0].role,
              businessDetailsName:
                TEST_CREDENTIALS_C.roles[0].business_details.name,
            },
          ],
        },
      },
    });
    expect(mockUpdate).toHaveBeenNthCalledWith(1, {
      data: {
        serverCredentialsId: TEST_CPO_CREDENTIALS_ENTITY.id,
      },
      where: {
        id: TEST_EMSP_CREDENTIALS_ENTITY.id,
      },
    });
    expect(mockUpdate).toHaveBeenNthCalledWith(2, {
      data: { deletedAt: new Date() },
      where: { token: TEST_CREDENTIALS_STORE.serverCredentials.token },
    });
    expect(mockCreateAuthorisers).toHaveBeenCalledWith({
      type: 'ocpi',
      uid: TEST_CPO_CREDENTIALS_ENTITY.id,
    });
    expect(mockFindOneAuthorisers).toHaveBeenCalledWith({
      where: { type: 'ocpi', uid: TEST_CPO_CREDENTIALS_ENTITY.id },
    });
    expect(mockDestroyAuthorisers).not.toHaveBeenCalled();
  });

  it('should update client credentials in our database and return updated credentials for the client', async () => {
    const updatedClientToken = '3ba35ee8-d95e-45a2-9e8a-e26877b2ff0e';
    const mockGeneratedToken = 'c5bf9ad0-5f52-48bc-b246-8063dc9f2840';

    const mockFindFirst = (database.credentials.findFirst = jest.fn());
    const mockUpdate = (database.credentials.update = jest.fn());
    mockFindFirst.mockResolvedValueOnce(TEST_EMSP_CREDENTIALS_ENTITY);
    mockUuid.mockReturnValueOnce(mockGeneratedToken);

    expect(
      await service.update(TEST_CPO_BASE_URL, {
        ...TEST_CREDENTIALS_B,
        token: updatedClientToken,
      })
    ).toEqual({ ...TEST_CREDENTIALS_C, token: mockGeneratedToken });

    expect(mockUpdate).toHaveBeenNthCalledWith(1, {
      data: {
        object: {
          ...TEST_CREDENTIALS_B,
          token: updatedClientToken,
        },
        roles: {
          create: [
            {
              businessDetailsName:
                TEST_CREDENTIALS_B.roles[0].business_details.name,
              countryCode: TEST_CREDENTIALS_B.roles[0].country_code,
              partyId: TEST_CREDENTIALS_B.roles[0].party_id,
              role: TEST_CREDENTIALS_B.roles[0].role,
            },
          ],
        },
        token: updatedClientToken,
        url: TEST_CREDENTIALS_B.url,
      },
      where: {
        token: TEST_CREDENTIALS_B.token,
        deletedAt: null,
      },
    });
    expect(mockUpdate).toHaveBeenNthCalledWith(2, {
      data: {
        object: {
          ...TEST_CREDENTIALS_C,
          token: mockGeneratedToken,
        },
        roles: {
          create: [
            {
              businessDetailsName:
                TEST_CREDENTIALS_C.roles[0].business_details.name,
              countryCode: TEST_CREDENTIALS_C.roles[0].country_code,
              partyId: TEST_CREDENTIALS_C.roles[0].party_id,
              role: TEST_CREDENTIALS_C.roles[0].role,
            },
          ],
        },
        token: mockGeneratedToken,
        url: TEST_CREDENTIALS_C.url,
      },
      where: { token: TEST_CREDENTIALS_C.token, deletedAt: null },
    });
  });

  it('should throw an error when existing client credentials are not found when updating', async () => {
    jest.spyOn(als, 'getStore').mockReturnValue({
      ...TEST_CREDENTIALS_STORE,
      clientCredentials: null,
    });

    await expect(() =>
      service.update(TEST_CPO_BASE_URL, TEST_CREDENTIALS_B)
    ).rejects.toThrow(CredentialsNotFoundException);
  });

  it('should retrieve credentials from async localstorage', async () => {
    expect(await service.retrieve()).toEqual(TEST_CREDENTIALS_C);
  });

  it('should retrieve access flags from the database', async () => {
    const mockFindFirstOrThrow = (database.credentials.findFirstOrThrow = jest
      .fn()
      .mockResolvedValueOnce({
        enableCdrsModule: true,
        enableCommandsModule: false,
        enableSessionsModule: true,
        enableTokensModule: false,
      }));

    const accessFlags = await service.retrieveAccessFlags(
      TEST_CREDENTIALS_C.token
    );

    expect(accessFlags).toEqual({
      enableCdrsModule: true,
      enableCommandsModule: false,
      enableSessionsModule: true,
      enableTokensModule: false,
    });
    expect(mockFindFirstOrThrow).toHaveBeenCalledWith({
      select: {
        enableCdrsModule: true,
        enableCommandsModule: true,
        enableSessionsModule: true,
        enableTokensModule: true,
      },
      where: {
        token: TEST_CREDENTIALS_C.token,
        deletedAt: null,
      },
    });
  });

  it('should delete credentials from the database', async () => {
    const mockUpdate = (database.credentials.update = jest
      .fn()
      .mockResolvedValueOnce(TEST_EMSP_CREDENTIALS_ENTITY));
    const mockFindOneAuthorisers = (authorisers.findOne = jest
      .fn()
      .mockResolvedValueOnce(TEST_AUTHORISER_ENTITY_WITH_TYPE_OCPI));
    const mockDestroyAuthorisers = (authorisers.destroy = jest.fn());
    jest.spyOn(als, 'getStore').mockReturnValue(TEST_CREDENTIALS_STORE);

    await service.delete();

    expect(mockUpdate).toHaveBeenNthCalledWith(1, {
      data: { deletedAt: new Date() },
      where: { token: TEST_CREDENTIALS_C.token },
    });
    expect(mockUpdate).toHaveBeenNthCalledWith(2, {
      data: { deletedAt: new Date() },
      where: { token: TEST_CREDENTIALS_B.token },
    });
    expect(mockFindOneAuthorisers).toHaveBeenCalledWith({
      where: { type: 'ocpi', uid: TEST_EMSP_CREDENTIALS_ENTITY.id },
    });
    expect(mockDestroyAuthorisers).toHaveBeenCalledWith({
      where: { type: 'ocpi', uid: TEST_EMSP_CREDENTIALS_ENTITY.id },
    });
  });

  it('should create store from auth token', async () => {
    const mockFindUnique = (database.credentials.findUnique = jest
      .fn()
      .mockResolvedValueOnce({
        ...TEST_CPO_CREDENTIALS_ENTITY,
        clientCredentialsId: TEST_EMSP_CREDENTIALS_ENTITY.id,
      })
      .mockResolvedValueOnce(TEST_EMSP_CREDENTIALS_ENTITY));

    expect(
      await service.createStoreFromAuthToken(TEST_CREDENTIALS_C.token)
    ).toEqual({
      serverCredentials: TEST_CREDENTIALS_C,
      serverCredentialsId: TEST_CREDENTIALS_C_ID,
      clientCredentials: TEST_CREDENTIALS_B,
      clientCredentialsId: TEST_CREDENTIALS_B_ID,
    });

    expect(mockFindUnique).toHaveBeenNthCalledWith(1, {
      ...credentialsDeepIncludeOptions,
      where: { token: TEST_CREDENTIALS_C.token, deletedAt: null },
    });
    expect(mockFindUnique).toHaveBeenNthCalledWith(2, {
      ...credentialsDeepIncludeOptions,
      where: { id: TEST_EMSP_CREDENTIALS_ENTITY.id, deletedAt: null },
    });
  });

  it('should create store from auth token if client credentials are not found', async () => {
    const mockFindUnique = (database.credentials.findUnique = jest
      .fn()
      .mockResolvedValueOnce(TEST_CPO_CREDENTIALS_ENTITY)
      .mockResolvedValueOnce(null));

    expect(
      await service.createStoreFromAuthToken(TEST_CREDENTIALS_C.token)
    ).toEqual({
      serverCredentials: TEST_CREDENTIALS_C,
      serverCredentialsId: TEST_CREDENTIALS_C_ID,
      clientCredentials: null,
      clientCredentialsId: null,
    });

    expect(mockFindUnique).toHaveBeenNthCalledWith(1, {
      ...credentialsDeepIncludeOptions,
      where: { token: TEST_CREDENTIALS_C.token, deletedAt: null },
    });
  });

  it('should not create store from auth token if server credentials are not found', async () => {
    const mockFindUnique = (database.credentials.findUnique = jest.fn());
    mockFindUnique.mockResolvedValueOnce(null);

    await expect(() =>
      service.createStoreFromAuthToken(TEST_CREDENTIALS_C.token)
    ).rejects.toThrow(CredentialsNotFoundException);
  });

  it('should create store from token', async () => {
    const mockFindFirst = (database.credentials.findFirst = jest
      .fn()
      .mockResolvedValueOnce({
        ...TEST_EMSP_CREDENTIALS_ENTITY,
        serverCredentialsId: TEST_CPO_CREDENTIALS_ENTITY.id,
      }));
    const mockFindUnique = (database.credentials.findUnique = jest
      .fn()
      .mockResolvedValueOnce(TEST_CPO_CREDENTIALS_ENTITY));

    expect(await service.createStoreFromToken(TEST_TOKEN)).toEqual({
      serverCredentials: TEST_CREDENTIALS_C,
      serverCredentialsId: TEST_CREDENTIALS_C_ID,
      clientCredentials: TEST_CREDENTIALS_B,
      clientCredentialsId: TEST_CREDENTIALS_B_ID,
      pushCdrsReceiver: null,
      pushLocationsReceiver: null,
      pushSessionsReceiver: null,
      pushTariffsReceiver: null,
    });

    expect(mockFindFirst).toHaveBeenCalledWith({
      ...credentialsDeepIncludeOptions,
      where: {
        roles: {
          every: {
            partyId: TEST_TOKEN.party_id,
            countryCode: TEST_TOKEN.country_code,
            role: Role.enum.EMSP,
          },
        },
        deletedAt: null,
      },
    });
    expect(mockFindUnique).toHaveBeenCalledWith({
      ...credentialsDeepIncludeOptions,
      where: { id: TEST_CPO_CREDENTIALS_ENTITY.id, deletedAt: null },
    });
  });

  it('should not create store from token if client credentials are not found', async () => {
    const mockFindFirst = (database.credentials.findFirst = jest
      .fn()
      .mockResolvedValueOnce({
        ...TEST_EMSP_CREDENTIALS_ENTITY,
        serverCredentialsId: TEST_CPO_CREDENTIALS_ENTITY.id,
      }));
    const mockFindUnique = (database.credentials.findUnique = jest
      .fn()
      .mockResolvedValueOnce(null));

    await expect(() =>
      service.createStoreFromToken(TEST_TOKEN)
    ).rejects.toThrow(CredentialsNotFoundException);

    expect(mockFindFirst).toHaveBeenCalledWith({
      ...credentialsDeepIncludeOptions,
      where: {
        roles: {
          every: {
            partyId: TEST_TOKEN.party_id,
            countryCode: TEST_TOKEN.country_code,
            role: Role.enum.EMSP,
          },
        },
        deletedAt: null,
      },
    });
    expect(mockFindUnique).toHaveBeenCalledWith({
      ...credentialsDeepIncludeOptions,
      where: { id: TEST_CPO_CREDENTIALS_ENTITY.id, deletedAt: null },
    });
  });

  it('should not create store from token if server credentials are not found', async () => {
    const mockFindFirst = (database.credentials.findFirst = jest
      .fn()
      .mockResolvedValueOnce(null));
    const mockFindUnique = (database.credentials.findUnique = jest.fn());

    await expect(() =>
      service.createStoreFromToken(TEST_TOKEN)
    ).rejects.toThrow(CredentialsNotFoundException);

    expect(mockFindFirst).toHaveBeenCalledWith({
      ...credentialsDeepIncludeOptions,
      where: {
        roles: {
          every: {
            partyId: TEST_TOKEN.party_id,
            countryCode: TEST_TOKEN.country_code,
            role: Role.enum.EMSP,
          },
        },
        deletedAt: null,
      },
    });
    expect(mockFindUnique).not.toHaveBeenCalled();
  });

  it('should retrieve all credentials from the database', async () => {
    const mockFindMany = (database.credentials.findMany = jest.fn());
    mockFindMany.mockResolvedValueOnce([TEST_CPO_CREDENTIALS_ENTITY]);

    expect(await service.retrieveAll()).toEqual([TEST_CREDENTIALS_C]);

    expect(mockFindMany).toHaveBeenCalledWith({
      ...credentialsDeepIncludeOptions,
      where: { deletedAt: null },
    });
  });

  it.each([true, false])(
    'should check if a set of credentials can pull tokens from the client - %s',
    async (pullTokens) => {
      const mockFindUnique = (database.credentials.findUnique = jest.fn());
      mockFindUnique.mockResolvedValueOnce({
        ...TEST_EMSP_CREDENTIALS_ENTITY,
        pullTokens,
      });

      expect(
        await service.shouldPullTokens(TEST_EMSP_CREDENTIALS_ENTITY.token)
      ).toBe(pullTokens);
    }
  );

  it('should get stores for push cdrs', async () => {
    const serverCredentials = {
      ...TEST_CPO_CREDENTIALS_ENTITY,
      clientCredentialsId: TEST_EMSP_CREDENTIALS_ENTITY.id,
    };
    const clientCredentials = {
      ...TEST_EMSP_CREDENTIALS_ENTITY,
      serverCredentialsId: serverCredentials.id,
      serverCredentials,
    };
    const mockFindMany = (database.credentials.findMany = jest
      .fn()
      .mockResolvedValueOnce([clientCredentials]));

    const stores = await service.createStoresForPushCdrs();

    expect(mockFindMany).toHaveBeenCalledWith({
      include: { roles: true, serverCredentials: { include: { roles: true } } },
      where: {
        deletedAt: null,
        pushCdrs: true,
        pushCdrsReceiver: { not: null },
        serverCredentialsId: { not: null },
      },
    });
    expect(stores).toEqual([
      {
        serverCredentials: TEST_CREDENTIALS_C,
        serverCredentialsId: TEST_CREDENTIALS_C_ID,
        clientCredentials: TEST_CREDENTIALS_B,
        clientCredentialsId: TEST_CREDENTIALS_B_ID,
        pushCdrsReceiver: null,
      },
    ]);
  });

  it('should get stores for push cdrs with a token (matching country code and party id)', async () => {
    const serverCredentials = {
      ...TEST_CPO_CREDENTIALS_ENTITY,
      clientCredentialsId: TEST_EMSP_CREDENTIALS_ENTITY.id,
    };
    const clientCredentials = {
      ...TEST_EMSP_CREDENTIALS_ENTITY,
      serverCredentialsId: serverCredentials.id,
      serverCredentials,
    };
    database.credentials.findMany = jest
      .fn()
      .mockResolvedValueOnce([clientCredentials]);

    const stores = await service.createStoresForPushCdrs(TEST_CDR_TOKEN);

    expect(stores).toEqual([
      {
        serverCredentials: TEST_CREDENTIALS_C,
        serverCredentialsId: TEST_CREDENTIALS_C_ID,
        clientCredentials: TEST_CREDENTIALS_B,
        clientCredentialsId: TEST_CREDENTIALS_B_ID,
        pushCdrsReceiver: null,
      },
    ]);
  });

  it('should get stores for push cdrs with a token (non-matching country code and party id)', async () => {
    const serverCredentials = {
      ...TEST_CPO_CREDENTIALS_ENTITY,
      clientCredentialsId: TEST_EMSP_CREDENTIALS_ENTITY.id,
    };
    const clientCredentials = {
      ...TEST_EMSP_CREDENTIALS_ENTITY,
      serverCredentialsId: serverCredentials.id,
      serverCredentials,
    };
    database.credentials.findMany = jest
      .fn()
      .mockResolvedValueOnce([clientCredentials]);

    const stores = await service.createStoresForPushCdrs({
      ...TEST_CDR_TOKEN,
      party_id: 'ABC',
    });

    expect(stores).toEqual([]);
  });

  it('should get stores for push locations', async () => {
    const serverCredentials = {
      ...TEST_CPO_CREDENTIALS_ENTITY,
      clientCredentialsId: TEST_EMSP_CREDENTIALS_ENTITY.id,
    };
    const clientCredentials = {
      ...TEST_EMSP_CREDENTIALS_ENTITY,
      serverCredentialsId: serverCredentials.id,
      serverCredentials,
    };
    const mockFindMany = (database.credentials.findMany = jest
      .fn()
      .mockResolvedValueOnce([clientCredentials]));

    const stores = await service.createStoresForPushLocations();

    expect(mockFindMany).toHaveBeenCalledWith({
      include: { roles: true, serverCredentials: { include: { roles: true } } },
      where: {
        deletedAt: null,
        pushLocations: true,
        pushLocationsReceiver: { not: null },
        serverCredentialsId: { not: null },
      },
    });
    expect(stores).toEqual([
      {
        serverCredentials: TEST_CREDENTIALS_C,
        serverCredentialsId: TEST_CREDENTIALS_C_ID,
        clientCredentials: TEST_CREDENTIALS_B,
        clientCredentialsId: TEST_CREDENTIALS_B_ID,
        pushLocationsReceiver: null,
      },
    ]);
  });

  it('should get stores for push evses', async () => {
    const serverCredentials = {
      ...TEST_CPO_CREDENTIALS_ENTITY,
      clientCredentialsId: TEST_EMSP_CREDENTIALS_ENTITY.id,
    };
    const clientCredentials = {
      ...TEST_EMSP_CREDENTIALS_ENTITY,
      serverCredentialsId: serverCredentials.id,
      serverCredentials,
      pushEvses: true,
      pushEvsesReceiver: 'receiver',
    };
    const mockFindMany = (database.credentials.findMany = jest
      .fn()
      .mockResolvedValueOnce([clientCredentials]));

    const stores = await service.createStoresForPushEvses();

    expect(mockFindMany).toHaveBeenCalledWith({
      include: { roles: true, serverCredentials: { include: { roles: true } } },
      where: {
        deletedAt: null,
        pushEvses: true,
        pushEvsesReceiver: { not: null },
        serverCredentialsId: { not: null },
      },
    });
    expect(stores).toHaveLength(1);
    expect(stores[0].serverCredentials).toBeDefined();
    expect(stores[0].clientCredentials).toBeDefined();
    expect(stores[0].pushEvsesReceiver).toBe('receiver');
  });

  it('should get stores for push tariffs', async () => {
    const serverCredentials = {
      ...TEST_CPO_CREDENTIALS_ENTITY,
      clientCredentialsId: TEST_EMSP_CREDENTIALS_ENTITY.id,
    };
    const clientCredentials = {
      ...TEST_EMSP_CREDENTIALS_ENTITY,
      serverCredentialsId: serverCredentials.id,
      serverCredentials,
    };
    const mockFindMany = (database.credentials.findMany = jest
      .fn()
      .mockResolvedValueOnce([clientCredentials]));

    const stores = await service.createStoresForPushTariffs();

    expect(mockFindMany).toHaveBeenCalledWith({
      include: { roles: true, serverCredentials: { include: { roles: true } } },
      where: {
        deletedAt: null,
        pushTariffs: true,
        pushTariffsReceiver: { not: null },
        serverCredentialsId: { not: null },
      },
    });
    expect(stores).toEqual([
      {
        serverCredentials: TEST_CREDENTIALS_C,
        serverCredentialsId: TEST_CREDENTIALS_C_ID,
        clientCredentials: TEST_CREDENTIALS_B,
        clientCredentialsId: TEST_CREDENTIALS_B_ID,
        pushTariffsReceiver: null,
      },
    ]);
  });
});
