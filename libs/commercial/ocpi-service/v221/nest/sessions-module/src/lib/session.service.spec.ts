import { AsyncLocalStorage } from 'async_hooks';
import { ConfigService } from '@nestjs/config';
import {
  CredentialsStore,
  SessionNotFoundException,
  SessionSchema,
} from '@experience/commercial/ocpi-service/v221/shared';
import { Map } from 'immutable';
import {
  OCPIPrismaClient,
  Prisma,
} from '@experience/commercial/ocpi-service/v221/prisma/client';
import { PrismaHealthIndicator } from '@nestjs/terminus';
import { SessionService } from './session.service';
import {
  TEST_ACTIVE_SESSION,
  TEST_CREDENTIALS_A,
  TEST_CREDENTIALS_A_ID,
  TEST_CREDENTIALS_B,
  TEST_CREDENTIALS_STORE,
  TEST_CREDENTIALS_STORE_FOR_PUSH_SESSION,
  TEST_SESSIONS_PAGE,
  TEST_START_SESSION_COMMAND,
  TEST_STOP_SESSION_COMMAND,
  UUID_FORMAT,
} from '@experience/commercial/ocpi-service/v221/shared/specs';
import {
  TEST_ACTIVE_SESSION_ENTITY_DEEP,
  TEST_COMPLETED_SESSION_ENTITY_DEEP,
} from '@experience/commercial/ocpi-service/v221/prisma/client/specs';
import { Test, TestingModule } from '@nestjs/testing';

describe('SessionService', () => {
  let als: AsyncLocalStorage<CredentialsStore>;
  let database: OCPIPrismaClient;
  let service: SessionService;

  const dateFrom = new Date(0);
  const dateTo = new Date();
  const defaultPaginationParams = { dateFrom, dateTo, limit: 100, offset: 0 };
  const defaultCreateSessionParams = {
    include: { token: true, cdrs: { include: { chargingPeriods: true } } },
    data: {
      authMethod: 'WHITELIST',
      authorisationReference:
        TEST_START_SESSION_COMMAND.authorization_reference as string,
      connectorId: TEST_START_SESSION_COMMAND.connector_id as string,
      countryCode: TEST_START_SESSION_COMMAND.token.country_code,
      currency: 'GBP',
      evseUid: TEST_START_SESSION_COMMAND.evse_uid as string,
      kwh: 0,
      locationId: TEST_START_SESSION_COMMAND.location_id,
      partyId: 'POD',
      startDateTime: new Date(),
      status: 'ACTIVE',
      token: {
        create: {
          contractId: TEST_START_SESSION_COMMAND.token.contract_id,
          countryCode: TEST_START_SESSION_COMMAND.token.country_code,
          partyId: TEST_START_SESSION_COMMAND.token.party_id,
          type: TEST_START_SESSION_COMMAND.token.type,
          uid: TEST_START_SESSION_COMMAND.token.uid,
        },
      },
    },
  };

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        AsyncLocalStorage<CredentialsStore>,
        ConfigService,
        OCPIPrismaClient,
        PrismaHealthIndicator,
        SessionService,
        { provide: 'ALS_PRISMA_OCPI_V221', useValue: new AsyncLocalStorage() },
      ],
    }).compile();

    als = module.get(AsyncLocalStorage<CredentialsStore>);
    database = module.get<OCPIPrismaClient>(OCPIPrismaClient);
    service = module.get<SessionService>(SessionService);

    jest
      .spyOn(database, '$transaction')
      .mockImplementation((operations) => Promise.all(operations));
  });

  it('should be defined', () => {
    expect(als).toBeDefined();
    expect(database).toBeDefined();
    expect(service).toBeDefined();
  });

  it.each([new Date(), undefined])(
    'should start a new session with date time %s',
    async (dateTime) => {
      const mockCreateSession = (database.session.create =
        jest.fn()).mockResolvedValueOnce(TEST_ACTIVE_SESSION_ENTITY_DEEP);

      const session = await service.start(TEST_START_SESSION_COMMAND, dateTime);

      expect(session).toEqual(TEST_ACTIVE_SESSION);
      expect(mockCreateSession).toHaveBeenCalledWith(
        defaultCreateSessionParams
      );
    }
  );

  it('should start a new session with a specific id', async () => {
    const mockCreateSession = (database.session.create =
      jest.fn()).mockResolvedValueOnce(TEST_ACTIVE_SESSION_ENTITY_DEEP);
    const expectedCreateSessionParams = Map(defaultCreateSessionParams)
      .setIn(['data', 'id'], TEST_ACTIVE_SESSION_ENTITY_DEEP.id)
      .toObject();

    const session = await service.start(
      TEST_START_SESSION_COMMAND,
      new Date(),
      TEST_ACTIVE_SESSION_ENTITY_DEEP.id
    );

    expect(session).toEqual(TEST_ACTIVE_SESSION);
    expect(mockCreateSession).toHaveBeenCalledWith(expectedCreateSessionParams);
  });

  it.each([
    [new Date(), 10, 490, 10, 4.08, 4.9],
    [new Date(), 10, undefined, 10, undefined, undefined],
    [new Date(), undefined, 490, 0, 4.08, 4.9],
    [undefined, 10, 490, 10, 4.08, 4.9],
    [undefined, 10, undefined, 10, undefined, undefined],
    [undefined, undefined, 490, undefined, 4.08, 4.9],
  ])(
    'should stop an existing session with end date time %s and kwh %s and cost %s',
    async (
      endDateTime,
      kwh,
      cost,
      expectedKwh,
      expectedCostExclVat,
      expectedCostInclVat
    ) => {
      const mockFindUnique = (database.session.findUnique = jest
        .fn()
        .mockResolvedValueOnce(TEST_ACTIVE_SESSION_ENTITY_DEEP));
      const mockUpdate = (database.session.update =
        jest.fn()).mockResolvedValueOnce(TEST_ACTIVE_SESSION_ENTITY_DEEP);

      await service.stop(TEST_STOP_SESSION_COMMAND, endDateTime, kwh, cost);

      expect(mockFindUnique).toHaveBeenCalledWith({
        include: { token: true, cdrs: { include: { chargingPeriods: true } } },
        where: { id: TEST_ACTIVE_SESSION_ENTITY_DEEP.id },
      });
      expect(mockUpdate).toHaveBeenCalledWith({
        include: { token: true, cdrs: { include: { chargingPeriods: true } } },
        data: {
          endDateTime: endDateTime ?? new Date(),
          kwh: expectedKwh ? new Prisma.Decimal(expectedKwh) : undefined,
          status: 'COMPLETED',
          totalCostExclVat: expectedCostExclVat
            ? new Prisma.Decimal(expectedCostExclVat)
            : undefined,
          totalCostInclVat: expectedCostExclVat
            ? new Prisma.Decimal(expectedCostInclVat)
            : undefined,
        },
        where: { id: TEST_ACTIVE_SESSION_ENTITY_DEEP.id },
      });
    }
  );

  it('should return a session', async () => {
    const mockFindUnique = (database.session.findUnique = jest
      .fn()
      .mockResolvedValueOnce(TEST_ACTIVE_SESSION_ENTITY_DEEP));

    const session = await service.find(TEST_ACTIVE_SESSION_ENTITY_DEEP.id);

    expect(session).toEqual(TEST_ACTIVE_SESSION);
    expect(mockFindUnique).toHaveBeenCalledWith({
      include: { token: true, cdrs: { include: { chargingPeriods: true } } },
      where: { id: TEST_ACTIVE_SESSION_ENTITY_DEEP.id },
    });
  });

  it('should throw an error when session is not found', async () => {
    const mockFindUnique = (database.session.findUnique = jest
      .fn()
      .mockResolvedValueOnce(null));

    await expect(
      service.find(TEST_ACTIVE_SESSION_ENTITY_DEEP.id)
    ).rejects.toThrow(SessionNotFoundException);

    expect(mockFindUnique).toHaveBeenCalledWith({
      include: { token: true, cdrs: { include: { chargingPeriods: true } } },
      where: { id: TEST_ACTIVE_SESSION_ENTITY_DEEP.id },
    });
  });

  it('should push session to emsp', async () => {
    jest
      .spyOn(als, 'getStore')
      .mockReturnValue(TEST_CREDENTIALS_STORE_FOR_PUSH_SESSION);
    database.session.findUnique = jest
      .fn()
      .mockResolvedValueOnce(TEST_ACTIVE_SESSION_ENTITY_DEEP);
    const mockFetch = jest.spyOn(global, 'fetch').mockResolvedValue({
      ok: true,
      json: { success: true },
      status: 200,
    } as unknown as Response);

    await service.push(TEST_ACTIVE_SESSION);

    expect(mockFetch).toHaveBeenCalledWith(
      `http://localhost:9876/ocpi/emsp/2.2.1/sessions/GB/POD/${TEST_ACTIVE_SESSION.id}`,
      {
        body: JSON.stringify(SessionSchema.parse(TEST_ACTIVE_SESSION)),
        headers: {
          Authorization: `Token ${Buffer.from(
            TEST_CREDENTIALS_B.token
          ).toString('base64')}`,
          'content-type': 'application/json',
          'x-correlation-id': expect.stringMatching(UUID_FORMAT),
          'x-request-id': expect.stringMatching(UUID_FORMAT),
        },
        method: 'PUT',
      }
    );
  });

  it('should return all sessions for a given client role', async () => {
    jest.spyOn(als, 'getStore').mockReturnValue(TEST_CREDENTIALS_STORE);
    const mockFindMany = (database.session.findMany =
      jest.fn()).mockResolvedValueOnce([
      TEST_ACTIVE_SESSION_ENTITY_DEEP,
      TEST_COMPLETED_SESSION_ENTITY_DEEP,
    ]);
    const mockCount = (database.session.count =
      jest.fn()).mockResolvedValueOnce(2);

    const sessions = await service.findAll(defaultPaginationParams);

    expect(mockFindMany).toHaveBeenCalledWith({
      include: { token: true, cdrs: { include: { chargingPeriods: true } } },
      orderBy: { lastUpdated: 'asc' },
      skip: defaultPaginationParams.offset,
      take: defaultPaginationParams.limit,
      where: {
        lastUpdated: { gte: dateFrom, lt: dateTo },
        token: {
          countryCode: TEST_CREDENTIALS_B.roles[0].country_code,
          partyId: TEST_CREDENTIALS_B.roles[0].party_id,
        },
      },
    });
    expect(mockCount).toHaveBeenCalled();
    expect(sessions).toEqual(TEST_SESSIONS_PAGE);
  });

  it('should return a empty page if the client does not have the correct role', async () => {
    jest.spyOn(als, 'getStore').mockReturnValue({
      clientCredentials: TEST_CREDENTIALS_A,
      clientCredentialsId: TEST_CREDENTIALS_A_ID,
      serverCredentials: TEST_CREDENTIALS_A,
      serverCredentialsId: TEST_CREDENTIALS_A_ID,
    });
    const mockFindMany = (database.session.findMany = jest.fn());
    const mockCount = (database.session.count = jest.fn());

    const sessions = await service.findAll(defaultPaginationParams);

    expect(mockFindMany).not.toHaveBeenCalled();
    expect(mockCount).not.toHaveBeenCalled();
    expect(sessions).toEqual({
      elements: [],
      number: 1,
      size: 0,
      totalElements: 0,
      totalPages: 0,
    });
  });
});
