import { Test, TestingModule } from '@nestjs/testing';
import { UnlockConnectorCommandConsumer } from './unlock-connector-command.consumer';
import { UnlockConnectorCommandModule } from './unlock-connector-command.module';

describe('unlock connector command consumer', () => {
  let consumer: UnlockConnectorCommandConsumer;

  beforeAll(async () => {
    const module: TestingModule = await Test.createTestingModule({
      imports: [UnlockConnectorCommandModule],
    }).compile();

    consumer = module.get<UnlockConnectorCommandConsumer>(
      UnlockConnectorCommandConsumer
    );
  });

  it('should be defined', () => {
    expect(consumer).toBeDefined();
  });
});
