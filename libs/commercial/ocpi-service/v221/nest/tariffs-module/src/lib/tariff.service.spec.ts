import { AsyncLocalStorage } from 'async_hooks';
import { ConfigService } from '@nestjs/config';
import {
  CredentialsStore,
  TariffNotFoundException,
  TariffSchema,
} from '@experience/commercial/ocpi-service/v221/shared';
import { OCPIPrismaClient } from '@experience/commercial/ocpi-service/v221/prisma/client';
import { PrismaHealthIndicator } from '@nestjs/terminus';
import {
  TEST_CREDENTIALS_B,
  TEST_CREDENTIALS_STORE_FOR_PUSH_TARIFFS,
  TEST_TARIFF,
  TEST_TARIFF_PAGE,
  UUID_FORMAT,
} from '@experience/commercial/ocpi-service/v221/shared/specs';
import {
  TEST_TARIFF_ELEMENT_ENTITY,
  TEST_TARIFF_ENTITY,
  TEST_TARIFF_ENTITY_DEEP,
} from '@experience/commercial/ocpi-service/v221/prisma/client/specs';
import { TariffService } from './tariff.service';
import { Test, TestingModule } from '@nestjs/testing';

describe('TariffService', () => {
  let als: AsyncLocalStorage<CredentialsStore>;
  let database: OCPIPrismaClient;
  let service: TariffService;

  const dateFrom = new Date(0);
  const dateTo = new Date();
  const defaultPaginationParams = { dateFrom, dateTo, limit: 100, offset: 0 };

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        AsyncLocalStorage<CredentialsStore>,
        ConfigService,
        TariffService,
        OCPIPrismaClient,
        PrismaHealthIndicator,
        { provide: 'ALS_PRISMA_OCPI_V221', useValue: new AsyncLocalStorage() },
      ],
    }).compile();

    als = module.get(AsyncLocalStorage<CredentialsStore>);
    database = module.get<OCPIPrismaClient>(OCPIPrismaClient);
    service = module.get<TariffService>(TariffService);
  });

  it('should be defined', () => {
    expect(als).toBeDefined();
    expect(database).toBeDefined();
    expect(service).toBeDefined();
  });

  it('should return all tariffs', async () => {
    const mockFindMany = (database.tariff.findMany =
      jest.fn()).mockResolvedValueOnce([
      { ...TEST_TARIFF_ENTITY, elements: [TEST_TARIFF_ELEMENT_ENTITY] },
    ]);
    const mockCount = (database.tariff.count = jest.fn()).mockResolvedValueOnce(
      1
    );
    jest
      .spyOn(database, '$transaction')
      .mockImplementation((operations) => Promise.all(operations));

    const tariffs = await service.findAll(defaultPaginationParams);

    expect(tariffs).toEqual(TEST_TARIFF_PAGE);
    expect(mockFindMany).toHaveBeenCalledWith({
      include: { elements: true },
      orderBy: { lastUpdated: 'asc' },
      skip: 0,
      take: 100,
      where: {
        lastUpdated: { gte: dateFrom, lt: dateTo },
        currency: { in: ['GBP'] },
      },
    });
    expect(mockCount).toHaveBeenCalledWith({
      where: {
        currency: { in: ['GBP'] },
      },
    });
  });

  it('should return a tariff', async () => {
    const mockFindUnique = (database.tariff.findUnique = jest
      .fn()
      .mockResolvedValueOnce(TEST_TARIFF_ENTITY_DEEP));

    const tariff = await service.findTariff(TEST_TARIFF_ENTITY_DEEP.id);

    expect(tariff).toEqual(TEST_TARIFF);
    expect(mockFindUnique).toHaveBeenCalledWith({
      include: { elements: true },
      where: {
        id: TEST_TARIFF_ENTITY_DEEP.id,
      },
    });
  });

  it('should throw an error when tariff is not found', async () => {
    const mockFindUnique = (database.tariff.findUnique = jest
      .fn()
      .mockResolvedValueOnce(null));

    await expect(
      service.findTariff(TEST_TARIFF_ENTITY_DEEP.id)
    ).rejects.toThrow(TariffNotFoundException);
    expect(mockFindUnique).toHaveBeenCalledWith({
      include: { elements: true },
      where: {
        id: TEST_TARIFF_ENTITY_DEEP.id,
      },
    });
  });

  it('should push tariff to emsp', async () => {
    const mockGetStore = jest
      .spyOn(als, 'getStore')
      .mockReturnValue(TEST_CREDENTIALS_STORE_FOR_PUSH_TARIFFS);
    const mockFetch = jest.spyOn(global, 'fetch').mockResolvedValue({
      ok: true,
      json: { success: true },
      status: 200,
    } as unknown as Response);

    await service.pushTariff(TEST_TARIFF);

    expect(mockGetStore).toHaveBeenCalled();
    expect(mockFetch).toHaveBeenCalledWith(
      `http://localhost:9876/ocpi/emsp/2.2.1/tariffs/GB/POD/${TEST_TARIFF.id}`,
      {
        body: JSON.stringify(TariffSchema.parse(TEST_TARIFF)),
        headers: {
          Authorization: `Token ${Buffer.from(
            TEST_CREDENTIALS_B.token
          ).toString('base64')}`,
          'content-type': 'application/json',
          'x-correlation-id': expect.stringMatching(UUID_FORMAT),
          'x-request-id': expect.stringMatching(UUID_FORMAT),
        },
        method: 'PUT',
      }
    );
  });
});
