import { AsyncLocalStorage } from 'async_hooks';
import { ConfigService } from '@nestjs/config';
import { Logger } from '@nestjs/common';
import { OCPIPrismaClient } from '@experience/commercial/ocpi-service/v221/prisma/client';
import { Op } from 'sequelize';
import { PrismaHealthIndicator } from '@nestjs/terminus';
import {
  RevenueProfiles,
  TEST_REVENUE_PROFILE_ENTITY,
  TEST_REVENUE_PROFILE_TIERS_PUBLIC_ENTITY,
} from '@experience/shared/sequelize/podadmin';
import { TEST_TARIFF_ELEMENT_ENTITY_ID } from '@experience/commercial/ocpi-service/v221/prisma/client/specs';
import { TEST_TARIFF_ID } from '@experience/commercial/ocpi-service/v221/shared/specs';
import { TariffMigrator, includeOptions } from './tariff-migrator';
import { Test, TestingModule } from '@nestjs/testing';
import {
  mapRevenueProfileTiersToTariffElements,
  mapRevenueProfileToTariff,
} from './tariff-mapper';
import { mockDeep } from 'jest-mock-extended';
import MockDate from 'mockdate';

jest.mock('uuid', () => ({
  ...jest.requireActual('uuid'),
  v4: jest.fn(() => TEST_TARIFF_ELEMENT_ENTITY_ID),
}));

describe('TariffMigrator', () => {
  let database: OCPIPrismaClient;
  let revenueProfiles: typeof RevenueProfiles;
  let migrator: TariffMigrator;

  const logger = mockDeep<Logger>();
  const error = new Error('Oops');

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        {
          provide: 'ALS_PRISMA_OCPI_V221',
          useValue: new AsyncLocalStorage(),
        },
        {
          provide: 'REVENUE_PROFILES_REPOSITORY',
          useValue: RevenueProfiles,
        },
        ConfigService,
        OCPIPrismaClient,
        PrismaHealthIndicator,
        TariffMigrator,
      ],
    }).compile();

    database = module.get<OCPIPrismaClient>(OCPIPrismaClient);
    revenueProfiles = module.get<typeof RevenueProfiles>(
      'REVENUE_PROFILES_REPOSITORY'
    );
    migrator = module.get<TariffMigrator>(TariffMigrator);

    module.useLogger(logger);

    MockDate.set(new Date());
  });

  it('should migrate a revenue profile to a tariff', async () => {
    const mockFindAll = jest
      .spyOn(revenueProfiles, 'findAll')
      .mockResolvedValue([TEST_REVENUE_PROFILE_ENTITY]);
    const mockUpsert = (database.tariff.upsert =
      jest.fn()).mockResolvedValueOnce({ id: TEST_TARIFF_ID });
    const mockDeleteMany = (database.tariffElement.deleteMany = jest.fn());
    const mockCreateMany = (database.tariffElement.createMany = jest.fn());

    const create = mapRevenueProfileToTariff(TEST_REVENUE_PROFILE_ENTITY);
    const { id, ...update } = create;

    const expectedTariffElements = mapRevenueProfileTiersToTariffElements([
      TEST_REVENUE_PROFILE_TIERS_PUBLIC_ENTITY,
    ]);

    await migrator.migrate(new Date());

    expect(mockFindAll).toHaveBeenCalledWith({
      include: includeOptions,
      paranoid: true,
      where: {
        [Op.and]: [
          { '$podLocations.is_public$': true },
          { '$revenueProfileTiers.deleted_at$': null },
          { '$revenueProfileTiers.type$': 'energy' },
          { '$revenueProfileTiers.user_type$': 'public' },
          {
            [Op.or]: [
              { updatedAt: { [Op.gt]: new Date() } },
              { '$podLocations.updated_at$': { [Op.gt]: new Date() } },
              { '$revenueProfileTiers.updated_at$': { [Op.gt]: new Date() } },
            ],
          },
        ],
      },
    });
    expect(mockUpsert).toHaveBeenCalledWith({
      create,
      update,
      where: { id },
    });
    expect(mockDeleteMany).toHaveBeenCalledWith({ where: { tariffId: id } });
    expect(mockCreateMany).toHaveBeenCalledWith({
      data: expectedTariffElements.map((element) => ({
        ...element,
        id: TEST_TARIFF_ELEMENT_ENTITY_ID,
        tariffId: TEST_TARIFF_ID,
      })),
    });
  });

  it('should log an error when failing to migrate a revenue profile', async () => {
    jest
      .spyOn(revenueProfiles, 'findAll')
      .mockResolvedValue([TEST_REVENUE_PROFILE_ENTITY]);
    (database.tariff.upsert = jest.fn()).mockRejectedValue(error);

    await migrator.migrate(new Date());

    expect(logger.warn).toHaveBeenNthCalledWith(
      1,
      {
        error,
        id: TEST_TARIFF_ID,
      },
      'failed to migrate tariff',
      'TariffMigrator'
    );
    expect(logger.warn).toHaveBeenNthCalledWith(
      2,
      {
        failedMigrationIds: [TEST_TARIFF_ID],
      },
      'failed to migrate 1 tariffs',
      'TariffMigrator'
    );
  });

  it('should continue the migration task after an error is thrown', async () => {
    const mockFindAll = jest
      .spyOn(revenueProfiles, 'findAll')
      .mockResolvedValue([
        TEST_REVENUE_PROFILE_ENTITY,
        TEST_REVENUE_PROFILE_ENTITY,
      ]);
    database.tariff.upsert = jest
      .fn()
      .mockRejectedValueOnce(error)
      .mockResolvedValueOnce({ id: TEST_TARIFF_ID });
    database.tariffElement.deleteMany = jest.fn();
    database.tariffElement.createMany = jest.fn();

    await migrator.migrate(new Date());

    expect(mockFindAll).toHaveBeenCalledTimes(1);
    expect(logger.warn).toHaveBeenNthCalledWith(
      1,
      {
        error,
        id: TEST_TARIFF_ID,
      },
      'failed to migrate tariff',
      'TariffMigrator'
    );
    expect(logger.warn).toHaveBeenNthCalledWith(
      2,
      {
        failedMigrationIds: [TEST_TARIFF_ID],
      },
      'failed to migrate 1 tariffs',
      'TariffMigrator'
    );
    expect(logger.log).toHaveBeenNthCalledWith(
      3,
      'migrated 1 tariffs',
      'TariffMigrator'
    );
  });
});
