import { $Enums } from '@prisma/clients/ocpi/v221';
import { AsyncLocalStorage } from 'async_hooks';
import { ConfigService } from '@nestjs/config';
import {
  ConnectorNotFoundException,
  CredentialsStore,
  EvseNotFoundException,
  EvseSchema,
  EvseStatus,
  FREE_OF_CHARGE_TARIFF_ID,
  LocationNotFoundException,
  LocationSchema,
} from '@experience/commercial/ocpi-service/v221/shared';
import { LocationService } from './location.service';
import { OCPIPrismaClient } from '@experience/commercial/ocpi-service/v221/prisma/client';
import { PrismaHealthIndicator } from '@nestjs/terminus';
import {
  TEST_CONNECTOR,
  TEST_CONNECTORS_PAGE,
  TEST_CREDENTIALS_B,
  TEST_CREDENTIALS_STORE_FOR_PUSH_EVSES,
  TEST_CREDENTIALS_STORE_FOR_PUSH_LOCATIONS,
  TEST_EVSE,
  TEST_EVSES_PAGE,
  TEST_LOCATION,
  TEST_LOCATIONS_PAGE,
  TEST_LOCATION_ID,
  UUID_FORMAT,
} from '@experience/commercial/ocpi-service/v221/shared/specs';
import {
  TEST_CONNECTOR_ENTITY,
  TEST_CONNECTOR_ENTITY_DEEP,
  TEST_EVSE_ENTITY,
  TEST_EVSE_ENTITY_DEEP,
  TEST_LOCATION_ENTITY,
  TEST_LOCATION_ENTITY_DEEP,
  TEST_LOCATION_ENTITY_ID,
  TEST_TARIFF_ENTITY,
} from '@experience/commercial/ocpi-service/v221/prisma/client/specs';
import { Test, TestingModule } from '@nestjs/testing';
import { v4 as uuid } from 'uuid';
import MockDate from 'mockdate';

describe('LocationService', () => {
  let als: AsyncLocalStorage<CredentialsStore>;
  let database: OCPIPrismaClient;
  let service: LocationService;

  const dateFrom = new Date(0);
  const dateTo = new Date();
  const defaultPaginationParams = { dateFrom, dateTo, limit: 100, offset: 0 };
  MockDate.set('2024-01-01T12:30:00.000Z');

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        AsyncLocalStorage<CredentialsStore>,
        ConfigService,
        LocationService,
        OCPIPrismaClient,
        PrismaHealthIndicator,
        { provide: 'ALS_PRISMA_OCPI_V221', useValue: new AsyncLocalStorage() },
      ],
    }).compile();

    als = module.get(AsyncLocalStorage<CredentialsStore>);
    database = module.get<OCPIPrismaClient>(OCPIPrismaClient);
    service = module.get<LocationService>(LocationService);

    jest
      .spyOn(database, '$transaction')
      .mockImplementation((operations) => Promise.all(operations));
  });

  it('should be defined', () => {
    expect(als).toBeDefined();
    expect(database).toBeDefined();
    expect(service).toBeDefined();
  });

  it('should return a list of locations', async () => {
    const mockFindMany = (database.location.findMany = jest
      .fn()
      .mockResolvedValueOnce([TEST_LOCATION_ENTITY_DEEP]));
    const mockCount = (database.location.count =
      jest.fn()).mockResolvedValueOnce(1);

    const locations = await service.findAll(defaultPaginationParams);

    expect(mockFindMany).toHaveBeenCalledWith({
      include: {
        evses: { include: { connectors: { include: { tariffs: true } } } },
      },
      orderBy: { lastUpdated: 'asc' },
      skip: 0,
      take: 100,
      where: {
        lastUpdated: { gte: dateFrom, lt: dateTo },
        timezone: { in: ['Europe/Isle_of_Man', 'Europe/London'] },
      },
    });
    expect(mockCount).toHaveBeenCalledWith({
      where: { timezone: { in: ['Europe/Isle_of_Man', 'Europe/London'] } },
    });
    expect(locations).toEqual(TEST_LOCATIONS_PAGE);
  });

  it('should return a list of evses', async () => {
    const mockFindMany = (database.evse.findMany = jest
      .fn()
      .mockResolvedValueOnce([TEST_EVSE_ENTITY_DEEP]));
    const mockCount = (database.evse.count = jest.fn()).mockResolvedValueOnce(
      1
    );

    const evses = await service.findAllEvses(defaultPaginationParams);

    expect(mockFindMany).toHaveBeenCalledWith({
      include: { connectors: { include: { tariffs: true } } },
      orderBy: { lastUpdated: 'asc' },
      skip: 0,
      take: 100,
      where: { lastUpdated: { gte: dateFrom, lt: dateTo } },
    });
    expect(mockCount).toHaveBeenCalledWith();
    expect(evses).toEqual(TEST_EVSES_PAGE);
  });

  it('should return a list of evses and exclude removed', async () => {
    const mockFindMany = (database.evse.findMany = jest
      .fn()
      .mockResolvedValueOnce([TEST_EVSE_ENTITY_DEEP]));
    const mockCount = (database.evse.count = jest.fn()).mockResolvedValueOnce(
      1
    );

    const evses = await service.findAllEvses(defaultPaginationParams, true);

    expect(mockFindMany).toHaveBeenCalledWith({
      include: { connectors: { include: { tariffs: true } } },
      orderBy: { lastUpdated: 'asc' },
      skip: 0,
      take: 100,
      where: {
        NOT: { status: 'REMOVED' },
        lastUpdated: { gte: dateFrom, lt: dateTo },
      },
    });
    expect(mockCount).toHaveBeenCalledWith();
    expect(evses).toEqual(TEST_EVSES_PAGE);
  });

  it('should return a list of connectors', async () => {
    const mockFindMany = (database.connector.findMany = jest
      .fn()
      .mockResolvedValueOnce([TEST_CONNECTOR_ENTITY_DEEP]));
    const mockCount = (database.connector.count =
      jest.fn()).mockResolvedValueOnce(1);

    const connectors = await service.findAllConnectors(defaultPaginationParams);

    expect(mockFindMany).toHaveBeenCalledWith({
      include: { tariffs: true },
      orderBy: { lastUpdated: 'asc' },
      skip: 0,
      take: 100,
      where: { lastUpdated: { gte: dateFrom, lt: dateTo } },
    });
    expect(mockCount).toHaveBeenCalledWith();
    expect(connectors).toEqual(TEST_CONNECTORS_PAGE);
  });

  it('should return a list of locations and filter any location containing a validation error', async () => {
    const mockFindMany = (database.location.findMany = jest
      .fn()
      .mockResolvedValueOnce([{ ...TEST_LOCATION_ENTITY_DEEP, id: null }]));
    const mockCount = (database.location.count =
      jest.fn()).mockResolvedValueOnce(1);

    const locations = await service.findAll(defaultPaginationParams);

    expect(mockFindMany).toHaveBeenCalledWith({
      include: {
        evses: { include: { connectors: { include: { tariffs: true } } } },
      },
      orderBy: { lastUpdated: 'asc' },
      skip: 0,
      take: 100,
      where: {
        lastUpdated: { gte: dateFrom, lt: dateTo },
        timezone: { in: ['Europe/Isle_of_Man', 'Europe/London'] },
      },
    });
    expect(mockCount).toHaveBeenCalledWith({
      where: { timezone: { in: ['Europe/Isle_of_Man', 'Europe/London'] } },
    });
    expect(locations).toEqual({
      ...TEST_LOCATIONS_PAGE,
      elements: [],
      size: 0,
    });
  });

  it('should return a list of locations and filter any EVSE containing a validation error', async () => {
    const mockFindMany = (database.location.findMany = jest
      .fn()
      .mockResolvedValueOnce([
        {
          ...TEST_LOCATION_ENTITY,
          evses: [
            {
              ...TEST_EVSE_ENTITY,
              connectors: [
                {
                  ...TEST_CONNECTOR_ENTITY,
                  tariffs: [TEST_TARIFF_ENTITY],
                },
              ],
              id: null,
            },
          ],
        },
      ]));
    const mockCount = (database.location.count =
      jest.fn()).mockResolvedValueOnce(1);

    const locations = await service.findAll(defaultPaginationParams);

    expect(mockFindMany).toHaveBeenCalledWith({
      include: {
        evses: { include: { connectors: { include: { tariffs: true } } } },
      },
      orderBy: { lastUpdated: 'asc' },
      skip: 0,
      take: 100,
      where: {
        lastUpdated: { gte: dateFrom, lt: dateTo },
        timezone: { in: ['Europe/Isle_of_Man', 'Europe/London'] },
      },
    });
    expect(mockCount).toHaveBeenCalledWith({
      where: { timezone: { in: ['Europe/Isle_of_Man', 'Europe/London'] } },
    });
    expect(locations).toEqual({
      ...TEST_LOCATIONS_PAGE,
      elements: [{ ...TEST_LOCATION, evses: [] }],
    });
  });

  it('should return a list of locations and filter any EVSE containing a validation error on the connector', async () => {
    const mockFindMany = (database.location.findMany = jest
      .fn()
      .mockResolvedValueOnce([
        {
          ...TEST_LOCATION_ENTITY_DEEP,
          evses: [
            {
              ...TEST_EVSE_ENTITY,
              connectors: [
                {
                  ...TEST_CONNECTOR_ENTITY,
                  tariffs: [TEST_TARIFF_ENTITY],
                  standard: 'FOOBAR',
                },
              ],
            },
          ],
        },
      ]));
    const mockCount = (database.location.count =
      jest.fn()).mockResolvedValueOnce(1);

    const locations = await service.findAll(defaultPaginationParams);

    expect(mockFindMany).toHaveBeenCalledWith({
      include: {
        evses: { include: { connectors: { include: { tariffs: true } } } },
      },
      orderBy: { lastUpdated: 'asc' },
      skip: 0,
      take: 100,
      where: {
        lastUpdated: { gte: dateFrom, lt: dateTo },
        timezone: { in: ['Europe/Isle_of_Man', 'Europe/London'] },
      },
    });
    expect(mockCount).toHaveBeenCalledWith({
      where: { timezone: { in: ['Europe/Isle_of_Man', 'Europe/London'] } },
    });
    expect(locations).toEqual({
      ...TEST_LOCATIONS_PAGE,
      elements: [{ ...TEST_LOCATION, evses: [] }],
    });
  });

  it('should return a location', async () => {
    const mockFindUnique = (database.location.findUnique = jest
      .fn()
      .mockResolvedValueOnce(TEST_LOCATION_ENTITY_DEEP));

    const location = await service.findLocation(TEST_LOCATION_ENTITY_DEEP.id);

    expect(location).toEqual(TEST_LOCATION);
    expect(mockFindUnique).toHaveBeenCalledWith({
      include: {
        evses: { include: { connectors: { include: { tariffs: true } } } },
      },
      where: {
        id: TEST_LOCATION_ENTITY_DEEP.id,
      },
    });
  });

  it('should find a location by evse id', async () => {
    const mockFindEvse = (database.evse.findUnique = jest
      .fn()
      .mockResolvedValueOnce(TEST_EVSE_ENTITY));
    const mockFindLocation = (database.location.findUnique = jest
      .fn()
      .mockResolvedValueOnce(TEST_LOCATION_ENTITY_DEEP));

    const location = await service.findLocationByEvseId(
      TEST_EVSE_ENTITY.evseId
    );

    expect(location).toEqual(TEST_LOCATION);
    expect(mockFindEvse).toHaveBeenCalledWith({
      where: {
        evseId: TEST_EVSE_ENTITY.evseId,
      },
    });
    expect(mockFindLocation).toHaveBeenCalledWith({
      include: {
        evses: {
          include: {
            connectors: {
              include: {
                tariffs: true,
              },
            },
          },
        },
      },
      where: {
        id: TEST_LOCATION_ENTITY_ID,
      },
    });
  });

  it('should throw an error when location is not found by evse id', async () => {
    const mockFindUnique = (database.location.findUnique = jest
      .fn()
      .mockResolvedValueOnce(null));

    await expect(
      service.findLocation(TEST_LOCATION_ENTITY_DEEP.id)
    ).rejects.toThrow(LocationNotFoundException);
    expect(mockFindUnique).toHaveBeenCalledWith({
      include: {
        evses: { include: { connectors: { include: { tariffs: true } } } },
      },
      where: {
        id: TEST_LOCATION_ENTITY_DEEP.id,
      },
    });
  });

  it('should find a location by ppid', async () => {
    const mockFindEvse = (database.evse.findFirst = jest
      .fn()
      .mockResolvedValueOnce(TEST_EVSE_ENTITY));
    const mockFindLocation = (database.location.findUnique = jest
      .fn()
      .mockResolvedValueOnce(TEST_LOCATION_ENTITY_DEEP));

    const location = await service.findLocationByPpid(TEST_EVSE_ENTITY.ppid);

    expect(location).toEqual(TEST_LOCATION);
    expect(mockFindEvse).toHaveBeenCalledWith({
      where: {
        ppid: TEST_EVSE_ENTITY.ppid,
      },
    });
    expect(mockFindLocation).toHaveBeenCalledWith({
      include: {
        evses: { include: { connectors: { include: { tariffs: true } } } },
      },
      where: { id: TEST_LOCATION_ID },
    });
  });

  it('should throw an error when location is not found by ppid', async () => {
    database.evse.findFirst = jest.fn().mockResolvedValueOnce(null);

    await expect(
      service.findLocationByPpid(TEST_EVSE_ENTITY.ppid)
    ).rejects.toThrow(EvseNotFoundException);
  });

  it('should throw an error when location contains a validation error', async () => {
    const mockFindUnique = (database.location.findUnique = jest
      .fn()
      .mockResolvedValueOnce({
        ...TEST_LOCATION_ENTITY_DEEP,
        id: null,
      }));

    await expect(
      service.findLocation(TEST_LOCATION_ENTITY_DEEP.id)
    ).rejects.toThrow(LocationNotFoundException);
    expect(mockFindUnique).toHaveBeenCalledWith({
      include: {
        evses: { include: { connectors: { include: { tariffs: true } } } },
      },
      where: {
        id: TEST_LOCATION_ENTITY_DEEP.id,
      },
    });
  });

  it('should return an evse', async () => {
    const mockFindLocation = (database.location.findUnique = jest
      .fn()
      .mockResolvedValueOnce(TEST_LOCATION_ENTITY_DEEP));

    const evse = await service.findEvse(
      TEST_LOCATION_ENTITY_DEEP.id,
      TEST_LOCATION_ENTITY_DEEP.evses[0].id
    );

    expect(evse).toEqual(TEST_EVSE);
    expect(mockFindLocation).toHaveBeenCalledWith({
      where: {
        id: TEST_LOCATION_ENTITY_DEEP.id,
      },
      include: {
        evses: { include: { connectors: { include: { tariffs: true } } } },
      },
    });
  });

  it('should return an evse with ppid', async () => {
    const mockFindLocation = (database.location.findUnique = jest
      .fn()
      .mockResolvedValueOnce(TEST_LOCATION_ENTITY_DEEP));
    const mockFindEvse = (database.evse.findUniqueOrThrow = jest
      .fn()
      .mockResolvedValueOnce({ ppid: TEST_EVSE_ENTITY.ppid }));

    const evse = await service.findEvseWithPpid(
      TEST_LOCATION_ENTITY_DEEP.id,
      TEST_LOCATION_ENTITY_DEEP.evses[0].id
    );

    expect(evse).toEqual({ ...TEST_EVSE, ppid: TEST_EVSE_ENTITY.ppid });
    expect(mockFindLocation).toHaveBeenCalledWith({
      where: {
        id: TEST_LOCATION_ENTITY_DEEP.id,
      },
      include: {
        evses: { include: { connectors: { include: { tariffs: true } } } },
      },
    });
    expect(mockFindEvse).toHaveBeenCalledWith({
      select: { ppid: true },
      where: { id: TEST_EVSE_ENTITY.id },
    });
  });

  it('should return a list of evses by status', async () => {
    const mockFindEvses = (database.evse.findMany = jest
      .fn()
      .mockResolvedValueOnce([TEST_EVSE_ENTITY_DEEP]));

    const evses = await service.findEvseByStatus(EvseStatus.enum.AVAILABLE);

    expect(evses).toEqual([TEST_EVSE]);
    expect(mockFindEvses).toHaveBeenCalledWith({
      where: {
        status: EvseStatus.enum.AVAILABLE,
        statusLastUpdated: { gte: new Date(0) },
      },
      include: { connectors: { include: { tariffs: true } } },
    });
  });

  it('should return a list of evses by status bounded by the status last updated field', async () => {
    const mockFindEvses = (database.evse.findMany = jest
      .fn()
      .mockResolvedValueOnce([TEST_EVSE_ENTITY_DEEP]));

    const evses = await service.findEvseByStatus(
      EvseStatus.enum.AVAILABLE,
      new Date()
    );

    expect(evses).toEqual([TEST_EVSE]);
    expect(mockFindEvses).toHaveBeenCalledWith({
      where: {
        status: EvseStatus.enum.AVAILABLE,
        statusLastUpdated: { gte: new Date() },
      },
      include: { connectors: { include: { tariffs: true } } },
    });
  });

  it('should throw an error when EVSE is not found', async () => {
    database.location.findUnique = jest
      .fn()
      .mockResolvedValueOnce(TEST_LOCATION_ENTITY_DEEP);

    await expect(() =>
      service.findEvse(TEST_LOCATION_ENTITY_DEEP.id, uuid())
    ).rejects.toThrow(EvseNotFoundException);
  });

  it('should throw an error when EVSE contains a validation error', async () => {
    database.location.findUnique = jest.fn().mockResolvedValueOnce({
      ...TEST_LOCATION_ENTITY,
      evses: [
        {
          ...TEST_EVSE_ENTITY,
          connectors: [
            {
              ...TEST_CONNECTOR_ENTITY,
              tariffs: [TEST_TARIFF_ENTITY],
            },
          ],
          id: null,
        },
      ],
    });

    await expect(() =>
      service.findEvse(
        TEST_LOCATION_ENTITY_DEEP.id,
        TEST_LOCATION_ENTITY_DEEP.evses[0].id
      )
    ).rejects.toThrow(EvseNotFoundException);
  });

  it('should return a connector', async () => {
    const mockFindLocation = (database.location.findUnique = jest
      .fn()
      .mockResolvedValueOnce(TEST_LOCATION_ENTITY_DEEP));

    const connector = await service.findConnector(
      TEST_LOCATION_ENTITY_DEEP.id,
      TEST_LOCATION_ENTITY_DEEP.evses[0].id,
      TEST_LOCATION_ENTITY_DEEP.evses[0].connectors[0].id
    );

    expect(connector).toEqual(TEST_CONNECTOR);
    expect(mockFindLocation).toHaveBeenCalledWith({
      where: {
        id: TEST_LOCATION_ENTITY_DEEP.id,
      },
      include: {
        evses: { include: { connectors: { include: { tariffs: true } } } },
      },
    });
  });

  it('should return a connector with free of charge tariff id', async () => {
    const mockFindLocation = (database.location.findUnique = jest
      .fn()
      .mockResolvedValueOnce({
        ...TEST_LOCATION_ENTITY_DEEP,
        evses: [
          {
            ...TEST_EVSE_ENTITY,
            connectors: [
              {
                ...TEST_CONNECTOR_ENTITY,
                tariffs: [],
              },
            ],
          },
        ],
      }));

    const connector = await service.findConnector(
      TEST_LOCATION_ENTITY_DEEP.id,
      TEST_LOCATION_ENTITY_DEEP.evses[0].id,
      TEST_LOCATION_ENTITY_DEEP.evses[0].connectors[0].id
    );

    expect(connector).toEqual({
      ...TEST_CONNECTOR,
      tariff_ids: [FREE_OF_CHARGE_TARIFF_ID],
    });
    expect(mockFindLocation).toHaveBeenCalledWith({
      where: {
        id: TEST_LOCATION_ENTITY_DEEP.id,
      },
      include: {
        evses: { include: { connectors: { include: { tariffs: true } } } },
      },
    });
  });

  it('should throw an error when connector is not found', async () => {
    database.location.findUnique = jest
      .fn()
      .mockResolvedValueOnce(TEST_LOCATION_ENTITY_DEEP);

    await expect(() =>
      service.findConnector(
        TEST_LOCATION_ENTITY_DEEP.id,
        TEST_LOCATION_ENTITY_DEEP.evses[0].id,
        uuid()
      )
    ).rejects.toThrow(ConnectorNotFoundException);
  });

  it('should throw an error when EVSE contains a validation error on the connector', async () => {
    database.location.findUnique = jest.fn().mockResolvedValueOnce({
      ...TEST_LOCATION_ENTITY_DEEP,
      evses: [
        {
          ...TEST_EVSE_ENTITY,
          connectors: [
            {
              ...TEST_CONNECTOR_ENTITY,
              tariffs: [TEST_TARIFF_ENTITY],
              standard: 'FOOBAR',
            },
          ],
        },
      ],
    });

    await expect(() =>
      service.findConnector(
        TEST_LOCATION_ENTITY_DEEP.id,
        TEST_LOCATION_ENTITY_DEEP.evses[0].id,
        TEST_LOCATION_ENTITY_DEEP.evses[0].connectors[0].id
      )
    ).rejects.toThrow(EvseNotFoundException);
  });

  it('should return a connector with socket', async () => {
    const mockFindLocation = (database.location.findUnique = jest
      .fn()
      .mockResolvedValueOnce(TEST_LOCATION_ENTITY_DEEP));
    const mindFindConnector = (database.connector.findUniqueOrThrow = jest
      .fn()
      .mockResolvedValueOnce({ socket: TEST_CONNECTOR_ENTITY.socket }));

    const connector = await service.findConnectorWithSocket(
      TEST_LOCATION_ENTITY_DEEP.id,
      TEST_LOCATION_ENTITY_DEEP.evses[0].id,
      TEST_LOCATION_ENTITY_DEEP.evses[0].connectors[0].id
    );

    expect(connector).toEqual({
      ...TEST_CONNECTOR,
      socket: TEST_CONNECTOR_ENTITY.socket,
    });
    expect(mockFindLocation).toHaveBeenCalledWith({
      where: {
        id: TEST_LOCATION_ENTITY_DEEP.id,
      },
      include: {
        evses: { include: { connectors: { include: { tariffs: true } } } },
      },
    });
    expect(mindFindConnector).toHaveBeenCalledWith({
      select: { socket: true },
      where: { id: TEST_CONNECTOR_ENTITY.id },
    });
  });

  it('should update the status of an EVSE', async () => {
    const mockFindLocation = (database.location.findUnique = jest
      .fn()
      .mockResolvedValueOnce(TEST_LOCATION_ENTITY_DEEP)
      .mockResolvedValueOnce({
        ...TEST_LOCATION_ENTITY_DEEP,
        evses: [{ ...TEST_EVSE_ENTITY_DEEP, status: 'CHARGING' }],
      }));
    const mockUpdateManyEvse = (database.evse.updateMany = jest.fn());

    const evse = await service.updateEvseStatus(
      TEST_LOCATION_ENTITY_DEEP.id,
      TEST_LOCATION_ENTITY_DEEP.evses[0].id,
      EvseStatus.enum.CHARGING,
      new Date()
    );

    expect(evse).toEqual({ ...TEST_EVSE, status: EvseStatus.enum.CHARGING });
    expect(mockFindLocation).toHaveBeenCalledWith({
      where: {
        id: TEST_LOCATION_ENTITY_DEEP.id,
      },
      include: {
        evses: { include: { connectors: { include: { tariffs: true } } } },
      },
    });
    expect(mockUpdateManyEvse).toHaveBeenCalledWith({
      data: {
        status: $Enums.EVSEStatus.CHARGING,
        statusLastUpdated: new Date(),
      },
      where: {
        id: TEST_LOCATION_ENTITY_DEEP.evses[0].id,
        OR: [
          {
            statusLastUpdated: { lte: new Date() },
            NOT: { status: { in: ['REMOVED', 'CHARGING'] } },
          },
          {
            statusLastUpdated: new Date(0),
            NOT: { status: 'REMOVED' },
          },
        ],
      },
    });
  });

  it('should push location to emsp', async () => {
    const mockGetStore = jest
      .spyOn(als, 'getStore')
      .mockReturnValue(TEST_CREDENTIALS_STORE_FOR_PUSH_LOCATIONS);
    const mockFetch = jest.spyOn(global, 'fetch').mockResolvedValue({
      ok: true,
      json: { success: true },
      status: 200,
    } as unknown as Response);

    await service.pushLocation(TEST_LOCATION);

    expect(mockGetStore).toHaveBeenCalled();
    expect(mockFetch).toHaveBeenCalledWith(
      `http://localhost:9876/ocpi/emsp/2.2.1/locations/GB/POD/${TEST_LOCATION.id}`,
      {
        body: JSON.stringify(LocationSchema.parse(TEST_LOCATION)),
        headers: {
          Authorization: `Token ${Buffer.from(
            TEST_CREDENTIALS_B.token
          ).toString('base64')}`,
          'content-type': 'application/json',
          'x-correlation-id': expect.stringMatching(UUID_FORMAT),
          'x-request-id': expect.stringMatching(UUID_FORMAT),
        },
        method: 'PUT',
        signal: AbortSignal.timeout(1000),
      }
    );
  });

  it('should push evse to emsp', async () => {
    const mockGetStore = jest
      .spyOn(als, 'getStore')
      .mockReturnValue(TEST_CREDENTIALS_STORE_FOR_PUSH_EVSES);
    database.location.findUnique = jest
      .fn()
      .mockResolvedValueOnce(TEST_LOCATION_ENTITY_DEEP);
    const mockFetch = jest.spyOn(global, 'fetch').mockResolvedValue({
      ok: true,
      json: { success: true },
      status: 200,
    } as unknown as Response);

    await service.pushEvse(TEST_LOCATION.id, TEST_EVSE);

    expect(mockGetStore).toHaveBeenCalled();
    expect(mockFetch).toHaveBeenCalledWith(
      `http://localhost:9876/ocpi/emsp/2.2.1/locations/GB/POD/${TEST_LOCATION.id}/${TEST_EVSE.uid}`,
      {
        body: JSON.stringify(EvseSchema.parse(TEST_EVSE)),
        headers: {
          Authorization: `Token ${Buffer.from(
            TEST_CREDENTIALS_B.token
          ).toString('base64')}`,
          'content-type': 'application/json',
          'x-correlation-id': expect.stringMatching(UUID_FORMAT),
          'x-request-id': expect.stringMatching(UUID_FORMAT),
        },
        method: 'PUT',
        signal: AbortSignal.timeout(1000),
      }
    );
  });

  it('should push evse patch to emsp', async () => {
    const mockGetStore = jest
      .spyOn(als, 'getStore')
      .mockReturnValue(TEST_CREDENTIALS_STORE_FOR_PUSH_EVSES);
    database.location.findUnique = jest
      .fn()
      .mockResolvedValueOnce(TEST_LOCATION_ENTITY_DEEP);
    const mockFetch = jest.spyOn(global, 'fetch').mockResolvedValue({
      ok: true,
      json: { success: true },
      status: 200,
    } as unknown as Response);

    await service.pushEvse(TEST_LOCATION.id, TEST_EVSE, ['status']);

    expect(mockGetStore).toHaveBeenCalled();
    expect(mockFetch).toHaveBeenCalledWith(
      `http://localhost:9876/ocpi/emsp/2.2.1/locations/GB/POD/${TEST_LOCATION.id}/${TEST_EVSE.uid}`,
      {
        body: JSON.stringify({
          last_updated: TEST_EVSE.last_updated,
          status: TEST_EVSE.status,
        }),
        headers: {
          Authorization: `Token ${Buffer.from(
            TEST_CREDENTIALS_B.token
          ).toString('base64')}`,
          'content-type': 'application/json',
          'x-correlation-id': expect.stringMatching(UUID_FORMAT),
          'x-request-id': expect.stringMatching(UUID_FORMAT),
        },
        method: 'PATCH',
        signal: AbortSignal.timeout(1000),
      }
    );
  });

  it('should fall back to ppid if physical reference is too long', async () => {
    database.location.findMany = jest.fn().mockResolvedValueOnce([
      {
        ...TEST_LOCATION_ENTITY,
        evses: [
          {
            ...TEST_EVSE_ENTITY_DEEP,
            physicalReference: 'test-physical-reference-that-is-too-long',
          },
        ],
      },
    ]);
    (database.location.count = jest.fn()).mockResolvedValueOnce(1);

    const locations = await service.findAll(defaultPaginationParams);

    expect(locations.elements[0].evses?.[0].physical_reference).toEqual(
      TEST_EVSE_ENTITY_DEEP.ppid
    );
  });
});
