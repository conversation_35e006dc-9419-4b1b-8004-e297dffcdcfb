import { AsyncLocalStorage } from 'async_hooks';
import { ConfigModule } from '@nestjs/config';
import { CredentialsStore } from '@experience/commercial/ocpi-service/v221/shared';
import { LocationService } from '../../location.service';
import { Logger } from '@nestjs/common';
import { OCPIPrismaClient } from '@experience/commercial/ocpi-service/v221/prisma/client';
import { PrismaHealthIndicator } from '@nestjs/terminus';
import { PushEvseUpdatesProducer } from '../../producer/push-evse-updates/push-evse-updates.producer';
import { RemoveEvsesCommand } from './remove-evses.command';
import { SqsClientService } from '@experience/shared/nest/aws/sqs-module';
import { TEST_EVSE } from '@experience/commercial/ocpi-service/v221/shared/specs';
import { Test, TestingModule } from '@nestjs/testing';
import { mockDeep } from 'jest-mock-extended';
import MockDate from 'mockdate';

jest.mock('@experience/shared/nest/aws/sqs-module');

describe('remove evses command', () => {
  let command: RemoveEvsesCommand;
  let locationService: LocationService;
  let pushEvseUpdatesProducer: PushEvseUpdatesProducer;
  const logger = mockDeep<Logger>();
  MockDate.set('2024-01-01T12:30:00Z');

  beforeAll(async () => {
    const module: TestingModule = await Test.createTestingModule({
      imports: [
        await ConfigModule.forRoot({
          load: [
            () => ({
              PUSH_EVSE_UPDATES_QUEUE_URL:
                'http://localhost/000000/push-evse-updates',
            }),
          ],
        }),
      ],
      providers: [
        AsyncLocalStorage<CredentialsStore>,
        LocationService,
        OCPIPrismaClient,
        PushEvseUpdatesProducer,
        PrismaHealthIndicator,
        RemoveEvsesCommand,
        SqsClientService,
        { provide: 'ALS_PRISMA_OCPI_V221', useValue: new AsyncLocalStorage() },
      ],
    }).compile();

    command = module.get<RemoveEvsesCommand>(RemoveEvsesCommand);
    locationService = module.get<LocationService>(LocationService);
    pushEvseUpdatesProducer = module.get<PushEvseUpdatesProducer>(
      PushEvseUpdatesProducer
    );
    module.useLogger(logger);
  });

  it('should be defined', () => {
    expect(command).toBeDefined();
    expect(locationService).toBeDefined();
    expect(pushEvseUpdatesProducer).toBeDefined();
  });

  it('should queue a removed evse request', async () => {
    const mockFindRemovedEvses = jest
      .spyOn(locationService, 'findEvseByStatus')
      .mockResolvedValueOnce([TEST_EVSE]);
    const mockQueuePushEvseUpdateRequest = jest.spyOn(
      pushEvseUpdatesProducer,
      'queuePushEvseUpdatesRequest'
    );

    await command.run();

    expect(mockFindRemovedEvses).toHaveBeenCalledWith(
      'REMOVED',
      new Date('2023-12-31T12:30:00Z')
    );
    expect(logger.log).toHaveBeenCalledWith(
      { removedEvses: [TEST_EVSE] },
      'queueing removed evses',
      'RemoveEvsesCommand'
    );
    expect(mockQueuePushEvseUpdateRequest).toHaveBeenCalledWith({
      evseId: TEST_EVSE.evse_id,
    });
  });
});
