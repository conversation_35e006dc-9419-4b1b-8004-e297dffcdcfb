import { AsyncLocalStorage } from 'async_hooks';
import { ConfigService } from '@nestjs/config';
import { Logger } from '@nestjs/common';
import { OCPIPrismaClient } from '@experience/commercial/ocpi-service/v221/prisma/client';
import { Op } from 'sequelize';
import {
  PodAddresses,
  TEST_POD_ADDRESSES_ENTITY,
} from '@experience/shared/sequelize/podadmin';
import { PrismaHealthIndicator } from '@nestjs/terminus';
import { TEST_LOCATION_ENTITY_ID } from '@experience/commercial/ocpi-service/v221/prisma/client/specs';
import { Test, TestingModule } from '@nestjs/testing';
import { mapPodAddressToLocation } from './location-mapper';
import { mockDeep } from 'jest-mock-extended';
import LocationMigrator, { includeOptions } from './location-migrator';
import MockDate from 'mockdate';

describe('Location migrator', () => {
  let database: OCPIPrismaClient;
  let podAddresses: typeof PodAddresses;
  let migrator: LocationMigrator;

  const logger = mockDeep<Logger>();
  const createdId = '2d113d33-46e3-42b3-a31a-cfc828d9d8be';
  const error = new Error('Oops');

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        {
          provide: 'ALS_PRISMA_OCPI_V221',
          useValue: new AsyncLocalStorage(),
        },
        {
          provide: 'POD_ADDRESSES_REPOSITORY',
          useValue: PodAddresses,
        },
        ConfigService,
        LocationMigrator,
        OCPIPrismaClient,
        PrismaHealthIndicator,
      ],
    }).compile();

    database = module.get<OCPIPrismaClient>(OCPIPrismaClient);
    podAddresses = module.get<typeof PodAddresses>('POD_ADDRESSES_REPOSITORY');
    migrator = module.get<LocationMigrator>(LocationMigrator);

    module.useLogger(logger);

    MockDate.set('2024-01-01T00:00:00.000Z');
  });

  it('should migrate a pod address to a location', async () => {
    const mockFindAll = jest
      .spyOn(podAddresses, 'findAll')
      .mockResolvedValue([TEST_POD_ADDRESSES_ENTITY]);
    const mockUpsert = (database.location.upsert = jest
      .fn()
      .mockResolvedValueOnce({ id: createdId }));
    const mockDeleteMany = (database.openingHours.deleteMany = jest.fn());
    const mockCreateMany = (database.openingHours.createMany = jest.fn());
    const expectedCreate = mapPodAddressToLocation(TEST_POD_ADDRESSES_ENTITY);
    const { countryCode, id, partyId, ...expectedUpdate } = expectedCreate;

    await migrator.migrate(new Date());

    expect(mockFindAll).toHaveBeenCalledWith({
      include: includeOptions,
      paranoid: true,
      where: {
        [Op.and]: [
          { '$podLocations.is_public$': true },
          {
            [Op.or]: [
              { updatedAt: { [Op.gt]: new Date() } },
              { '$group.updated_at$': { [Op.gt]: new Date() } },
              { '$podLocations.updated_at$': { [Op.gt]: new Date() } },
            ],
          },
        ],
      },
    });
    expect(mockUpsert).toHaveBeenCalledWith({
      create: expectedCreate,
      update: expectedUpdate,
      where: { id },
    });
    expect(mockDeleteMany).toHaveBeenCalledWith({
      where: { locationId: createdId },
    });
    expect(mockCreateMany).toHaveBeenCalledWith({
      data: [...Array(7).keys()].map((i) => ({
        weekday: i + 1,
        twentyfourseven:
          !!TEST_POD_ADDRESSES_ENTITY.parkingOpeningTimes[i].allDay,
        periodBegin: TEST_POD_ADDRESSES_ENTITY.parkingOpeningTimes[i].from,
        periodEnd: TEST_POD_ADDRESSES_ENTITY.parkingOpeningTimes[i].to,
        locationId: createdId,
      })),
    });
  });

  it('should catch errors during migration task and log them as warnings', async () => {
    jest
      .spyOn(podAddresses, 'findAll')
      .mockResolvedValue([TEST_POD_ADDRESSES_ENTITY]);
    database.location.upsert = jest.fn().mockRejectedValueOnce(error);

    await migrator.migrate(new Date());

    expect(logger.warn).toHaveBeenNthCalledWith(
      1,
      {
        error,
        id: TEST_LOCATION_ENTITY_ID,
      },
      'failed to migrate location',
      'LocationMigrator'
    );
    expect(logger.warn).toHaveBeenNthCalledWith(
      2,
      {
        failedMigrationIds: [TEST_LOCATION_ENTITY_ID],
      },
      'failed to migrate 1 locations',
      'LocationMigrator'
    );
  });

  it('should continue the migration task after an error is thrown ', async () => {
    jest
      .spyOn(podAddresses, 'findAll')
      .mockResolvedValue([
        TEST_POD_ADDRESSES_ENTITY,
        TEST_POD_ADDRESSES_ENTITY,
      ]);
    database.location.upsert = jest
      .fn()
      .mockRejectedValueOnce(error)
      .mockResolvedValueOnce({ id: createdId });
    database.openingHours.deleteMany = jest.fn();
    database.openingHours.createMany = jest.fn();

    await migrator.migrate(new Date());

    expect(logger.warn).toHaveBeenNthCalledWith(
      2,
      {
        failedMigrationIds: [TEST_LOCATION_ENTITY_ID],
      },
      'failed to migrate 1 locations',
      'LocationMigrator'
    );
    expect(logger.log).toHaveBeenNthCalledWith(
      3,
      'migrated 1 locations',
      'LocationMigrator'
    );
  });
});
