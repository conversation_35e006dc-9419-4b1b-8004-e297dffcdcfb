import { AsyncLocalStorage } from 'async_hooks';
import { ConfigService } from '@nestjs/config';
import { Logger } from '@nestjs/common';
import { OCPIPrismaClient } from '@experience/commercial/ocpi-service/v221/prisma/client';
import { Op } from 'sequelize';
import {
  PodUnitConnector,
  TEST_POD_UNIT_CONNECTOR_ENTITY_WITH_LOCATION_AND_REVENUE_PROFILE,
  TEST_REVENUE_PROFILE_ENTITY,
} from '@experience/shared/sequelize/podadmin';
import { PrismaHealthIndicator } from '@nestjs/terminus';
import { TEST_CONNECTOR_ENTITY_ID } from '@experience/commercial/ocpi-service/v221/prisma/client/specs';
import { Test, TestingModule } from '@nestjs/testing';
import { mapPodUnitConnectorFieldsToConnectorMigrationUuid } from './connector-mapper';
import { mapRevenueProfileFieldsToMigrationUuid } from '@experience/commercial/ocpi-service/v221/nest/tariffs-module';
import { mockDeep } from 'jest-mock-extended';
import ConnectorTariffMigrator, {
  includeOptions,
} from './connector-tariff-migrator';
import MockDate from 'mockdate';

describe('Connector tariff migrator', () => {
  let database: OCPIPrismaClient;
  let podUnitConnectors: typeof PodUnitConnector;
  let migrator: ConnectorTariffMigrator;

  const logger = mockDeep<Logger>();
  const error = new Error('Oops');

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        {
          provide: 'POD_UNIT_CONNECTOR_REPOSITORY',
          useValue: PodUnitConnector,
        },
        {
          provide: 'ALS_PRISMA_OCPI_V221',
          useValue: new AsyncLocalStorage(),
        },
        ConfigService,
        ConnectorTariffMigrator,
        OCPIPrismaClient,
        PrismaHealthIndicator,
      ],
    }).compile();

    database = module.get<OCPIPrismaClient>(OCPIPrismaClient);
    podUnitConnectors = module.get<typeof PodUnitConnector>(
      'POD_UNIT_CONNECTOR_REPOSITORY'
    );
    migrator = module.get<ConnectorTariffMigrator>(ConnectorTariffMigrator);

    module.useLogger(logger);

    MockDate.set(new Date());
  });

  it('should migrate a pod unit connector to a connector tariff', async () => {
    const mockFindAll = jest
      .spyOn(podUnitConnectors, 'findAll')
      .mockResolvedValue([
        TEST_POD_UNIT_CONNECTOR_ENTITY_WITH_LOCATION_AND_REVENUE_PROFILE,
      ]);
    const mockUpdate = (database.connector.update = jest.fn());

    await migrator.migrate(new Date());

    expect(mockFindAll).toHaveBeenCalledWith({
      include: includeOptions,
      paranoid: true,
      where: {
        [Op.and]: [
          { '$unit.podLocation.is_public$': true },
          {
            [Op.or]: [
              { '$unit.podLocation.updated_at$': { [Op.gt]: new Date() } },
              {
                '$unit.podLocation.revenueProfile.updated_at$': {
                  [Op.gt]: new Date(),
                },
              },
              {
                '$unit.podLocation.revenueProfile.revenueProfileTiers.updated_at$':
                  { [Op.gt]: new Date() },
              },
            ],
          },
        ],
      },
    });

    expect(mockUpdate).toHaveBeenCalledWith({
      data: {
        tariffs: {
          set: [
            {
              id: mapRevenueProfileFieldsToMigrationUuid(
                TEST_REVENUE_PROFILE_ENTITY
              ),
            },
          ],
        },
      },
      where: {
        id: mapPodUnitConnectorFieldsToConnectorMigrationUuid(
          TEST_POD_UNIT_CONNECTOR_ENTITY_WITH_LOCATION_AND_REVENUE_PROFILE
        ),
      },
    });
  });

  it('should catch errors during the migration task and log a warning', async () => {
    jest
      .spyOn(podUnitConnectors, 'findAll')
      .mockResolvedValueOnce([
        TEST_POD_UNIT_CONNECTOR_ENTITY_WITH_LOCATION_AND_REVENUE_PROFILE,
      ]);
    database.connector.update = jest.fn().mockRejectedValueOnce(error);

    await migrator.migrate(new Date());

    expect(logger.warn).toHaveBeenNthCalledWith(
      1,
      {
        error,
        id: TEST_CONNECTOR_ENTITY_ID,
      },
      'failed to migrate connector tariff',
      'ConnectorTariffMigrator'
    );
    expect(logger.warn).toHaveBeenNthCalledWith(
      2,
      {
        failedMigrationIds: [TEST_CONNECTOR_ENTITY_ID],
      },
      'failed to migrate 1 connector tariffs',
      'ConnectorTariffMigrator'
    );
  });

  it('should continue with the migration task after an error is thrown', async () => {
    jest
      .spyOn(podUnitConnectors, 'findAll')
      .mockResolvedValue([
        TEST_POD_UNIT_CONNECTOR_ENTITY_WITH_LOCATION_AND_REVENUE_PROFILE,
        TEST_POD_UNIT_CONNECTOR_ENTITY_WITH_LOCATION_AND_REVENUE_PROFILE,
      ]);
    database.connector.update = jest
      .fn()
      .mockRejectedValueOnce(error)
      .mockResolvedValueOnce(undefined);

    await migrator.migrate(new Date());

    expect(logger.log).toHaveBeenNthCalledWith(
      3,
      'migrated 1 connector tariffs',
      'ConnectorTariffMigrator'
    );
    expect(logger.warn).toHaveBeenNthCalledWith(
      2,
      { failedMigrationIds: [TEST_CONNECTOR_ENTITY_ID] },
      'failed to migrate 1 connector tariffs',
      'ConnectorTariffMigrator'
    );
  });
});
