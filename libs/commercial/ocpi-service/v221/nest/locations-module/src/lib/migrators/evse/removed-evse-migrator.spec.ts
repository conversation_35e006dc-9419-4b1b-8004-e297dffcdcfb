import { AsyncLocalStorage } from 'async_hooks';
import { ConfigService } from '@nestjs/config';
import { Logger } from '@nestjs/common';
import { OCPIPrismaClient } from '@experience/commercial/ocpi-service/v221/prisma/client';
import {
  PodUnitConnector,
  TEST_POD_UNIT_CONNECTOR_ENTITY_WITH_LOCATION,
} from '@experience/shared/sequelize/podadmin';
import { PrismaHealthIndicator } from '@nestjs/terminus';
import { Test, TestingModule } from '@nestjs/testing';
import { includeOptions } from './evse-migrator';
import { mockDeep } from 'jest-mock-extended';
import MockDate from 'mockdate';
import RemovedEvseMigrator from './removed-evse-migrator';

describe('removed EVSE migrator', () => {
  let database: OCPIPrismaClient;
  let migrator: RemovedEvseMigrator;
  let podUnitConnectors: typeof PodUnitConnector;

  const connector = TEST_POD_UNIT_CONNECTOR_ENTITY_WITH_LOCATION;
  const { unit } = connector;
  const { ppid } = unit;

  const logger = mockDeep<Logger>();

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        {
          provide: 'ALS_PRISMA_OCPI_V221',
          useValue: new AsyncLocalStorage(),
        },
        {
          provide: 'POD_UNIT_CONNECTOR_REPOSITORY',
          useValue: PodUnitConnector,
        },
        ConfigService,
        RemovedEvseMigrator,
        OCPIPrismaClient,
        PrismaHealthIndicator,
      ],
    }).compile();

    database = module.get<OCPIPrismaClient>(OCPIPrismaClient);
    migrator = module.get<RemovedEvseMigrator>(RemovedEvseMigrator);
    podUnitConnectors = module.get<typeof PodUnitConnector>(
      'POD_UNIT_CONNECTOR_REPOSITORY'
    );

    module.useLogger(logger);

    MockDate.set(new Date());
  });

  it('should update the status of EVSE to REMOVED if not public', async () => {
    const mockFindManyEvses = (database.evse.findMany = jest
      .fn()
      .mockResolvedValueOnce([{ ppid }]));
    const mockFindAllConnectors = jest
      .spyOn(podUnitConnectors, 'findAll')
      .mockResolvedValue([]);
    const mockUpdateManyEvses = (database.evse.updateMany = jest
      .fn()
      .mockResolvedValueOnce({ count: 1 }));

    await migrator.migrate();

    expect(mockFindManyEvses).toHaveBeenCalledWith({
      distinct: 'ppid',
      select: { ppid: true },
      where: { NOT: { status: 'REMOVED' } },
    });
    expect(mockFindAllConnectors).toHaveBeenCalledWith({
      include: includeOptions,
      paranoid: true,
      where: { '$unit.podLocation.is_public$': true },
    });
    expect(mockUpdateManyEvses).toHaveBeenCalledWith({
      data: { status: 'REMOVED', statusLastUpdated: new Date() },
      where: {
        ppid: { in: [ppid] },
      },
    });
    expect(logger.log).toHaveBeenCalledWith(
      { removed: [ppid] },
      'migrated removed EVSEs',
      'RemovedEvseMigrator'
    );
  });

  it('should not update the status of EVSE to REMOVED if public', async () => {
    const mockFindManyEvses = (database.evse.findMany = jest
      .fn()
      .mockResolvedValueOnce([{ ppid }]));
    const mockFindAllConnectors = jest
      .spyOn(podUnitConnectors, 'findAll')
      .mockResolvedValue([connector]);
    const mockUpdateManyEvses = (database.evse.updateMany = jest.fn());

    await migrator.migrate();

    expect(mockFindManyEvses).toHaveBeenCalledWith({
      distinct: 'ppid',
      select: { ppid: true },
      where: { NOT: { status: 'REMOVED' } },
    });
    expect(mockFindAllConnectors).toHaveBeenCalledWith({
      include: includeOptions,
      paranoid: true,
      where: { '$unit.podLocation.is_public$': true },
    });
    expect(mockUpdateManyEvses).not.toHaveBeenCalled();
  });

  it('should handle errors when updating the status of EVSE to REMOVED', async () => {
    const mockFindManyEvses = (database.evse.findMany = jest
      .fn()
      .mockResolvedValueOnce([{ ppid }]));
    const mockFindAllConnectors = jest
      .spyOn(podUnitConnectors, 'findAll')
      .mockResolvedValue([]);
    const mockUpdateManyEvses = (database.evse.updateMany = jest
      .fn()
      .mockRejectedValueOnce(new Error('Oops')));

    await migrator.migrate();

    expect(mockFindManyEvses).toHaveBeenCalledWith({
      distinct: 'ppid',
      select: { ppid: true },
      where: { NOT: { status: 'REMOVED' } },
    });
    expect(mockFindAllConnectors).toHaveBeenCalledWith({
      include: includeOptions,
      paranoid: true,
      where: { '$unit.podLocation.is_public$': true },
    });
    expect(mockUpdateManyEvses).toHaveBeenCalledWith({
      data: { status: 'REMOVED', statusLastUpdated: new Date() },
      where: {
        ppid: { in: [ppid] },
      },
    });
    expect(logger.warn).toHaveBeenCalledWith(
      {
        error: new Error('Oops'),
        removed: [ppid],
      },
      'failed to migrate removed EVSEs',
      'RemovedEvseMigrator'
    );
  });

  it('should update the status of EVSE from REMOVED to UNKNOWN if public', async () => {
    const mockFindManyEvses = (database.evse.findMany = jest
      .fn()
      .mockResolvedValueOnce([]));
    const mockFindAllConnectors = jest
      .spyOn(podUnitConnectors, 'findAll')
      .mockResolvedValue([connector]);
    const mockUpdateManyEvses = (database.evse.updateMany = jest
      .fn()
      .mockResolvedValueOnce({ count: 1 }));

    await migrator.migrate();

    expect(mockFindManyEvses).toHaveBeenCalledWith({
      distinct: 'ppid',
      select: { ppid: true },
      where: { NOT: { status: 'REMOVED' } },
    });
    expect(mockFindAllConnectors).toHaveBeenCalledWith({
      include: includeOptions,
      paranoid: true,
      where: { '$unit.podLocation.is_public$': true },
    });
    expect(mockUpdateManyEvses).toHaveBeenCalledWith({
      data: { status: 'UNKNOWN', statusLastUpdated: new Date() },
      where: {
        ppid: { in: [ppid] },
        status: 'REMOVED',
      },
    });
    expect(logger.log).toHaveBeenCalledWith(
      { added: [ppid] },
      'migrated added EVSEs',
      'RemovedEvseMigrator'
    );
  });

  it('should not update the status of EVSE from REMOVED to UNKNOWN if not public', async () => {
    const mockFindManyEvses = (database.evse.findMany = jest
      .fn()
      .mockResolvedValueOnce([]));
    const mockFindAllConnectors = jest
      .spyOn(podUnitConnectors, 'findAll')
      .mockResolvedValue([]);
    const mockUpdateManyEvses = (database.evse.updateMany = jest.fn());

    await migrator.migrate();

    expect(mockFindManyEvses).toHaveBeenCalledWith({
      distinct: 'ppid',
      select: { ppid: true },
      where: { NOT: { status: 'REMOVED' } },
    });
    expect(mockFindAllConnectors).toHaveBeenCalledWith({
      include: includeOptions,
      paranoid: true,
      where: { '$unit.podLocation.is_public$': true },
    });
    expect(mockUpdateManyEvses).not.toHaveBeenCalled();
  });

  it('should handle errors when updating the status of EVSE from REMOVED to UNKNOWN', async () => {
    const mockFindManyEvses = (database.evse.findMany = jest
      .fn()
      .mockResolvedValueOnce([]));
    const mockFindAllConnectors = jest
      .spyOn(podUnitConnectors, 'findAll')
      .mockResolvedValue([connector]);
    const mockUpdateManyEvses = (database.evse.updateMany = jest
      .fn()
      .mockRejectedValueOnce(new Error('Oops')));

    await migrator.migrate();

    expect(mockFindManyEvses).toHaveBeenCalledWith({
      distinct: 'ppid',
      select: { ppid: true },
      where: { NOT: { status: 'REMOVED' } },
    });
    expect(mockFindAllConnectors).toHaveBeenCalledWith({
      include: includeOptions,
      paranoid: true,
      where: { '$unit.podLocation.is_public$': true },
    });
    expect(mockUpdateManyEvses).toHaveBeenCalledWith({
      data: { status: 'UNKNOWN', statusLastUpdated: new Date() },
      where: {
        ppid: { in: [ppid] },
        status: 'REMOVED',
      },
    });
    expect(logger.warn).toHaveBeenCalledWith(
      {
        error: new Error('Oops'),
        added: [ppid],
      },
      'failed to migrate added EVSEs',
      'RemovedEvseMigrator'
    );
  });
});
