import { AsyncLocalStorage } from 'async_hooks';
import { ConfigModule } from '@nestjs/config';
import { CredentialsStore } from '@experience/commercial/ocpi-service/v221/shared';
import { IndexLocationsProducer } from './index-locations.producer';
import { OCPIPrismaClient } from '@experience/commercial/ocpi-service/v221/prisma/client';
import { PrismaHealthIndicator } from '@nestjs/terminus';
import { SqsClientService } from '@experience/shared/nest/aws/sqs-module';
import {
  TEST_EVSE_ENTITY,
  TEST_LOCATION_ENTITY_DEEP,
} from '@experience/commercial/ocpi-service/v221/prisma/client/specs';
import { Test, TestingModule } from '@nestjs/testing';

jest.mock('@experience/shared/nest/aws/sqs-module', () => ({
  ...jest.requireActual('@experience/shared/nest/aws/sqs-module'),
  SqsClientService: jest.fn().mockImplementation(() => ({
    sendBatchMessages: jest.fn().mockResolvedValue(undefined),
  })),
}));
jest.mock('uuid', () => ({ v4: () => 'e3e92d8a-966e-4e0d-8895-6e4d8d75dc49' }));

describe('index locations producer', () => {
  let als: AsyncLocalStorage<CredentialsStore>;
  let database: OCPIPrismaClient;
  let producer: IndexLocationsProducer;
  let sqsClientService: SqsClientService;

  beforeAll(async () => {
    const module: TestingModule = await Test.createTestingModule({
      imports: [
        await ConfigModule.forRoot({
          load: [
            () => ({
              INDEX_LOCATIONS_QUEUE_URL:
                'http://localhost/000000/index-locations-requests',
            }),
          ],
        }),
      ],
      providers: [
        AsyncLocalStorage<CredentialsStore>,
        IndexLocationsProducer,
        OCPIPrismaClient,
        PrismaHealthIndicator,
        SqsClientService,
        { provide: 'ALS_PRISMA_OCPI_V221', useValue: new AsyncLocalStorage() },
      ],
    }).compile();

    als = module.get(AsyncLocalStorage<CredentialsStore>);
    database = module.get<OCPIPrismaClient>(OCPIPrismaClient);
    producer = module.get<IndexLocationsProducer>(IndexLocationsProducer);
    sqsClientService = module.get<SqsClientService>(SqsClientService);
  });

  it('should be defined', () => {
    expect(als).toBeDefined();
    expect(database).toBeDefined();
    expect(producer).toBeDefined();
    expect(sqsClientService).toBeDefined();
  });

  it('should queue index location requests', async () => {
    const mockFindMany = jest
      .spyOn(database.location, 'findMany')
      .mockResolvedValue([TEST_LOCATION_ENTITY_DEEP]);

    await producer.queueIndexLocationRequests();

    expect(mockFindMany).toHaveBeenCalledWith({
      include: { evses: true },
      take: 1,
    });
    expect(sqsClientService.sendBatchMessages).toHaveBeenCalledWith(
      'http://localhost/000000/index-locations-requests',
      [
        {
          messageAttributes: undefined,
          messageBody: JSON.stringify({ ppid: TEST_EVSE_ENTITY.ppid }),
          messageId: 'e3e92d8a-966e-4e0d-8895-6e4d8d75dc49',
        },
      ]
    );
  });
});
