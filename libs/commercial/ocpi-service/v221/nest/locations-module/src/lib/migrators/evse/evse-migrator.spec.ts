import { AsyncLocalStorage } from 'async_hooks';
import { ConfigService } from '@nestjs/config';
import { Logger } from '@nestjs/common';
import { OCPIPrismaClient } from '@experience/commercial/ocpi-service/v221/prisma/client';
import { Op } from 'sequelize';
import {
  PodUnitConnector,
  TEST_POD_UNIT_CONNECTOR_ENTITY_WITH_LOCATION,
} from '@experience/shared/sequelize/podadmin';
import { PrismaHealthIndicator } from '@nestjs/terminus';
import { TEST_EVSE_ENTITY_ID } from '@experience/commercial/ocpi-service/v221/prisma/client/specs';
import { Test, TestingModule } from '@nestjs/testing';
import { mapPodUnitConnectorToEvse } from './evse-mapper';
import { mockDeep } from 'jest-mock-extended';
import EvseMigrator, { includeOptions } from './evse-migrator';
import MockDate from 'mockdate';

describe('EVSE migrator', () => {
  let database: OCPIPrismaClient;
  let podUnitConnectors: typeof PodUnitConnector;
  let migrator: EvseMigrator;

  const logger = mockDeep<Logger>();
  const error = new Error('Oops');

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        {
          provide: 'ALS_PRISMA_OCPI_V221',
          useValue: new AsyncLocalStorage(),
        },
        {
          provide: 'POD_UNIT_CONNECTOR_REPOSITORY',
          useValue: PodUnitConnector,
        },
        ConfigService,
        EvseMigrator,
        OCPIPrismaClient,
        PrismaHealthIndicator,
      ],
    }).compile();

    database = module.get<OCPIPrismaClient>(OCPIPrismaClient);
    podUnitConnectors = module.get<typeof PodUnitConnector>(
      'POD_UNIT_CONNECTOR_REPOSITORY'
    );
    migrator = module.get<EvseMigrator>(EvseMigrator);

    module.useLogger(logger);

    MockDate.set(new Date());
  });

  it('should migrate a pod unit connector to an EVSE', async () => {
    const mockFindAll = jest
      .spyOn(podUnitConnectors, 'findAll')
      .mockResolvedValue([TEST_POD_UNIT_CONNECTOR_ENTITY_WITH_LOCATION]);
    const mockUpsert = (database.evse.upsert = jest.fn());
    const expectedCreate = mapPodUnitConnectorToEvse(
      TEST_POD_UNIT_CONNECTOR_ENTITY_WITH_LOCATION
    );

    const { id, status, ...expectedUpdate } = expectedCreate;

    await migrator.migrate(new Date());

    expect(mockFindAll).toHaveBeenCalledWith({
      include: includeOptions,
      paranoid: true,
      where: {
        [Op.and]: [
          { '$unit.podLocation.is_public$': true },
          { '$unit.podLocation.updated_at$': { [Op.gt]: new Date() } },
        ],
      },
    });

    expect(mockUpsert).toHaveBeenCalledWith({
      create: expectedCreate,
      update: expectedUpdate,
      where: { id },
    });
  });

  it('should catch errors during the migration task and log a warning', async () => {
    jest
      .spyOn(podUnitConnectors, 'findAll')
      .mockResolvedValue([TEST_POD_UNIT_CONNECTOR_ENTITY_WITH_LOCATION]);
    database.evse.upsert = jest.fn().mockRejectedValue(error);

    await migrator.migrate(new Date());

    expect(logger.warn).toHaveBeenNthCalledWith(
      1,
      {
        error,
        id: TEST_EVSE_ENTITY_ID,
      },
      'failed to migrate EVSE',
      'EvseMigrator'
    );
    expect(logger.warn).toHaveBeenNthCalledWith(
      2,
      {
        failedMigrationIds: [TEST_EVSE_ENTITY_ID],
      },
      'failed to migrate 1 EVSEs',
      'EvseMigrator'
    );
  });

  it('should continue with the migration task after an error is thrown', async () => {
    jest
      .spyOn(podUnitConnectors, 'findAll')
      .mockResolvedValueOnce([
        TEST_POD_UNIT_CONNECTOR_ENTITY_WITH_LOCATION,
        TEST_POD_UNIT_CONNECTOR_ENTITY_WITH_LOCATION,
      ]);
    database.evse.upsert = jest
      .fn()
      .mockRejectedValueOnce(error)
      .mockResolvedValueOnce(undefined);

    await migrator.migrate(new Date());

    expect(logger.log).toHaveBeenNthCalledWith(
      3,
      'migrated 1 EVSEs',
      'EvseMigrator'
    );
    expect(logger.warn).toHaveBeenNthCalledWith(
      2,
      { failedMigrationIds: [TEST_EVSE_ENTITY_ID] },
      'failed to migrate 1 EVSEs',
      'EvseMigrator'
    );
  });
});
