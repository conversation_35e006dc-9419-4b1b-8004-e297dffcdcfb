import { faker } from '@faker-js/faker';

export const describeTariffPages = (): void => {
  describe('tariff pages', () => {
    beforeEach(() => {
      cy.clickLink('Tariffs');
    });

    it(`should render correctly`, () => {
      cy.shouldHaveHeading(1, /Tariffs/);
      cy.shouldHaveText(/List of \d+ tariffs? across your network./);
      cy.shouldHaveTable('Table of Tariffs');

      cy.clickFirstLinkWithinTable(/.*/, 'Table of Tariffs');
      cy.shouldHaveHeading(1, /.*/);
      cy.shouldHaveHeading(2, 'Schedules');
      cy.shouldHaveHeading(3, 'Drivers schedule');
      cy.shouldHaveHeading(3, 'Members schedule');
      cy.shouldNotHaveHeading(3, 'Public schedule');
      cy.shouldHaveHeading(2, 'Chargers');
      cy.shouldHaveTable('Table of chargers');
      cy.findByText(
        'Tariffs are made up of schedules. Each charger can only be assigned a single tariff, so that tariff needs to contain a schedule for every type of driver who can use the charger.'
      ).should('not.exist');
    });

    it('should add a new tariff', () => {
      cy.clickButton('Add tariff');
      cy.shouldHaveHeading(2, 'Add tariff');
      cy.enterText('Tariff name', faker.person.zodiacSign());
      cy.clickButton('Add tariff');
      cy.shouldNotHaveHeading(2, 'Add tariff');
      cy.shouldHaveTable('Table of chargers');
    });

    it(`should add a flat rate schedule to a tariff`, () => {
      cy.shouldHaveHeading(1, /Tariffs/);
      cy.shouldHaveText(/List of \d+ tariffs? across your network./);
      cy.shouldHaveTable('Table of Tariffs');

      cy.clickFirstLinkWithinTable(/.*/, 'Table of Tariffs');
      cy.wait('@tariff');
      cy.clickButton('Add schedule');
      cy.clickMenuItem('Flat rate');

      cy.shouldHaveHeading(2, 'Add flat rate schedule');

      cy.selectOption('Tariff tier', 'Public');
      cy.enterText(
        'Price per kWh (in pence)',
        faker.number.int({ min: 0, max: 199 }).toString(),
        true
      );
      cy.clickButton('Add schedule');

      cy.shouldNotHaveHeading(2, 'Add flat rate schedule');
    });

    it(`should add a week/weekend schedule to a tariff`, () => {
      cy.shouldHaveHeading(1, /Tariffs/);
      cy.shouldHaveText(/List of \d+ tariffs? across your network./);
      cy.shouldHaveTable('Table of Tariffs');

      cy.clickFirstLinkWithinTable(/.*/, 'Table of Tariffs');
      cy.wait('@tariff');
      cy.clickButton('Add schedule');
      cy.clickMenuItem('Week/weekend');

      cy.shouldHaveHeading(2, 'Add week/weekend schedule');

      cy.selectOption('Tariff tier', 'Public');
      cy.enterText(
        'Weekday price per kWh (in pence)',
        faker.number.int({ min: 0, max: 199 }).toString(),
        true
      );
      cy.enterText(
        'Weekend price per kWh (in pence)',
        faker.number.int({ min: 0, max: 199 }).toString(),
        true
      );
      cy.clickButton('Add schedule', 0, true);

      cy.shouldNotHaveHeading(2, 'Add week/weekend schedule');
    });

    it(`should add a day/night schedule to a tariff`, () => {
      cy.shouldHaveHeading(1, /Tariffs/);
      cy.shouldHaveText(/List of \d+ tariffs? across your network./);
      cy.shouldHaveTable('Table of Tariffs');

      cy.clickFirstLinkWithinTable(/.*/, 'Table of Tariffs');
      cy.wait('@tariff');
      cy.clickButton('Add schedule');
      cy.clickMenuItem('Day/night');

      cy.shouldHaveHeading(2, 'Add day/night schedule');

      cy.selectOption('Tariff tier', 'Public');
      cy.enterText('Day start time', '09:00', true);
      cy.enterText(
        'Day price per kWh (in pence)',
        faker.number.int({ min: 0, max: 199 }).toString(),
        true
      );
      cy.enterText('Night start time', '05:00', true);
      cy.enterText(
        'Night price per kWh (in pence)',
        faker.number.int({ min: 0, max: 199 }).toString(),
        true
      );
      cy.clickButton('Add schedule', 0, true);

      cy.shouldNotHaveHeading(2, 'Add day/night schedule');
    });

    it(`should add a custom schedule to a tariff`, () => {
      cy.shouldHaveHeading(1, /Tariffs/);
      cy.shouldHaveText(/List of \d+ tariffs? across your network./);
      cy.shouldHaveTable('Table of Tariffs');

      cy.clickFirstLinkWithinTable(/.*/, 'Table of Tariffs');
      cy.wait('@tariff');
      cy.clickButton('Add schedule');
      cy.clickMenuItem('Custom');

      cy.shouldHaveHeading(2, 'Add schedule');

      cy.selectOption('Tariff tier', 'Public');
      cy.selectOption('Pricing model', 'per kWh', 0, true);
      cy.selectOption('Start day', 'Tuesday', 0, true);
      cy.enterText('Start time', '09:00', true);
      cy.selectOption('End day', 'Friday', 0, true);
      cy.enterText('End time', '12:00', true);
      cy.enterText(
        'Price (in pence)',
        faker.number.int({ min: 0, max: 199 }).toString(),
        true
      );
      cy.clickButton('Add schedule');

      cy.shouldNotHaveHeading(2, 'Add schedule');
    });
  });
};
