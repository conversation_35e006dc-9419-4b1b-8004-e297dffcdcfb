export const describeBillingPage = (): void => {
  describe('billing pages', () => {
    it('should render correctly', () => {
      cy.clickLink('Billing');
      cy.shouldHaveHeading(1, /Billing/);
      cy.shouldHaveTable('Table of billing statements');
      cy.clickTab('Subscription');
      cy.shouldHaveTable('Table of subscription payments');
      cy.shouldHaveTable('Table of subscription chargers');
      cy.shouldHaveHeading(2, /Subscription payments/);
      cy.shouldHaveHeading(2, /Subscription chargers/);
      cy.clickTab('Business details');
      cy.shouldHaveText('Business details');
      cy.shouldHaveText('Business address');
    });
  });
};
