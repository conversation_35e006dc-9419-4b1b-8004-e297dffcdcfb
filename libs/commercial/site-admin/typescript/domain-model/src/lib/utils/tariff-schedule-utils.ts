import { Pod } from '../pod.dto';
import { Tariff } from '../tariff.dto';
import { TariffSchedule } from '../tariff-schedule.dto';

export const isFlatRateSchedule = (schedule: TariffSchedule) =>
  schedule.startDay === schedule.endDay &&
  schedule.startTime === schedule.endTime;

export const isFreeForPublic = (pod: Pod, tariffs: Tariff[]): boolean => {
  if (!pod.isPublic) {
    return false;
  }

  const matchingTariff = tariffs.find((tariff) => tariff.id === pod.tariff?.id);

  // a tariff is assigned to the charger but doesn't match any tariffs in the group - e.g. tariff is from the scheme
  if (pod.tariff && !matchingTariff) {
    return false;
  }

  const publicSchedules = matchingTariff?.schedule?.public;

  if (!publicSchedules || publicSchedules.length === 0) {
    return true;
  }

  return publicSchedules.every((schedule: TariffSchedule) =>
    schedule.bands.every((band) => band.cost === 0)
  );
};
