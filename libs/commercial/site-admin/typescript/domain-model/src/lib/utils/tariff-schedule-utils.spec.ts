import {
  TEST_FREE_PUBLIC_TARIFF,
  TEST_TARIFF_NO_SCHEDULES,
  TEST_TARIFF_WITH_EMPTY_SCHEDULES,
  TEST_TARIFF_WITH_PRICE_BANDS,
} from '../__fixtures__/tariff.dto';
import { TEST_POD_WITH_TARIFF } from '../__fixtures__/pod.dto';
import { createTestTariffSchedule } from '../__fixtures__/tariff-schedule.dto';
import { isFlatRateSchedule, isFreeForPublic } from './tariff-schedule-utils';

describe('Tariff schedule utils', () => {
  describe('isFlatRateSchedule', () => {
    it('should return true if the schedule has the same start/end day and time', () => {
      const schedule = createTestTariffSchedule({
        startDay: 2,
        endDay: 2,
        startTime: '10:00',
        endTime: '10:00',
      });

      expect(isFlatRateSchedule(schedule)).toEqual(true);
    });

    it.each([
      [2, '22:00'],
      [3, '10:00'],
      [4, '16:00'],
    ])(
      'should return false if the schedule start day is set to %s and start time is set to %p',
      (startDay, startTime) => {
        const schedule = createTestTariffSchedule({
          startDay,
          endDay: 2,
          startTime,
          endTime: '10:00',
        });

        expect(isFlatRateSchedule(schedule)).toEqual(false);
      }
    );
    it.each([
      [2, '14:00'],
      [5, '10:00'],
      [3, '20:00'],
    ])(
      'should return false if the schedule end day is set to %s and end time is set to %p',
      (endDay, endTime) => {
        const schedule = createTestTariffSchedule({
          startDay: 2,
          endDay,
          startTime: '10:00',
          endTime,
        });

        expect(isFlatRateSchedule(schedule)).toEqual(false);
      }
    );
  });

  describe('isFreeForPublic', () => {
    it.each([
      [
        'the charger has no tariff assigned',
        { ...TEST_POD_WITH_TARIFF, tariff: undefined },
        [TEST_TARIFF_WITH_PRICE_BANDS],
      ],
      [
        'the charger has no public tariff schedule',
        TEST_POD_WITH_TARIFF,
        [TEST_TARIFF_NO_SCHEDULES],
      ],
      [
        'the charger has empty tariff schedules',
        TEST_POD_WITH_TARIFF,
        [TEST_TARIFF_WITH_EMPTY_SCHEDULES],
      ],
      [
        'the charger has a public tariff schedule with a price of £0.00',
        TEST_POD_WITH_TARIFF,
        [TEST_FREE_PUBLIC_TARIFF],
      ],
    ])('should return true if %s', (_, pod, tariffs) => {
      expect(isFreeForPublic(pod, tariffs)).toEqual(true);
    });

    it.each([
      [
        'the charger is not public',
        { ...TEST_POD_WITH_TARIFF, isPublic: false },
        [TEST_TARIFF_NO_SCHEDULES],
      ],
      [
        'the charger has an assigned tariff but it does not match the tariffs in the current group',
        TEST_POD_WITH_TARIFF,
        [{ ...TEST_TARIFF_WITH_PRICE_BANDS, id: 2 }],
      ],
      [
        'the charger has a public tariff schedule with a price above £0.00',
        TEST_POD_WITH_TARIFF,
        [TEST_TARIFF_WITH_PRICE_BANDS],
      ],
    ])('should return false if %s', (_, pod, tariffs) => {
      expect(isFreeForPublic(pod, tariffs)).toEqual(false);
    });
  });
});
