export class CustomerDetails {
  address?: {
    line1?: string;
    line2?: string;
    town?: string;
    county?: string;
    postcode?: string;
  };
  email: string;
  name: string;
  accountReference?: string;
  poNumber?: string;
}

export class BillingStatement {
  siteName: string;
  month: string;
  feeInvoiceNumber: string;
  feeInvoiceStatus: string;
  hostedInvoiceUrl?: string;
  invoiceId: string;
  revenuePayoutStatus: string;
  statementId: string;
}

export class SubscriptionInvoice {
  status: string;
  created: string;
  amount: number;
  due: string;
  hostedInvoiceUrl: string;
  invoiceNumber: string;
  invoicePdfUrl: string;
  customerEmail: string;
}

export class Subscription {
  chargers: SubscriptionCharger[];
  status: string;
  invoices: SubscriptionInvoice[];
}

export class SubscriptionCharger {
  ppid: string;
  socket: 'A' | 'B';
}

export class BillingInformation {
  customerDetails: CustomerDetails;
  onboardingLink: string | undefined;
  statements: BillingStatement[];
  subscription: Subscription;
}
