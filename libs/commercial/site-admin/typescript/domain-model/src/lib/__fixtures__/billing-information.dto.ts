import { BillingInformation } from '../billing-information.dto';

export const TEST_BILLING_INFORMATION: BillingInformation = {
  customerDetails: {
    accountReference: '*********',
    address: {
      county: 'Test County',
      line1: 'Flat 1',
      line2: '1 Test Street',
      postcode: 'TE1 1ST',
      town: 'Test Town',
    },
    email: '<EMAIL>',
    name: 'Test Group Inc Invoice office',
    poNumber: 'UK10010001',
  },
  statements: [
    {
      feeInvoiceNumber: 'ADF-1001',
      feeInvoiceStatus: 'open',
      invoiceId: '5ab6cbf4-9902-4942-a052-b6a62bcdbaac',
      month: '2023-01-13',
      revenuePayoutStatus: 'PENDING',
      siteName: 'Site 1',
      statementId: '9bd247e0-92b9-11ee-b9d1-0242ac120002',
    },
    {
      feeInvoiceNumber: 'ADF-1002',
      feeInvoiceStatus: 'overdue',
      invoiceId: '9be0da46-1462-42b5-85b1-d43cf64f339e',
      month: '2023-02-13',
      revenuePayoutStatus: 'TRANSFERRED',
      siteName: 'Site 1',
      statementId: '369b1302-92b9-11ee-b9d1-0242ac120002',
    },
    {
      feeInvoiceNumber: 'ADF-1003',
      feeInvoiceStatus: 'paid',
      invoiceId: 'ff407cc1-6898-4559-a220-ae66acb3f391',
      month: '2023-02-13',
      revenuePayoutStatus: 'PAID_OUT',
      siteName: 'Banner Street',
      statementId: 'f7c7552c-92b9-11ee-b9d1-0242ac120002',
    },
    {
      feeInvoiceNumber: 'ADF-1004',
      feeInvoiceStatus: 'paid',
      invoiceId: 'ff407cc1-6898-4559-a220-ae66acb3f111',
      month: '2023-02-13',
      revenuePayoutStatus: 'WARNING',
      siteName: 'Banner Street',
      statementId: 'f7c7552c-92b9-11ee-b9d1-0242ac121234',
    },
  ],
  subscription: {
    chargers: [
      {
        ppid: 'PG-70500',
        socket: 'A',
      },
    ],
    invoices: [
      {
        amount: 50,
        created: '2022-01-14',
        customerEmail: '<EMAIL>',
        due: '2022-02-11',
        hostedInvoiceUrl: 'https://stripe.com/invoice?id=in_NggdGfEfND3Bfs',
        invoiceNumber: 'ADF-1001',
        invoicePdfUrl: 'https://stripe.com/invoice.pdf',
        status: 'paid',
      },
    ],
    status: 'active',
  },
  onboardingLink: undefined,
};
