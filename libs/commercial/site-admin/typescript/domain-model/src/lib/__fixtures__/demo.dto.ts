import { Admin, AdminStatus } from '../admin.dto';
import { BillingInformation } from '../billing-information.dto';
import { Charge } from '../charge.dto';
import { ChargeSummary, GroupChargeSummary } from '../charge-summary.dto';
import { Domain } from '../domain.dto';
import {
  Driver,
  DriverCharges,
  DriverStatus,
  DriverTariffTier,
} from '../driver.dto';
import { Faker, base, en, en_GB } from '@faker-js/faker';
import { Group } from '../group.dto';
import { GroupType, User, UserStatus } from '../user.dto';
import { Insight } from '../insights.dto';
import { Pod } from '../pod.dto';
import { Site, SiteStats } from '../site.dto';
import { SocketStatus } from '../socket.dto';
import { Tariff } from '../tariff.dto';
import { TariffPricingModel, TariffTier } from '../tariff-schedule.dto';
import { formatDateStringToTimestamp } from '@experience/shared/typescript/utils';
import {
  sortByEndedAt,
  sortBySiteNamePodNamePluggedIn,
} from '../utils/charge-sort-utils';
import { sortDrivers } from '../utils/driver-utils';
import dayjs from 'dayjs';

const faker: Faker = new Faker({
  locale: [en_GB, en, base],
  seed: 1,
});

const fakeFourLetterNames: string[] =
  faker.rawDefinitions.person?.first_name?.generic?.filter(
    (a) => a.length === 4
  ) as string[];

const fakeAdmin = (id: number): Admin => ({
  activatedOn: faker.date.past(),
  authId: faker.string.uuid(),
  email: faker.internet.email(),
  emailBounced: faker.datatype.boolean(),
  firstName: faker.person.firstName(),
  id: id,
  lastName: faker.person.lastName(),
  lastLogin: faker.date.past(),
  status: id % 2 === 0 ? AdminStatus.REGISTERED : AdminStatus.DEACTIVATED,
});

const fakeAdmins = (count: number): Admin[] =>
  [...new Array(count).keys()].map((i) => fakeAdmin(i + 1));

const fakeCharge = (
  sites: Site[],
  pods: Pod[],
  drivers: Driver[],
  tariffs: Tariff[]
): Charge => {
  // randomly select a site
  const site = faker.helpers.arrayElement(sites);

  // randomly select a pod
  const pod = faker.helpers.arrayElement(pods);

  // randomly decide if the charge is confirmed
  const confirmed = faker.datatype.boolean({ probability: 0.8 });

  // randomly select a driver for confirmed charges
  const driver = confirmed
    ? faker.helpers.maybe(() => faker.helpers.arrayElement(drivers), {
        probability: pod.isPublic ? 0.25 : 1,
      })
    : undefined;

  // look up the tariff for the pod
  const tariff = confirmed
    ? tariffs.find((tariff) => tariff.id === pod.tariff?.id)
    : undefined;

  // calculate the price per energyUsage based on driver and tariff
  const pricePerKwh =
    ((driver?.tariffTier === DriverTariffTier.MEMBER
      ? tariff?.schedule?.members?.[0].bands[0].cost
      : driver?.tariffTier === DriverTariffTier.DRIVER
      ? tariff?.schedule?.drivers?.[0].bands[0].cost
      : confirmed
      ? tariff?.schedule?.public?.[0].bands[0].cost
      : undefined) ?? 0) / 100;

  // randomly generate a plugged in time
  const pluggedIn = dayjs(faker.date.past());

  // randomly generate a started at time after the plugged in time
  const startedAt = pluggedIn.add(faker.number.int({ max: 30 }), 'seconds');

  // randomly generate an ended at time after the started at time
  const endedAt = startedAt.add(faker.number.int({ max: 10 }), 'hours');

  // calculate the total duration
  const totalDuration = endedAt.diff(pluggedIn, 'seconds');

  // randomly calculate the charging duration
  const chargingDuration = faker.number.int({
    min: totalDuration * 0.9,
    max: totalDuration,
  });

  // calculate the energy usage
  const energyUsage = (chargingDuration / 3600) * 7.2;

  // calculate the CO2 savings
  const co2Savings = energyUsage * 4 * 0.14;

  // calculate the energy cost
  const energyCost = energyUsage * (site.energyCost ?? 0.14);

  // calculate the revenue generated
  const revenueGenerated = energyUsage * pricePerKwh;

  // build the charge object
  const charge: Charge = {
    chargingDuration: chargingDuration.toString(),
    confirmed: confirmed,
    co2Savings: co2Savings.toFixed(2).toString(),
    door:
      pod.sockets.length ?? 0 > 1
        ? faker.helpers.arrayElement(['A', 'B'])
        : 'A',
    endedAt: formatDateStringToTimestamp(endedAt.toISOString()),
    energyCost: energyCost.toFixed(2),
    energyUsage: energyUsage.toFixed(2),
    pluggedIn: formatDateStringToTimestamp(pluggedIn.toISOString()),
    podName: pod.name,
    revenueGenerated: revenueGenerated.toFixed(2),
    siteName: site.address.name,
    startedAt: formatDateStringToTimestamp(startedAt.toISOString()),
    totalDuration: totalDuration.toString(),
    userName: driver ? driver.fullName : '-',
    userEmail: driver ? driver.email : '-',
    vehicle: faker.helpers.maybe(() => faker.vehicle.vehicle(), {
      probability: 0.75,
    }),
  };

  // add the charge to the recent charges of the pod
  pod.recentCharges = [...(pod.recentCharges ?? []), charge]
    .sort(sortByEndedAt)
    .slice(0, 10);

  // add the charge to the charges of the driver and update their stats
  if (driver) {
    const driverStats = driver.chargingStats as DriverCharges;
    driverStats.charges.push(charge);
    driverStats.co2Avoided = (
      parseFloat(driverStats.co2Avoided) + co2Savings
    ).toFixed(2);
    driverStats.energyCost = (
      parseFloat(driverStats.energyCost) + energyCost
    ).toFixed(2);
    driverStats.energyDelivered = (
      parseFloat(driverStats.energyDelivered) + energyUsage
    ).toFixed(2);
    driverStats.revenue = (
      parseFloat(driverStats.revenue) + revenueGenerated
    ).toFixed(2);
  }

  if (endedAt.isAfter(dayjs().startOf('month'))) {
    // update the monthly stats of the pod
    const podStats = pod.chargeSummary as ChargeSummary;
    podStats.co2Savings += parseFloat(charge.co2Savings);
    podStats.energyCost += parseFloat(charge.energyCost);
    podStats.energyUsage += parseFloat(charge.energyUsage);
    podStats.energyDelivered += parseFloat(charge.energyUsage);
    podStats.revenueGenerated += parseFloat(charge.revenueGenerated) * 100;
    podStats.numberOfCharges += 1;

    // update the monthly stats of the site
    const siteStats = site.chargeSummary as ChargeSummary;
    siteStats.co2Savings += parseFloat(charge.co2Savings);
    siteStats.energyCost += parseFloat(charge.energyCost);
    siteStats.energyDelivered += parseFloat(charge.energyUsage);
    siteStats.revenueGenerated += parseFloat(charge.revenueGenerated) * 100;
    siteStats.numberOfCharges += 1;

    // update the monthly stats of the group
    const groupStats = site.group.stats as GroupChargeSummary;
    groupStats.chargingDuration += parseInt(charge.chargingDuration);
    groupStats.co2Savings += parseFloat(charge.co2Savings);
    groupStats.energyCost += parseFloat(charge.energyCost);
    groupStats.energyDelivered += parseFloat(charge.energyUsage);
    groupStats.numberOfCharges += 1;
    groupStats.numberOfDrivers +=
      confirmed && !driver
        ? faker.helpers.maybe(() => 1, { probability: 0.75 }) ?? 0
        : 0;
    groupStats.revenueGenerated += parseFloat(charge.revenueGenerated) * 100;
  }

  // return the charge
  return charge;
};

const fakeCharges = (
  sites: Site[],
  pods: Pod[],
  drivers: Driver[],
  tariffs: Tariff[],
  count: number
): Charge[] =>
  [...new Array(count).keys()]
    .map(() => fakeCharge(sites, pods, drivers, tariffs))
    .sort((a, b) => sortBySiteNamePodNamePluggedIn(a, b));

const fakeDomain = (id: number, group: Group): Domain => ({
  activatedOn: faker.date.past(),
  domainName: faker.internet.domainName(),
  id: id,
  groupId: group.id,
});

const fakeDomains = (count: number, group: Group): Domain[] =>
  [...new Array(count).keys()].map((i) => fakeDomain(i + 1, group));

const fakeDriver = (id: number): Driver => ({
  canExpense: faker.datatype.boolean(),
  chargingStats: {
    charges: [],
    co2Avoided: '0',
    energyCost: '0',
    energyDelivered: '0',
    revenue: '0',
  },
  email: faker.internet.email(),
  emailBounced: faker.datatype.boolean(),
  firstName: faker.person.firstName(),
  fullName: faker.person.fullName(),
  id: id,
  lastName: faker.person.lastName(),
  registeredDate: faker.date.past().toISOString(),
  status:
    faker.helpers.maybe(
      () =>
        faker.helpers.arrayElement([
          DriverStatus.DEACTIVATED,
          DriverStatus.DELETED,
          DriverStatus.PENDING,
        ]),
      { probability: 0.3 }
    ) ?? DriverStatus.REGISTERED,
  tariffTier:
    faker.helpers.maybe(() => DriverTariffTier.DRIVER, { probability: 0.7 }) ??
    DriverTariffTier.MEMBER,
});

const fakeDrivers = (count: number): Driver[] => {
  const drivers = [...new Array(count).keys()].map((i) => fakeDriver(i + 1));
  return sortDrivers(drivers);
};

const fakeGroup = (id: number): Group => ({
  id: id,
  name: 'Example Company',
  stats: {
    chargingDuration: 0,
    co2Savings: 0,
    energyCost: 0,
    energyDelivered: 0,
    numberOfChargers: 0,
    numberOfCharges: 0,
    numberOfDrivers: 0,
    numberOfSites: 0,
    revenueGenerated: 0,
  },
  type: GroupType.HOST,
  uid: faker.string.uuid(),
});

const fakeInsight = (subtractMonths: number): Insight => ({
  co2Savings: faker.number.int({ max: 100000 }),
  cost: faker.number.int({ max: 100000 }),
  intervalStartDate: dayjs()
    .startOf('month')
    .subtract(subtractMonths, 'month')
    .format('YYYY-MM-DD'),
  revenueGenerated: faker.number.int({ max: 100000 }),
  totalUsage: faker.number.int({ max: 100000 }),
});

const fakePod = (id: number): Pod => {
  const isPublic = faker.datatype.boolean();
  const isTwin = faker.datatype.boolean();

  const coordinates = faker.location.nearbyGPSCoordinate({
    isMetric: true,
    origin: [51.507351, -0.127758],
    radius: 10,
  });

  return {
    chargeSummary: {
      chargingDuration: 0,
      claimedEnergyUsage: 0,
      co2Savings: 0,
      energyCost: 0,
      energyDelivered: 0,
      energyUsage: 0,
      numberOfCharges: 0,
      revenueGenerated: 0,
      revenueGeneratingClaimedUsage: 0,
    },
    confirmChargeEnabled: true,
    coordinates: {
      latitude: coordinates[0],
      longitude: coordinates[1],
    },
    description: faker.word.words(10),
    id: id,
    isEvZone: false,
    isPublic: isPublic,
    lastContact: faker.date.past().toISOString(),
    model: isTwin ? 'T7-S-07-AMC-BLK' : 'S7-UP-05-AMB-UK-0001',
    name: faker.helpers.arrayElements(fakeFourLetterNames, 2).join('-'),
    ppid: `PG-${faker.number.int({ min: 100000, max: 500000 })}`,
    schemes: [],
    sockets: [
      {
        door: 'A',
        firmwareVersion: 'A50P-1.19.7',
        isUpdateAvailable: false,
        lastContact: dayjs().toISOString(),
        serialNumber: faker.number
          .int({ min: 1000000000, max: 9999999999 })
          .toString(),
        status: fakeSocketStatus(),
      },
      {
        door: 'B',
        firmwareVersion: 'A50P-1.19.7',
        isUpdateAvailable: false,
        lastContact: dayjs().toISOString(),
        serialNumber: faker.number
          .int({ min: 1000000000, max: 9999999999 })
          .toString(),
        status: fakeSocketStatus(),
      },
    ].filter((socket) => isTwin || socket.door === 'A'),
    status: fakeSocketStatus(),
    supportsContactless: true,
    supportsEnergyTariff: true,
    supportsPerKwh: true,
    supportsTariffs: true,
    tariff: isPublic
      ? { id: 1, name: 'Public tariff' }
      : { id: 2, name: 'Private tariff' },
  };
};

const fakePods = (site: Site): Pod[] =>
  [...new Array(faker.number.int({ min: 2, max: 8 })).keys()].map((j) =>
    fakePod(site.id * 10 + j)
  );

const fakeSite = (id: number, group: Group): Site => ({
  address: {
    country: 'GB',
    line1: faker.location.streetAddress(),
    line2: '',
    name: faker.location.street(),
    postcode: faker.location.zipCode(),
    prettyPrint: faker.location.streetAddress({ useFullAddress: true }),
    town: faker.location.city(),
  },
  chargeSummary: {
    chargingDuration: 0,
    claimedEnergyUsage: 0,
    co2Savings: 0,
    energyCost: 0,
    energyDelivered: 0,
    energyUsage: 0,
    numberOfCharges: 0,
    revenueGenerated: 0,
    revenueGeneratingClaimedUsage: 0,
  },
  contactDetails: {
    email: faker.internet.email(),
    name: faker.person.fullName(),
    telephone: faker.phone.number(),
  },
  description: faker.word.words(10),
  energyCost: 25,
  group: group,
  id: id,
  parking: { openingTimes: { notes: [faker.lorem.words(12)] } },
});

const fakeSites = (count: number, group: Group): Site[] => {
  const groupStats = group.stats as GroupChargeSummary;
  groupStats.numberOfSites = count;
  return [...new Array(count).keys()].map((i) => {
    const site = fakeSite(i + 1, group);
    const pods = fakePods(site);
    return { ...site, pods };
  });
};

const fakeSiteStats = (site: Site): SiteStats => ({
  siteName: site.address.name,
  co2AvoidedKg: faker.number.int({ max: 50000 }),
  energyCost: faker.number.int({ max: 50000 }),
  energyUsageKwh: faker.number.int({ max: 50000 }),
  numberOfCharges: faker.number.int({ max: 50000 }),
  numberOfDrivers: faker.number.int({ max: 50000 }),
  revenueGenerated: faker.number.int({ max: 50000 }),
  totalDuration: faker.number.int({ max: 50000 }),
});

const fakeUser = (id: number): User => ({
  authId: faker.string.uuid(),
  email: faker.internet.email(),
  emailVerified: true,
  firstName: faker.person.firstName(),
  id: id,
  lastName: faker.person.lastName(),
  termsAndConditions: {
    acceptedVersion: '2025-03-31',
    currentVersion: '2025-03-31',
  },
  status: UserStatus.ACTIVATED,
});

const fakeSocketStatus = (): SocketStatus =>
  faker.helpers.maybe(() => SocketStatus.Unavailable, { probability: 0.05 }) ??
  faker.helpers.maybe(() => SocketStatus.Offline, { probability: 0.05 }) ??
  faker.helpers.maybe(() => SocketStatus.Charging, { probability: 0.5 }) ??
  SocketStatus.Available;

const fakeStatements = () =>
  [...new Array(faker.number.int({ min: 5, max: 8 })).keys()].map(() => ({
    siteName: faker.location.street(),
    month: dayjs(faker.date.past()).format('YYYY-MM-DD'),
    feeInvoiceNumber: `ADF-${faker.number.int({ max: 1000 })}`,
    feeInvoiceStatus: faker.helpers.arrayElement(['open', 'paid', 'overdue']),
    hostedInvoiceUrl: 'http://localhost',
    invoiceId: faker.string.uuid(),
    revenuePayoutStatus: faker.helpers.arrayElement([
      'PAID_OUT',
      'PENDING',
      'DEFERRED',
    ]),
    statementId: faker.string.uuid(),
  }));

const fakeSubscriptionInvoices = () =>
  [...new Array(faker.number.int({ min: 2, max: 8 })).keys()].map(() => ({
    status: faker.helpers.arrayElement(['open', 'paid', 'overdue']),
    created: faker.date.past().toISOString(),
    amount: faker.number.int({ min: 0, max: 1000 }),
    due: faker.date.future().toISOString(),
    hostedInvoiceUrl: 'http://localhost',
    invoiceNumber: `INV-${faker.number.int({ max: 1000 })}`,
    invoicePdfUrl: 'http://localhost',
    customerEmail: faker.internet.email(),
  }));

const fakeSubscriptionChargers = () =>
  [...new Array(faker.number.int({ min: 2, max: 8 })).keys()].map(() => ({
    ppid: `PG-${faker.number.int({ min: 100000, max: 500000 })}`,
    socket: faker.helpers.arrayElement(['A', 'B']),
  }));

const fakeBillingInformation = (): BillingInformation => ({
  customerDetails: {
    address: {
      line1: faker.location.streetAddress(),
      line2: '',
      postcode: faker.location.zipCode(),
      town: faker.location.city(),
    },
    email: faker.internet.email(),
    name: faker.person.fullName(),
    accountReference: faker.finance.accountName(),
    poNumber: faker.finance.accountNumber(),
  },
  onboardingLink: faker.internet.url(),
  statements: fakeStatements(),
  subscription: {
    chargers: fakeSubscriptionChargers(),
    invoices: fakeSubscriptionInvoices(),
    status: faker.helpers.arrayElement([
      'active',
      'incomplete',
      'paid',
      'paused',
    ]),
  },
});

export class DemoData {
  static instance: DemoData;

  static getInstance = (): DemoData => {
    if (!DemoData.instance) {
      DemoData.instance = new DemoData();
    }
    return DemoData.instance;
  };

  admins: Admin[];
  billing: BillingInformation;
  charges: Charge[];
  domains: Domain[];
  drivers: Driver[];
  group: Group;
  insights: Insight[];
  pods: Pod[];
  siteStats: SiteStats[];
  sites: Site[];
  tariffs: Tariff[];
  user: User;

  constructor() {
    this.user = fakeUser(1);

    this.group = fakeGroup(1);

    this.drivers = fakeDrivers(25);

    this.domains = fakeDomains(2, this.group);

    this.sites = fakeSites(6, this.group);

    this.pods = this.sites.flatMap((site) => {
      const { pods, ...siteWithoutPods } = site;
      return site.pods?.map((pod) => ({ ...pod, site: siteWithoutPods })) ?? [];
    });

    this.tariffs = [
      {
        currency: 'GBP',
        pods: this.pods.filter((pod) => pod.isPublic),
        id: 1,
        name: 'Public tariff',
        schedule: {
          public: [
            {
              id: 1,
              bands: [{ after: 0, cost: 60, id: 1, prettyPrint: '60p' }],
              endDay: 0,
              endTime: '00:00:00',
              pricingModel: TariffPricingModel.energy,
              startDay: 0,
              startTime: '00:00:00',
              tariffTier: TariffTier.public,
            },
          ],
          drivers: [
            {
              id: 2,
              bands: [{ after: 0, cost: 50, id: 1, prettyPrint: '50p' }],
              endDay: 0,
              endTime: '00:00:00',
              pricingModel: TariffPricingModel.energy,
              startDay: 0,
              startTime: '00:00:00',
              tariffTier: TariffTier.drivers,
            },
          ],
          members: [
            {
              id: 3,
              bands: [{ after: 0, cost: 0, id: 1, prettyPrint: '0p' }],
              endDay: 0,
              endTime: '00:00:00',
              pricingModel: TariffPricingModel.energy,
              startDay: 0,
              startTime: '00:00:00',
              tariffTier: TariffTier.public,
            },
          ],
        },
      },
      {
        currency: 'GBP',
        pods: this.pods.filter((pod) => !pod.isPublic),
        id: 2,
        name: 'Private tariff',
        schedule: {
          drivers: [
            {
              id: 1,
              bands: [{ after: 0, cost: 50, id: 1, prettyPrint: '50p' }],
              endDay: 0,
              endTime: '00:00:00',
              pricingModel: TariffPricingModel.energy,
              startDay: 0,
              startTime: '00:00:00',
              tariffTier: TariffTier.drivers,
            },
          ],
          members: [
            {
              id: 2,
              bands: [{ after: 0, cost: 0, id: 1, prettyPrint: '0p' }],
              endDay: 0,
              endTime: '00:00:00',
              pricingModel: TariffPricingModel.energy,
              startDay: 0,
              startTime: '00:00:00',
              tariffTier: TariffTier.public,
            },
          ],
        },
      },
    ];

    this.admins = [
      ...fakeAdmins(3),
      {
        activatedOn: faker.date.past(),
        authId: this.user.authId,
        email: this.user.email,
        emailBounced: false,
        firstName: this.user.firstName,
        id: this.user.id,
        lastName: this.user.lastName,
        lastLogin: new Date(),
        status: AdminStatus.REGISTERED,
      },
    ];

    this.charges = fakeCharges(
      this.sites,
      this.pods,
      this.drivers,
      this.tariffs,
      1000
    );

    this.siteStats = this.sites.map((site) => fakeSiteStats(site));

    this.insights = [...new Array(13).keys()].map((i) => fakeInsight(i));

    this.billing = fakeBillingInformation();
  }
}
