import {
  COMMON_CURRENCY_PENCE_ERROR,
  COMMON_PRICE_DECIMAL_PLACES_ERROR,
  COMMON_PRICE_MAX_VALUE_ERROR,
  COMMON_PRICE_MIN_VALUE_ERROR,
  <PERSON><PERSON><PERSON>r<PERSON>hanMin,
  Is<PERSON><PERSON>ThanFourDecimalPlaces,
  IsLessThanMax,
  IsValidCurrencyValueInPence,
} from '@experience/shared/typescript/validation';
import { IsOptional, Validate } from 'class-validator';

export class UpdateEnergyCostRequest {
  @Validate(IsLessThanMax, { message: COMMON_PRICE_MAX_VALUE_ERROR() })
  @Validate(IsGreaterThanMin, {
    message: COMMON_PRICE_MIN_VALUE_ERROR(),
  })
  @Validate(IsLessThanFourDecimalPlaces, {
    message: COMMON_PRICE_DECIMAL_PLACES_ERROR,
  })
  @Validate(IsValidCurrencyValueInPence, {
    message: COMMON_CURRENCY_PENCE_ERROR,
  })
  @IsOptional()
  energyCost: string;
}

export default UpdateEnergyCostRequest;
