import {
  Body,
  Controller,
  Get,
  HttpCode,
  Param,
  ParseUUIDPipe,
  Post,
  Put,
  Query,
  SerializeOptions,
  UseInterceptors,
  ValidationPipe,
} from '@nestjs/common';
import {
  CreateRfidCardRequest,
  RfidCard,
  UpdateRfidCardRequest,
} from '@experience/commercial/site-admin/domain/rfid';
import { RfidCardService } from './rfid-card.service';
import { RfidInterceptor } from './rfid.interceptor';

@Controller('rfid')
@UseInterceptors(RfidInterceptor)
@SerializeOptions({ exposeUnsetFields: false })
export class RfidCardController {
  constructor(private service: RfidCardService) {}

  @Get('cards')
  async findByGroupId(
    @Query('groupUid', ParseUUIDPipe) groupUid: string
  ): Promise<RfidCard[]> {
    return this.service.findByGroupUid(groupUid);
  }

  @Post('cards')
  async createByGroupId(
    @Query('groupUid', ParseUUIDPipe) groupUid: string,
    @Query('adminUid', ParseUUIDPipe) adminUid: string,
    @Body(ValidationPipe) request: CreateRfidCardRequest
  ): Promise<void> {
    await this.service.createByGroupUid(groupUid, adminUid, request);
  }

  @HttpCode(204)
  @Put('cards/:cardUid')
  async updateByGroupId(
    @Query('groupUid', ParseUUIDPipe) groupUid: string,
    @Query('adminUid', ParseUUIDPipe) adminUid: string,
    @Param('cardUid') cardUid: string,
    @Body(ValidationPipe) request: UpdateRfidCardRequest
  ): Promise<void> {
    await this.service.updateByGroupUidAndCardUid(
      groupUid,
      adminUid,
      cardUid,
      request
    );
  }
}
