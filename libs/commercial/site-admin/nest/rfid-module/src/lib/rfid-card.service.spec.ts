import * as MockDate from 'mockdate';
import { Authorisers } from '@experience/shared/sequelize/podadmin';
import { ConfigService } from '@nestjs/config';
import { CreateCardPayload, RfidCardService } from './rfid-card.service';
import { PrismaHealthIndicator } from '@nestjs/terminus';
import {
  RfidCardNotFoundException,
  RfidUserNotFoundException,
} from './rfid.exception';
import {
  RfidPrismaClient,
  TEST_RFID_CARD_ENTITY,
} from '@experience/commercial/site-admin/prisma/rfid/client';
import {
  TEST_ADMINISTRATOR,
  TEST_GROUP,
} from '@experience/commercial/site-admin/typescript/domain-model';
import {
  TEST_CREATE_RFID_CARD_REQUEST,
  TEST_RFID_CARD,
  TEST_UPDATE_RFID_CARD_REQUEST,
} from '@experience/commercial/site-admin/domain/rfid';
import { TEST_RFID_TAG_ENTITY } from '@experience/commercial/site-admin/prisma/rfid/client';
import { Test, TestingModule } from '@nestjs/testing';

jest.mock('uuid', () => ({ v4: () => '6ab494da-3479-47d1-bc5d-86c6fe66c2a5' }));
const mockDate = new Date(2023, 10, 17, 13, 12, 23, 0);

const TEST_CREATE_RFID_CARD: CreateCardPayload = {
  data: {
    card_companies: {
      create: {
        company_uid: TEST_GROUP.uid,
        created_at: mockDate,
        created_by: TEST_ADMINISTRATOR.id,
      },
    },
    card_statuses: {
      create: {
        created_at: mockDate,
        created_by: TEST_ADMINISTRATOR.id,
        is_active: 1,
      },
    },
    card_tags: {
      create: {
        created_at: mockDate,
        created_by: TEST_ADMINISTRATOR.id,
        tag_id: TEST_RFID_TAG_ENTITY.id,
      },
    },
    created_at: mockDate,
    created_by: TEST_ADMINISTRATOR.id,
    reference: TEST_CREATE_RFID_CARD_REQUEST.reference,
    uid: TEST_CREATE_RFID_CARD_REQUEST.uid,
    updated_by: TEST_ADMINISTRATOR.id,
    updated_at: mockDate,
  },
};

describe('RfidCardService', () => {
  let service: RfidCardService;
  let prismaClient: RfidPrismaClient;
  let authorisersRepository: typeof Authorisers;

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        ConfigService,
        PrismaHealthIndicator,
        RfidCardService,
        RfidPrismaClient,
        { provide: 'AUTHORISERS_REPOSITORY', useValue: Authorisers },
      ],
    }).compile();

    service = module.get<RfidCardService>(RfidCardService);
    prismaClient = module.get<RfidPrismaClient>(RfidPrismaClient);
    authorisersRepository = module.get<typeof Authorisers>(
      'AUTHORISERS_REPOSITORY'
    );

    MockDate.set(mockDate);
  });

  afterEach(() => {
    MockDate.reset();
  });

  it('should be defined', () => {
    expect(service).toBeDefined();
    expect(authorisersRepository).toBeDefined();
  });

  describe('findByGroupUid', () => {
    it('should find by group uid (active)', async () => {
      const mockFindAllCards = (prismaClient.cards.findMany = jest
        .fn()
        .mockReturnValueOnce([TEST_RFID_CARD_ENTITY]));
      const mockFindAuthorisers = jest
        .spyOn(authorisersRepository, 'findAll')
        .mockResolvedValueOnce([{ uid: TEST_RFID_CARD.uid } as Authorisers]);

      const rfidCards = await service.findByGroupUid(TEST_GROUP.uid);

      expect(mockFindAllCards).toHaveBeenCalledWith({
        include: {
          card_companies: true,
          card_tags: {
            include: {
              tags: true,
            },
          },
        },
        where: {
          card_companies: {
            some: { company_uid: TEST_GROUP.uid, deleted_at: null },
          },
          deleted_at: null,
        },
      });
      expect(mockFindAuthorisers).toHaveBeenCalledWith({
        where: {
          type: 'rfid',
          uid: [TEST_RFID_CARD.uid],
        },
      });
      expect(rfidCards).toEqual([TEST_RFID_CARD]);
    });

    it('should find by group uid (inactive)', async () => {
      const mockFindAllCards = (prismaClient.cards.findMany = jest
        .fn()
        .mockReturnValueOnce([TEST_RFID_CARD_ENTITY]));
      const mockFindAuthorisers = jest
        .spyOn(authorisersRepository, 'findAll')
        .mockResolvedValueOnce([
          { uid: TEST_RFID_CARD.uid, deletedAt: new Date() } as Authorisers,
        ]);

      const rfidCards = await service.findByGroupUid(TEST_GROUP.uid);

      expect(mockFindAllCards).toHaveBeenCalledWith({
        include: {
          card_companies: true,
          card_tags: {
            include: {
              tags: true,
            },
          },
        },
        where: {
          card_companies: {
            some: { company_uid: TEST_GROUP.uid, deleted_at: null },
          },
          deleted_at: null,
        },
      });
      expect(mockFindAuthorisers).toHaveBeenCalledWith({
        where: {
          type: 'rfid',
          uid: [TEST_RFID_CARD.uid],
        },
      });
      expect(rfidCards).toEqual([{ ...TEST_RFID_CARD, active: false }]);
    });
  });

  it('should filter out deleted card tags', async () => {
    const mockFindAllCards = (prismaClient.cards.findMany = jest
      .fn()
      .mockReturnValueOnce([
        {
          ...TEST_RFID_CARD_ENTITY,
          card_tags: [
            {
              ...TEST_RFID_CARD_ENTITY.card_tags[0],
              deleted_at: new Date(),
            },
          ],
        },
      ]));
    const mockFindAuthorisers = jest
      .spyOn(authorisersRepository, 'findAll')
      .mockResolvedValueOnce([{ uid: TEST_RFID_CARD.uid } as Authorisers]);

    const rfidCards = await service.findByGroupUid(TEST_GROUP.uid);

    expect(mockFindAllCards).toHaveBeenCalledWith({
      include: {
        card_companies: true,
        card_tags: {
          include: {
            tags: true,
          },
        },
      },
      where: {
        card_companies: {
          some: { company_uid: TEST_GROUP.uid, deleted_at: null },
        },
        deleted_at: null,
      },
    });
    expect(mockFindAuthorisers).toHaveBeenCalledWith({
      where: {
        type: 'rfid',
        uid: [TEST_RFID_CARD.uid],
      },
    });
    expect(rfidCards).toEqual([{ ...TEST_RFID_CARD, tags: [] }]);
  });

  describe('createByGroupUid', () => {
    it('should create by group uid with an existing tag', async () => {
      const mockCreateCard = (prismaClient.cards.create = jest
        .fn()
        .mockReturnValueOnce(TEST_RFID_CARD_ENTITY));

      const mockFindUser = (prismaClient.users.findFirst = jest
        .fn()
        .mockReturnValueOnce(TEST_ADMINISTRATOR));

      const mockFindTag = (prismaClient.tags.findFirst = jest
        .fn()
        .mockReturnValueOnce(TEST_RFID_TAG_ENTITY));

      const mockCreateAuthorisersRepository = jest
        .spyOn(authorisersRepository, 'create')
        .mockResolvedValueOnce(undefined);

      await service.createByGroupUid(
        TEST_GROUP.uid,
        TEST_ADMINISTRATOR.authId,
        TEST_CREATE_RFID_CARD_REQUEST
      );

      expect(mockFindUser).toHaveBeenCalledWith({
        where: { uid: TEST_ADMINISTRATOR.authId },
      });

      expect(mockFindTag).toHaveBeenCalledWith({
        where: {
          company_uid: TEST_GROUP.uid,
          name: TEST_CREATE_RFID_CARD_REQUEST.tag,
        },
      });

      expect(mockCreateCard).toHaveBeenCalledWith(TEST_CREATE_RFID_CARD);

      expect(mockCreateAuthorisersRepository).toHaveBeenCalledWith({
        groupUid: TEST_GROUP.uid,
        type: 'rfid',
        uid: TEST_RFID_CARD_ENTITY.uid,
      });
    });

    it('should create by group uid with an new tag', async () => {
      const mockCreateCard = (prismaClient.cards.create = jest
        .fn()
        .mockReturnValueOnce(TEST_RFID_CARD_ENTITY));

      const mockFindUser = (prismaClient.users.findFirst = jest
        .fn()
        .mockReturnValueOnce(TEST_ADMINISTRATOR));

      const mockFindTag = (prismaClient.tags.findFirst = jest
        .fn()
        .mockReturnValueOnce(null));

      const mockFindOrCreateTag = (prismaClient.tags.create = jest
        .fn()
        .mockReturnValueOnce(TEST_RFID_TAG_ENTITY));

      const mockCreateAuthorisersRepository = jest
        .spyOn(authorisersRepository, 'create')
        .mockResolvedValueOnce(undefined);

      await service.createByGroupUid(
        TEST_GROUP.uid,
        TEST_ADMINISTRATOR.authId,
        TEST_CREATE_RFID_CARD_REQUEST
      );

      expect(mockFindUser).toHaveBeenCalledWith({
        where: { uid: TEST_ADMINISTRATOR.authId },
      });

      expect(mockFindTag).toHaveBeenCalledWith({
        where: {
          company_uid: TEST_GROUP.uid,
          name: TEST_CREATE_RFID_CARD_REQUEST.tag,
        },
      });

      expect(mockFindOrCreateTag).toHaveBeenCalledWith({
        data: {
          created_at: new Date(),
          company_uid: TEST_GROUP.uid,
          created_by: TEST_ADMINISTRATOR.id,
          name: TEST_CREATE_RFID_CARD_REQUEST.tag,
          uid: '6ab494da-3479-47d1-bc5d-86c6fe66c2a5',
          updated_at: mockDate,
          updated_by: TEST_ADMINISTRATOR.id,
        },
      });

      expect(mockCreateCard).toHaveBeenCalledWith(TEST_CREATE_RFID_CARD);

      expect(mockCreateAuthorisersRepository).toHaveBeenCalledWith({
        groupUid: TEST_GROUP.uid,
        type: 'rfid',
        uid: TEST_RFID_CARD_ENTITY.uid,
      });
    });

    it('should throw a RfidUserNotFoundException if the user does not exist', async () => {
      prismaClient.users.findFirst = jest.fn().mockReturnValueOnce(null);

      await expect(
        service.createByGroupUid(
          TEST_GROUP.uid,
          TEST_ADMINISTRATOR.authId,
          TEST_CREATE_RFID_CARD_REQUEST
        )
      ).rejects.toThrow(RfidUserNotFoundException);
    });
  });

  describe('updateByGroupUidAndCardUid', () => {
    it('should update by group uid and card uid', async () => {
      const mockFindUser = (prismaClient.users.findFirst = jest
        .fn()
        .mockReturnValueOnce(TEST_ADMINISTRATOR));
      const mockFindCard = (prismaClient.cards.findFirst = jest
        .fn()
        .mockReturnValueOnce(TEST_RFID_CARD_ENTITY));
      const mockUpdateManyCardTags = (prismaClient.card_tags.updateMany = jest
        .fn()
        .mockReturnValueOnce({
          where: {
            card_id: TEST_RFID_CARD_ENTITY.id,
          },
          data: {
            deleted_at: mockDate,
            deleted_by: TEST_ADMINISTRATOR.id,
          },
        }));
      const mockFindTag = (prismaClient.tags.findFirst = jest
        .fn()
        .mockReturnValueOnce(null));
      const mockFindOrCreateTag = (prismaClient.tags.create = jest
        .fn()
        .mockReturnValueOnce(TEST_RFID_TAG_ENTITY));
      const mockCreateCardTags = (prismaClient.card_tags.create = jest
        .fn()
        .mockReturnValueOnce({
          data: {
            created_at: mockDate,
            created_by: TEST_ADMINISTRATOR.id,
            tag_id: TEST_RFID_TAG_ENTITY.id,
            card_id: TEST_RFID_CARD_ENTITY.id,
          },
        }));

      await service.updateByGroupUidAndCardUid(
        TEST_GROUP.uid,
        TEST_ADMINISTRATOR.authId,
        TEST_RFID_CARD_ENTITY.uid,
        TEST_UPDATE_RFID_CARD_REQUEST
      );

      expect(mockFindUser).toHaveBeenCalledWith({
        where: { uid: TEST_ADMINISTRATOR.authId },
      });

      expect(mockFindCard).toHaveBeenCalledWith({
        where: { uid: TEST_RFID_CARD_ENTITY.uid },
      });

      expect(mockUpdateManyCardTags).toHaveBeenCalledWith({
        where: {
          card_id: TEST_RFID_CARD_ENTITY.id,
        },
        data: {
          deleted_at: mockDate,
          deleted_by: TEST_ADMINISTRATOR.id,
        },
      });

      expect(mockFindTag).toHaveBeenCalledWith({
        where: {
          company_uid: TEST_GROUP.uid,
          name: TEST_UPDATE_RFID_CARD_REQUEST.tag,
        },
      });

      expect(mockFindOrCreateTag).toHaveBeenCalledWith({
        data: {
          created_at: new Date(),
          company_uid: TEST_GROUP.uid,
          created_by: TEST_ADMINISTRATOR.id,
          name: TEST_UPDATE_RFID_CARD_REQUEST.tag,
          uid: '6ab494da-3479-47d1-bc5d-86c6fe66c2a5',
          updated_at: mockDate,
          updated_by: TEST_ADMINISTRATOR.id,
        },
      });

      expect(mockCreateCardTags).toHaveBeenCalledWith({
        data: {
          created_at: mockDate,
          created_by: TEST_ADMINISTRATOR.id,
          tag_id: TEST_RFID_TAG_ENTITY.id,
          card_id: TEST_RFID_CARD_ENTITY.id,
        },
      });
    });

    it('should update by group uid and card uid with pre-existing tag', async () => {
      const mockFindUser = (prismaClient.users.findFirst = jest
        .fn()
        .mockReturnValueOnce(TEST_ADMINISTRATOR));

      const mockFindCard = (prismaClient.cards.findFirst = jest
        .fn()
        .mockReturnValueOnce(TEST_RFID_CARD_ENTITY));

      const mockUpdateManyCardTags = (prismaClient.card_tags.updateMany = jest
        .fn()
        .mockReturnValueOnce({
          where: {
            card_id: TEST_RFID_CARD_ENTITY.id,
          },
          data: {
            deleted_at: mockDate,
            deleted_by: TEST_ADMINISTRATOR.id,
          },
        }));

      const mockFindTag = (prismaClient.tags.findFirst = jest
        .fn()
        .mockReturnValueOnce(TEST_RFID_TAG_ENTITY));

      const mockCreateCardTags = (prismaClient.card_tags.create = jest
        .fn()
        .mockReturnValueOnce({
          data: {
            created_at: mockDate,
            created_by: TEST_ADMINISTRATOR.id,
            tag_id: TEST_RFID_TAG_ENTITY.id,
            card_id: TEST_RFID_CARD_ENTITY.id,
          },
        }));

      await service.updateByGroupUidAndCardUid(
        TEST_GROUP.uid,
        TEST_ADMINISTRATOR.authId,
        TEST_RFID_CARD_ENTITY.uid,
        TEST_UPDATE_RFID_CARD_REQUEST
      );

      expect(mockFindUser).toHaveBeenCalledWith({
        where: { uid: TEST_ADMINISTRATOR.authId },
      });

      expect(mockFindCard).toHaveBeenCalledWith({
        where: { uid: TEST_RFID_CARD_ENTITY.uid },
      });

      expect(mockUpdateManyCardTags).toHaveBeenCalledWith({
        where: {
          card_id: TEST_RFID_CARD_ENTITY.id,
        },
        data: {
          deleted_at: mockDate,
          deleted_by: TEST_ADMINISTRATOR.id,
        },
      });

      expect(mockFindTag).toHaveBeenCalledWith({
        where: {
          company_uid: TEST_GROUP.uid,
          name: TEST_UPDATE_RFID_CARD_REQUEST.tag,
        },
      });

      expect(mockCreateCardTags).toHaveBeenCalledWith({
        data: {
          created_at: mockDate,
          created_by: TEST_ADMINISTRATOR.id,
          tag_id: TEST_RFID_TAG_ENTITY.id,
          card_id: TEST_RFID_CARD_ENTITY.id,
        },
      });
    });

    it('should throw a RfidUserNotFoundException if the user does not exist', async () => {
      prismaClient.users.findFirst = jest.fn().mockReturnValueOnce(null);

      await expect(
        service.updateByGroupUidAndCardUid(
          TEST_GROUP.uid,
          TEST_ADMINISTRATOR.authId,
          TEST_RFID_CARD_ENTITY.uid,
          TEST_UPDATE_RFID_CARD_REQUEST
        )
      ).rejects.toThrow(RfidUserNotFoundException);
    });

    it('should throw a RfidCardNotFoundException if the card does not exist', async () => {
      prismaClient.users.findFirst = jest
        .fn()
        .mockReturnValueOnce(TEST_ADMINISTRATOR);
      prismaClient.cards.findFirst = jest.fn().mockReturnValueOnce(null);

      await expect(
        service.updateByGroupUidAndCardUid(
          TEST_GROUP.uid,
          TEST_ADMINISTRATOR.authId,
          TEST_RFID_CARD_ENTITY.uid,
          TEST_UPDATE_RFID_CARD_REQUEST
        )
      ).rejects.toThrow(RfidCardNotFoundException);
    });
  });
});
