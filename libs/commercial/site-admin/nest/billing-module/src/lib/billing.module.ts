import {
  AdminMiddleware,
  AdminModule,
} from '@experience/commercial/site-admin/nest/admin-module';
import { BillingController } from './billing.controller';
import { BillingInvoiceController } from './invoice/invoice.controller';
import { BillingInvoiceService } from './invoice/invoice.service';
import { BillingService } from './billing.service';
import { BillingStatementController } from './statement/statement.controller';
import { BillingStatementService } from './statement/statement.service';
import { ConfigModule, ConfigService } from '@nestjs/config';
import { MiddlewareConsumer, Module } from '@nestjs/common';
import {
  DefaultApi as StatementServiceApi,
  Configuration as StatementServiceApiConfiguration,
} from '@experience/commercial/statement-service/axios';
import axios from 'axios';

@Module({
  imports: [AdminModule, ConfigModule],
  controllers: [
    Billing<PERSON>ontroller,
    BillingInvoiceController,
    BillingStatementController,
  ],
  providers: [
    BillingService,
    BillingInvoiceService,
    BillingStatementService,
    {
      inject: [ConfigService],
      provide: StatementServiceApi,
      useFactory: async (configService: ConfigService) => {
        const client = axios.create({
          timeout: configService.get('STATEMENT_SERVICE_API_TIMEOUT', 10000),
        });
        return new StatementServiceApi(
          new StatementServiceApiConfiguration(),
          configService.get('STATEMENT_SERVICE_API_BASE_URL'),
          client
        );
      },
    },
  ],
  exports: [BillingService],
})
export class BillingModule {
  configure(consumer: MiddlewareConsumer) {
    consumer.apply(AdminMiddleware).forRoutes(BillingController);
  }
}
