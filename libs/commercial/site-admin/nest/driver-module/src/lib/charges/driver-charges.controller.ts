import {
  Controller,
  Get,
  Param,
  ParseEnumPipe,
  ParseIntPipe,
  ParseUUIDPipe,
  Query,
  Res,
  SerializeOptions,
  StreamableFile,
  UseInterceptors,
} from '@nestjs/common';
import {
  Driver,
  DriverTariffTier,
  driverChargeCsvColumns,
  generateChargesCsvFileName,
} from '@experience/commercial/site-admin/typescript/domain-model';
import { DriverChargesService } from './driver-charges.service';
import { DriverInterceptor } from '../driver.interceptor';
import { FeatureFlag, generateCsvFile } from '@experience/shared/nest/utils';
import { Response } from 'express';

@Controller('drivers/:driverId/charges')
@UseInterceptors(DriverInterceptor)
@SerializeOptions({ exposeUnsetFields: false })
export class DriverChargesController {
  constructor(private driverChargesService: DriverChargesService) {}

  @Get()
  async findByGroupIdAndDriverId(
    @Query('groupId', ParseIntPipe) groupId: number,
    @Query('groupUid', ParseUUIDPipe) groupUid: string,
    @Query('tariffTier', new ParseEnumPipe(DriverTariffTier))
    tariffTier: DriverTariffTier,
    @Param('driverId', ParseIntPipe) driverId: number,
    @FeatureFlag('useChargeProjectionEndpoints')
    useChargeProjectionEndpoints = false
  ): Promise<Driver> {
    return await this.driverChargesService.findByGroupIdAndDriverId(
      groupId,
      groupUid,
      driverId,
      tariffTier,
      useChargeProjectionEndpoints,
      undefined,
      true
    );
  }

  @Get('/csv')
  async generateCsv(
    @Query('groupId', ParseIntPipe) groupId: number,
    @Query('groupUid', ParseUUIDPipe) groupUid: string,
    @Query('tariffTier', new ParseEnumPipe(DriverTariffTier))
    tariffTier: DriverTariffTier,
    @Param('driverId', ParseIntPipe) driverId: number,
    @Res({ passthrough: true }) res: Response,
    @Query('date') date?: string,
    @Query('all') allValue?: string,
    @FeatureFlag('useChargeProjectionEndpoints')
    useChargeProjectionEndpoints = false
  ): Promise<StreamableFile> {
    const all = allValue === 'true';
    const driver = await this.driverChargesService.findByGroupIdAndDriverId(
      groupId,
      groupUid,
      driverId,
      tariffTier,
      useChargeProjectionEndpoints,
      date,
      all
    );

    const filename = generateChargesCsvFileName(driver.fullName, date, all);

    res.set({
      'Content-Type': 'text/csv; charset=UTF-8',
      'Content-Disposition': `attachment; filename=${filename}`,
    });

    return generateCsvFile(
      driverChargeCsvColumns,
      driver.chargingStats?.charges ?? [],
      {
        time: ['pluggedIn', 'chargingDuration'],
        boolean: ['confirmed'],
      }
    );
  }
}
