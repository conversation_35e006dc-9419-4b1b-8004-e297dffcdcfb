// eslint-disable-next-line @nx/enforce-module-boundaries
import { AdminService } from '@experience/commercial/site-admin/nest/admin-module';
import {
  CreateDriverRequest,
  DeleteDriverRequest,
  Domain,
  Driver,
  DriverCanExpense,
  DriverStatus,
  DriverTariffTier,
  UpdateDriverRequest,
  sortDrivers,
} from '@experience/commercial/site-admin/typescript/domain-model';
import {
  DriverNotFoundException,
  DuplicateDriverException,
} from './driver.exception';
import {
  EvDrivers,
  Groups,
  Members,
  Users,
} from '@experience/shared/sequelize/podadmin';
import { FindOptions, Op } from 'sequelize';
import { Inject, Injectable, Logger, forwardRef } from '@nestjs/common';
import { SimpleEmailService } from '@experience/shared/nest/aws/ses-module';
import { injectParametersIntoTemplateString } from '@experience/shared/typescript/utils';
import { sort } from 'fast-sort';
import dayjs from 'dayjs';
import fs from 'fs';
import path from 'path';

type DriverOptionalStatus = Omit<Driver, 'status'> & {
  status?: DriverStatus;
};

export interface EmailParameters {
  adminName: string;
  emailAddress: string;
  subject: string;
  firstName: string;
  groupName: string;
  year: string;
}

export const includeOptions = [{ model: Groups, as: 'group' }];

@Injectable()
export class DriverService {
  private readonly logger = new Logger(DriverService.name);

  constructor(
    private simpleEmailService: SimpleEmailService,
    @Inject(forwardRef(() => AdminService))
    private adminService: AdminService,
    @Inject('EV_DRIVERS_REPOSITORY')
    private driversRepository: typeof EvDrivers,
    @Inject('MEMBERS_REPOSITORY')
    private membersRepository: typeof Members,
    @Inject('USERS_REPOSITORY')
    private usersRepository: typeof Users
  ) {}

  async findByGroupId(
    groupId: number,
    includeDeleted?: boolean
  ): Promise<Driver[]> {
    this.logger.log({ groupId, includeDeleted }, 'finding drivers');

    return Promise.all([
      this.driversRepository
        .findAll({
          include: includeOptions,
          paranoid: !includeDeleted,
          where: { groupId: groupId },
        })
        .then((drivers) =>
          this.mapDriversArray(drivers, DriverTariffTier.DRIVER)
        ),
      this.membersRepository
        .findAll({
          include: includeOptions,
          paranoid: !includeDeleted,
          where: { groupId: groupId },
        })
        .then((members) =>
          this.mapDriversArray(members, DriverTariffTier.MEMBER)
        ),
    ])
      .then(([drivers, members]) =>
        this.filterDuplicateDrivers(drivers, members)
      )
      .then((drivers) => this.populateDriverStatuses(drivers))
      .then((drivers) => this.populateEmailBouncedStatuses(drivers))
      .then((drivers) => sortDrivers(drivers));
  }

  async findByGroupIdAndDriverId(
    groupId: number,
    driverId: number,
    tariffTier: DriverTariffTier
  ): Promise<Driver> {
    this.logger.log({ groupId, driverId, tariffTier }, 'finding driver');

    const repository =
      tariffTier === DriverTariffTier.DRIVER
        ? this.driversRepository
        : this.membersRepository;

    const driver = await repository
      .findOne({
        include: includeOptions,
        paranoid: false,
        where: { id: driverId, groupId },
      })
      .then((driver) =>
        driver
          ? this.populateDriverStatus(this.mapDriver(driver, tariffTier))
          : undefined
      );

    if (!driver) {
      throw new DriverNotFoundException();
    }
    return driver;
  }

  async findByGroupIdAndDriverEmail(
    groupId: number,
    email: string
  ): Promise<Driver> {
    this.logger.log({ groupId, email }, 'finding driver');
    const driver = await this.getDriverByGroupIdAndEmail(groupId, email);
    if (!driver) {
      throw new DriverNotFoundException();
    }
    return driver;
  }

  async findByEmail(email: string): Promise<Driver[]> {
    return Promise.all([
      this.driversRepository
        .findAll({ where: { email }, include: includeOptions })
        .then((drivers) =>
          this.mapDriversArray(drivers, DriverTariffTier.DRIVER)
        ),
      this.membersRepository
        .findAll({ where: { email }, include: includeOptions })
        .then((members) =>
          this.mapDriversArray(members, DriverTariffTier.MEMBER)
        ),
    ])
      .then(([drivers, members]) =>
        this.populateDriverStatuses([...drivers, ...members])
      )
      .then((drivers) => sortDrivers(drivers));
  }

  async createByGroupId(
    groupId: number,
    request: CreateDriverRequest
  ): Promise<Driver> {
    this.logger.log({ groupId, request }, 'creating driver');
    await this.checkForDuplicateDriversByEmail(groupId, request.email);

    const values = {
      canExpense:
        request.canExpense === true ||
        request.canExpense === DriverCanExpense.YES
          ? 1
          : 0,
      email: request.email,
      firstName: request.firstName,
      groupId,
      lastName: request.lastName,
    };

    const entity =
      request.tariffTier === DriverTariffTier.MEMBER
        ? await this.membersRepository.create(values)
        : await this.driversRepository.create(values);

    return await this.mapDriverWithStatus(entity, request.tariffTier);
  }

  async updateByGroupIdAndDriverId(
    groupId: number,
    driverId: number,
    request: UpdateDriverRequest
  ): Promise<void> {
    this.logger.log({ driverId, groupId, request }, 'updating driver');

    if (
      request.originalTariffTier &&
      request.originalTariffTier !== request.tariffTier
    ) {
      await this.deleteByGroupIdAndDriverId(groupId, driverId, {
        tariffTier: request.originalTariffTier,
      });

      const { originalTariffTier, ...createRequest } = request;
      await this.createByGroupId(groupId, createRequest);
      return;
    }

    const values = {
      canExpense: request.canExpense ? 1 : 0,
      email: request.email,
      firstName: request.firstName,
      lastName: request.lastName,
    };

    const repository =
      request.tariffTier === DriverTariffTier.MEMBER
        ? this.membersRepository
        : this.driversRepository;

    await repository.update(values, {
      where: { id: driverId, groupId },
    });
  }

  async deleteByGroupIdAndDriverId(
    groupId: number,
    driverId: number,
    request: DeleteDriverRequest
  ): Promise<void> {
    this.logger.log({ driverId, groupId, request }, 'deleting driver');
    request.tariffTier === DriverTariffTier.MEMBER
      ? await this.membersRepository.destroy({
          where: { id: driverId, groupId },
        })
      : await this.driversRepository.destroy({
          where: { id: driverId, groupId },
        });
  }

  async inviteByGroupIdAndDriverId(
    groupId: number,
    driverId: number,
    adminId: number,
    tariffTier: DriverTariffTier
  ): Promise<void> {
    this.logger.log(
      { groupId, driverId, adminId, tariffTier },
      'inviting driver'
    );

    const options: FindOptions = {
      include: includeOptions,
      where: { id: driverId, groupId },
    };

    const entity =
      tariffTier === DriverTariffTier.MEMBER
        ? await this.membersRepository.findOne(options)
        : await this.driversRepository.findOne(options);

    if (!entity) {
      throw new DriverNotFoundException();
    }

    const { name: groupName } = entity?.group ?? { name: '' };

    const admin = await this.adminService.findByAdminId(adminId);
    const adminName = `${admin?.firstName ?? ''} ${
      admin?.lastName ?? ''
    }`.trim();

    const driverWithStatus = await this.mapDriverWithStatus(entity, tariffTier);
    const emailParams = this.getEmailParameters(
      driverWithStatus,
      groupName,
      adminName
    );

    await this.sendEmail(emailParams, driverWithStatus.status);
  }

  async findEmailsByGroupId(
    groupId: number,
    includeDeleted?: boolean
  ): Promise<string[]> {
    this.logger.log(
      { groupId },
      'finding driver and member emails by group id'
    );
    return await Promise.all([
      this.driversRepository
        .findAll({
          attributes: ['email'],
          paranoid: !includeDeleted,
          where: { groupId },
        })
        .then((drivers) => drivers.map((driver) => driver.email.toLowerCase())),
      await this.membersRepository
        .findAll({
          attributes: ['email'],
          paranoid: !includeDeleted,
          where: { groupId },
        })
        .then((members) => members.map((member) => member.email.toLowerCase())),
    ]).then(([drivers, members]) => [...drivers, ...members]);
  }

  async createMissing(domain: Domain) {
    this.logger.log({ domain }, 'creating missing drivers');
    const emails = await this.findEmailsByGroupId(domain.groupId, true);
    await this.usersRepository
      .findAll({
        where: {
          [Op.and]: [
            { email: { [Op.like]: `%${domain.domainName}` } },
            { email: { [Op.notIn]: emails } },
          ],
        },
        paranoid: false,
      })
      .then((users) =>
        users.map((user) => ({
          canExpense: 0,
          email: user.email,
          firstName: user.firstName,
          groupId: domain.groupId,
          lastName: user.lastName,
        }))
      )
      .then((records) => this.driversRepository.bulkCreate(records));
  }

  async findByGroupIdKeyedByAuthId(
    groupId: number,
    includeDeleted?: boolean
  ): Promise<Map<string, Driver>> {
    this.logger.log(
      { groupId, includeDeleted },
      'finding drivers keyed by auth id'
    );
    const drivers = await this.findByGroupId(groupId, includeDeleted);
    const users = await this.usersRepository.findAll({
      where: { email: drivers.map((driver) => driver.email) },
      paranoid: false,
    });

    const driverEmailToDriverMap = new Map<string, DriverOptionalStatus>(
      drivers.map((driver) => [driver.email.toLowerCase(), driver])
    );

    return users.reduce((map, { authId, email }) => {
      const userEmail = email.toLowerCase();
      if (driverEmailToDriverMap.has(userEmail)) {
        map.set(authId, driverEmailToDriverMap.get(userEmail));
      }
      return map;
    }, new Map());
  }

  private async checkForDuplicateDriversByEmail(
    groupId: number,
    email: string
  ) {
    this.logger.log({ groupId, email }, 'checking for duplicate drivers');
    const driver = await this.getDriverByGroupIdAndEmail(groupId, email);
    if (driver) {
      this.logger.log({ groupId, email }, 'duplicate drivers found');
      throw new DuplicateDriverException();
    }
  }

  private async getDriverByGroupIdAndEmail(
    groupId: number,
    email: string
  ): Promise<Driver | undefined> {
    const drivers = await Promise.all([
      this.driversRepository
        .findOne({ where: { groupId, email } })
        .then((driver) =>
          driver
            ? this.populateDriverStatus(
                this.mapDriver(driver, DriverTariffTier.DRIVER)
              )
            : null
        ),
      this.membersRepository
        .findOne({ where: { groupId, email } })
        .then((member) =>
          member
            ? this.populateDriverStatus(
                this.mapDriver(member, DriverTariffTier.MEMBER)
              )
            : null
        ),
    ]);

    return drivers.find((driver) => !!driver);
  }

  private mapDriversArray(
    entities: EvDrivers[] | Members[],
    tariffTier: DriverTariffTier
  ): DriverOptionalStatus[] {
    return entities.map((entity) => this.mapDriver(entity, tariffTier));
  }

  private mapDriver(
    entity: EvDrivers | Members,
    tariffTier: DriverTariffTier
  ): DriverOptionalStatus {
    return {
      ...this.map(entity, tariffTier),
      canExpense: !!entity.canExpense,
    };
  }

  private async mapDriverWithStatus(
    entity: EvDrivers | Members,
    tariffTier: DriverTariffTier
  ): Promise<Driver> {
    return await this.populateDriverStatus(this.mapDriver(entity, tariffTier));
  }

  private async populateDriverStatus(
    driver: DriverOptionalStatus
  ): Promise<Driver> {
    const user = await this.usersRepository.findOne({
      where: { email: driver.email },
      paranoid: false,
    });
    return this.populateDriverStatusFromUser(driver, user);
  }

  private async populateDriverStatuses(
    drivers: DriverOptionalStatus[]
  ): Promise<Driver[]> {
    const users = await this.usersRepository.findAll({
      where: { email: drivers.map((driver) => driver.email) },
      paranoid: false,
    });
    return drivers.map((driver) => {
      const user = users.find(
        (user) => user.email.toLowerCase() === driver.email.toLowerCase()
      );
      return this.populateDriverStatusFromUser(driver, user);
    });
  }

  private populateDriverStatusFromUser(
    driver: DriverOptionalStatus,
    user?: Users | null
  ): Driver {
    const driverStatus = this.mapUserStatusToDriverStatus(user, driver.status);
    const driverCanExpense =
      [DriverStatus.PENDING, DriverStatus.REGISTERED].includes(driverStatus) &&
      driver.canExpense;
    return {
      ...driver,
      canExpense: driverCanExpense,
      status: driverStatus,
    };
  }

  private async populateEmailBouncedStatuses(
    drivers: Driver[]
  ): Promise<Driver[]> {
    const suppressedDestinations = await this.simpleEmailService
      .listSuppressedDestinations('BOUNCE')
      .catch(() => []);
    return drivers.map((driver) => {
      const emailBounced = !!suppressedDestinations.find(
        (suppressedDestination) =>
          suppressedDestination.toLowerCase() === driver.email.toLowerCase()
      );
      return { ...driver, emailBounced };
    });
  }

  private mapUserStatusToDriverStatus(
    user?: Users | null,
    driverStatus?: DriverStatus
  ): DriverStatus {
    if (user?.deletedAt) {
      return DriverStatus.DEACTIVATED;
    }
    if (driverStatus === DriverStatus.DELETED) {
      return DriverStatus.DELETED;
    }
    if (user) {
      return DriverStatus.REGISTERED;
    }
    return DriverStatus.PENDING;
  }

  private filterDuplicateDrivers(
    drivers: DriverOptionalStatus[],
    members: DriverOptionalStatus[]
  ): DriverOptionalStatus[] {
    return [
      ...this.deduplicateDeletedDrivers(drivers),
      ...this.deduplicateDeletedDrivers(members),
    ].filter(
      (item, _, combinedDrivers) =>
        item.status !== DriverStatus.DELETED ||
        !combinedDrivers.find(
          (driver) =>
            driver.email.toLowerCase() === item.email.toLowerCase() &&
            driver.status !== DriverStatus.DELETED
        )
    );
  }

  private deduplicateDeletedDrivers(
    drivers: DriverOptionalStatus[]
  ): DriverOptionalStatus[] {
    const seen = new Set<string>();

    return sort(drivers)
      .desc((driver) => driver.registeredDate)
      .filter((driver) => {
        if (driver.status !== DriverStatus.DELETED) {
          return true;
        }

        const duplicate = seen.has(driver.email.toLowerCase());
        seen.add(driver.email.toLowerCase());
        return !duplicate;
      });
  }

  private getEmailParameters(
    driver: Driver,
    groupName: string,
    adminName: string
  ): EmailParameters {
    return {
      adminName,
      emailAddress: driver.email,
      firstName: driver.firstName,
      groupName,
      subject: adminName
        ? `${adminName} invites you to charge your electric vehicle with ${groupName}`
        : `You have been invited to charge your electric vehicle with ${groupName}`,
      year: dayjs().year().toString(),
    };
  }

  private map(
    entity: EvDrivers | Members,
    tariffTier: DriverTariffTier
  ): Omit<DriverOptionalStatus, 'canExpense'> {
    return {
      id: entity.id,
      email: entity.email,
      emailBounced: false,
      firstName: entity.firstName,
      fullName: [entity.firstName, entity.lastName]
        .filter((value) => !!value)
        .join(' '),
      group: entity.group
        ? { id: entity.id, name: entity.group.name, uid: entity.group.uid }
        : undefined,
      lastName: entity.lastName,
      registeredDate: entity.createdAt.toISOString(),
      status: entity.deletedAt ? DriverStatus.DELETED : undefined,
      tariffTier,
    };
  }

  private async sendEmail(
    params: EmailParameters,
    status?: DriverStatus
  ): Promise<void> {
    const { emailAddress, subject } = params;

    const template =
      status === DriverStatus.REGISTERED
        ? 'invitation-network-user'
        : 'invitation-non-network-user';

    const htmlFilepath = path.resolve(
      `./assets/site-admin-api/email-templates/en/${template}.html`
    );
    const htmlTemplateString = fs.readFileSync(htmlFilepath).toString();

    const textFilepath = path.resolve(
      `./assets/site-admin-api/email-templates/en/${template}.txt`
    );
    const plainTextTemplateString = fs.readFileSync(textFilepath).toString();

    await this.simpleEmailService.sendEmail({
      bodyHtml: injectParametersIntoTemplateString(htmlTemplateString, {
        ...params,
      }),
      bodyText: injectParametersIntoTemplateString(plainTextTemplateString, {
        ...params,
      }),
      subject,
      to: emailAddress,
    });
  }
}
