import { GroupService } from '@experience/commercial/site-admin/nest/admin-module';
import { INestApplication } from '@nestjs/common';
import { StatsController } from './stats.controller';
import { StatsService } from './stats.service';
import {
  TEST_GROUP,
  TEST_USER,
} from '@experience/commercial/site-admin/typescript/domain-model';
import { Test, TestingModule } from '@nestjs/testing';
import { useGlobalPipes } from '@experience/shared/nest/utils';
import MockDate from 'mockdate';
import request from 'supertest';

jest.mock('@experience/commercial/site-admin/nest/admin-module');
jest.mock('./stats.service');

describe('GroupController', () => {
  let app: INestApplication;
  let groupService: GroupService;
  let statsService: StatsService;

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      controllers: [StatsController],
      providers: [
        { provide: GroupService, useClass: GroupService },
        { provide: StatsService, useClass: StatsService },
      ],
    }).compile();

    groupService = module.get<GroupService>(GroupService);
    statsService = module.get<StatsService>(StatsService);

    app = module.createNestApplication();
    useGlobalPipes(app);
    await app.init();

    MockDate.set(new Date(2023, 2, 20));
  });

  afterEach(() => {
    MockDate.reset();
  });

  afterAll(async () => {
    await app.close();
  });

  it('should generate a csv file for the current month', async () => {
    jest.spyOn(groupService, 'findByGroupId').mockResolvedValueOnce(TEST_GROUP);
    jest
      .spyOn(statsService, 'findGroupSiteStatisticsByGroupUid')
      .mockResolvedValueOnce([]);

    await request(app.getHttpServer())
      .get(`/sites/stats/csv?groupId=${TEST_USER.groupId}`)
      .expect(200);

    expect(statsService.findGroupSiteStatisticsByGroupUid).toHaveBeenCalledWith(
      TEST_GROUP.uid,
      2023,
      3
    );
  });

  it('should generate a csv file for a specific month', async () => {
    jest.spyOn(groupService, 'findByGroupId').mockResolvedValueOnce(TEST_GROUP);
    jest
      .spyOn(statsService, 'findGroupSiteStatisticsByGroupUid')
      .mockResolvedValueOnce([]);

    await request(app.getHttpServer())
      .get(`/sites/stats/csv?groupId=${TEST_USER.groupId}&date=2023-01-01`)
      .expect(200);

    expect(statsService.findGroupSiteStatisticsByGroupUid).toHaveBeenCalledWith(
      TEST_GROUP.uid,
      2023,
      1
    );
  });
});
