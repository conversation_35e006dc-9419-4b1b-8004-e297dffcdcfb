import {
  Body,
  Controller,
  Get,
  Param,
  ParseIntPipe,
  ParseUUIDPipe,
  Post,
  Query,
  SerializeOptions,
  ValidationPipe,
} from '@nestjs/common';
import {
  CreateSiteStatementRequest,
  SiteStatement,
  StatementPeriod,
} from '@experience/commercial/site-admin/typescript/domain-model';
import { FeatureFlag } from '@experience/shared/nest/utils';
import { StatementService } from './statement.service';

@Controller('sites/:siteId/statement')
@SerializeOptions({ exposeUnsetFields: false })
export class StatementController {
  constructor(private statementService: StatementService) {}

  @Get()
  async findByGroupUidAndSiteId(
    @Query('groupUid', ParseUUIDPipe) groupUid: string,
    @Param('siteId', ParseIntPipe) siteId: number,
    @Query('period') period: StatementPeriod,
    @FeatureFlag('useChargeProjectionEndpoints')
    useChargeProjectionEndpoints = false
  ): Promise<SiteStatement> {
    return this.statementService.findByGroupUidAndSiteId({
      groupUid,
      siteId,
      period,
      useChargeProjectionEndpoints,
    });
  }

  @Post()
  async createByGroupUidAndSiteId(
    @Query('groupUid', ParseUUIDPipe) groupUid: string,
    @Param('siteId', ParseIntPipe) siteId: number,
    @Query('period') period: StatementPeriod,
    @Query('dateOverride') dateOverride: string,
    @Body(new ValidationPipe({ transform: true }))
    request: CreateSiteStatementRequest,
    @FeatureFlag('useChargeProjectionEndpoints')
    useChargeProjectionEndpoints = false
  ): Promise<SiteStatement> {
    return this.statementService.createByGroupUidAndSiteId({
      groupUid,
      siteId,
      period,
      dateOverride,
      request,
      useChargeProjectionEndpoints,
    });
  }
}
