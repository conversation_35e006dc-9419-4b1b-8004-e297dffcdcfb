{"name": "commercial-site-admin-nest-advert-module", "$schema": "../../../../../node_modules/nx/schemas/project-schema.json", "sourceRoot": "libs/commercial/site-admin/nest/advert-module/src", "projectType": "library", "tags": ["commercial", "site-admin"], "targets": {"test": {"executor": "@nx/jest:jest", "outputs": ["{workspaceRoot}/coverage/libs/commercial/site-admin/nest/advert-module"], "options": {"jestConfig": "libs/commercial/site-admin/nest/advert-module/jest.config.ts", "passWithNoTests": false}}}}