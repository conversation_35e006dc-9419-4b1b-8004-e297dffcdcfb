import * as MockDate from 'mockdate';
import { GroupService } from '@experience/commercial/site-admin/nest/admin-module';
import { INestApplication } from '@nestjs/common';
import { InsightsController } from './insights.controller';
import { InsightsService } from './insights.service';
import { PodService } from '@experience/commercial/site-admin/nest/pod-module';
import { SiteService } from '@experience/commercial/site-admin/nest/site-module';
import {
  TEST_GROUP,
  TEST_POD,
  TEST_SITE,
} from '@experience/commercial/site-admin/typescript/domain-model';
import { TEST_USAGE_RESPONSE } from '@experience/shared/axios/data-platform-api-client/fixtures';
import { Test, TestingModule } from '@nestjs/testing';
import { useGlobalPipes } from '@experience/shared/nest/utils';
import request from 'supertest';

jest.mock('./insights.service');
jest.mock('@experience/commercial/site-admin/nest/admin-module');
jest.mock('@experience/commercial/site-admin/nest/pod-module');
jest.mock('@experience/commercial/site-admin/nest/site-module');

describe('InsightsController', () => {
  let app: INestApplication;
  let insightsService: InsightsService;
  let groupService: GroupService;
  let siteService: SiteService;
  let podService: PodService;

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      controllers: [InsightsController],
      providers: [
        { provide: GroupService, useClass: GroupService },
        { provide: InsightsService, useClass: InsightsService },
        { provide: PodService, useClass: PodService },
        { provide: SiteService, useClass: SiteService },
      ],
    }).compile();

    insightsService = module.get<InsightsService>(InsightsService);
    groupService = module.get<GroupService>(GroupService);
    siteService = module.get<SiteService>(SiteService);
    podService = module.get<PodService>(PodService);

    app = module.createNestApplication();
    useGlobalPipes(app);
    await app.init();
    MockDate.set(new Date(2022, 0, 15, 12, 0, 0, 0));
  });

  afterEach(() => {
    MockDate.reset();
  });

  afterAll(async () => {
    await app.close();
  });

  describe.each([true, false])(
    'with projections feature flag %s',
    (useChargeProjectionEndpoints) => {
      const featureFlagsHeader = useChargeProjectionEndpoints
        ? 'useChargeProjectionEndpoints'
        : '';
      const chargerId = useChargeProjectionEndpoints
        ? TEST_POD.ppid
        : TEST_POD.id;

      it('should find by group uid', async () => {
        jest
          .spyOn(insightsService, 'findByGroupUid')
          .mockResolvedValueOnce(TEST_USAGE_RESPONSE.usage);

        await request(app.getHttpServer())
          .get(`/insights?groupId=${TEST_GROUP.id}&groupUid=${TEST_GROUP.uid}`)
          .set('x-feature-flags', featureFlagsHeader)
          .expect(200)
          .expect(TEST_USAGE_RESPONSE.usage);

        expect(insightsService.findByGroupUid).toHaveBeenCalledWith(
          TEST_GROUP.uid,
          NaN,
          useChargeProjectionEndpoints
        );
      });

      it('should find by group uid for a given year', async () => {
        jest
          .spyOn(insightsService, 'findByGroupUid')
          .mockResolvedValueOnce(TEST_USAGE_RESPONSE.usage);

        await request(app.getHttpServer())
          .get(
            `/insights?groupId=${TEST_GROUP.id}&groupUid=${TEST_GROUP.uid}&year=2023`
          )
          .set('x-feature-flags', featureFlagsHeader)
          .expect(200)
          .expect(TEST_USAGE_RESPONSE.usage);

        expect(insightsService.findByGroupUid).toHaveBeenCalledWith(
          TEST_GROUP.uid,
          2023,
          useChargeProjectionEndpoints
        );
      });

      it('should generate a csv by group uid', async () => {
        jest
          .spyOn(insightsService, 'findByGroupUid')
          .mockResolvedValueOnce(TEST_USAGE_RESPONSE.usage);
        jest
          .spyOn(groupService, 'findByGroupId')
          .mockResolvedValueOnce(TEST_GROUP);

        await request(app.getHttpServer())
          .get(
            `/insights/csv?groupId=${TEST_GROUP.id}&groupUid=${TEST_GROUP.uid}`
          )
          .set('x-feature-flags', featureFlagsHeader)
          .expect(200)
          .expect(
            'Content-Disposition',
            `attachment; filename=${encodeURIComponent(
              TEST_GROUP.name
            )}_Jan-01-2021-Jan-15-2022.csv`
          );

        expect(insightsService.findByGroupUid).toHaveBeenCalledWith(
          TEST_GROUP.uid,
          NaN,
          useChargeProjectionEndpoints
        );
      });

      it('should generate a csv by group uid by year', async () => {
        jest
          .spyOn(insightsService, 'findByGroupUid')
          .mockResolvedValueOnce(TEST_USAGE_RESPONSE.usage);
        jest
          .spyOn(groupService, 'findByGroupId')
          .mockResolvedValueOnce(TEST_GROUP);

        await request(app.getHttpServer())
          .get(
            `/insights/csv?year=2023&groupId=${TEST_GROUP.id}&groupUid=${TEST_GROUP.uid}`
          )
          .set('x-feature-flags', featureFlagsHeader)
          .expect(200)
          .expect(
            'Content-Disposition',
            `attachment; filename=${encodeURIComponent(
              TEST_GROUP.name
            )}_2023.csv`
          );

        expect(insightsService.findByGroupUid).toHaveBeenCalledWith(
          TEST_GROUP.uid,
          2023,
          useChargeProjectionEndpoints
        );
      });

      it('should find by group uid and site id', async () => {
        jest
          .spyOn(insightsService, 'findByGroupUidAndSiteId')
          .mockResolvedValueOnce(TEST_USAGE_RESPONSE.usage);

        await request(app.getHttpServer())
          .get(
            `/insights/sites/${TEST_SITE.id}?groupId=${TEST_GROUP.id}&groupUid=${TEST_GROUP.uid}`
          )
          .set('x-feature-flags', featureFlagsHeader)
          .expect(200)
          .expect(TEST_USAGE_RESPONSE.usage);

        expect(insightsService.findByGroupUidAndSiteId).toHaveBeenCalledWith(
          TEST_GROUP.uid,
          TEST_SITE.id,
          NaN,
          useChargeProjectionEndpoints
        );
      });

      it('should find by group uid and site id for a given year', async () => {
        jest
          .spyOn(insightsService, 'findByGroupUidAndSiteId')
          .mockResolvedValueOnce(TEST_USAGE_RESPONSE.usage);

        await request(app.getHttpServer())
          .get(
            `/insights/sites/${TEST_SITE.id}?groupId=${TEST_GROUP.id}&groupUid=${TEST_GROUP.uid}&year=2023`
          )
          .set('x-feature-flags', featureFlagsHeader)
          .expect(200)
          .expect(TEST_USAGE_RESPONSE.usage);

        expect(insightsService.findByGroupUidAndSiteId).toHaveBeenCalledWith(
          TEST_GROUP.uid,
          TEST_SITE.id,
          2023,
          useChargeProjectionEndpoints
        );
      });

      it('should generate a csv by group uid and site id', async () => {
        jest
          .spyOn(insightsService, 'findByGroupUidAndSiteId')
          .mockResolvedValueOnce(TEST_USAGE_RESPONSE.usage);
        jest
          .spyOn(siteService, 'findByGroupUidAndSiteId')
          .mockResolvedValueOnce(TEST_SITE);

        await request(app.getHttpServer())
          .get(
            `/insights/sites/${TEST_SITE.id}/csv?groupId=${TEST_GROUP.id}&groupUid=${TEST_GROUP.uid}`
          )
          .set('x-feature-flags', featureFlagsHeader)
          .expect(200)
          .expect(
            'Content-Disposition',
            `attachment; filename=${encodeURIComponent(
              TEST_SITE.address.name
            )}-${encodeURIComponent(
              TEST_SITE.address.postcode
            )}_Jan-01-2021-Jan-15-2022.csv`
          );

        expect(insightsService.findByGroupUidAndSiteId).toHaveBeenCalledWith(
          TEST_GROUP.uid,
          TEST_SITE.id,
          NaN,
          useChargeProjectionEndpoints
        );
      });

      it('should generate a csv by group uid and site id by year', async () => {
        jest
          .spyOn(insightsService, 'findByGroupUidAndSiteId')
          .mockResolvedValueOnce(TEST_USAGE_RESPONSE.usage);
        jest
          .spyOn(siteService, 'findByGroupUidAndSiteId')
          .mockResolvedValueOnce(TEST_SITE);

        await request(app.getHttpServer())
          .get(
            `/insights/sites/${TEST_SITE.id}/csv?year=2023&groupId=${TEST_GROUP.id}&groupUid=${TEST_GROUP.uid}`
          )
          .set('x-feature-flags', featureFlagsHeader)
          .expect(200)
          .expect(
            'Content-Disposition',
            `attachment; filename=${encodeURIComponent(
              TEST_SITE.address.name
            )}-${encodeURIComponent(TEST_SITE.address.postcode)}_2023.csv`
          );

        expect(insightsService.findByGroupUidAndSiteId).toHaveBeenCalledWith(
          TEST_GROUP.uid,
          TEST_SITE.id,
          2023,
          useChargeProjectionEndpoints
        );
      });

      it('should find by group uid and charger id', async () => {
        jest
          .spyOn(insightsService, 'findByGroupUidAndChargerId')
          .mockResolvedValueOnce(TEST_USAGE_RESPONSE.usage);

        await request(app.getHttpServer())
          .get(
            `/insights/chargers/${chargerId}?groupId=${TEST_GROUP.id}&groupUid=${TEST_GROUP.uid}`
          )
          .set('x-feature-flags', featureFlagsHeader)
          .expect(200)
          .expect(TEST_USAGE_RESPONSE.usage);

        expect(insightsService.findByGroupUidAndChargerId).toHaveBeenCalledWith(
          TEST_GROUP.uid,
          chargerId,
          NaN,
          useChargeProjectionEndpoints
        );
      });

      it('should find by group uid and charger id for a given year', async () => {
        jest
          .spyOn(insightsService, 'findByGroupUidAndChargerId')
          .mockResolvedValueOnce(TEST_USAGE_RESPONSE.usage);

        await request(app.getHttpServer())
          .get(
            `/insights/chargers/${chargerId}?groupId=${TEST_GROUP.id}&groupUid=${TEST_GROUP.uid}&year=2023`
          )
          .set('x-feature-flags', featureFlagsHeader)
          .expect(200)
          .expect(TEST_USAGE_RESPONSE.usage);

        expect(insightsService.findByGroupUidAndChargerId).toHaveBeenCalledWith(
          TEST_GROUP.uid,
          chargerId,
          2023,
          useChargeProjectionEndpoints
        );
      });

      it('should generate a csv by group uid and charger id', async () => {
        jest
          .spyOn(insightsService, 'findByGroupUidAndChargerId')
          .mockResolvedValueOnce(TEST_USAGE_RESPONSE.usage);
        jest
          .spyOn(podService, 'findByGroupIdAndPodId')
          .mockResolvedValueOnce(TEST_POD);

        await request(app.getHttpServer())
          .get(
            `/insights/chargers/${chargerId}/csv?groupId=${TEST_GROUP.id}&groupUid=${TEST_GROUP.uid}`
          )
          .set('x-feature-flags', featureFlagsHeader)
          .expect(200)
          .expect(
            'Content-Disposition',
            `attachment; filename=${TEST_POD.name}_Jan-01-2021-Jan-15-2022.csv`
          );

        expect(insightsService.findByGroupUidAndChargerId).toHaveBeenCalledWith(
          TEST_GROUP.uid,
          chargerId,
          NaN,
          useChargeProjectionEndpoints
        );
      });

      it('should generate a csv by group uid and charger id by year', async () => {
        jest
          .spyOn(insightsService, 'findByGroupUidAndChargerId')
          .mockResolvedValueOnce(TEST_USAGE_RESPONSE.usage);
        jest
          .spyOn(podService, 'findByGroupIdAndPodId')
          .mockResolvedValueOnce(TEST_POD);

        await request(app.getHttpServer())
          .get(
            `/insights/chargers/${chargerId}/csv?year=2022&groupId=${TEST_GROUP.id}&groupUid=${TEST_GROUP.uid}`
          )
          .set('x-feature-flags', featureFlagsHeader)
          .expect(200)
          .expect(
            'Content-Disposition',
            `attachment; filename=${TEST_POD.name}_2022.csv`
          );

        expect(insightsService.findByGroupUidAndChargerId).toHaveBeenCalledWith(
          TEST_GROUP.uid,
          chargerId,
          2022,
          useChargeProjectionEndpoints
        );
      });
    }
  );
});
