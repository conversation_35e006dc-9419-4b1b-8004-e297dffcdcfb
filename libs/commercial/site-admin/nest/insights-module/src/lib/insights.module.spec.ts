import axios from 'axios';

export const describeInsightsModule = (baseUrl: string) => {
  describe('insights module', () => {
    describe('insights controller', () => {
      describe.each([true, false])(
        'with projections feature flag %s',
        (useChargeProjectionEndpoints) => {
          const featureFlagsHeader = useChargeProjectionEndpoints
            ? 'useChargeProjectionEndpoints'
            : '';

          it('should return insight statistics for the current group', async () => {
            const response = await axios.get(
              `${baseUrl}/insights?authId=d54561e3-222e-4e8d-be86-6c77891160ee`,
              { headers: { 'x-feature-flags': featureFlagsHeader } }
            );
            expect(response.status).toEqual(200);
            expect(response.data).toMatchSnapshot();
          });

          it('should generate insights csv for the current group', async () => {
            const response = await axios.get(
              `${baseUrl}/insights/csv?authId=d54561e3-222e-4e8d-be86-6c77891160ee`,
              { headers: { 'x-feature-flags': featureFlagsHeader } }
            );
            expect(response.status).toEqual(200);
            expect(response.data).toContain(
              ',Energy delivered (kWh),CO2 avoided (kg),Revenue (£),Energy cost (£)'
            );
            expect(response.data).toContain('January 2021,400,100,£3.00,£2.00');
            expect(response.data).toContain('February 2021,30,70,£0.60,£0.50');
          });

          it('should return insight statistics for the current group and a given site', async () => {
            const response = await axios.get(
              `${baseUrl}/insights/sites/75710?authId=d54561e3-222e-4e8d-be86-6c77891160ee`,
              { headers: { 'x-feature-flags': featureFlagsHeader } }
            );
            expect(response.status).toEqual(200);
            expect(response.data).toMatchSnapshot();
          });

          it('should generate insights csv for the current group and a given site', async () => {
            const response = await axios.get(
              `${baseUrl}/insights/sites/75710/csv?authId=d54561e3-222e-4e8d-be86-6c77891160ee`,
              { headers: { 'x-feature-flags': featureFlagsHeader } }
            );
            expect(response.status).toEqual(200);
            expect(response.data).toContain(
              ',Energy delivered (kWh),CO2 avoided (kg),Revenue (£),Energy cost (£)'
            );
            expect(response.data).toContain('January 2021,400,100,£3.00,£2.00');
            expect(response.data).toContain('February 2021,30,70,£0.60,£0.50');
          });

          it('should return insight statistics for the current group and a given charger', async () => {
            const response = await axios.get(
              `${baseUrl}/insights/chargers/5189?authId=d54561e3-222e-4e8d-be86-6c77891160ee`,
              { headers: { 'x-feature-flags': featureFlagsHeader } }
            );
            expect(response.status).toEqual(200);
            expect(response.data).toMatchSnapshot();
          });

          it('should generate insights csv for the current group and a given charger', async () => {
            const response = await axios.get(
              `${baseUrl}/insights/chargers/5189/csv?authId=d54561e3-222e-4e8d-be86-6c77891160ee`,
              { headers: { 'x-feature-flags': featureFlagsHeader } }
            );
            expect(response.status).toEqual(200);
            expect(response.data).toContain(
              ',Energy delivered (kWh),CO2 avoided (kg),Revenue (£),Energy cost (£)'
            );
            expect(response.data).toContain('January 2021,400,100,£3.00,£2.00');
            expect(response.data).toContain('February 2021,30,70,£0.60,£0.50');
          });
        }
      );
    });
  });
};
