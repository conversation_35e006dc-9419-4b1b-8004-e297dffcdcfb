import {
  Body,
  Controller,
  Delete,
  Get,
  HttpCode,
  Param,
  ParseIntPipe,
  Post,
  Put,
  Query,
  SerializeOptions,
  UseInterceptors,
  ValidationPipe,
} from '@nestjs/common';
import {
  CreateDomainRequest,
  Domain,
} from '@experience/commercial/site-admin/typescript/domain-model';
import { DomainInterceptor } from './domain.interceptor';
import { DomainService } from './domain.service';

@Controller('domains')
@UseInterceptors(DomainInterceptor)
@SerializeOptions({ exposeUnsetFields: false })
export class DomainController {
  constructor(private domainService: DomainService) {}

  @Get()
  async findByGroupId(
    @Query('groupId', ParseIntPipe) groupId: number
  ): Promise<Domain[]> {
    return this.domainService.findByGroupId(groupId);
  }

  @Post()
  async createByGroupId(
    @Query('groupId', ParseIntPipe) groupId: number,
    @Body(ValidationPipe) request: CreateDomainRequest
  ): Promise<Domain> {
    return this.domainService.createByGroupId(groupId, request);
  }

  @Put(':domainId')
  @HttpCode(204)
  async updateByGroupId(
    @Query('groupId', ParseIntPipe) groupId: number,
    @Param('domainId', ParseIntPipe) domainId: number,
    @Body(ValidationPipe) request: CreateDomainRequest
  ): Promise<void> {
    await this.domainService.updateByGroupIdAndDomainId(
      groupId,
      domainId,
      request
    );
  }

  @Delete(':domainId')
  @HttpCode(204)
  async deleteByGroupId(
    @Query('groupId', ParseIntPipe) groupId: number,
    @Param('domainId', ParseIntPipe) domainId: number
  ): Promise<void> {
    await this.domainService.deleteByGroupIdAndDomainId(groupId, domainId);
  }
}
