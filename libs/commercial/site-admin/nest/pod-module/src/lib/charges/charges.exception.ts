import {
  AUTHORISER_NOT_FOUND_ERROR,
  INSUFFICIENT_FUNDS_ERROR,
  USER_NOT_FOUND_ERROR,
} from '@experience/commercial/site-admin/typescript/domain-model-validation';

export class AuthoriserNotFoundException extends Error {
  constructor() {
    super(AUTHORISER_NOT_FOUND_ERROR);
  }
}

export class InsufficientFundsException extends Error {
  constructor() {
    super(INSUFFICIENT_FUNDS_ERROR);
  }
}

export class UserNotFoundException extends Error {
  constructor() {
    super(USER_NOT_FOUND_ERROR);
  }
}
