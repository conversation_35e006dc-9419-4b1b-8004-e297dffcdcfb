import {
  Body,
  Controller,
  HttpCode,
  Param,
  ParseIntPipe,
  Post,
  Query,
  SerializeOptions,
  UseInterceptors,
  ValidationPipe,
} from '@nestjs/common';
import { ChargesService } from './charges.service';
import { ConfirmChargeInterceptor } from './charges.interceptor';
import { ConfirmChargeRequest } from '@experience/commercial/site-admin/typescript/domain-model';

@Controller('pods')
@SerializeOptions({ exposeUnsetFields: false })
export class PodChargesController {
  constructor(private chargesService: ChargesService) {}

  @Post(':podId/charges')
  @HttpCode(204)
  @UseInterceptors(ConfirmChargeInterceptor)
  async confirmChargeByGroupIdAndPodId(
    @Query('groupId', ParseIntPipe) groupId: number,
    @Query('adminId', ParseIntPipe) adminId: number,
    @Param('podId', ParseIntPipe) podId: number,
    @Body(ValidationPipe) request: ConfirmChargeRequest
  ): Promise<void> {
    await this.chargesService.confirmChargeByGroupIdAndPodId(
      groupId,
      adminId,
      podId,
      request
    );
  }
}
