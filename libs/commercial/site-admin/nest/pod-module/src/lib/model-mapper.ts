import {
  PcbPodUnit,
  PodLocations,
  PodUnitChargeSchedules,
  PodUnitConnector,
  RevenueProfiles,
} from '@experience/shared/sequelize/podadmin';
import {
  Pod,
  Schedule,
  Socket,
  SocketStatus,
  TariffSummary,
} from '@experience/commercial/site-admin/typescript/domain-model';
// eslint-disable-next-line @nx/enforce-module-boundaries
import {
  mapGroupsEntity,
  mapPodAddressesEntity,
} from '@experience/commercial/site-admin/nest/site-module';
import dayjs from 'dayjs';

export const mapPodLocationsEntity = (entity: PodLocations): Pod => {
  if (!entity) {
    return undefined;
  }

  const isPodPointCharger = entity.unit?.model?.vendorId === 1;
  const supportsConfirmCharge = !!entity.unit?.model?.supportsPayg;
  const supportsPerKwh = !!entity.midmeterEnabled;
  const supportsOcpp = !!entity.unit?.model?.supportsOcpp;
  const supportsTariffs =
    supportsConfirmCharge && (isPodPointCharger || !supportsOcpp);

  return {
    ageYears: mapAgeYears(entity),
    confirmChargeEnabled: !!entity.paygEnabled,
    coordinates: {
      latitude: +entity.latitude,
      longitude: +entity.longitude,
    },
    description: entity.description,
    id: entity.id,
    isPublic: !!entity.isPublic,
    isEvZone: !!entity.isEvZone,
    installDate: entity.podLocationUnits?.[0]?.insertedAt?.toISOString(),
    lastContact: entity.unit?.lastContact?.toISOString(),
    model: entity.unit?.model?.name,
    name: entity.unit?.name,
    ppid: entity.unit?.ppid,
    schedules: mapPodUnitChargeScheduleEntityArray(
      entity.unit?.podUnitChargeSchedules
    ),
    schemes: entity.groupLocations
      .map((e) => e.group)
      .filter((e) => e?.typeId === 3)
      .map((e) => mapGroupsEntity(e)),
    site: mapPodAddressesEntity(entity.address),
    sockets: mapPodUnitConnectorEntityArray(
      entity.unit?.podUnitConnectors,
      entity.unit?.pcbPodUnits
    ),
    status: entity.unit?.status?.name || 'Unavailable',
    supportsConfirmCharge,
    supportsContactless: !!entity.contactlessEnabled,
    supportsEnergyTariff: supportsPerKwh && supportsConfirmCharge,
    supportsOcpp,
    supportsPerKwh,
    supportsTariffs,
    tariff: mapRevenueProfile(entity.revenueProfile),
  };
};

export const mapPodUnitChargeScheduleEntity = (
  entity: PodUnitChargeSchedules
): Schedule =>
  entity
    ? {
        startDay: entity.chargeSchedule.startDay,
        startTime: entity.chargeSchedule.startTime,
        endDay: entity.chargeSchedule.endDay,
        endTime: entity.chargeSchedule.endTime,
        isActive: !!entity.chargeSchedule.chargeScheduleStatuses.sort(
          (a, b) => b.id - a.id
        )[0]?.isActive,
      }
    : undefined;

export const mapPodUnitConnectorEntity = (
  podUnitConnector: PodUnitConnector,
  pcbPodUnit: PcbPodUnit
): Socket =>
  podUnitConnector
    ? {
        door: podUnitConnector.connector.door.name,
        firmwareVersion: 'Unknown',
        lastContact: pcbPodUnit?.pcb?.lastContact?.toISOString(),
        serialNumber: pcbPodUnit?.pcb?.serialNumber,
        status: podUnitConnector.status.name as SocketStatus,
        isUpdateAvailable: false,
      }
    : undefined;

export const mapPodLocationsEntityArray = (entities: PodLocations[]): Pod[] =>
  entities ? entities.map(mapPodLocationsEntity) : undefined;

const mapPodUnitChargeScheduleEntityArray = (
  entities: PodUnitChargeSchedules[]
): Schedule[] =>
  entities ? entities.map(mapPodUnitChargeScheduleEntity) : undefined;

export const mapPodUnitConnectorEntityArray = (
  podUnitConnectors: PodUnitConnector[],
  pcbPodUnits: PcbPodUnit[]
): Socket[] => {
  if (!podUnitConnectors) {
    return undefined;
  }
  if (!pcbPodUnits) {
    return podUnitConnectors.map((podUnitConnector) =>
      mapPodUnitConnectorEntity(podUnitConnector, undefined)
    );
  }
  const filteredPcbPodUnits = pcbPodUnits.filter(
    (pcbPodUnit) => !pcbPodUnit.removedAt
  );
  return podUnitConnectors
    .map((podUnitConnector) => {
      if (
        filteredPcbPodUnits.length === 1 &&
        podUnitConnector.connector.doorId === 1
      ) {
        return mapPodUnitConnectorEntity(
          podUnitConnector,
          filteredPcbPodUnits[0]
        );
      }
      const pcbPodUnit = filteredPcbPodUnits.find(
        (pcbPodUnit) => pcbPodUnit.doorId === podUnitConnector.connector.doorId
      );
      return mapPodUnitConnectorEntity(podUnitConnector, pcbPodUnit);
    })
    .sort((a, b) => a.door.localeCompare(b.door));
};

const mapRevenueProfile = (entity: RevenueProfiles): TariffSummary =>
  entity
    ? {
        id: entity.id,
        name: entity.name,
      }
    : undefined;

const mapAgeYears = (entity: PodLocations): number | undefined => {
  const insertedAtDates =
    entity.podLocationUnits?.map((e) => e.insertedAt.getTime()) ?? [];

  return insertedAtDates.length > 0
    ? dayjs().diff(dayjs(Math.min(...insertedAtDates)), 'year')
    : undefined;
};
