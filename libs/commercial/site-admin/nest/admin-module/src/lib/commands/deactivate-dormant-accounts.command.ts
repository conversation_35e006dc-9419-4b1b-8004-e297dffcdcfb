import { Command, CommandRunner } from 'nest-commander';
import { Logger } from '@nestjs/common';
import { UserRecord } from 'firebase-admin/auth';
import { getAuth } from '@experience/shared/firebase/admin';
import dayjs from 'dayjs';

export const POD_POINT_USER_DEACTIVATION_PERIOD = 30;
export const EXTERNAL_USER_DEACTIVATION_PERIOD = 365;

@Command({ name: 'deactivate-dormant-accounts' })
export class DeactivateDormantAccountsCommand extends CommandRunner {
  private readonly logger = new Logger(DeactivateDormantAccountsCommand.name);

  constructor() {
    super();
  }

  async run() {
    await this.deactivateDormantAccounts();
  }

  private async deactivateDormantAccounts(pageToken?: string): Promise<void> {
    const result = await getAuth().listUsers(1000, pageToken);
    for (const user of result.users) {
      this.logger.log({ user }, 'checking if user should be deactivated');

      if (
        user.disabled ||
        ['<EMAIL>'].includes(user.email as string)
      ) {
        this.logger.log({ user }, 'skipping user');
        continue;
      }

      const isInternalUser =
        user.email?.endsWith('@pod-point.com') ||
        user.email?.endsWith('@podenergy.com');

      const accountPeriod = dayjs().subtract(
        isInternalUser
          ? POD_POINT_USER_DEACTIVATION_PERIOD
          : EXTERNAL_USER_DEACTIVATION_PERIOD,
        'days'
      );

      const { creationTime, lastSignInTime } = user.metadata;

      await this.deactivateUserIfInactive(
        lastSignInTime ? dayjs(lastSignInTime) : dayjs(creationTime),
        accountPeriod,
        user
      );
    }

    if (result.pageToken) {
      await this.deactivateDormantAccounts(result.pageToken);
    }
  }

  private async deactivateUserIfInactive(
    activityDate: dayjs.Dayjs,
    accountPeriod: dayjs.Dayjs,
    user: UserRecord
  ): Promise<void> {
    if (accountPeriod.isAfter(activityDate)) {
      this.logger.log({ user }, 'deactivating user');
      await getAuth().updateUser(user.uid, { disabled: true });
    }
  }
}
