enum ServiceNames {
  SITES = 'Site Management Service',
  SITES_INTERNAL = 'Internal Site Management Service',
}

enum LocalServiceUrls {
  SITES_LOCAL = 'localhost:4201',
  SITES_INTERNAL_LOCAL = 'localhost:4101',
}

enum PodPointServiceUrls {
  SITES = 'sites.pod-point.com',
  SITES_DEV = 'sites-dev.pod-point.com',
  SITES_STAGE = 'sites-stage.pod-point.com',
  SITES_INTERNAL = 'sites-internal.pod-point.com',
  SITES_INTERNAL_DEV = 'sites-internal-dev.pod-point.com',
  SITES_INTERNAL_STAGE = 'sites-internal-stage.pod-point.com',
}

enum PodEnergyServiceUrls {
  SITES = 'sites.podenergy.com',
  SITES_DEV = 'sites-dev.podenergy.com',
  SITES_STAGE = 'sites-stage.podenergy.com',
  SITES_INTERNAL = 'sites-internal.podenergy.com',
  SITES_INTERNAL_DEV = 'sites-internal-dev.podenergy.com',
  SITES_INTERNAL_STAGE = 'sites-internal-stage.podenergy.com',
}

enum Domains {
  POD_POINT = 'pod-point.com',
  PODENERGY = 'podenergy.com',
}

export interface Service {
  domain: string;
  serviceName: string;
  url: string;
}

const SERVICE_MAP: Service[] = [
  {
    domain: Domains.PODENERGY,
    serviceName: ServiceNames.SITES,
    url: PodEnergyServiceUrls.SITES,
  },
  {
    domain: Domains.PODENERGY,
    serviceName: ServiceNames.SITES,
    url: PodEnergyServiceUrls.SITES_DEV,
  },
  {
    domain: Domains.PODENERGY,
    serviceName: ServiceNames.SITES,
    url: PodEnergyServiceUrls.SITES_STAGE,
  },
  {
    domain: Domains.PODENERGY,
    serviceName: ServiceNames.SITES_INTERNAL,
    url: PodEnergyServiceUrls.SITES_INTERNAL,
  },
  {
    domain: Domains.PODENERGY,
    serviceName: ServiceNames.SITES_INTERNAL,
    url: PodEnergyServiceUrls.SITES_INTERNAL_DEV,
  },
  {
    domain: Domains.PODENERGY,
    serviceName: ServiceNames.SITES_INTERNAL,
    url: PodEnergyServiceUrls.SITES_INTERNAL_STAGE,
  },
  {
    domain: Domains.POD_POINT,
    serviceName: ServiceNames.SITES,
    url: PodPointServiceUrls.SITES,
  },
  {
    domain: Domains.POD_POINT,
    serviceName: ServiceNames.SITES,
    url: PodPointServiceUrls.SITES_DEV,
  },
  {
    domain: Domains.POD_POINT,
    serviceName: ServiceNames.SITES,
    url: PodPointServiceUrls.SITES_STAGE,
  },
  {
    domain: Domains.POD_POINT,
    serviceName: ServiceNames.SITES_INTERNAL,
    url: PodPointServiceUrls.SITES_INTERNAL,
  },
  {
    domain: Domains.POD_POINT,
    serviceName: ServiceNames.SITES_INTERNAL,
    url: PodPointServiceUrls.SITES_INTERNAL_DEV,
  },
  {
    domain: Domains.POD_POINT,
    serviceName: ServiceNames.SITES_INTERNAL,
    url: PodPointServiceUrls.SITES_INTERNAL_STAGE,
  },
  {
    domain: Domains.PODENERGY,
    serviceName: ServiceNames.SITES,
    url: LocalServiceUrls.SITES_LOCAL,
  },
  {
    domain: Domains.PODENERGY,
    serviceName: ServiceNames.SITES_INTERNAL,
    url: LocalServiceUrls.SITES_INTERNAL_LOCAL,
  },
];

export const getServiceFromOrigin = (
  origin: string,
  allowInternal: boolean
): Service => {
  const filteredOrigin = allowInternal
    ? origin
    : origin.replace(/-internal/g, '');

  return (
    SERVICE_MAP.find((service) => filteredOrigin.includes(service.url)) ?? {
      domain: Domains.PODENERGY,
      serviceName: ServiceNames.SITES,
      url: PodEnergyServiceUrls.SITES,
    }
  );
};
