import {
  COMMON_EMAIL_MAX_LENGTH_ERROR,
  COMMON_INVALID_EMAIL_ERROR,
  COMMON_REQUIRED_ERROR,
} from '@experience/shared/typescript/validation';
import { INestApplication } from '@nestjs/common';
import { PasswordResetController } from './password-reset.controller';
import { PasswordResetService } from './password-reset.service';
import { TEST_SEND_PASSWORD_RESET_REQUEST } from '@experience/commercial/site-admin/domain/auth';
import { Test, TestingModule } from '@nestjs/testing';
import { v4 as uuid } from 'uuid';
import request from 'supertest';

jest.mock('./password-reset.service.ts');

describe('PasswordResetController', () => {
  let app: INestApplication;
  let passwordResetService: PasswordResetService;

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      controllers: [PasswordResetController],
      providers: [
        { provide: PasswordResetService, useClass: PasswordResetService },
      ],
    }).compile();

    passwordResetService =
      module.get<PasswordResetService>(PasswordResetService);

    app = module.createNestApplication();
    await app.init();
  });

  afterAll(async () => {
    await app.close();
  });

  it('should send password reset', async () => {
    const mockSendPasswordReset = jest
      .spyOn(passwordResetService, 'sendPasswordReset')
      .mockResolvedValueOnce();

    const response = await request(app.getHttpServer())
      .post('/admins/password-reset')
      .set('Referer', 'https://sites.podenergy.com')
      .send(TEST_SEND_PASSWORD_RESET_REQUEST);

    expect(response.status).toEqual(202);
    expect(mockSendPasswordReset).toHaveBeenCalledWith(
      TEST_SEND_PASSWORD_RESET_REQUEST,
      new URL('https://sites.podenergy.com')
    );
  });

  it('should throw a validation error when email address is missing when sending password reset', () =>
    request(app.getHttpServer())
      .post('/admins/password-reset')
      .set('Referer', 'https://sites.podenergy.com')
      .send({ email: undefined })
      .expect(400)
      .expect({
        statusCode: 400,
        message: [
          COMMON_REQUIRED_ERROR,
          COMMON_EMAIL_MAX_LENGTH_ERROR,
          COMMON_INVALID_EMAIL_ERROR,
        ],
        error: 'Bad Request',
      }));

  it('should throw a validation error when email address is invalid when sending password reset', () =>
    request(app.getHttpServer())
      .post('/admins/password-reset')
      .set('Referer', 'https://sites.podenergy.com')
      .send({ email: 'hello' })
      .expect(400)
      .expect({
        statusCode: 400,
        message: [COMMON_INVALID_EMAIL_ERROR],
        error: 'Bad Request',
      }));

  it('should throw a validation error when email address is too long when sending password reset', () =>
    request(app.getHttpServer())
      .post('/admins/password-reset')
      .set('Referer', 'https://sites.podenergy.com')
      .send({ email: uuid().repeat(10) + '@email.com' })
      .expect(400)
      .expect({
        statusCode: 400,
        message: [COMMON_EMAIL_MAX_LENGTH_ERROR, COMMON_INVALID_EMAIL_ERROR],
        error: 'Bad Request',
      }));
});
