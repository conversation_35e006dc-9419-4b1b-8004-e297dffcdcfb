import {
  AdminService,
  GroupService,
} from '@experience/commercial/site-admin/nest/admin-module';
import { Attributes, FindOptions } from 'sequelize/types/model';
import { BillingService } from '@experience/commercial/site-admin/nest/billing-module';
import { Charger, ChargerNotFoundException } from './charger.dto';
import { DomainService } from '@experience/commercial/site-admin/nest/domain-module';
import { DriverService } from '@experience/commercial/site-admin/nest/driver-module';
import { Inject, Injectable, Logger } from '@nestjs/common';
import { Op } from 'sequelize';
import {
  PodLocations,
  PodUnits,
  podLocationsDeepIncludeOptions,
} from '@experience/shared/sequelize/podadmin';
import { RfidCardService } from '@experience/commercial/site-admin/nest/rfid-module';
import { SiteService } from '@experience/commercial/site-admin/nest/site-module';
import { TariffService } from '@experience/commercial/site-admin/nest/tariff-module';

@Injectable()
export class ChargerService {
  private readonly logger = new Logger(ChargerService.name);

  constructor(
    @Inject('POD_LOCATIONS_REPOSITORY')
    private readonly podLocations: typeof PodLocations,
    @Inject('POD_UNITS_REPOSITORY')
    private readonly podUnits: typeof PodUnits,
    private readonly adminService: AdminService,
    private readonly billingService: BillingService,
    private readonly domainService: DomainService,
    private readonly driverService: DriverService,
    private readonly groupService: GroupService,
    private readonly rfidCardService: RfidCardService,
    private readonly siteService: SiteService,
    private readonly tariffService: TariffService
  ) {}

  async findByIdentifier(identifier: string): Promise<Charger> {
    this.logger.log({ identifier }, 'finding charger');

    return this.findOneByOptions({
      include: podLocationsDeepIncludeOptions,
      where: {
        [Op.or]: [{ '$unit.name$': identifier }, { '$unit.ppid$': identifier }],
      },
    });
  }

  async findPpidByChargerName(name: string): Promise<string> {
    this.logger.log({ name }, 'finding charger ppid');

    return await this.podUnits
      .findOne({ where: { name } })
      .then((unit) => unit.ppid);
  }

  async findChargerNameByPpid(ppid: string): Promise<string> {
    this.logger.log({ ppid }, 'finding charger name');

    return await this.podUnits
      .findOne({ where: { ppid } })
      .then((unit) => unit.name);
  }

  private async findOneByOptions(
    options: FindOptions<Attributes<PodLocations>>
  ): Promise<Charger> {
    return this.podLocations.findOne(options).then((location) => {
      if (!location || !location.address || !location.address.group) {
        throw new ChargerNotFoundException();
      }
      return this.map(location);
    });
  }

  private async map(location: PodLocations): Promise<Charger> {
    const { address } = location;
    const { group } = address;
    const { unit } = location;
    return {
      admins: await this.adminService.findByGroupId(group.id),
      billing: await this.billingService.findBillingInformationByGroupUid(
        group.uid
      ),
      domains: await this.domainService.findByGroupId(group.id),
      drivers: await this.driverService.findByGroupId(group.id),
      group: await this.groupService.findByGroupId(group.id),
      name: unit?.name,
      ppid: unit?.ppid,
      rfidCards: await this.rfidCardService.findByGroupUid(group.uid),
      settings: {
        confirmCharge: !!location.paygEnabled,
      },
      site: await this.siteService.findByGroupUidAndSiteId({
        groupUid: group.uid,
        siteId: address.id,
      }),
      tariff: location.revenueProfile
        ? await this.tariffService.findById(location.revenueProfile.id)
        : null,
    };
  }
}
