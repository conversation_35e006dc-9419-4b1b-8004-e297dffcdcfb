import { Charger, ChargerNotFoundException } from './charger.dto';
import { ChargerController } from './charger.controller';
import { ChargerService } from './charger.service';
import { INestApplication } from '@nestjs/common';
import { TEST_CHARGER } from './__fixtures__/charger.dto';
import { Test, TestingModule } from '@nestjs/testing';
import { convertAllDatesToISOString } from '@experience/shared/typescript/utils';
import { removeIn } from 'immutable';
import request from 'supertest';

jest.mock('./charger.service');

describe('ChargerController', () => {
  let app: INestApplication;
  let chargerService: ChargerService;

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      controllers: [ChargerController],
      providers: [{ provide: ChargerService, useClass: ChargerService }],
    }).compile();

    chargerService = module.get<ChargerService>(ChargerService);

    app = module.createNestApplication();
    await app.init();
  });

  afterAll(async () => {
    await app.close();
  });

  it('should find by ppid', () => {
    jest
      .spyOn(chargerService, 'findByIdentifier')
      .mockResolvedValueOnce(TEST_CHARGER);

    const expectedCharger = removeIn(
      convertAllDatesToISOString(TEST_CHARGER) as Charger,
      ['billing', 'onboardingLink']
    );

    return request(app.getHttpServer())
      .get(`/support/chargers/${TEST_CHARGER.ppid}`)
      .expect(200)
      .expect(expectedCharger);
  });

  it('should find by ppid and handle not found exception', () => {
    jest
      .spyOn(chargerService, 'findByIdentifier')
      .mockRejectedValueOnce(new ChargerNotFoundException());

    return request(app.getHttpServer())
      .get(`/support/chargers/${TEST_CHARGER.ppid}`)
      .expect(404);
  });

  it('should find ppid by charger name', () => {
    jest
      .spyOn(chargerService, 'findPpidByChargerName')
      .mockResolvedValueOnce(TEST_CHARGER.ppid);

    return request(app.getHttpServer())
      .get(`/support/chargers/${TEST_CHARGER.name}/ppid`)
      .expect(200)
      .expect(TEST_CHARGER.ppid);
  });

  it('should find charger name by ppid', () => {
    jest
      .spyOn(chargerService, 'findChargerNameByPpid')
      .mockResolvedValueOnce(TEST_CHARGER.name);

    return request(app.getHttpServer())
      .get(`/support/chargers/${TEST_CHARGER.ppid}/name`)
      .expect(200)
      .expect(TEST_CHARGER.name);
  });
});
