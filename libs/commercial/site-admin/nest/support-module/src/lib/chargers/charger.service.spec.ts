import {
  AdminService,
  GroupService,
} from '@experience/commercial/site-admin/nest/admin-module';
import { BillingService } from '@experience/commercial/site-admin/nest/billing-module';
import { ChargerNotFoundException } from './charger.dto';
import { ChargerService } from './charger.service';
import { DomainService } from '@experience/commercial/site-admin/nest/domain-module';
import { DriverService } from '@experience/commercial/site-admin/nest/driver-module';
import { Op } from 'sequelize';
import {
  PodLocations,
  PodUnits,
  TEST_POD_LOCATIONS_ENTITY_WITH_ADDRESS,
  TEST_REVENUE_PROFILE_ENTITY,
  podLocationsDeepIncludeOptions,
} from '@experience/shared/sequelize/podadmin';
import { RfidCardService } from '@experience/commercial/site-admin/nest/rfid-module';
import { SiteService } from '@experience/commercial/site-admin/nest/site-module';
import {
  TEST_ADMINISTRATOR,
  TEST_BILLING_INFORMATION,
  TEST_DOMAIN,
  TEST_DRIVER,
  TEST_GROUP,
  TEST_MEMBER,
  TEST_SITE,
  TEST_TARIFF,
} from '@experience/commercial/site-admin/typescript/domain-model';
import { TEST_CHARGER } from './__fixtures__/charger.dto';
import { TEST_RFID_CARD } from '@experience/commercial/site-admin/domain/rfid';
import { TariffService } from '@experience/commercial/site-admin/nest/tariff-module';
import { Test, TestingModule } from '@nestjs/testing';
import { setIn } from 'immutable';

jest.mock('@experience/commercial/site-admin/nest/admin-module');
jest.mock('@experience/commercial/site-admin/nest/billing-module');
jest.mock('@experience/commercial/site-admin/nest/domain-module');
jest.mock('@experience/commercial/site-admin/nest/driver-module');
jest.mock('@experience/commercial/site-admin/nest/rfid-module');
jest.mock('@experience/commercial/site-admin/nest/tariff-module');
jest.mock('@experience/commercial/site-admin/nest/site-module');

const location = setIn(
  TEST_POD_LOCATIONS_ENTITY_WITH_ADDRESS,
  ['revenueProfile'],
  TEST_REVENUE_PROFILE_ENTITY
);

describe('ChargerService', () => {
  let adminService: AdminService;
  let billingService: BillingService;
  let chargerService: ChargerService;
  let domainService: DomainService;
  let driverService: DriverService;
  let groupService: GroupService;
  let podLocations: typeof PodLocations;
  let podUnits: typeof PodUnits;
  let rfidCardService: RfidCardService;
  let siteService: SiteService;
  let tariffService: TariffService;

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        ChargerService,
        { provide: 'POD_LOCATIONS_REPOSITORY', useValue: PodLocations },
        { provide: 'POD_UNITS_REPOSITORY', useValue: PodUnits },
        { provide: AdminService, useClass: AdminService },
        { provide: BillingService, useClass: BillingService },
        { provide: DomainService, useClass: DomainService },
        { provide: DriverService, useClass: DriverService },
        { provide: GroupService, useClass: GroupService },
        { provide: RfidCardService, useClass: RfidCardService },
        { provide: SiteService, useClass: SiteService },
        { provide: TariffService, useClass: TariffService },
      ],
    }).compile();

    adminService = module.get<AdminService>(AdminService);
    billingService = module.get<BillingService>(BillingService);
    chargerService = module.get<ChargerService>(ChargerService);
    domainService = module.get<DomainService>(DomainService);
    driverService = module.get<DriverService>(DriverService);
    groupService = module.get<GroupService>(GroupService);
    podLocations = module.get<typeof PodLocations>('POD_LOCATIONS_REPOSITORY');
    podUnits = module.get<typeof PodUnits>('POD_UNITS_REPOSITORY');
    rfidCardService = module.get<RfidCardService>(RfidCardService);
    siteService = module.get<SiteService>(SiteService);
    tariffService = module.get<TariffService>(TariffService);
  });

  it('should be defined', () => {
    expect(adminService).toBeDefined();
    expect(billingService).toBeDefined();
    expect(chargerService).toBeDefined();
    expect(domainService).toBeDefined();
    expect(driverService).toBeDefined();
    expect(groupService).toBeDefined();
    expect(podLocations).toBeDefined();
    expect(podUnits).toBeDefined();
    expect(rfidCardService).toBeDefined();
    expect(siteService).toBeDefined();
    expect(tariffService).toBeDefined();
  });

  it('should find by ppid', async () => {
    const findAdminsByGroupId = jest
      .spyOn(adminService, 'findByGroupId')
      .mockResolvedValueOnce([TEST_ADMINISTRATOR]);
    const findBillingInformationByGroupUid = jest
      .spyOn(billingService, 'findBillingInformationByGroupUid')
      .mockResolvedValueOnce(TEST_BILLING_INFORMATION);
    const findDomainsByGroupId = jest
      .spyOn(domainService, 'findByGroupId')
      .mockResolvedValueOnce([TEST_DOMAIN]);
    const findDriversByGroupId = jest
      .spyOn(driverService, 'findByGroupId')
      .mockResolvedValueOnce([TEST_DRIVER, TEST_MEMBER]);
    const findGroupByGroupId = jest
      .spyOn(groupService, 'findByGroupId')
      .mockResolvedValueOnce(TEST_GROUP);
    const findOne = jest
      .spyOn(podLocations, 'findOne')
      .mockResolvedValueOnce(location);
    const findRfidCardsByGroupId = jest
      .spyOn(rfidCardService, 'findByGroupUid')
      .mockResolvedValueOnce([TEST_RFID_CARD]);
    const findSiteByGroupUidAndSiteId = jest
      .spyOn(siteService, 'findByGroupUidAndSiteId')
      .mockResolvedValueOnce(TEST_SITE);
    const findTariffById = jest
      .spyOn(tariffService, 'findById')
      .mockResolvedValueOnce(TEST_TARIFF);

    const charger = await chargerService.findByIdentifier(location.unit.ppid);

    expect(charger).toEqual(TEST_CHARGER);
    expect(findOne).toHaveBeenCalledWith({
      include: podLocationsDeepIncludeOptions,
      where: {
        [Op.or]: [
          { '$unit.name$': location.unit.ppid },
          { '$unit.ppid$': location.unit.ppid },
        ],
      },
    });
    expect(findAdminsByGroupId).toHaveBeenCalledWith(location.address.group.id);
    expect(findBillingInformationByGroupUid).toHaveBeenCalledWith(
      location.address.group.uid
    );
    expect(findDomainsByGroupId).toHaveBeenCalledWith(
      location.address.group.id
    );
    expect(findDriversByGroupId).toHaveBeenCalledWith(
      location.address.group.id
    );
    expect(findGroupByGroupId).toHaveBeenCalledWith(location.address.group.id);
    expect(findRfidCardsByGroupId).toHaveBeenCalledWith(
      location.address.group.uid
    );
    expect(findSiteByGroupUidAndSiteId).toHaveBeenCalledWith({
      groupUid: location.address.group.uid,
      siteId: location.address.id,
    });
    expect(findTariffById).toHaveBeenCalledWith(location.revenueProfile.id);
  });

  it('should find by ppid and throw not found if charger does not exist', async () => {
    const findOne = jest
      .spyOn(podLocations, 'findOne')
      .mockResolvedValueOnce(null);

    await expect(
      async () => await chargerService.findByIdentifier(location.unit.ppid)
    ).rejects.toThrow(ChargerNotFoundException);

    expect(findOne).toHaveBeenCalledWith({
      include: podLocationsDeepIncludeOptions,
      where: {
        [Op.or]: [
          { '$unit.name$': location.unit.ppid },
          { '$unit.ppid$': location.unit.ppid },
        ],
      },
    });
  });

  it('should find by ppid and throw not found if charger does not have an address', async () => {
    const findOne = jest
      .spyOn(podLocations, 'findOne')
      .mockResolvedValueOnce(setIn(location, ['address'], null));

    await expect(
      async () => await chargerService.findByIdentifier(location.unit.ppid)
    ).rejects.toThrow(ChargerNotFoundException);

    expect(findOne).toHaveBeenCalledWith({
      include: podLocationsDeepIncludeOptions,
      where: {
        [Op.or]: [
          { '$unit.name$': location.unit.ppid },
          { '$unit.ppid$': location.unit.ppid },
        ],
      },
    });
  });

  it('should find by ppid and throw not found if charger does not have a group', async () => {
    const findOne = jest
      .spyOn(podLocations, 'findOne')
      .mockResolvedValueOnce(setIn(location, ['address', 'group'], null));

    await expect(
      async () => await chargerService.findByIdentifier(location.unit.ppid)
    ).rejects.toThrow(ChargerNotFoundException);

    expect(findOne).toHaveBeenCalledWith({
      include: podLocationsDeepIncludeOptions,
      where: {
        [Op.or]: [
          { '$unit.name$': location.unit.ppid },
          { '$unit.ppid$': location.unit.ppid },
        ],
      },
    });
  });

  it('should find ppid by charger name', async () => {
    const findOne = jest
      .spyOn(podUnits, 'findOne')
      .mockResolvedValueOnce(location.unit);

    expect(
      await chargerService.findPpidByChargerName(location.unit.name)
    ).toEqual(location.unit.ppid);
    expect(findOne).toHaveBeenCalledWith({
      where: { name: location.unit.name },
    });
  });

  it('should find charger name by ppid', async () => {
    const findOne = jest
      .spyOn(podUnits, 'findOne')
      .mockResolvedValueOnce(location.unit);

    expect(
      await chargerService.findChargerNameByPpid(location.unit.ppid)
    ).toEqual(location.unit.name);
    expect(findOne).toHaveBeenCalledWith({
      where: { ppid: location.unit.ppid },
    });
  });
});
