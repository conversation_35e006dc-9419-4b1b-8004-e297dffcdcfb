import { Charger } from '../charger.dto';
import {
  TEST_ADMINISTRATOR,
  TEST_BILLING_INFORMATION,
  TEST_DOMAIN,
  TEST_DRIVER,
  TEST_GROUP,
  TEST_MEMBER,
  TEST_SITE,
  TEST_TARIFF,
} from '@experience/commercial/site-admin/typescript/domain-model';
import {
  TEST_POD_LOCATIONS_ENTITY_WITH_ADDRESS,
  TEST_REVENUE_PROFILE_ENTITY,
} from '@experience/shared/sequelize/podadmin';
import { TEST_RFID_CARD } from '@experience/commercial/site-admin/domain/rfid';
import { setIn } from 'immutable';

const location = setIn(
  TEST_POD_LOCATIONS_ENTITY_WITH_ADDRESS,
  ['revenueProfile'],
  TEST_REVENUE_PROFILE_ENTITY
);

export const TEST_CHARGER: Charger = {
  admins: [TEST_ADMINISTRATOR],
  billing: TEST_BILLING_INFORMATION,
  domains: [TEST_DOMAIN],
  drivers: [TEST_DRIVER, TEST_MEMBER],
  group: TEST_GROUP,
  name: location.unit?.name,
  ppid: location.unit?.ppid,
  rfidCards: [TEST_RFID_CARD],
  settings: {
    confirmCharge: !!location.paygEnabled,
  },
  site: TEST_SITE,
  tariff: TEST_TARIFF,
};
