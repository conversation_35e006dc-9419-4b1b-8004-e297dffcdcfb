import {
  Admin,
  BillingInformation,
  Domain,
  Driver,
  Group,
  Site,
  Tariff,
} from '@experience/commercial/site-admin/typescript/domain-model';
import { RfidCard } from '@experience/commercial/site-admin/domain/rfid';

export class ChargerSettings {
  confirmCharge: boolean;
}

export class Charger {
  admins: Admin[];
  billing: BillingInformation;
  domains: Domain[];
  drivers: Driver[];
  group: Group;
  name?: string;
  ppid: string;
  rfidCards: RfidCard[];
  settings: ChargerSettings;
  site: Site;
  tariff: Tariff;
}

export class ChargerNotFoundException extends Error {
  constructor() {
    super('Charger not found');
  }
}
