import {
  AdminMiddleware,
  AdminModule,
} from '@experience/commercial/site-admin/nest/admin-module';
import { BillingModule } from '@experience/commercial/site-admin/nest/billing-module';
import { BouncedEmailsController } from './emails/bounced-emails.controller';
import { BouncedEmailsService } from './emails/bounced-emails.service';
import { ChargerController } from './chargers/charger.controller';
import { ChargerService } from './chargers/charger.service';
import { CloudWatchModule } from '@experience/shared/nest/aws/cloudwatch-module';
import { CreateMissingDriversCommand } from './commands/create-missing-drivers.command';
import { DomainModule } from '@experience/commercial/site-admin/nest/domain-module';
import { DriverModule } from '@experience/commercial/site-admin/nest/driver-module';
import { MiddlewareConsumer, Module } from '@nestjs/common';
import {
  PodLocations,
  PodUnits,
  PodadminSequelizeModule,
} from '@experience/shared/sequelize/podadmin';
import { RfidModule } from '@experience/commercial/site-admin/nest/rfid-module';
import { ScheduleModule } from '@nestjs/schedule';
import { SesModule } from '@experience/shared/nest/aws/ses-module';
import { SiteModule } from '@experience/commercial/site-admin/nest/site-module';
import { TariffModule } from '@experience/commercial/site-admin/nest/tariff-module';
import ScheduledTasksService from './scheduled-tasks/scheduled-tasks.service';

@Module({
  imports: [
    ScheduleModule.forRoot(),
    AdminModule,
    BillingModule,
    CloudWatchModule,
    DomainModule,
    DriverModule,
    PodadminSequelizeModule,
    RfidModule,
    SesModule,
    SiteModule,
    TariffModule,
  ],
  controllers: [BouncedEmailsController, ChargerController],
  providers: [
    BouncedEmailsService,
    ChargerService,
    CreateMissingDriversCommand,
    ScheduledTasksService,
    { provide: 'POD_LOCATIONS_REPOSITORY', useValue: PodLocations },
    { provide: 'POD_UNITS_REPOSITORY', useValue: PodUnits },
  ],
})
export class SupportModule {
  configure(consumer: MiddlewareConsumer) {
    consumer.apply(AdminMiddleware).forRoutes(BouncedEmailsController);
  }
}
