import {
  Modal,
  ModalProps,
  Paragraph,
} from '@experience/shared/react/design-system';
import { Pod } from '@experience/commercial/site-admin/typescript/domain-model';
import { mutate } from 'swr';
import { useErrorHandler } from '@experience/shared/react/hooks';
import { useState } from 'react';
import axios from 'axios';

interface RemoveTariffModalProps {
  pod: Pod;
  open: boolean;
  setOpen: (value: boolean) => void;
}

export const RemoveTariffModal = ({
  pod,
  open,
  setOpen,
}: RemoveTariffModalProps) => {
  const handleError = useErrorHandler();
  const [isSubmitting, setIsSubmitting] = useState(false);

  const handleConfirm = async (): Promise<void> => {
    setIsSubmitting(true);

    try {
      await axios.delete(`/api/pods/${pod.id}/tariff`);
      await mutate(`/api/pods/${pod.ppid}`);

      setOpen(false);
    } catch (error) {
      handleError(error);
    } finally {
      setIsSubmitting(false);
    }
  };

  const modalProps: ModalProps = {
    cancelButtonText: 'No',
    confirmButtonText: 'Yes',
    content: (
      <Paragraph>
        Are you sure you want to remove the tariff from this charger?
      </Paragraph>
    ),
    handleConfirm,
    isDisabled: !pod.supportsTariffs,
    isSubmitting,
    open,
    setOpen,
    title: 'Remove tariff',
  };

  return <Modal {...modalProps} />;
};

export default RemoveTariffModal;
