import {
  Alert,
  AlertType,
  Anchor,
  Badge,
  Paragraph,
} from '@experience/shared/react/design-system';
import {
  CheckmarkIcon,
  CrossIcon,
} from '@experience/shared/react/design-system-icons';
import { ErrorPage } from '@experience/shared/react/error-pages';
import {
  Pod,
  Tariff,
  tariffHasEnergySchedule,
  tariffIsCompliantWithPricingRegulations,
} from '@experience/commercial/site-admin/typescript/domain-model';
// eslint-disable-next-line @nx/enforce-module-boundaries
import { VerticalSpacer } from '@experience/shared/react/layouts';
import useSWR from 'swr';

export interface PodBadgesProps {
  pod: Pod;
  tariffs: Tariff[];
}

// Fetch if the pod has a tariff assigned and the tariff is owned by the group.
const shouldFetchTariff = (pod: Pod, tariffs: Tariff[]) =>
  pod.tariff && tariffs.find((tariff) => tariff.id === pod.tariff?.id);

export const PodBadges = ({ pod, tariffs }: PodBadgesProps) => {
  const { data: tariff, error: tariffError } = useSWR(
    shouldFetchTariff(pod, tariffs) ? `/api/tariffs/${pod.tariff?.id}` : null
  );

  if (tariffError) return <ErrorPage />;

  const tariffIsCompliant = tariff
    ? tariffIsCompliantWithPricingRegulations({ ...tariff, pods: [pod] })
    : true;

  const checkmarkIcon = <CheckmarkIcon.SOLID />;
  const crossIcon = (
    <CrossIcon.LIGHT
      className="stroke-1 stroke-current fill-current mt-[3px]"
      height="h-3"
      width="w-3"
      title="Cross"
    />
  );

  return (
    <>
      <div className="flex flex-row flex-wrap gap-x-4 gap-y-2">
        <Badge.Neutral label={`PPID: ${pod.ppid}`} />
        <Badge.Neutral label={`Model: ${pod.model}`} />
        {pod.schemes.length > 0 ? (
          <>
            <Badge.Neutral icon={checkmarkIcon} label="Part of a scheme" />
            {pod.site ? (
              <Badge.Neutral label={`Host: ${pod.site.group.name}`} />
            ) : null}
            {pod.schemes.map((scheme) => (
              <Badge.Neutral key={scheme.id} label={`Scheme: ${scheme.name}`} />
            ))}
          </>
        ) : null}
        {pod.isPublic ? (
          !tariffIsCompliant ? (
            <Badge.Warning icon={checkmarkIcon} label="Public access" />
          ) : (
            <Badge.Neutral icon={checkmarkIcon} label="Public access" />
          )
        ) : (
          <Badge.Neutral icon={checkmarkIcon} label="Private access" />
        )}
        {pod.tariff ? (
          !tariffIsCompliant ||
          !pod.confirmChargeEnabled ||
          (tariffHasEnergySchedule(tariff) && !pod.supportsPerKwh) ? (
            <Badge.Warning icon={checkmarkIcon} label="Tariff assigned" />
          ) : (
            <Badge.Neutral icon={checkmarkIcon} label="Tariff assigned" />
          )
        ) : (
          <Badge.Neutral icon={crossIcon} label="Tariff assigned" />
        )}
        {pod.confirmChargeEnabled ? (
          <Badge.Neutral icon={checkmarkIcon} label="Confirm charge" />
        ) : pod.tariff ? (
          <Badge.Warning icon={crossIcon} label="Confirm charge" />
        ) : (
          <Badge.Neutral icon={crossIcon} label="Confirm charge" />
        )}
        {pod.supportsEnergyTariff ? (
          <Badge.Neutral icon={checkmarkIcon} label="Supports energy tariff" />
        ) : tariff && tariffHasEnergySchedule(tariff) ? (
          <Badge.Warning icon={crossIcon} label="Supports energy tariff" />
        ) : (
          <Badge.Neutral icon={crossIcon} label="Supports energy tariff" />
        )}
        {pod.supportsContactless ? (
          <Badge.Neutral icon={checkmarkIcon} label="Contactless" />
        ) : (
          <Badge.Neutral icon={crossIcon} label="Contactless" />
        )}
        {pod.supportsRfid ? (
          <Badge.Neutral icon={checkmarkIcon} label="RFID reader" />
        ) : (
          <Badge.Neutral icon={crossIcon} label="RFID reader" />
        )}
      </div>

      {!tariffIsCompliant ? (
        <>
          <VerticalSpacer />
          <Alert type={AlertType.WARNING}>
            <>
              <Paragraph>
                The Public Charge Point Regulations 2023 mandate that the price
                for charging through a public charger must be displayed to
                drivers in pence per kilowatt hour from the appointed date of
                24th November 2023. In order to comply, tariffs assigned to a
                publicly accessible charger must only contain per kWh pricing
                schedules for all drivers.
              </Paragraph>
              <VerticalSpacer />
              <Paragraph>
                Should the tariff remain non-compliant, Pod Point will remove
                the charger from the public map, preventing public use.
              </Paragraph>
            </>
          </Alert>
        </>
      ) : null}

      {pod.tariff && !pod.confirmChargeEnabled ? (
        <>
          <VerticalSpacer />
          <Alert type={AlertType.WARNING}>
            <Paragraph>
              This charger does not require drivers to confirm their charge. If
              they don’t confirm their charge we are not able to take payment
              from them. Please{' '}
              <Anchor
                href="https://help.pod-point.com/s/contactsupport"
                isNativeLink={true}
                target="_blank"
              >
                contact us
              </Anchor>{' '}
              to change this configuration.
            </Paragraph>
          </Alert>
        </>
      ) : null}

      {tariffHasEnergySchedule(tariff) && !pod.supportsPerKwh ? (
        <>
          <VerticalSpacer />
          <Alert type={AlertType.WARNING}>
            <Paragraph>
              This charger does not support energy tariffs. Please reassign or
              amend the tariff so it only contains per hour or per charge
              schedules for all drivers. If the charger is publicly accessible
              please{' '}
              <Anchor
                href="https://help.pod-point.com/s/contactsupport"
                isNativeLink={true}
                target="_blank"
              >
                contact us
              </Anchor>{' '}
              .
            </Paragraph>
          </Alert>
        </>
      ) : null}
    </>
  );
};

export default PodBadges;
