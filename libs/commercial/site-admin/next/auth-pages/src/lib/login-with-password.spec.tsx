import '@testing-library/jest-dom';
import 'whatwg-fetch';
import { render, screen } from '@testing-library/react';
import { signIn, useSession } from 'next-auth/react';
import { useRouter } from 'next/navigation';
import LoginWithPasswordPage from './login-with-password';

const mockPublicRuntimeConfig = jest.fn(() => ({
  publicRuntimeConfig: {
    googleCloudApiKey: 'google-cloud-api-key',
    nextAuthProvider: 'pod-point',
  },
}));

jest.mock('next/config', () => () => mockPublicRuntimeConfig());

(signIn as jest.Mock).mockImplementation(() => jest.fn());

jest.mock('next-auth/react');
jest.mock('next/navigation', () => ({ useRouter: jest.fn() }));
jest.mock('@experience/shared/next/firebase', () => ({
  getAuth: jest.fn(),
}));
jest.mock('firebase/auth');

describe('Login with password page', () => {
  const mockWindowAssign = jest.fn();
  const originalWindowLocation = window.location;

  beforeEach(() => {
    // eslint-disable-next-line @typescript-eslint/ban-ts-comment
    // @ts-ignore
    delete window.location;

    // eslint-disable-next-line @typescript-eslint/ban-ts-comment
    // @ts-ignore
    window.location = Object.defineProperties(
      {},
      {
        ...Object.getOwnPropertyDescriptors(originalWindowLocation),
        assign: {
          configurable: true,
          value: mockWindowAssign,
        },
      }
    );
  });

  it('should render successfully', async () => {
    (useSession as jest.Mock).mockReturnValueOnce({
      status: 'unauthenticated',
    });

    const { baseElement } = render(<LoginWithPasswordPage />);
    expect(baseElement).toBeTruthy();
  });

  it('should match snapshot', async () => {
    (useSession as jest.Mock).mockReturnValueOnce({
      status: 'unauthenticated',
    });

    const { baseElement } = render(<LoginWithPasswordPage />);
    expect(baseElement).toMatchSnapshot();
  });

  it('should render login button if status is unauthenticated', async () => {
    (useSession as jest.Mock).mockReturnValueOnce({
      status: 'unauthenticated',
    });

    render(<LoginWithPasswordPage />);

    expect(screen.getByRole('button', { name: 'Log in' })).toBeInTheDocument();
  });

  it('should return to home screen if status is authenticated', async () => {
    (useSession as jest.Mock).mockReturnValueOnce({
      status: 'authenticated',
    });
    const mockRouter = {
      push: jest.fn(),
    };
    (useRouter as jest.Mock).mockReturnValue(mockRouter);

    render(<LoginWithPasswordPage />);

    expect(mockWindowAssign).toHaveBeenCalledWith('/');
  });
});
