import {
  SubscriptionChargersTable,
  SubscriptionChargersTableProps,
} from './subscription-chargers-table';
import {
  TEST_BILLING_INFORMATION,
  TEST_POD_WITH_SITE,
} from '@experience/commercial/site-admin/typescript/domain-model';
import { render } from '@testing-library/react';
import { setIn } from 'immutable';
import useSWR, { SWRResponse } from 'swr';

jest.mock('swr');
const mockSwr = jest.mocked(useSWR);

const defaultProps: SubscriptionChargersTableProps = {
  subscriptionChargers: TEST_BILLING_INFORMATION.subscription.chargers,
};

describe('subscription chargers table', () => {
  beforeEach(() => {
    mockSwr.mockReturnValue({
      data: [setIn(TEST_POD_WITH_SITE, ['ppid'], 'PG-70500')],
    } as SWRResponse);
  });

  it('should render successfully', () => {
    const { baseElement } = render(
      <SubscriptionChargersTable {...defaultProps} />
    );
    expect(baseElement).toBeInTheDocument();
  });

  it('should match snapshot', () => {
    const { baseElement } = render(
      <SubscriptionChargersTable {...defaultProps} />
    );
    expect(baseElement).toMatchSnapshot();
  });
});
