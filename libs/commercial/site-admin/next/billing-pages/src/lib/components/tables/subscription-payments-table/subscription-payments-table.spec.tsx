import { SubscriptionPaymentsTable } from './subscription-payments-table';
import { TEST_BILLING_INFORMATION } from '@experience/commercial/site-admin/typescript/domain-model';
import { render, screen } from '@testing-library/react';
import { setIn } from 'immutable';

describe('Subscription payments table', () => {
  it('should render successfully', () => {
    const { baseElement } = render(
      <SubscriptionPaymentsTable
        subscriptionsPayments={TEST_BILLING_INFORMATION.subscription.invoices}
      />
    );
    expect(baseElement).toBeInTheDocument();
  });

  it('should match snapshot', () => {
    const { baseElement } = render(
      <SubscriptionPaymentsTable
        subscriptionsPayments={TEST_BILLING_INFORMATION.subscription.invoices}
      />
    );
    expect(baseElement).toMatchSnapshot();
  });

  it('should render a pay online link if subscription has not been paid', () => {
    render(
      <SubscriptionPaymentsTable
        subscriptionsPayments={setIn(
          TEST_BILLING_INFORMATION.subscription.invoices,
          [0, 'status'],
          'draft'
        )}
      />
    );
    expect(
      screen.getByRole('button', { name: 'Pay online for invoice ADF-1001' })
        .parentElement
    ).toHaveAttribute(
      'href',
      TEST_BILLING_INFORMATION.subscription.invoices[0].hostedInvoiceUrl
    );
  });

  it('should not render a pay online link if subscription has been paid', () => {
    render(
      <SubscriptionPaymentsTable
        subscriptionsPayments={TEST_BILLING_INFORMATION.subscription.invoices}
      />
    );
    expect(
      screen.queryByRole('button', { name: 'Pay online for invoice ADF-1001' })
    ).toBeNull();
  });

  it('should render a download PDF link for an invoice', () => {
    render(
      <SubscriptionPaymentsTable
        subscriptionsPayments={TEST_BILLING_INFORMATION.subscription.invoices}
      />
    );
    expect(
      screen.getByRole('button', { name: 'Download invoice ADF-1001' })
        .parentElement
    ).toHaveAttribute(
      'href',
      TEST_BILLING_INFORMATION.subscription.invoices[0].invoicePdfUrl
    );
  });
});
