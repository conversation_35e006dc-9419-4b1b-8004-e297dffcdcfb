import { CookiesProvider } from 'react-cookie';
import { FlagsProvider } from 'flagged';
import {
  TEST_BILLING_INFORMATION,
  TEST_POD_WITH_SITE,
} from '@experience/commercial/site-admin/typescript/domain-model';
import { fireEvent, render, screen } from '@testing-library/react';
import { setIn } from 'immutable';
import BillingPage from './billing-page';
import useSWR, { SWRResponse } from 'swr';

jest.mock('@experience/shared/react/hooks');
jest.mock('swr');
const mockUseSWR = jest.mocked(useSWR);
const mockRouter = jest.fn();
jest.mock('next/router', () => ({
  useRouter: () => mockRouter(),
}));

describe('BillingPage', () => {
  beforeEach(() => {
    mockUseSWR.mockImplementation((key) => {
      if (key === '/api/billing') {
        return { data: TEST_BILLING_INFORMATION, error: null } as SWRResponse;
      }
      if (key === '/api/pods') {
        return {
          data: [setIn(TEST_POD_WITH_SITE, ['ppid'], 'PG-70500')],
          error: null,
        } as SWRResponse;
      }
      return { data: null, error: new Error() } as SWRResponse;
    });
  });

  it('should render successfully', () => {
    const { baseElement } = render(
      <CookiesProvider>
        <BillingPage />
      </CookiesProvider>
    );
    expect(baseElement).toBeInTheDocument();
  });

  it('should match snapshot ', () => {
    const { baseElement } = render(
      <CookiesProvider>
        <BillingPage />
      </CookiesProvider>
    );
    expect(baseElement).toMatchSnapshot();
  });

  it.each([
    ['Statements', TEST_BILLING_INFORMATION.statements[0].feeInvoiceNumber],
    [
      'Subscription',
      TEST_BILLING_INFORMATION.subscription.invoices[0].customerEmail,
    ],
    ['Business details', TEST_BILLING_INFORMATION.customerDetails.poNumber],
  ])(
    'should show the correct contents for the %s tab',
    (tab, expectedString) => {
      render(
        <CookiesProvider>
          <BillingPage />
        </CookiesProvider>
      );

      fireEvent.click(screen.getByRole('tab', { name: tab }));

      expect(expectedString).toBe(expectedString);
    }
  );

  it('should show chargers on subscription table in subscriptions when feature flag is on', () => {
    render(
      <FlagsProvider
        features={{
          chargersOnSubscription: true,
        }}
      >
        <CookiesProvider>
          <BillingPage />
        </CookiesProvider>
      </FlagsProvider>
    );

    fireEvent.click(screen.getByRole('tab', { name: 'Subscription' }));

    expect(
      screen.getByRole('heading', { name: 'Subscription chargers' })
    ).toBeInTheDocument();
  });

  it('should not show chargers on subscription table when feature flag is off', () => {
    render(
      <FlagsProvider
        features={{
          chargersOnSubscription: false,
        }}
      >
        <CookiesProvider>
          <BillingPage />
        </CookiesProvider>
      </FlagsProvider>
    );

    fireEvent.click(screen.getByRole('tab', { name: 'Subscription' }));

    expect(
      screen.queryByRole('heading', { name: 'Subscription chargers' })
    ).not.toBeInTheDocument();
  });

  it('should match snapshot when onboarding link is present', () => {
    mockUseSWR.mockReturnValueOnce({
      data: {
        ...TEST_BILLING_INFORMATION,
        onboardingLink: 'https://connect.stripe.com/',
      },
    } as SWRResponse);

    const { baseElement } = render(
      <CookiesProvider>
        <BillingPage />
      </CookiesProvider>
    );

    expect(baseElement).toMatchSnapshot();
  });

  it('should render error page if there is an error', () => {
    mockUseSWR.mockReturnValueOnce({
      data: undefined,
      error: new Error(),
    } as SWRResponse);
    render(
      <CookiesProvider>
        <BillingPage />
      </CookiesProvider>
    );

    expect(screen.getByText('Something went wrong')).toBeInTheDocument();
  });
});
