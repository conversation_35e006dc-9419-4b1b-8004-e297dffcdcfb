import { ErrorBoundary } from '@sentry/react';
import {
  TARIFF_PER_KWH_PRICE_ERROR,
  TARIFF_PRICE_MUST_NOT_BE_LESS_THAN_1P_IF_NOT_FREE_ERROR,
  TARIFF_SCHEDULE_LIMIT_ERROR,
} from '@experience/commercial/site-admin/typescript/domain-model-validation';
import {
  TEST_TARIFF,
  TEST_TARIFF_DRIVER_DURATION_SCHEDULE,
} from '@experience/commercial/site-admin/typescript/domain-model';
import { fireEvent, render, screen, waitFor } from '@testing-library/react';
import { mutate } from 'swr';
import EditTariffFlatRateScheduleModal from './edit-tariff-flat-rate-schedule-modal';
import axios from 'axios';

jest.mock('axios');
const mockAxiosPut = jest.mocked(axios.put);

jest.mock('swr');
const mockMutate = jest.mocked(mutate);

const mockSetOpen = jest.fn();

const defaultProps = {
  open: true,
  schedule: TEST_TARIFF_DRIVER_DURATION_SCHEDULE,
  setOpen: mockSetOpen,
  tariff: TEST_TARIFF,
};

describe('EditTariffFlatRateScheduleModal', () => {
  it('should render correctly', () => {
    const { baseElement } = render(
      <EditTariffFlatRateScheduleModal {...defaultProps} />
    );

    expect(baseElement).toBeInTheDocument();
  });

  it('should match snapshot', () => {
    const { baseElement } = render(
      <EditTariffFlatRateScheduleModal {...defaultProps} />
    );

    expect(baseElement).toMatchSnapshot();
  });

  it('should disable tariff tier select field', () => {
    render(<EditTariffFlatRateScheduleModal {...defaultProps} />);

    expect(screen.getByLabelText('Tariff tier')).toBeDisabled();
  });

  it('should render prepopulated default price value', () => {
    render(<EditTariffFlatRateScheduleModal {...defaultProps} />);

    expect(screen.getByLabelText('Price per kWh (in pence)')).toHaveValue(50);
  });

  it('should close modal and reset inputs if cancel button is clicked', () => {
    render(<EditTariffFlatRateScheduleModal {...defaultProps} />);

    fireEvent.change(screen.getByLabelText('Price per kWh (in pence)'), {
      target: { value: '20' },
    });

    fireEvent.click(screen.getByRole('button', { name: 'Cancel' }));
    expect(screen.getByLabelText('Price per kWh (in pence)')).toHaveValue(
      TEST_TARIFF_DRIVER_DURATION_SCHEDULE.bands[0].cost
    );
  });

  it.each([
    ['a positive price', '1'],
    ['a price of zero', '0'],
  ])(
    'should call the API when the form is submitted with %s',
    async (_, price) => {
      render(<EditTariffFlatRateScheduleModal {...defaultProps} />);

      const submitButton = screen.getByRole('button', { name: 'Save changes' });

      fireEvent.change(screen.getByLabelText('Price per kWh (in pence)'), {
        target: { value: price },
      });

      fireEvent.click(submitButton);

      expect(submitButton).toBeDisabled();

      await waitFor(() => {
        expect(mockAxiosPut).toHaveBeenCalledWith(
          `/api/tariffs/${TEST_TARIFF.id}/schedules/${TEST_TARIFF_DRIVER_DURATION_SCHEDULE.id}`,
          {
            endDay: TEST_TARIFF_DRIVER_DURATION_SCHEDULE.endDay,
            endTime: TEST_TARIFF_DRIVER_DURATION_SCHEDULE.endTime,
            price,
            pricingModel: TEST_TARIFF_DRIVER_DURATION_SCHEDULE.pricingModel,
            startDay: TEST_TARIFF_DRIVER_DURATION_SCHEDULE.startDay,
            startTime: TEST_TARIFF_DRIVER_DURATION_SCHEDULE.startTime,
            tariffTier: TEST_TARIFF_DRIVER_DURATION_SCHEDULE.tariffTier,
          }
        );
        expect(mockMutate).toHaveBeenCalledWith(
          `/api/tariffs/${TEST_TARIFF.id}`
        );
        expect(mockSetOpen).toHaveBeenCalledWith(false);
        expect(submitButton).not.toBeDisabled();
      });
    }
  );

  it('should throw a validation error if the price is invalid', async () => {
    render(
      <EditTariffFlatRateScheduleModal
        {...defaultProps}
        schedule={{
          ...TEST_TARIFF_DRIVER_DURATION_SCHEDULE,
          pricingModel: 'energy',
        }}
      />
    );
    fireEvent.change(screen.getByLabelText('Price per kWh (in pence)'), {
      target: { value: '10.1234' },
    });

    fireEvent.click(screen.getByRole('button', { name: 'Save changes' }));

    expect(
      await screen.findByText(TARIFF_PER_KWH_PRICE_ERROR)
    ).toBeInTheDocument();
  });

  it('should throw a validation error if the price is less than 1p (if not free)', async () => {
    render(
      <EditTariffFlatRateScheduleModal
        {...defaultProps}
        schedule={{
          ...TEST_TARIFF_DRIVER_DURATION_SCHEDULE,
          pricingModel: 'energy',
        }}
      />
    );
    fireEvent.change(screen.getByLabelText('Price per kWh (in pence)'), {
      target: { value: '0.999' },
    });

    fireEvent.click(screen.getByRole('button', { name: 'Save changes' }));

    expect(
      await screen.findByText(
        TARIFF_PRICE_MUST_NOT_BE_LESS_THAN_1P_IF_NOT_FREE_ERROR
      )
    ).toBeInTheDocument();
  });

  it('should throw a validation error if price is greater than limit', async () => {
    render(
      <EditTariffFlatRateScheduleModal
        {...defaultProps}
        schedule={{
          ...TEST_TARIFF_DRIVER_DURATION_SCHEDULE,
          pricingModel: 'energy',
        }}
      />
    );

    fireEvent.change(screen.getByLabelText('Price per kWh (in pence)'), {
      target: { value: '2001' },
    });

    fireEvent.click(screen.getByRole('button', { name: 'Save changes' }));
    expect(
      await screen.findByText(TARIFF_SCHEDULE_LIMIT_ERROR('£2.00'))
    ).toBeInTheDocument();
  });

  it('should handle errors and direct to error boundary', async () => {
    mockAxiosPut.mockRejectedValueOnce({
      response: {
        status: 500,
      },
    });

    render(
      <ErrorBoundary fallback={<h1>Error detected</h1>}>
        <EditTariffFlatRateScheduleModal {...defaultProps} />
      </ErrorBoundary>
    );
    fireEvent.change(screen.getByLabelText('Price per kWh (in pence)'), {
      target: { value: '20' },
    });

    fireEvent.click(screen.getByRole('button', { name: 'Save changes' }));

    expect(
      await screen.findByRole('heading', { level: 1, name: 'Error detected' })
    ).toBeInTheDocument();
  });

  it('should autofocus the first available input', () => {
    render(<EditTariffFlatRateScheduleModal {...defaultProps} />);
    expect(screen.getByLabelText('Price per kWh (in pence)')).toHaveFocus();
  });
});
