import { Button, ButtonPadding } from '@experience/shared/react/design-system';
import { mutate } from 'swr';
import { useCallback } from 'react';
import { useCookies } from 'react-cookie';
import { useRouter } from 'next/router';

export const DemoModeButton = () => {
  const [, setCookies] = useCookies();
  const router = useRouter();

  const enterDemoMode = useCallback(async () => {
    setCookies('pod-point.demo-mode', 'true', {
      path: '/',
      secure: true,
      sameSite: 'strict',
    });
    await mutate(() => true, null, {});
    await router.push('/');
  }, [router, setCookies]);

  return (
    <Button
      className="whitespace-nowrap"
      onClick={enterDemoMode}
      padding={ButtonPadding.LARGE}
    >
      View example data
    </Button>
  );
};
