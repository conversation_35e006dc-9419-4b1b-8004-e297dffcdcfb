import { Footer } from './footer';
import {
  TEST_BILLING_INFORMATION,
  TEST_USER,
} from '@experience/commercial/site-admin/typescript/domain-model';
import { render, screen } from '@testing-library/react';

const mockUseUser = jest.fn();
const mockUseSubscription = jest.fn();

jest.mock('../providers/user-provider/user-provider', () => ({
  useUser: () => mockUseUser(),
}));

jest.mock('../providers/subscription-provider/subscription-provider', () => ({
  useSubscription: () => mockUseSubscription(),
}));

const publicRuntimeConfig = {
  hasAboutPage: true,
};

jest.mock('next/config', () => () => ({
  publicRuntimeConfig,
}));

describe('footer', () => {
  beforeEach(() => {
    publicRuntimeConfig.hasAboutPage = true;
  });

  it('should render correctly', () => {
    mockUseUser.mockReturnValueOnce(TEST_USER);
    mockUseSubscription.mockReturnValueOnce(
      TEST_BILLING_INFORMATION.subscription
    );

    const { baseElement } = render(<Footer />);
    expect(baseElement).toBeInTheDocument();
  });

  it('should match snapshot', () => {
    mockUseUser.mockReturnValueOnce(TEST_USER);
    mockUseSubscription.mockReturnValueOnce(
      TEST_BILLING_INFORMATION.subscription
    );

    const { baseElement } = render(<Footer />);
    expect(baseElement).toMatchSnapshot();
  });

  it('should show help page when has about page is true in public runtime config', () => {
    publicRuntimeConfig.hasAboutPage = true;

    mockUseUser.mockReturnValueOnce(TEST_USER);
    mockUseSubscription.mockReturnValueOnce(
      TEST_BILLING_INFORMATION.subscription
    );

    render(<Footer />);

    expect(screen.getByRole('link', { name: 'Help' })).toBeInTheDocument();
  });

  it('should not show help page link when has about page is false in public runtime config', () => {
    publicRuntimeConfig.hasAboutPage = false;

    mockUseUser.mockReturnValueOnce(TEST_USER);
    mockUseSubscription.mockReturnValueOnce(
      TEST_BILLING_INFORMATION.subscription
    );

    render(<Footer />);

    expect(
      screen.queryByRole('link', { name: 'Help' })
    ).not.toBeInTheDocument();
  });

  it('should show terms and conditions link when user has accepted latest terms', () => {
    mockUseUser.mockReturnValueOnce({
      ...TEST_USER,
      termsAndConditions: {
        acceptedVersion: '2025-03-30',
        currentVersion: '2025-03-30',
      },
    });
    mockUseSubscription.mockReturnValueOnce(
      TEST_BILLING_INFORMATION.subscription
    );

    render(<Footer />);

    expect(
      screen.getByRole('link', { name: 'Terms and conditions' })
    ).toBeInTheDocument();
  });

  it('should not show terms and conditions link when user has not accepted latest terms', () => {
    mockUseUser.mockReturnValueOnce({
      ...TEST_USER,
      termsAndConditions: {
        acceptedVersion: undefined,
        currentVersion: '2025-03-30',
      },
    });
    mockUseSubscription.mockReturnValueOnce(
      TEST_BILLING_INFORMATION.subscription
    );

    render(<Footer />);

    expect(
      screen.queryByRole('link', { name: 'Terms and conditions' })
    ).not.toBeInTheDocument();
  });
});
