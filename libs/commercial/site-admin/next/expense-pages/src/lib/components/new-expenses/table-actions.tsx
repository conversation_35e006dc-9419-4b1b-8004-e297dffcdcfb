import { ExpensableCharge } from '@experience/shared/axios/data-platform-api-client';
import {
  MenuButton,
  ToastMessageWithHeading,
} from '@experience/shared/react/design-system';
import { mutate } from 'swr';
import { newExpenseSummaryTransformer } from '../../transformers/new-expense.transformer';
import { useBoundStore } from '../services/store';
import { useRouter } from 'next/navigation';
import { useShallow } from 'zustand/react/shallow';
import { useState } from 'react';
import ProcessExpensesModal from '../modals/process-expenses-modal/process-expenses-modal';
import downloadCsv from 'json-to-csv-export';
import pluralize from 'pluralize';
import toast from 'react-hot-toast';

interface NewExpensesTableActionsProps {
  data: ExpensableCharge[];
}

export const NewExpensesTableActions = ({
  data: expenses,
}: NewExpensesTableActionsProps) => {
  const router = useRouter();
  const [openProcessExpensesModal, setOpenProcessExpensesModal] =
    useState(false);
  const [chargeIds, setChargeIds] = useState([]);
  const [currentTabId, tabStore, setTabStore] = useBoundStore(
    useShallow((state) => [
      state.currentTabId,
      state.tabStore,
      state.setTabStore,
    ])
  );

  const handleExportAll = () => {
    downloadCsv({
      filename: 'All new expenses',
      delimiter: ',',
      data: expenses.map((charge: ExpensableCharge) =>
        newExpenseSummaryTransformer.toCsv(charge)
      ),
    });
  };

  const handleExportSelected = () => {
    downloadCsv({
      filename: 'New expenses',
      delimiter: ',',
      data: expenses
        .filter((charge: ExpensableCharge) =>
          tabStore.get(currentTabId)?.selectedChargeIds.includes(charge.id)
        )
        .map((charge: ExpensableCharge) =>
          newExpenseSummaryTransformer.toCsv(charge)
        ),
    });
  };

  const handleMarkAsProcessed = () => {
    setChargeIds(tabStore.get(currentTabId)?.selectedChargeIds);
    setOpenProcessExpensesModal(true);
  };

  const handleMarkAsProcessedSuccessful = async () => {
    setTabStore(currentTabId, {
      selectedDriverIds: [],
      selectedChargeIds: [],
      searchTerm: '',
    });

    toast.success(
      <ToastMessageWithHeading
        heading="Expenses processed"
        message={`The expenses for ${pluralize(
          'charge',
          chargeIds.length,
          true
        )} have been processed.`}
      />,
      { duration: 5000 }
    );

    await mutate(
      (key) => typeof key === 'string' && key.startsWith('/api/expenses')
    );
    router.push('/expenses');
  };

  return (
    <>
      <MenuButton title="Actions">
        <MenuButton.Item onClick={handleExportAll}>
          Export all as CSV
        </MenuButton.Item>
        {tabStore.get(currentTabId)?.selectedChargeIds?.length > 0 ? (
          <>
            <MenuButton.Item onClick={handleExportSelected}>
              Export selected as CSV
            </MenuButton.Item>
            <MenuButton.Item onClick={handleMarkAsProcessed}>
              Mark as processed
            </MenuButton.Item>
          </>
        ) : null}
      </MenuButton>
      <ProcessExpensesModal
        chargeIds={chargeIds}
        driver={expenses[0]?.driver}
        open={openProcessExpensesModal}
        setOpen={setOpenProcessExpensesModal}
        onSuccess={handleMarkAsProcessedSuccessful}
      />
    </>
  );
};
