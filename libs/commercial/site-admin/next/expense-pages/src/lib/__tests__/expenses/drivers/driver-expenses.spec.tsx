import { TEST_DRIVER } from '@experience/shared/axios/data-platform-api-client/fixtures';
import {
  TEST_DRIVER_EXPENSE,
  TEST_HOME_ADDRESS,
  TEST_HOME_LOCATION,
  TEST_PUBLIC_LOCATION,
  TEST_SUBMITTED_HOME_CHARGE,
  TEST_SUBMITTED_PUBLIC_CHARGE,
} from '@experience/shared/axios/data-platform-api-client/fixtures';
import {
  fireEvent,
  render,
  screen,
  waitFor,
  within,
} from '@testing-library/react';
import DriverExpensesPage from '../../../expenses/drivers/[id]';
import useSWR, { SWRResponse } from 'swr';

const mockRouter = jest.fn();
jest.mock('next/navigation', () => ({
  useRouter: () => mockRouter(),
  useParams: () => ({
    id: 1,
  }),
}));

jest.mock('swr');
const mockedUseSWR = jest.mocked(useSWR);

const mockData = TEST_DRIVER_EXPENSE;

describe('DriverExpensesPage', () => {
  beforeAll(() => {
    mockedUseSWR.mockReturnValue({
      data: mockData,
      error: null,
    } as SWRResponse);
  });

  beforeEach(() => {
    jest.mocked(useSWR).mockImplementation(
      () =>
        ({
          data: TEST_DRIVER_EXPENSE,
          error: null,
        } as SWRResponse)
    );
  });

  it('should render successfully', () => {
    const { baseElement } = render(<DriverExpensesPage />);
    expect(baseElement).toBeTruthy();
  });

  it('should match snapshot', () => {
    const { baseElement } = render(<DriverExpensesPage />);
    expect(baseElement).toMatchSnapshot();
  });

  it('should return the collated total results', () => {
    render(<DriverExpensesPage />);

    expect(
      screen.getByRole('heading', { level: 1, name: 'Jane Bloggs' })
    ).toBeInTheDocument();
    expect(
      screen.getByText('Expenses submitted by this driver.')
    ).toBeInTheDocument();
    expect(screen.getByText('Home Charges')).toBeInTheDocument();
    expect(screen.getByText('Public Charges')).toBeInTheDocument();
    expect(screen.getByText('£1.23')).toBeInTheDocument();
    expect(screen.getByText('£3.21')).toBeInTheDocument();
    expect(screen.getByText('135 kWh')).toBeInTheDocument();
    expect(screen.getByText('531 kWh')).toBeInTheDocument();
  });

  it('should return the full results set by default', () => {
    render(<DriverExpensesPage />);

    expect(
      screen.getByRole('heading', { level: 1, name: 'Jane Bloggs' })
    ).toBeInTheDocument();
    expect(
      screen.getByText('Expenses submitted by this driver.')
    ).toBeInTheDocument();

    expect(screen.getByText('16 Sep 2022 - 14:58')).toBeInTheDocument();
    expect(screen.getByText('16 Sep 2022 - 16:58')).toBeInTheDocument();
    expect(screen.getByText('£70.12')).toBeInTheDocument();
    expect(screen.getByText('12.5 kWh')).toBeInTheDocument();
    expect(screen.getByText('Kate-Jane')).toBeInTheDocument();
    expect(screen.getByText('public')).toBeInTheDocument();
    expect(screen.getByText('John Wick')).toBeInTheDocument();

    expect(screen.getByText('09 Aug 2022 - 01:58')).toBeInTheDocument();
    expect(screen.getByText('09 Oct 2022 - 08:01')).toBeInTheDocument();
    expect(screen.getByText('£70.12')).toBeInTheDocument();
    expect(screen.getByText('£123.32')).toBeInTheDocument();
    expect(screen.getByText('43.5 kWh')).toBeInTheDocument();
    expect(screen.getByText('PSL123456')).toBeInTheDocument();
    expect(screen.getByText('home')).toBeInTheDocument();
    expect(screen.getByText('Alan Smith')).toBeInTheDocument();
  });

  it('should return the full results ordered by start time newest first by default', () => {
    render(<DriverExpensesPage />);

    const rows = screen.getAllByRole('row');

    expect(
      within(rows[1]).queryByText('16 Sep 2022 - 14:58')
    ).toBeInTheDocument();
    expect(
      within(rows[1]).queryByText('16 Sep 2022 - 16:58')
    ).toBeInTheDocument();
    expect(within(rows[1]).queryByText('£70.12')).toBeInTheDocument();
    expect(within(rows[1]).queryByText('12.5 kWh')).toBeInTheDocument();
    expect(within(rows[1]).queryByText('Kate-Jane')).toBeInTheDocument();
    expect(within(rows[1]).queryByText('public')).toBeInTheDocument();
    expect(within(rows[1]).queryByText('John Wick')).toBeInTheDocument();

    expect(
      within(rows[2]).queryByText('09 Aug 2022 - 01:58')
    ).toBeInTheDocument();
    expect(
      within(rows[2]).queryByText('09 Oct 2022 - 08:01')
    ).toBeInTheDocument();
    expect(within(rows[2]).queryByText('£123.32')).toBeInTheDocument();
    expect(within(rows[2]).queryByText('43.5 kWh')).toBeInTheDocument();
    expect(within(rows[2]).queryByText('PSL123456')).toBeInTheDocument();
    expect(within(rows[2]).queryByText('home')).toBeInTheDocument();
    expect(within(rows[2]).queryByText('Alan Smith')).toBeInTheDocument();
  });

  it.each([
    TEST_HOME_LOCATION.locationType,
    TEST_HOME_ADDRESS.prettyPrint,
    TEST_SUBMITTED_HOME_CHARGE.chargerName,
    TEST_SUBMITTED_HOME_CHARGE.processedByFullName,
  ])(
    'should filter the results if a matching term is used (%s)',
    async (searchTerm) => {
      render(<DriverExpensesPage />);

      fireEvent.change(screen.getByRole('searchbox'), {
        target: { value: searchTerm },
      });

      await waitFor(() => {
        expect(
          screen.getByRole('heading', { level: 1, name: TEST_DRIVER.fullName })
        ).toBeInTheDocument();
        expect(screen.getByText('Showing 1 of 2 results.')).toBeInTheDocument();

        expect(
          screen.getByText(TEST_HOME_LOCATION.locationType)
        ).toBeInTheDocument();
        expect(
          screen.getByText(TEST_SUBMITTED_HOME_CHARGE.chargerName)
        ).toBeInTheDocument();
        expect(
          screen.getByText(
            TEST_SUBMITTED_HOME_CHARGE.processedByFullName as string
          )
        ).toBeInTheDocument();

        expect(
          screen.queryByText(
            TEST_SUBMITTED_PUBLIC_CHARGE.processedByFullName as string
          )
        ).not.toBeInTheDocument();
        expect(
          screen.queryByText(TEST_PUBLIC_LOCATION.locationType)
        ).not.toBeInTheDocument();
        expect(
          screen.queryByText(
            TEST_SUBMITTED_PUBLIC_CHARGE.processedByFullName as string
          )
        ).not.toBeInTheDocument();
      });
    }
  );

  it('should show no results if a non-matching term is used', async () => {
    render(<DriverExpensesPage />);

    fireEvent.change(screen.getByRole('searchbox'), {
      target: { value: 'Homer' },
    });

    await waitFor(() => {
      expect(
        screen.getByRole('heading', { level: 1, name: 'Jane Bloggs' })
      ).toBeInTheDocument();
      expect(screen.getByText('Showing 0 of 2 results.')).toBeInTheDocument();
      expect(screen.queryByText('Kate-Jane')).not.toBeInTheDocument();
      expect(screen.queryByText('PSL123456')).not.toBeInTheDocument();
    });
  });

  it('should show all results if clear search is pressed', async () => {
    render(<DriverExpensesPage />);

    fireEvent.change(screen.getByRole('searchbox'), {
      target: { value: 'Kate' },
    });

    await waitFor(() => {
      expect(
        screen.getByRole('heading', { level: 1, name: 'Jane Bloggs' })
      ).toBeInTheDocument();
      expect(screen.getByText('Showing 1 of 2 results.')).toBeInTheDocument();

      expect(screen.getByText('Kate-Jane')).toBeInTheDocument();
    });

    fireEvent.click(screen.getByRole('button', { name: 'Clear search' }));

    await waitFor(() => {
      expect(
        screen.getByText('Expenses submitted by this driver.')
      ).toBeInTheDocument();

      expect(screen.getByText('Kate-Jane')).toBeInTheDocument();
      expect(screen.queryByText('PSL123456')).toBeInTheDocument();
    });
  });

  it('should render error message if needed', () => {
    mockedUseSWR.mockReturnValue({
      data: [],
      error: true,
    } as SWRResponse);
    render(<DriverExpensesPage />);

    expect(screen.getByText('Something went wrong')).toBeInTheDocument();
  });
});
