import { createZodDto } from 'nestjs-zod';
import { z } from 'zod';

export const CdrCreatedEventPayloadSchema = z.object({
  cdr: z.object({
    cdr_location: z.object({
      evse_uid: z.string().max(36),
    }),
    cdr_token: z.object({
      uid: z.string().max(36),
    }),
    id: z.string().max(39),
    total_cost: z.object({
      incl_vat: z.number().nonnegative(),
    }),
  }),
  connector: z.object({
    max_electric_power: z.number().int(),
  }),
  location: z.object({
    owner: z.object({
      name: z.string(),
    }),
  }),
});

export class CdrCreatedEventPayload extends createZodDto(
  CdrCreatedEventPayloadSchema
) {}

export const CdrCreatedEventSchema = z.object({
  routingKey: z.string(),
  type: z.string(),
  eventId: z.string(),
  publishedAt: z.string(),
  data: CdrCreatedEventPayloadSchema,
});

export class CdrCreated<PERSON>vent extends createZodDto(CdrCreatedEventSchema) {}
