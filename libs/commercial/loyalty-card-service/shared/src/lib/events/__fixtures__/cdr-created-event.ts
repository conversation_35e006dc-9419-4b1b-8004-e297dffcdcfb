import { CdrCreatedEvent } from '../cdr-created-event';
import { MOCK_TESCO_CLUBCARD } from '../../dto/__fixtures__/tesco-clubcard.dto';

export const TEST_CDR_CREATED_EVENT: CdrCreatedEvent = {
  routingKey: 'OCPI.CDR.Created.PG-12345',
  type: 'OCPI.CDR.Created',
  eventId: '728ace20-063e-4c77-bac4-c868ce3db68e',
  publishedAt: '2025-04-24T13:18:47Z',
  data: {
    cdr: {
      cdr_location: {
        evse_uid: 'GB*POD*E*PG12345E1',
      },
      cdr_token: {
        uid: MOCK_TESCO_CLUBCARD.authId,
      },
      id: '594e0e77-7c5f-40df-be39-cc82d87bd12a',
      total_cost: {
        incl_vat: 12.34,
      },
    },
    connector: {
      max_electric_power: 7000,
    },
    location: {
      owner: {
        name: 'Tesco Stores Ltd',
      },
    },
  },
};
