import { ConfigModule } from '@nestjs/config';
import { LoyaltyCardPrismaClient } from '@experience/commercial/loyalty-card-service/prisma/loyalty-card/client';
import { MOCK_TESCO_CLUBCARD } from '@experience/commercial/loyalty-card-service/shared/specs';
import { PrismaHealthIndicator } from '@nestjs/terminus';
import { StoredValueService } from './stored-value-service';
import { TescoClubcardPointsNotCreditedException } from '@experience/commercial/loyalty-card-service/shared';
import { Test, TestingModule } from '@nestjs/testing';

const MOCK_CREDIT_ID = '5b4723d2-173d-4221-b109-fda91e1861de';

describe('stored value service', () => {
  let service: StoredValueService;
  let database: LoyaltyCardPrismaClient;

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      imports: [
        await ConfigModule.forRoot({
          load: [
            () => ({
              TESCO_OIDC_CLIENT_ID: '01a10e91-76e8-4abc-a534-8510417d6bbd',
              TESCO_OIDC_CLIENT_SECRET: 'C5722384D8576EDCC8274BD5CB8FB',
              TESCO_API_BASE_URL: 'https://api-test.tesco.com',
              TESCO_RETAIL_API_BASE_URL: 'https://api.test.retail.tesco.com',
            }),
          ],
        }),
      ],
      providers: [
        LoyaltyCardPrismaClient,
        PrismaHealthIndicator,
        StoredValueService,
      ],
    }).compile();

    service = module.get<StoredValueService>(StoredValueService);
    database = module.get<LoyaltyCardPrismaClient>(LoyaltyCardPrismaClient);
  });

  it('should be defined', () => {
    expect(service).toBeDefined();
  });

  it('should credit points', async () => {
    const mockFetch = jest
      .spyOn(global, 'fetch')
      .mockResolvedValueOnce({
        ok: true,
        json: jest.fn().mockResolvedValueOnce({
          access_token: '444DC6D9DE1BE6DAFEB6985F5CEDC',
        }),
        status: 200,
      } as unknown as Response)
      .mockResolvedValueOnce({
        ok: true,
        json: jest.fn().mockResolvedValueOnce({}),
        status: 200,
      } as unknown as Response);
    const mockUpsert = (database.tescoClubcardAudit.upsert = jest.fn());

    await service.creditPoints({
      clubcard: MOCK_TESCO_CLUBCARD,
      creditId: MOCK_CREDIT_ID,
      points: 42,
    });

    expect(mockFetch).toHaveBeenNthCalledWith(
      1,
      'https://api-test.tesco.com/identity/v4/issue-token/token',
      {
        headers: {
          Accept: 'application/json',
          Authorization:
            'Basic MDFhMTBlOTEtNzZlOC00YWJjLWE1MzQtODUxMDQxN2Q2YmJkOkM1NzIyMzg0RDg1NzZFRENDODI3NEJENUNCOEZC',
          'Content-Type': 'application/x-www-form-urlencoded',
        },
        method: 'POST',
        body: new URLSearchParams({ grant_type: 'client_credentials' }),
      }
    );
    expect(mockFetch).toHaveBeenNthCalledWith(
      2,
      'https://api.test.retail.tesco.com/storedvalue/points' +
        `/customer/trn:tesco:uid:uuid:${MOCK_TESCO_CLUBCARD.customerId}` +
        '/scheme/UKClubcard' +
        '/credit/5b4723d2-173d-4221-b109-fda91e1861de',
      {
        headers: {
          Accept: 'application/json',
          Authorization: 'Bearer 444DC6D9DE1BE6DAFEB6985F5CEDC',
          'Content-Type': 'application/json',
          traceId: expect.any(String),
        },
        method: 'PUT',
        body: JSON.stringify({
          funder: 'Pod Point Charging',
          points: 42,
        }),
      }
    );
    expect(mockUpsert).toHaveBeenCalledWith({
      create: {
        authId: MOCK_TESCO_CLUBCARD.authId,
        creditId: MOCK_CREDIT_ID,
        customerId: MOCK_TESCO_CLUBCARD.customerId,
        points: 42,
      },
      update: {},
      where: {
        creditId: MOCK_CREDIT_ID,
      },
    });
  });

  it('should propagate errors when obtaining access token', async () => {
    const mockFetch = jest.spyOn(global, 'fetch').mockResolvedValueOnce({
      ok: false,
      json: jest.fn().mockResolvedValueOnce({}),
      status: 500,
    } as unknown as Response);
    const mockUpsert = (database.tescoClubcardAudit.upsert = jest.fn());

    await expect(
      service.creditPoints({
        clubcard: MOCK_TESCO_CLUBCARD,
        creditId: MOCK_CREDIT_ID,
        points: 42,
      })
    ).rejects.toThrow(new TescoClubcardPointsNotCreditedException());

    expect(mockFetch).toHaveBeenCalledTimes(1);
    expect(mockUpsert).not.toHaveBeenCalled();
  });

  it('should handle account opted out error when crediting points', async () => {
    const mockFetch = jest
      .spyOn(global, 'fetch')
      .mockResolvedValueOnce({
        ok: true,
        json: jest.fn().mockResolvedValueOnce({
          access_token: '444DC6D9DE1BE6DAFEB6985F5CEDC',
        }),
        status: 200,
      } as unknown as Response)
      .mockResolvedValueOnce({
        ok: false,
        json: jest.fn().mockResolvedValueOnce({
          code: 'conflict',
          details: 'ACCOUNT_OPTED_OUT',
          reference: 'ACCOUNT_OPTED_OUT',
        }),
        status: 409,
      } as unknown as Response);
    const mockUpsert = (database.tescoClubcardAudit.upsert = jest.fn());

    await service.creditPoints({
      clubcard: MOCK_TESCO_CLUBCARD,
      creditId: MOCK_CREDIT_ID,
      points: 42,
    });

    expect(mockFetch).toHaveBeenCalledTimes(2);
    expect(mockUpsert).not.toHaveBeenCalled();
  });

  it('should handle account not error when crediting points', async () => {
    const mockFetch = jest
      .spyOn(global, 'fetch')
      .mockResolvedValueOnce({
        ok: true,
        json: jest.fn().mockResolvedValueOnce({
          access_token: '444DC6D9DE1BE6DAFEB6985F5CEDC',
        }),
        status: 200,
      } as unknown as Response)
      .mockResolvedValueOnce({
        ok: false,
        json: jest.fn().mockResolvedValueOnce({
          code: 'no_account',
          details: "Account doesn't exist",
          reference: "Account doesn't exist",
        }),
        status: 404,
      } as unknown as Response);
    const mockUpsert = (database.tescoClubcardAudit.upsert = jest.fn());

    await service.creditPoints({
      clubcard: MOCK_TESCO_CLUBCARD,
      creditId: MOCK_CREDIT_ID,
      points: 42,
    });

    expect(mockFetch).toHaveBeenCalledTimes(2);
    expect(mockUpsert).not.toHaveBeenCalled();
  });

  it('should propagate errors when crediting points', async () => {
    const mockFetch = jest
      .spyOn(global, 'fetch')
      .mockResolvedValueOnce({
        ok: true,
        json: jest.fn().mockResolvedValueOnce({
          access_token: '444DC6D9DE1BE6DAFEB6985F5CEDC',
        }),
        status: 200,
      } as unknown as Response)
      .mockResolvedValueOnce({
        ok: false,
        json: jest.fn().mockResolvedValueOnce({}),
        status: 500,
      } as unknown as Response);
    const mockUpsert = (database.tescoClubcardAudit.upsert = jest.fn());

    await expect(
      service.creditPoints({
        clubcard: MOCK_TESCO_CLUBCARD,
        creditId: MOCK_CREDIT_ID,
        points: 42,
      })
    ).rejects.toThrow(new TescoClubcardPointsNotCreditedException());

    expect(mockFetch).toHaveBeenCalledTimes(2);
    expect(mockUpsert).not.toHaveBeenCalled();
  });
});
