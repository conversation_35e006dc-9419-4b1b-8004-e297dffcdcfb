import { HttpInterceptor } from '@experience/shared/nest/utils';
import { Injectable } from '@nestjs/common';
import {
  TescoClubcardErrorCodes,
  TescoClubcardNotFoundException,
} from '@experience/commercial/loyalty-card-service/shared';

@Injectable()
export class TescoClubcardInterceptor extends HttpInterceptor {
  constructor() {
    super([
      {
        code: TescoClubcardErrorCodes.NOT_FOUND,
        name: TescoClubcardNotFoundException,
        statusCode: 404,
      },
    ]);
  }
}
