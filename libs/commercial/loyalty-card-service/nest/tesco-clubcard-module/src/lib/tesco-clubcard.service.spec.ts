import * as MockDate from 'mockdate';
import { ConfigService } from '@nestjs/config';
import { LoyaltyCardPrismaClient } from '@experience/commercial/loyalty-card-service/prisma/loyalty-card/client';
import {
  MOCK_CREATE_TESCO_CLUBCARD_REQUEST,
  MOCK_TESCO_CLUBCARD,
} from '@experience/commercial/loyalty-card-service/shared/specs';
import { MOCK_TESCO_CLUBCARD_ENTITY } from '@experience/commercial/loyalty-card-service/prisma/loyalty-card/client/specs';
import { PrismaHealthIndicator } from '@nestjs/terminus';
import { TescoClubcardNotFoundException } from '@experience/commercial/loyalty-card-service/shared';
import { TescoClubcardService } from './tesco-clubcard.service';
import { Test, TestingModule } from '@nestjs/testing';

describe('tesco service', () => {
  let service: TescoClubcardService;
  let database: LoyaltyCardPrismaClient;

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        ConfigService,
        LoyaltyCardPrismaClient,
        PrismaHealthIndicator,
        TescoClubcardService,
      ],
    }).compile();

    service = module.get<TescoClubcardService>(TescoClubcardService);
    database = module.get<LoyaltyCardPrismaClient>(LoyaltyCardPrismaClient);

    MockDate.set('2023-10-01T00:00:00Z');
  });

  afterEach(() => {
    MockDate.reset();
  });

  it('should be defined', () => {
    expect(service).toBeDefined();
    expect(database).toBeDefined();
  });

  it('should create a tesco clubcard', async () => {
    const mockUpsert = (database.tescoClubcard.upsert = jest.fn());

    await service.create(MOCK_CREATE_TESCO_CLUBCARD_REQUEST);

    expect(mockUpsert).toHaveBeenCalledWith({
      create: {
        ...MOCK_CREATE_TESCO_CLUBCARD_REQUEST,
      },
      update: {
        customerId: MOCK_CREATE_TESCO_CLUBCARD_REQUEST.customerId,
        deletedAt: null,
      },
      where: {
        authId: MOCK_CREATE_TESCO_CLUBCARD_REQUEST.authId,
      },
    });
  });

  it('should get a tesco clubcard', async () => {
    const mockFindUnique = (database.tescoClubcard.findUnique = jest.fn());

    mockFindUnique.mockResolvedValue(MOCK_TESCO_CLUBCARD_ENTITY);

    const result = await service.get(MOCK_TESCO_CLUBCARD.authId);

    expect(result).toEqual(MOCK_TESCO_CLUBCARD);
    expect(mockFindUnique).toHaveBeenCalledWith({
      where: { authId: MOCK_TESCO_CLUBCARD.authId, deletedAt: null },
    });
  });

  it('should throw an error if tesco clubcard is not found', async () => {
    database.tescoClubcard.findUnique = jest.fn().mockResolvedValueOnce(null);

    await expect(service.get(MOCK_TESCO_CLUBCARD.authId)).rejects.toThrow(
      new TescoClubcardNotFoundException()
    );
  });

  it('should soft delete a tesco clubcard', async () => {
    const mockSoftDelete = (database.tescoClubcard.updateMany = jest.fn());

    await service.delete(MOCK_TESCO_CLUBCARD.authId);

    expect(mockSoftDelete).toHaveBeenCalledWith({
      data: { deletedAt: new Date('2023-10-01T00:00:00.000Z') },
      where: {
        authId: MOCK_TESCO_CLUBCARD.authId,
      },
    });
  });
});
