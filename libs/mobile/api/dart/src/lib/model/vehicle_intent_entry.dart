//
// AUTO-GENERATED FILE, DO NOT MODIFY!
//
// @dart=2.18

// ignore_for_file: unused_element, unused_import
// ignore_for_file: always_put_required_named_parameters_first
// ignore_for_file: constant_identifier_names
// ignore_for_file: lines_longer_than_80_chars

part of openapi.api;

class VehicleIntentEntry {
  /// Returns a new [VehicleIntentEntry] instance.
  VehicleIntentEntry({
    required this.chargeByTime,
    required this.chargeKWh,
    required this.dayOfWeek,
  });

  String chargeByTime;

  num chargeKWh;

  VehicleIntentEntryDayOfWeekEnum dayOfWeek;

  @override
  bool operator ==(Object other) => identical(this, other) || other is VehicleIntentEntry &&
    other.chargeByTime == chargeByTime &&
    other.chargeKWh == chargeKWh &&
    other.dayOfWeek == dayOfWeek;

  @override
  int get hashCode =>
    // ignore: unnecessary_parenthesis
    (chargeByTime.hashCode) +
    (chargeKWh.hashCode) +
    (dayOfWeek.hashCode);

  @override
  String toString() => 'VehicleIntentEntry[chargeByTime=$chargeByTime, chargeKWh=$chargeKWh, dayOfWeek=$dayOfWeek]';

  Map<String, dynamic> toJson() {
    final json = <String, dynamic>{};
      json[r'chargeByTime'] = this.chargeByTime;
      json[r'chargeKWh'] = this.chargeKWh;
      json[r'dayOfWeek'] = this.dayOfWeek;
    return json;
  }

  /// Returns a new [VehicleIntentEntry] instance and imports its values from
  /// [value] if it's a [Map], null otherwise.
  // ignore: prefer_constructors_over_static_methods
  static VehicleIntentEntry? fromJson(dynamic value) {
    if (value is Map) {
      final json = value.cast<String, dynamic>();

      // Ensure that the map contains the required keys.
      // Note 1: the values aren't checked for validity beyond being non-null.
      // Note 2: this code is stripped in release mode!
      assert(() {
        requiredKeys.forEach((key) {
          assert(json.containsKey(key), 'Required key "VehicleIntentEntry[$key]" is missing from JSON.');
          assert(json[key] != null, 'Required key "VehicleIntentEntry[$key]" has a null value in JSON.');
        });
        return true;
      }());

      return VehicleIntentEntry(
        chargeByTime: mapValueOfType<String>(json, r'chargeByTime')!,
        chargeKWh: num.parse('${json[r'chargeKWh']}'),
        dayOfWeek: VehicleIntentEntryDayOfWeekEnum.fromJson(json[r'dayOfWeek'])!,
      );
    }
    return null;
  }

  static List<VehicleIntentEntry> listFromJson(dynamic json, {bool growable = false,}) {
    final result = <VehicleIntentEntry>[];
    if (json is List && json.isNotEmpty) {
      for (final row in json) {
        final value = VehicleIntentEntry.fromJson(row);
        if (value != null) {
          result.add(value);
        }
      }
    }
    return result.toList(growable: growable);
  }

  static Map<String, VehicleIntentEntry> mapFromJson(dynamic json) {
    final map = <String, VehicleIntentEntry>{};
    if (json is Map && json.isNotEmpty) {
      json = json.cast<String, dynamic>(); // ignore: parameter_assignments
      for (final entry in json.entries) {
        final value = VehicleIntentEntry.fromJson(entry.value);
        if (value != null) {
          map[entry.key] = value;
        }
      }
    }
    return map;
  }

  // maps a json object with a list of VehicleIntentEntry-objects as value to a dart map
  static Map<String, List<VehicleIntentEntry>> mapListFromJson(dynamic json, {bool growable = false,}) {
    final map = <String, List<VehicleIntentEntry>>{};
    if (json is Map && json.isNotEmpty) {
      // ignore: parameter_assignments
      json = json.cast<String, dynamic>();
      for (final entry in json.entries) {
        map[entry.key] = VehicleIntentEntry.listFromJson(entry.value, growable: growable,);
      }
    }
    return map;
  }

  /// The list of required keys that must be present in a JSON.
  static const requiredKeys = <String>{
    'chargeByTime',
    'chargeKWh',
    'dayOfWeek',
  };
}


class VehicleIntentEntryDayOfWeekEnum {
  /// Instantiate a new enum with the provided [value].
  const VehicleIntentEntryDayOfWeekEnum._(this.value);

  /// The underlying value of this enum member.
  final String value;

  @override
  String toString() => value;

  String toJson() => value;

  static const MONDAY = VehicleIntentEntryDayOfWeekEnum._(r'MONDAY');
  static const TUESDAY = VehicleIntentEntryDayOfWeekEnum._(r'TUESDAY');
  static const WEDNESDAY = VehicleIntentEntryDayOfWeekEnum._(r'WEDNESDAY');
  static const THURSDAY = VehicleIntentEntryDayOfWeekEnum._(r'THURSDAY');
  static const FRIDAY = VehicleIntentEntryDayOfWeekEnum._(r'FRIDAY');
  static const SATURDAY = VehicleIntentEntryDayOfWeekEnum._(r'SATURDAY');
  static const SUNDAY = VehicleIntentEntryDayOfWeekEnum._(r'SUNDAY');

  /// List of all possible values in this [enum][VehicleIntentEntryDayOfWeekEnum].
  static const values = <VehicleIntentEntryDayOfWeekEnum>[
    MONDAY,
    TUESDAY,
    WEDNESDAY,
    THURSDAY,
    FRIDAY,
    SATURDAY,
    SUNDAY,
  ];

  static VehicleIntentEntryDayOfWeekEnum? fromJson(dynamic value) => VehicleIntentEntryDayOfWeekEnumTypeTransformer().decode(value);

  static List<VehicleIntentEntryDayOfWeekEnum> listFromJson(dynamic json, {bool growable = false,}) {
    final result = <VehicleIntentEntryDayOfWeekEnum>[];
    if (json is List && json.isNotEmpty) {
      for (final row in json) {
        final value = VehicleIntentEntryDayOfWeekEnum.fromJson(row);
        if (value != null) {
          result.add(value);
        }
      }
    }
    return result.toList(growable: growable);
  }
}

/// Transformation class that can [encode] an instance of [VehicleIntentEntryDayOfWeekEnum] to String,
/// and [decode] dynamic data back to [VehicleIntentEntryDayOfWeekEnum].
class VehicleIntentEntryDayOfWeekEnumTypeTransformer {
  factory VehicleIntentEntryDayOfWeekEnumTypeTransformer() => _instance ??= const VehicleIntentEntryDayOfWeekEnumTypeTransformer._();

  const VehicleIntentEntryDayOfWeekEnumTypeTransformer._();

  String encode(VehicleIntentEntryDayOfWeekEnum data) => data.value;

  /// Decodes a [dynamic value][data] to a VehicleIntentEntryDayOfWeekEnum.
  ///
  /// If [allowNull] is true and the [dynamic value][data] cannot be decoded successfully,
  /// then null is returned. However, if [allowNull] is false and the [dynamic value][data]
  /// cannot be decoded successfully, then an [UnimplementedError] is thrown.
  ///
  /// The [allowNull] is very handy when an API changes and a new enum value is added or removed,
  /// and users are still using an old app with the old code.
  VehicleIntentEntryDayOfWeekEnum? decode(dynamic data, {bool allowNull = true}) {
    if (data != null) {
      switch (data) {
        case r'MONDAY': return VehicleIntentEntryDayOfWeekEnum.MONDAY;
        case r'TUESDAY': return VehicleIntentEntryDayOfWeekEnum.TUESDAY;
        case r'WEDNESDAY': return VehicleIntentEntryDayOfWeekEnum.WEDNESDAY;
        case r'THURSDAY': return VehicleIntentEntryDayOfWeekEnum.THURSDAY;
        case r'FRIDAY': return VehicleIntentEntryDayOfWeekEnum.FRIDAY;
        case r'SATURDAY': return VehicleIntentEntryDayOfWeekEnum.SATURDAY;
        case r'SUNDAY': return VehicleIntentEntryDayOfWeekEnum.SUNDAY;
        default:
          if (!allowNull) {
            throw ArgumentError('Unknown enum value to decode: $data');
          }
      }
    }
    return null;
  }

  /// Singleton [VehicleIntentEntryDayOfWeekEnumTypeTransformer] instance.
  static VehicleIntentEntryDayOfWeekEnumTypeTransformer? _instance;
}


