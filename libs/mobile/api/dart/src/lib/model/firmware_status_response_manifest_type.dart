//
// AUTO-GENERATED FILE, DO NOT MODIFY!
//
// @dart=2.18

// ignore_for_file: unused_element, unused_import
// ignore_for_file: always_put_required_named_parameters_first
// ignore_for_file: constant_identifier_names
// ignore_for_file: lines_longer_than_80_chars

part of openapi.api;

class FirmwareStatusResponseManifestType {
  /// Returns a new [FirmwareStatusResponseManifestType] instance.
  FirmwareStatusResponseManifestType({
    required this.architecture,
    this.createdDate,
    required this.details,
    this.manifestId,
    this.status,
  });

  FirmwareStatusResponseManifestTypeArchitectureEnum architecture;

  String? createdDate;

  FirmwareStatusResponseManifestTypeDetails details;

  String? manifestId;

  FirmwareStatusResponseManifestTypeStatusEnum? status;

  @override
  bool operator ==(Object other) => identical(this, other) || other is FirmwareStatusResponseManifestType &&
    other.architecture == architecture &&
    other.createdDate == createdDate &&
    other.details == details &&
    other.manifestId == manifestId &&
    other.status == status;

  @override
  int get hashCode =>
    // ignore: unnecessary_parenthesis
    (architecture.hashCode) +
    (createdDate == null ? 0 : createdDate!.hashCode) +
    (details.hashCode) +
    (manifestId == null ? 0 : manifestId!.hashCode) +
    (status == null ? 0 : status!.hashCode);

  @override
  String toString() => 'FirmwareStatusResponseManifestType[architecture=$architecture, createdDate=$createdDate, details=$details, manifestId=$manifestId, status=$status]';

  Map<String, dynamic> toJson() {
    final json = <String, dynamic>{};
      json[r'architecture'] = this.architecture;
    if (this.createdDate != null) {
      json[r'createdDate'] = this.createdDate;
    } else {
      json[r'createdDate'] = null;
    }
      json[r'details'] = this.details;
    if (this.manifestId != null) {
      json[r'manifestId'] = this.manifestId;
    } else {
      json[r'manifestId'] = null;
    }
    if (this.status != null) {
      json[r'status'] = this.status;
    } else {
      json[r'status'] = null;
    }
    return json;
  }

  /// Returns a new [FirmwareStatusResponseManifestType] instance and imports its values from
  /// [value] if it's a [Map], null otherwise.
  // ignore: prefer_constructors_over_static_methods
  static FirmwareStatusResponseManifestType? fromJson(dynamic value) {
    if (value is Map) {
      final json = value.cast<String, dynamic>();

      // Ensure that the map contains the required keys.
      // Note 1: the values aren't checked for validity beyond being non-null.
      // Note 2: this code is stripped in release mode!
      assert(() {
        requiredKeys.forEach((key) {
          assert(json.containsKey(key), 'Required key "FirmwareStatusResponseManifestType[$key]" is missing from JSON.');
          assert(json[key] != null, 'Required key "FirmwareStatusResponseManifestType[$key]" has a null value in JSON.');
        });
        return true;
      }());

      return FirmwareStatusResponseManifestType(
        architecture: FirmwareStatusResponseManifestTypeArchitectureEnum.fromJson(json[r'architecture'])!,
        createdDate: mapValueOfType<String>(json, r'createdDate'),
        details: FirmwareStatusResponseManifestTypeDetails.fromJson(json[r'details'])!,
        manifestId: mapValueOfType<String>(json, r'manifestId'),
        status: FirmwareStatusResponseManifestTypeStatusEnum.fromJson(json[r'status']),
      );
    }
    return null;
  }

  static List<FirmwareStatusResponseManifestType> listFromJson(dynamic json, {bool growable = false,}) {
    final result = <FirmwareStatusResponseManifestType>[];
    if (json is List && json.isNotEmpty) {
      for (final row in json) {
        final value = FirmwareStatusResponseManifestType.fromJson(row);
        if (value != null) {
          result.add(value);
        }
      }
    }
    return result.toList(growable: growable);
  }

  static Map<String, FirmwareStatusResponseManifestType> mapFromJson(dynamic json) {
    final map = <String, FirmwareStatusResponseManifestType>{};
    if (json is Map && json.isNotEmpty) {
      json = json.cast<String, dynamic>(); // ignore: parameter_assignments
      for (final entry in json.entries) {
        final value = FirmwareStatusResponseManifestType.fromJson(entry.value);
        if (value != null) {
          map[entry.key] = value;
        }
      }
    }
    return map;
  }

  // maps a json object with a list of FirmwareStatusResponseManifestType-objects as value to a dart map
  static Map<String, List<FirmwareStatusResponseManifestType>> mapListFromJson(dynamic json, {bool growable = false,}) {
    final map = <String, List<FirmwareStatusResponseManifestType>>{};
    if (json is Map && json.isNotEmpty) {
      // ignore: parameter_assignments
      json = json.cast<String, dynamic>();
      for (final entry in json.entries) {
        map[entry.key] = FirmwareStatusResponseManifestType.listFromJson(entry.value, growable: growable,);
      }
    }
    return map;
  }

  /// The list of required keys that must be present in a JSON.
  static const requiredKeys = <String>{
    'architecture',
    'details',
  };
}


class FirmwareStatusResponseManifestTypeArchitectureEnum {
  /// Instantiate a new enum with the provided [value].
  const FirmwareStatusResponseManifestTypeArchitectureEnum._(this.value);

  /// The underlying value of this enum member.
  final String value;

  @override
  String toString() => value;

  String toJson() => value;

  static const arch1 = FirmwareStatusResponseManifestTypeArchitectureEnum._(r'arch1');
  static const arch2 = FirmwareStatusResponseManifestTypeArchitectureEnum._(r'arch2');
  static const arch2Period4 = FirmwareStatusResponseManifestTypeArchitectureEnum._(r'arch2.4');
  static const arch3 = FirmwareStatusResponseManifestTypeArchitectureEnum._(r'arch3');
  static const arch5 = FirmwareStatusResponseManifestTypeArchitectureEnum._(r'arch5');

  /// List of all possible values in this [enum][FirmwareStatusResponseManifestTypeArchitectureEnum].
  static const values = <FirmwareStatusResponseManifestTypeArchitectureEnum>[
    arch1,
    arch2,
    arch2Period4,
    arch3,
    arch5,
  ];

  static FirmwareStatusResponseManifestTypeArchitectureEnum? fromJson(dynamic value) => FirmwareStatusResponseManifestTypeArchitectureEnumTypeTransformer().decode(value);

  static List<FirmwareStatusResponseManifestTypeArchitectureEnum> listFromJson(dynamic json, {bool growable = false,}) {
    final result = <FirmwareStatusResponseManifestTypeArchitectureEnum>[];
    if (json is List && json.isNotEmpty) {
      for (final row in json) {
        final value = FirmwareStatusResponseManifestTypeArchitectureEnum.fromJson(row);
        if (value != null) {
          result.add(value);
        }
      }
    }
    return result.toList(growable: growable);
  }
}

/// Transformation class that can [encode] an instance of [FirmwareStatusResponseManifestTypeArchitectureEnum] to String,
/// and [decode] dynamic data back to [FirmwareStatusResponseManifestTypeArchitectureEnum].
class FirmwareStatusResponseManifestTypeArchitectureEnumTypeTransformer {
  factory FirmwareStatusResponseManifestTypeArchitectureEnumTypeTransformer() => _instance ??= const FirmwareStatusResponseManifestTypeArchitectureEnumTypeTransformer._();

  const FirmwareStatusResponseManifestTypeArchitectureEnumTypeTransformer._();

  String encode(FirmwareStatusResponseManifestTypeArchitectureEnum data) => data.value;

  /// Decodes a [dynamic value][data] to a FirmwareStatusResponseManifestTypeArchitectureEnum.
  ///
  /// If [allowNull] is true and the [dynamic value][data] cannot be decoded successfully,
  /// then null is returned. However, if [allowNull] is false and the [dynamic value][data]
  /// cannot be decoded successfully, then an [UnimplementedError] is thrown.
  ///
  /// The [allowNull] is very handy when an API changes and a new enum value is added or removed,
  /// and users are still using an old app with the old code.
  FirmwareStatusResponseManifestTypeArchitectureEnum? decode(dynamic data, {bool allowNull = true}) {
    if (data != null) {
      switch (data) {
        case r'arch1': return FirmwareStatusResponseManifestTypeArchitectureEnum.arch1;
        case r'arch2': return FirmwareStatusResponseManifestTypeArchitectureEnum.arch2;
        case r'arch2.4': return FirmwareStatusResponseManifestTypeArchitectureEnum.arch2Period4;
        case r'arch3': return FirmwareStatusResponseManifestTypeArchitectureEnum.arch3;
        case r'arch5': return FirmwareStatusResponseManifestTypeArchitectureEnum.arch5;
        default:
          if (!allowNull) {
            throw ArgumentError('Unknown enum value to decode: $data');
          }
      }
    }
    return null;
  }

  /// Singleton [FirmwareStatusResponseManifestTypeArchitectureEnumTypeTransformer] instance.
  static FirmwareStatusResponseManifestTypeArchitectureEnumTypeTransformer? _instance;
}



class FirmwareStatusResponseManifestTypeStatusEnum {
  /// Instantiate a new enum with the provided [value].
  const FirmwareStatusResponseManifestTypeStatusEnum._(this.value);

  /// The underlying value of this enum member.
  final String value;

  @override
  String toString() => value;

  String toJson() => value;

  static const candidate = FirmwareStatusResponseManifestTypeStatusEnum._(r'candidate');
  static const alpha = FirmwareStatusResponseManifestTypeStatusEnum._(r'alpha');
  static const beta = FirmwareStatusResponseManifestTypeStatusEnum._(r'beta');
  static const release = FirmwareStatusResponseManifestTypeStatusEnum._(r'release');
  static const archived = FirmwareStatusResponseManifestTypeStatusEnum._(r'archived');

  /// List of all possible values in this [enum][FirmwareStatusResponseManifestTypeStatusEnum].
  static const values = <FirmwareStatusResponseManifestTypeStatusEnum>[
    candidate,
    alpha,
    beta,
    release,
    archived,
  ];

  static FirmwareStatusResponseManifestTypeStatusEnum? fromJson(dynamic value) => FirmwareStatusResponseManifestTypeStatusEnumTypeTransformer().decode(value);

  static List<FirmwareStatusResponseManifestTypeStatusEnum> listFromJson(dynamic json, {bool growable = false,}) {
    final result = <FirmwareStatusResponseManifestTypeStatusEnum>[];
    if (json is List && json.isNotEmpty) {
      for (final row in json) {
        final value = FirmwareStatusResponseManifestTypeStatusEnum.fromJson(row);
        if (value != null) {
          result.add(value);
        }
      }
    }
    return result.toList(growable: growable);
  }
}

/// Transformation class that can [encode] an instance of [FirmwareStatusResponseManifestTypeStatusEnum] to String,
/// and [decode] dynamic data back to [FirmwareStatusResponseManifestTypeStatusEnum].
class FirmwareStatusResponseManifestTypeStatusEnumTypeTransformer {
  factory FirmwareStatusResponseManifestTypeStatusEnumTypeTransformer() => _instance ??= const FirmwareStatusResponseManifestTypeStatusEnumTypeTransformer._();

  const FirmwareStatusResponseManifestTypeStatusEnumTypeTransformer._();

  String encode(FirmwareStatusResponseManifestTypeStatusEnum data) => data.value;

  /// Decodes a [dynamic value][data] to a FirmwareStatusResponseManifestTypeStatusEnum.
  ///
  /// If [allowNull] is true and the [dynamic value][data] cannot be decoded successfully,
  /// then null is returned. However, if [allowNull] is false and the [dynamic value][data]
  /// cannot be decoded successfully, then an [UnimplementedError] is thrown.
  ///
  /// The [allowNull] is very handy when an API changes and a new enum value is added or removed,
  /// and users are still using an old app with the old code.
  FirmwareStatusResponseManifestTypeStatusEnum? decode(dynamic data, {bool allowNull = true}) {
    if (data != null) {
      switch (data) {
        case r'candidate': return FirmwareStatusResponseManifestTypeStatusEnum.candidate;
        case r'alpha': return FirmwareStatusResponseManifestTypeStatusEnum.alpha;
        case r'beta': return FirmwareStatusResponseManifestTypeStatusEnum.beta;
        case r'release': return FirmwareStatusResponseManifestTypeStatusEnum.release;
        case r'archived': return FirmwareStatusResponseManifestTypeStatusEnum.archived;
        default:
          if (!allowNull) {
            throw ArgumentError('Unknown enum value to decode: $data');
          }
      }
    }
    return null;
  }

  /// Singleton [FirmwareStatusResponseManifestTypeStatusEnumTypeTransformer] instance.
  static FirmwareStatusResponseManifestTypeStatusEnumTypeTransformer? _instance;
}


