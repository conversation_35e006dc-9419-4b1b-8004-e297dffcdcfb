//
// AUTO-GENERATED FILE, DO NOT MODIFY!
//
// @dart=2.18

// ignore_for_file: unused_element, unused_import
// ignore_for_file: always_put_required_named_parameters_first
// ignore_for_file: constant_identifier_names
// ignore_for_file: lines_longer_than_80_chars

part of openapi.api;

class FirmwareStatusResponseManifestTypeDetails {
  /// Returns a new [FirmwareStatusResponseManifestTypeDetails] instance.
  FirmwareStatusResponseManifestTypeDetails({
    this.rfidVersion,
    this.dspVersion,
    this.wifiVersion,
  });

  ///
  /// Please note: This property should have been non-nullable! Since the specification file
  /// does not include a default value (using the "default:" property), however, the generated
  /// source code must fall back to having a nullable type.
  /// Consider adding a "default:" property in the specification file to hide this note.
  ///
  String? rfidVersion;

  ///
  /// Please note: This property should have been non-nullable! Since the specification file
  /// does not include a default value (using the "default:" property), however, the generated
  /// source code must fall back to having a nullable type.
  /// Consider adding a "default:" property in the specification file to hide this note.
  ///
  String? dspVersion;

  ///
  /// Please note: This property should have been non-nullable! Since the specification file
  /// does not include a default value (using the "default:" property), however, the generated
  /// source code must fall back to having a nullable type.
  /// Consider adding a "default:" property in the specification file to hide this note.
  ///
  String? wifiVersion;

  @override
  bool operator ==(Object other) => identical(this, other) || other is FirmwareStatusResponseManifestTypeDetails &&
    other.rfidVersion == rfidVersion &&
    other.dspVersion == dspVersion &&
    other.wifiVersion == wifiVersion;

  @override
  int get hashCode =>
    // ignore: unnecessary_parenthesis
    (rfidVersion == null ? 0 : rfidVersion!.hashCode) +
    (dspVersion == null ? 0 : dspVersion!.hashCode) +
    (wifiVersion == null ? 0 : wifiVersion!.hashCode);

  @override
  String toString() => 'FirmwareStatusResponseManifestTypeDetails[rfidVersion=$rfidVersion, dspVersion=$dspVersion, wifiVersion=$wifiVersion]';

  Map<String, dynamic> toJson() {
    final json = <String, dynamic>{};
    if (this.rfidVersion != null) {
      json[r'rfidVersion'] = this.rfidVersion;
    } else {
      json[r'rfidVersion'] = null;
    }
    if (this.dspVersion != null) {
      json[r'dspVersion'] = this.dspVersion;
    } else {
      json[r'dspVersion'] = null;
    }
    if (this.wifiVersion != null) {
      json[r'wifiVersion'] = this.wifiVersion;
    } else {
      json[r'wifiVersion'] = null;
    }
    return json;
  }

  /// Returns a new [FirmwareStatusResponseManifestTypeDetails] instance and imports its values from
  /// [value] if it's a [Map], null otherwise.
  // ignore: prefer_constructors_over_static_methods
  static FirmwareStatusResponseManifestTypeDetails? fromJson(dynamic value) {
    if (value is Map) {
      final json = value.cast<String, dynamic>();

      // Ensure that the map contains the required keys.
      // Note 1: the values aren't checked for validity beyond being non-null.
      // Note 2: this code is stripped in release mode!
      assert(() {
        requiredKeys.forEach((key) {
          assert(json.containsKey(key), 'Required key "FirmwareStatusResponseManifestTypeDetails[$key]" is missing from JSON.');
          assert(json[key] != null, 'Required key "FirmwareStatusResponseManifestTypeDetails[$key]" has a null value in JSON.');
        });
        return true;
      }());

      return FirmwareStatusResponseManifestTypeDetails(
        rfidVersion: mapValueOfType<String>(json, r'rfidVersion'),
        dspVersion: mapValueOfType<String>(json, r'dspVersion'),
        wifiVersion: mapValueOfType<String>(json, r'wifiVersion'),
      );
    }
    return null;
  }

  static List<FirmwareStatusResponseManifestTypeDetails> listFromJson(dynamic json, {bool growable = false,}) {
    final result = <FirmwareStatusResponseManifestTypeDetails>[];
    if (json is List && json.isNotEmpty) {
      for (final row in json) {
        final value = FirmwareStatusResponseManifestTypeDetails.fromJson(row);
        if (value != null) {
          result.add(value);
        }
      }
    }
    return result.toList(growable: growable);
  }

  static Map<String, FirmwareStatusResponseManifestTypeDetails> mapFromJson(dynamic json) {
    final map = <String, FirmwareStatusResponseManifestTypeDetails>{};
    if (json is Map && json.isNotEmpty) {
      json = json.cast<String, dynamic>(); // ignore: parameter_assignments
      for (final entry in json.entries) {
        final value = FirmwareStatusResponseManifestTypeDetails.fromJson(entry.value);
        if (value != null) {
          map[entry.key] = value;
        }
      }
    }
    return map;
  }

  // maps a json object with a list of FirmwareStatusResponseManifestTypeDetails-objects as value to a dart map
  static Map<String, List<FirmwareStatusResponseManifestTypeDetails>> mapListFromJson(dynamic json, {bool growable = false,}) {
    final map = <String, List<FirmwareStatusResponseManifestTypeDetails>>{};
    if (json is Map && json.isNotEmpty) {
      // ignore: parameter_assignments
      json = json.cast<String, dynamic>();
      for (final entry in json.entries) {
        map[entry.key] = FirmwareStatusResponseManifestTypeDetails.listFromJson(entry.value, growable: growable,);
      }
    }
    return map;
  }

  /// The list of required keys that must be present in a JSON.
  static const requiredKeys = <String>{
  };
}

