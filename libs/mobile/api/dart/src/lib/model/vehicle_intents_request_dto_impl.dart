//
// AUTO-GENERATED FILE, DO NOT MODIFY!
//
// @dart=2.18

// ignore_for_file: unused_element, unused_import
// ignore_for_file: always_put_required_named_parameters_first
// ignore_for_file: constant_identifier_names
// ignore_for_file: lines_longer_than_80_chars

part of openapi.api;

class VehicleIntentsRequestDtoImpl {
  /// Returns a new [VehicleIntentsRequestDtoImpl] instance.
  VehicleIntentsRequestDtoImpl({
    this.intentDetails = const [],
  });

  /// Charging intents
  List<VehicleIntentEntryDtoImpl> intentDetails;

  @override
  bool operator ==(Object other) => identical(this, other) || other is VehicleIntentsRequestDtoImpl &&
    _deepEquality.equals(other.intentDetails, intentDetails);

  @override
  int get hashCode =>
    // ignore: unnecessary_parenthesis
    (intentDetails.hashCode);

  @override
  String toString() => 'VehicleIntentsRequestDtoImpl[intentDetails=$intentDetails]';

  Map<String, dynamic> toJson() {
    final json = <String, dynamic>{};
      json[r'intentDetails'] = this.intentDetails;
    return json;
  }

  /// Returns a new [VehicleIntentsRequestDtoImpl] instance and imports its values from
  /// [value] if it's a [Map], null otherwise.
  // ignore: prefer_constructors_over_static_methods
  static VehicleIntentsRequestDtoImpl? fromJson(dynamic value) {
    if (value is Map) {
      final json = value.cast<String, dynamic>();

      // Ensure that the map contains the required keys.
      // Note 1: the values aren't checked for validity beyond being non-null.
      // Note 2: this code is stripped in release mode!
      assert(() {
        requiredKeys.forEach((key) {
          assert(json.containsKey(key), 'Required key "VehicleIntentsRequestDtoImpl[$key]" is missing from JSON.');
          assert(json[key] != null, 'Required key "VehicleIntentsRequestDtoImpl[$key]" has a null value in JSON.');
        });
        return true;
      }());

      return VehicleIntentsRequestDtoImpl(
        intentDetails: VehicleIntentEntryDtoImpl.listFromJson(json[r'intentDetails']),
      );
    }
    return null;
  }

  static List<VehicleIntentsRequestDtoImpl> listFromJson(dynamic json, {bool growable = false,}) {
    final result = <VehicleIntentsRequestDtoImpl>[];
    if (json is List && json.isNotEmpty) {
      for (final row in json) {
        final value = VehicleIntentsRequestDtoImpl.fromJson(row);
        if (value != null) {
          result.add(value);
        }
      }
    }
    return result.toList(growable: growable);
  }

  static Map<String, VehicleIntentsRequestDtoImpl> mapFromJson(dynamic json) {
    final map = <String, VehicleIntentsRequestDtoImpl>{};
    if (json is Map && json.isNotEmpty) {
      json = json.cast<String, dynamic>(); // ignore: parameter_assignments
      for (final entry in json.entries) {
        final value = VehicleIntentsRequestDtoImpl.fromJson(entry.value);
        if (value != null) {
          map[entry.key] = value;
        }
      }
    }
    return map;
  }

  // maps a json object with a list of VehicleIntentsRequestDtoImpl-objects as value to a dart map
  static Map<String, List<VehicleIntentsRequestDtoImpl>> mapListFromJson(dynamic json, {bool growable = false,}) {
    final map = <String, List<VehicleIntentsRequestDtoImpl>>{};
    if (json is Map && json.isNotEmpty) {
      // ignore: parameter_assignments
      json = json.cast<String, dynamic>();
      for (final entry in json.entries) {
        map[entry.key] = VehicleIntentsRequestDtoImpl.listFromJson(entry.value, growable: growable,);
      }
    }
    return map;
  }

  /// The list of required keys that must be present in a JSON.
  static const requiredKeys = <String>{
    'intentDetails',
  };
}

