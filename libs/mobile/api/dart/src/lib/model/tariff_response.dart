//
// AUTO-GENERATED FILE, DO NOT MODIFY!
//
// @dart=2.18

// ignore_for_file: unused_element, unused_import
// ignore_for_file: always_put_required_named_parameters_first
// ignore_for_file: constant_identifier_names
// ignore_for_file: lines_longer_than_80_chars

part of openapi.api;

class TariffResponse {
  /// Returns a new [TariffResponse] instance.
  TariffResponse({
    required this.id,
    required this.name,
    required this.energySupplierId,
    required this.currency,
  });

  num id;

  String name;

  num energySupplierId;

  String currency;

  @override
  bool operator ==(Object other) => identical(this, other) || other is TariffResponse &&
    other.id == id &&
    other.name == name &&
    other.energySupplierId == energySupplierId &&
    other.currency == currency;

  @override
  int get hashCode =>
    // ignore: unnecessary_parenthesis
    (id.hashCode) +
    (name.hashCode) +
    (energySupplierId.hashCode) +
    (currency.hashCode);

  @override
  String toString() => 'TariffResponse[id=$id, name=$name, energySupplierId=$energySupplierId, currency=$currency]';

  Map<String, dynamic> toJson() {
    final json = <String, dynamic>{};
      json[r'id'] = this.id;
      json[r'name'] = this.name;
      json[r'energy_supplier_id'] = this.energySupplierId;
      json[r'currency'] = this.currency;
    return json;
  }

  /// Returns a new [TariffResponse] instance and imports its values from
  /// [value] if it's a [Map], null otherwise.
  // ignore: prefer_constructors_over_static_methods
  static TariffResponse? fromJson(dynamic value) {
    if (value is Map) {
      final json = value.cast<String, dynamic>();

      // Ensure that the map contains the required keys.
      // Note 1: the values aren't checked for validity beyond being non-null.
      // Note 2: this code is stripped in release mode!
      assert(() {
        requiredKeys.forEach((key) {
          assert(json.containsKey(key), 'Required key "TariffResponse[$key]" is missing from JSON.');
          assert(json[key] != null, 'Required key "TariffResponse[$key]" has a null value in JSON.');
        });
        return true;
      }());

      return TariffResponse(
        id: num.parse('${json[r'id']}'),
        name: mapValueOfType<String>(json, r'name')!,
        energySupplierId: num.parse('${json[r'energy_supplier_id']}'),
        currency: mapValueOfType<String>(json, r'currency')!,
      );
    }
    return null;
  }

  static List<TariffResponse> listFromJson(dynamic json, {bool growable = false,}) {
    final result = <TariffResponse>[];
    if (json is List && json.isNotEmpty) {
      for (final row in json) {
        final value = TariffResponse.fromJson(row);
        if (value != null) {
          result.add(value);
        }
      }
    }
    return result.toList(growable: growable);
  }

  static Map<String, TariffResponse> mapFromJson(dynamic json) {
    final map = <String, TariffResponse>{};
    if (json is Map && json.isNotEmpty) {
      json = json.cast<String, dynamic>(); // ignore: parameter_assignments
      for (final entry in json.entries) {
        final value = TariffResponse.fromJson(entry.value);
        if (value != null) {
          map[entry.key] = value;
        }
      }
    }
    return map;
  }

  // maps a json object with a list of TariffResponse-objects as value to a dart map
  static Map<String, List<TariffResponse>> mapListFromJson(dynamic json, {bool growable = false,}) {
    final map = <String, List<TariffResponse>>{};
    if (json is Map && json.isNotEmpty) {
      // ignore: parameter_assignments
      json = json.cast<String, dynamic>();
      for (final entry in json.entries) {
        map[entry.key] = TariffResponse.listFromJson(entry.value, growable: growable,);
      }
    }
    return map;
  }

  /// The list of required keys that must be present in a JSON.
  static const requiredKeys = <String>{
    'id',
    'name',
    'energy_supplier_id',
    'currency',
  };
}

