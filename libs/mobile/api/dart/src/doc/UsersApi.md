# openapi.api.UsersApi

## Load the API package

```dart
import 'package:openapi/api.dart';
```

All URIs are relative to _http://localhost_

| Method                                                                 | HTTP request                | Description                                                     |
| ---------------------------------------------------------------------- | --------------------------- | --------------------------------------------------------------- |
| [**usersControllerGetUser**](UsersApi.md#userscontrollergetuser)       | **GET** /users              | Gets the current user's info                                    |
| [**usersControllerLinkEnode**](UsersApi.md#userscontrollerlinkenode)   | **POST** /users/enode/link  | Generates a link for connecting to Enode                        |
| [**usersControllerTrackLogin**](UsersApi.md#userscontrollertracklogin) | **POST** /users/login/alert | Generates an email of when and from which ip the user logged in |

# **usersControllerGetUser**

> ExtendedUserInfoResponseDto usersControllerGetUser(xAppVersion)

Gets the current user's info

Get the user info for the authenticated user

### Example

```dart
import 'package:openapi/api.dart';
// TODO Configure HTTP Bearer authorization: bearer
// Case 1. Use String Token
//defaultApiClient.getAuthentication<HttpBearerAuth>('bearer').setAccessToken('YOUR_ACCESS_TOKEN');
// Case 2. Use Function which generate token.
// String yourTokenGeneratorFunction() { ... }
//defaultApiClient.getAuthentication<HttpBearerAuth>('bearer').setAccessToken(yourTokenGeneratorFunction);

final api_instance = UsersApi();
final xAppVersion = xAppVersion_example; // String | The mobile app version currently used to determine if rewards should be returned

try {
    final result = api_instance.usersControllerGetUser(xAppVersion);
    print(result);
} catch (e) {
    print('Exception when calling UsersApi->usersControllerGetUser: $e\n');
}
```

### Parameters

| Name            | Type       | Description                                                                      | Notes      |
| --------------- | ---------- | -------------------------------------------------------------------------------- | ---------- |
| **xAppVersion** | **String** | The mobile app version currently used to determine if rewards should be returned | [optional] |

### Return type

[**ExtendedUserInfoResponseDto**](ExtendedUserInfoResponseDto.md)

### Authorization

[bearer](../README.md#bearer)

### HTTP request headers

- **Content-Type**: Not defined
- **Accept**: application/json

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **usersControllerLinkEnode**

> GetLinkSessionResponse usersControllerLinkEnode(getLinkSessionRequest)

Generates a link for connecting to Enode

Generates and returns a link for connecting a user's account to Enode

### Example

```dart
import 'package:openapi/api.dart';
// TODO Configure HTTP Bearer authorization: bearer
// Case 1. Use String Token
//defaultApiClient.getAuthentication<HttpBearerAuth>('bearer').setAccessToken('YOUR_ACCESS_TOKEN');
// Case 2. Use Function which generate token.
// String yourTokenGeneratorFunction() { ... }
//defaultApiClient.getAuthentication<HttpBearerAuth>('bearer').setAccessToken(yourTokenGeneratorFunction);

final api_instance = UsersApi();
final getLinkSessionRequest = GetLinkSessionRequest(); // GetLinkSessionRequest |

try {
    final result = api_instance.usersControllerLinkEnode(getLinkSessionRequest);
    print(result);
} catch (e) {
    print('Exception when calling UsersApi->usersControllerLinkEnode: $e\n');
}
```

### Parameters

| Name                      | Type                                                  | Description | Notes |
| ------------------------- | ----------------------------------------------------- | ----------- | ----- |
| **getLinkSessionRequest** | [**GetLinkSessionRequest**](GetLinkSessionRequest.md) |             |

### Return type

[**GetLinkSessionResponse**](GetLinkSessionResponse.md)

### Authorization

[bearer](../README.md#bearer)

### HTTP request headers

- **Content-Type**: application/json
- **Accept**: application/json

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)

# **usersControllerTrackLogin**

> usersControllerTrackLogin(trackLoginRequest)

Generates an email of when and from which ip the user logged in

Generates an email of when and from which ip the user logged in

### Example

```dart
import 'package:openapi/api.dart';
// TODO Configure HTTP Bearer authorization: bearer
// Case 1. Use String Token
//defaultApiClient.getAuthentication<HttpBearerAuth>('bearer').setAccessToken('YOUR_ACCESS_TOKEN');
// Case 2. Use Function which generate token.
// String yourTokenGeneratorFunction() { ... }
//defaultApiClient.getAuthentication<HttpBearerAuth>('bearer').setAccessToken(yourTokenGeneratorFunction);

final api_instance = UsersApi();
final trackLoginRequest = TrackLoginRequest(); // TrackLoginRequest |

try {
    api_instance.usersControllerTrackLogin(trackLoginRequest);
} catch (e) {
    print('Exception when calling UsersApi->usersControllerTrackLogin: $e\n');
}
```

### Parameters

| Name                  | Type                                          | Description | Notes |
| --------------------- | --------------------------------------------- | ----------- | ----- |
| **trackLoginRequest** | [**TrackLoginRequest**](TrackLoginRequest.md) |             |

### Return type

void (empty response body)

### Authorization

[bearer](../README.md#bearer)

### HTTP request headers

- **Content-Type**: application/json
- **Accept**: Not defined

[[Back to top]](#) [[Back to API list]](../README.md#documentation-for-api-endpoints) [[Back to Model list]](../README.md#documentation-for-models) [[Back to README]](../README.md)
