//
// AUTO-GENERATED FILE, DO NOT MODIFY!
//
// @dart=2.18

// ignore_for_file: unused_element, unused_import
// ignore_for_file: always_put_required_named_parameters_first
// ignore_for_file: constant_identifier_names
// ignore_for_file: lines_longer_than_80_chars

import 'package:openapi/api.dart';
import 'package:test/test.dart';


/// tests for EnergyApi
void main() {
  // final instance = EnergyApi();

  group('tests for EnergyApi', () {
    // delete tariff information for a given charger
    //
    //Future energyControllerDeleteTariff(String ppid) async
    test('test energyControllerDeleteTariff', () async {
      // TODO
    });

    // get a list of suppliers
    //
    //Future<List<SupplierDtoImpl>> energyControllerGetSuppliers() async
    test('test energyControllerGetSuppliers', () async {
      // TODO
    });

    // get tariff information for a given charger
    //
    //Future<OffPeakHoursResponseDtoImpl> energyControllerGetTariffOffPeakHoursForCharger(String ppid) async
    test('test energyControllerGetTariffOffPeakHoursForCharger', () async {
      // TODO
    });

    // set a tariff's off peak hours for a given charger
    //
    //Future<OffPeakHoursResponseDtoImpl> energyControllerSetTariffOffPeakHours(SetOffPeakTariffDtoImpl setOffPeakTariffDtoImpl) async
    test('test energyControllerSetTariffOffPeakHours', () async {
      // TODO
    });

  });
}
