//
// AUTO-GENERATED FILE, DO NOT MODIFY!
//
// @dart=2.18

// ignore_for_file: unused_element, unused_import
// ignore_for_file: always_put_required_named_parameters_first
// ignore_for_file: constant_identifier_names
// ignore_for_file: lines_longer_than_80_chars

import 'package:openapi/api.dart';
import 'package:test/test.dart';

// tests for FcmTokenDtoImpl
void main() {
  // final instance = FcmTokenDtoImpl();

  group('test FcmTokenDtoImpl', () {
    // The notification token you want to save
    // String token
    test('to test the property `token`', () async {
      // TODO
    });

    // The timestamp at which the notification token was created
    // String timestamp
    test('to test the property `timestamp`', () async {
      // TODO
    });


  });

}
