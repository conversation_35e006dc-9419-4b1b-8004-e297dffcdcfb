//
// AUTO-GENERATED FILE, DO NOT MODIFY!
//
// @dart=2.18

// ignore_for_file: unused_element, unused_import
// ignore_for_file: always_put_required_named_parameters_first
// ignore_for_file: constant_identifier_names
// ignore_for_file: lines_longer_than_80_chars

import 'package:openapi/api.dart';
import 'package:test/test.dart';

// tests for Item
void main() {
  // final instance = Item();

  group('test Item', () {
    // This can be an address Id or a container Id for further results.
    // String id
    test('to test the property `id`', () async {
      // TODO
    });

    // Descriptive information about the result.
    // String description
    test('to test the property `description`', () async {
      // TODO
    });

    // If the Type is \"Address\" then the Id can be passed to the Retrieve service. Any other Id should be passed as the Container to a further Find request to get more results.
    // String type
    test('to test the property `type`', () async {
      // TODO
    });

    // The name of the result.
    // String text
    test('to test the property `text`', () async {
      // TODO
    });

    // A list of number ranges identifying the matched characters in the Text and Description.
    // String highlight
    test('to test the property `highlight`', () async {
      // TODO
    });


  });

}
