//
// AUTO-GENERATED FILE, DO NOT MODIFY!
//
// @dart=2.18

// ignore_for_file: unused_element, unused_import
// ignore_for_file: always_put_required_named_parameters_first
// ignore_for_file: constant_identifier_names
// ignore_for_file: lines_longer_than_80_chars

import 'package:openapi/api.dart';
import 'package:test/test.dart';

// tests for ConfirmationOfPayeeDTO
void main() {
  // final instance = ConfirmationOfPayeeDTO();

  group('test ConfirmationOfPayeeDTO', () {
    // The status of the confirmation of payee check
    // String status
    test('to test the property `status`', () async {
      // TODO
    });

    // The name provided
    // String provided
    test('to test the property `provided`', () async {
      // TODO
    });

    // The suggested name associated with the bank account if status is PARTIAL
    // String suggested
    test('to test the property `suggested`', () async {
      // TODO
    });


  });

}
