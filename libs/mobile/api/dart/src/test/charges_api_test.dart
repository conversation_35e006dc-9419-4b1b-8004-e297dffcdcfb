//
// AUTO-GENERATED FILE, DO NOT MODIFY!
//
// @dart=2.18

// ignore_for_file: unused_element, unused_import
// ignore_for_file: always_put_required_named_parameters_first
// ignore_for_file: constant_identifier_names
// ignore_for_file: lines_longer_than_80_chars

import 'package:openapi/api.dart';
import 'package:test/test.dart';


/// tests for ChargesApi
void main() {
  // final instance = ChargesApi();

  group('tests for ChargesApi', () {
    // retrieve charger sessions
    //
    // For given dates it should return charger sessions.
    //
    //Future<ChargesResponse> chargesControllerGetCharges(String from, String to) async
    test('test chargesControllerGetCharges', () async {
      // TODO
    });

    // retrieve charger stats
    //
    // For given dates and inteval it should return charger stats.
    //
    //Future<ChargeStatsResponse> chargesControllerGetChargesStats(String interval, { String from, String to }) async
    test('test chargesControllerGetChargesStats', () async {
      // TODO
    });

  });
}
