/**
 * Mobile API
 * Mobile Backend for Frontend API service
 *
 * The version of the OpenAPI document: 1.0
 *
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */
import { ChargeOverrideScheduleEves } from './chargeOverrideScheduleEves';
import { ChargeOverrideScheduleChargingStation } from './chargeOverrideScheduleChargingStation';

export interface ChargeOverrideScheduleResponse {
  chargingStation: ChargeOverrideScheduleChargingStation;
  deletedAt: string | null;
  endAt: string | null;
  evse: ChargeOverrideScheduleEves;
  id: string;
  receivedAt: string;
  requestedAt: string;
}
