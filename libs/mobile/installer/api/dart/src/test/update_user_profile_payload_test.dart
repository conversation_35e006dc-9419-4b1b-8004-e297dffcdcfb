//
// AUTO-GENERATED FILE, DO NOT MODIFY!
//
// @dart=2.18

// ignore_for_file: unused_element, unused_import
// ignore_for_file: always_put_required_named_parameters_first
// ignore_for_file: constant_identifier_names
// ignore_for_file: lines_longer_than_80_chars

import 'package:openapi/api.dart';
import 'package:test/test.dart';

// tests for UpdateUserProfilePayload
void main() {
  // final instance = UpdateUserProfilePayload();

  group('test UpdateUserProfilePayload', () {
    // First name
    // String firstName
    test('to test the property `firstName`', () async {
      // TODO
    });

    // Last name
    // String lastName
    test('to test the property `lastName`', () async {
      // TODO
    });

    // Phone number
    // String phoneNumber
    test('to test the property `phoneNumber`', () async {
      // TODO
    });

    // The type of profile that's being created. Validation depends on this value
    // String companyType
    test('to test the property `companyType`', () async {
      // TODO
    });

    // Company name must be included for companyType of company, but not for sole_trader
    // String companyName
    test('to test the property `companyName`', () async {
      // TODO
    });

    // Company number must be included for companyType of company, but not for sole_trader
    // String companyNumber
    test('to test the property `companyNumber`', () async {
      // TODO
    });

    // Allowing marketing consent
    // bool marketingConsent
    test('to test the property `marketingConsent`', () async {
      // TODO
    });

    // The type of installer
    // String installerType
    test('to test the property `installerType`', () async {
      // TODO
    });


  });

}
