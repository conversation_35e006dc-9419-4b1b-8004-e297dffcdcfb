//
// AUTO-GENERATED FILE, DO NOT MODIFY!
//
// @dart=2.18

// ignore_for_file: unused_element, unused_import
// ignore_for_file: always_put_required_named_parameters_first
// ignore_for_file: constant_identifier_names
// ignore_for_file: lines_longer_than_80_chars

part of openapi.api;

class DeviceConfig {
  /// Returns a new [DeviceConfig] instance.
  DeviceConfig({
    required this.powerGenerationSystemInstalled,
    required this.breakerSize,
    required this.powerBalancingSensorInstalled,
    required this.powerBalancingEnabled,
    required this.householdMaxSupply,
    required this.powerRatingPerPhase,
    required this.outOfService,
    this.outOfServiceReasons = const [],
    this.linkySchedulesEnabled,
    this.powerBalancingSensor,
  });

  bool powerGenerationSystemInstalled;

  num breakerSize;

  bool powerBalancingSensorInstalled;

  bool powerBalancingEnabled;

  num householdMaxSupply;

  num powerRatingPerPhase;

  bool outOfService;

  List<OutOfServiceReason> outOfServiceReasons;

  ///
  /// Please note: This property should have been non-nullable! Since the specification file
  /// does not include a default value (using the "default:" property), however, the generated
  /// source code must fall back to having a nullable type.
  /// Consider adding a "default:" property in the specification file to hide this note.
  ///
  bool? linkySchedulesEnabled;

  DeviceConfigPowerBalancingSensorEnum? powerBalancingSensor;

  @override
  bool operator ==(Object other) => identical(this, other) || other is DeviceConfig &&
    other.powerGenerationSystemInstalled == powerGenerationSystemInstalled &&
    other.breakerSize == breakerSize &&
    other.powerBalancingSensorInstalled == powerBalancingSensorInstalled &&
    other.powerBalancingEnabled == powerBalancingEnabled &&
    other.householdMaxSupply == householdMaxSupply &&
    other.powerRatingPerPhase == powerRatingPerPhase &&
    other.outOfService == outOfService &&
    _deepEquality.equals(other.outOfServiceReasons, outOfServiceReasons) &&
    other.linkySchedulesEnabled == linkySchedulesEnabled &&
    other.powerBalancingSensor == powerBalancingSensor;

  @override
  int get hashCode =>
    // ignore: unnecessary_parenthesis
    (powerGenerationSystemInstalled.hashCode) +
    (breakerSize.hashCode) +
    (powerBalancingSensorInstalled.hashCode) +
    (powerBalancingEnabled.hashCode) +
    (householdMaxSupply.hashCode) +
    (powerRatingPerPhase.hashCode) +
    (outOfService.hashCode) +
    (outOfServiceReasons.hashCode) +
    (linkySchedulesEnabled == null ? 0 : linkySchedulesEnabled!.hashCode) +
    (powerBalancingSensor == null ? 0 : powerBalancingSensor!.hashCode);

  @override
  String toString() => 'DeviceConfig[powerGenerationSystemInstalled=$powerGenerationSystemInstalled, breakerSize=$breakerSize, powerBalancingSensorInstalled=$powerBalancingSensorInstalled, powerBalancingEnabled=$powerBalancingEnabled, householdMaxSupply=$householdMaxSupply, powerRatingPerPhase=$powerRatingPerPhase, outOfService=$outOfService, outOfServiceReasons=$outOfServiceReasons, linkySchedulesEnabled=$linkySchedulesEnabled, powerBalancingSensor=$powerBalancingSensor]';

  Map<String, dynamic> toJson() {
    final json = <String, dynamic>{};
      json[r'powerGenerationSystemInstalled'] = this.powerGenerationSystemInstalled;
      json[r'breakerSize'] = this.breakerSize;
      json[r'powerBalancingSensorInstalled'] = this.powerBalancingSensorInstalled;
      json[r'powerBalancingEnabled'] = this.powerBalancingEnabled;
      json[r'householdMaxSupply'] = this.householdMaxSupply;
      json[r'powerRatingPerPhase'] = this.powerRatingPerPhase;
      json[r'outOfService'] = this.outOfService;
      json[r'outOfServiceReasons'] = this.outOfServiceReasons;
    if (this.linkySchedulesEnabled != null) {
      json[r'linkySchedulesEnabled'] = this.linkySchedulesEnabled;
    } else {
      json[r'linkySchedulesEnabled'] = null;
    }
    if (this.powerBalancingSensor != null) {
      json[r'powerBalancingSensor'] = this.powerBalancingSensor;
    } else {
      json[r'powerBalancingSensor'] = null;
    }
    return json;
  }

  /// Returns a new [DeviceConfig] instance and imports its values from
  /// [value] if it's a [Map], null otherwise.
  // ignore: prefer_constructors_over_static_methods
  static DeviceConfig? fromJson(dynamic value) {
    if (value is Map) {
      final json = value.cast<String, dynamic>();

      // Ensure that the map contains the required keys.
      // Note 1: the values aren't checked for validity beyond being non-null.
      // Note 2: this code is stripped in release mode!
      assert(() {
        requiredKeys.forEach((key) {
          assert(json.containsKey(key), 'Required key "DeviceConfig[$key]" is missing from JSON.');
          assert(json[key] != null, 'Required key "DeviceConfig[$key]" has a null value in JSON.');
        });
        return true;
      }());

      return DeviceConfig(
        powerGenerationSystemInstalled: mapValueOfType<bool>(json, r'powerGenerationSystemInstalled')!,
        breakerSize: num.parse('${json[r'breakerSize']}'),
        powerBalancingSensorInstalled: mapValueOfType<bool>(json, r'powerBalancingSensorInstalled')!,
        powerBalancingEnabled: mapValueOfType<bool>(json, r'powerBalancingEnabled')!,
        householdMaxSupply: num.parse('${json[r'householdMaxSupply']}'),
        powerRatingPerPhase: num.parse('${json[r'powerRatingPerPhase']}'),
        outOfService: mapValueOfType<bool>(json, r'outOfService')!,
        outOfServiceReasons: OutOfServiceReason.listFromJson(json[r'outOfServiceReasons']),
        linkySchedulesEnabled: mapValueOfType<bool>(json, r'linkySchedulesEnabled'),
        powerBalancingSensor: DeviceConfigPowerBalancingSensorEnum.fromJson(json[r'powerBalancingSensor']),
      );
    }
    return null;
  }

  static List<DeviceConfig> listFromJson(dynamic json, {bool growable = false,}) {
    final result = <DeviceConfig>[];
    if (json is List && json.isNotEmpty) {
      for (final row in json) {
        final value = DeviceConfig.fromJson(row);
        if (value != null) {
          result.add(value);
        }
      }
    }
    return result.toList(growable: growable);
  }

  static Map<String, DeviceConfig> mapFromJson(dynamic json) {
    final map = <String, DeviceConfig>{};
    if (json is Map && json.isNotEmpty) {
      json = json.cast<String, dynamic>(); // ignore: parameter_assignments
      for (final entry in json.entries) {
        final value = DeviceConfig.fromJson(entry.value);
        if (value != null) {
          map[entry.key] = value;
        }
      }
    }
    return map;
  }

  // maps a json object with a list of DeviceConfig-objects as value to a dart map
  static Map<String, List<DeviceConfig>> mapListFromJson(dynamic json, {bool growable = false,}) {
    final map = <String, List<DeviceConfig>>{};
    if (json is Map && json.isNotEmpty) {
      // ignore: parameter_assignments
      json = json.cast<String, dynamic>();
      for (final entry in json.entries) {
        map[entry.key] = DeviceConfig.listFromJson(entry.value, growable: growable,);
      }
    }
    return map;
  }

  /// The list of required keys that must be present in a JSON.
  static const requiredKeys = <String>{
    'powerGenerationSystemInstalled',
    'breakerSize',
    'powerBalancingSensorInstalled',
    'powerBalancingEnabled',
    'householdMaxSupply',
    'powerRatingPerPhase',
    'outOfService',
  };
}


class DeviceConfigPowerBalancingSensorEnum {
  /// Instantiate a new enum with the provided [value].
  const DeviceConfigPowerBalancingSensorEnum._(this.value);

  /// The underlying value of this enum member.
  final String value;

  @override
  String toString() => value;

  String toJson() => value;

  static const cTCLAMP1 = DeviceConfigPowerBalancingSensorEnum._(r'CT_CLAMP_1');
  static const LINKY = DeviceConfigPowerBalancingSensorEnum._(r'LINKY');
  static const ARRAY = DeviceConfigPowerBalancingSensorEnum._(r'ARRAY');
  static const NONE = DeviceConfigPowerBalancingSensorEnum._(r'NONE');

  /// List of all possible values in this [enum][DeviceConfigPowerBalancingSensorEnum].
  static const values = <DeviceConfigPowerBalancingSensorEnum>[
    cTCLAMP1,
    LINKY,
    ARRAY,
    NONE,
  ];

  static DeviceConfigPowerBalancingSensorEnum? fromJson(dynamic value) => DeviceConfigPowerBalancingSensorEnumTypeTransformer().decode(value);

  static List<DeviceConfigPowerBalancingSensorEnum> listFromJson(dynamic json, {bool growable = false,}) {
    final result = <DeviceConfigPowerBalancingSensorEnum>[];
    if (json is List && json.isNotEmpty) {
      for (final row in json) {
        final value = DeviceConfigPowerBalancingSensorEnum.fromJson(row);
        if (value != null) {
          result.add(value);
        }
      }
    }
    return result.toList(growable: growable);
  }
}

/// Transformation class that can [encode] an instance of [DeviceConfigPowerBalancingSensorEnum] to String,
/// and [decode] dynamic data back to [DeviceConfigPowerBalancingSensorEnum].
class DeviceConfigPowerBalancingSensorEnumTypeTransformer {
  factory DeviceConfigPowerBalancingSensorEnumTypeTransformer() => _instance ??= const DeviceConfigPowerBalancingSensorEnumTypeTransformer._();

  const DeviceConfigPowerBalancingSensorEnumTypeTransformer._();

  String encode(DeviceConfigPowerBalancingSensorEnum data) => data.value;

  /// Decodes a [dynamic value][data] to a DeviceConfigPowerBalancingSensorEnum.
  ///
  /// If [allowNull] is true and the [dynamic value][data] cannot be decoded successfully,
  /// then null is returned. However, if [allowNull] is false and the [dynamic value][data]
  /// cannot be decoded successfully, then an [UnimplementedError] is thrown.
  ///
  /// The [allowNull] is very handy when an API changes and a new enum value is added or removed,
  /// and users are still using an old app with the old code.
  DeviceConfigPowerBalancingSensorEnum? decode(dynamic data, {bool allowNull = true}) {
    if (data != null) {
      switch (data) {
        case r'CT_CLAMP_1': return DeviceConfigPowerBalancingSensorEnum.cTCLAMP1;
        case r'LINKY': return DeviceConfigPowerBalancingSensorEnum.LINKY;
        case r'ARRAY': return DeviceConfigPowerBalancingSensorEnum.ARRAY;
        case r'NONE': return DeviceConfigPowerBalancingSensorEnum.NONE;
        default:
          if (!allowNull) {
            throw ArgumentError('Unknown enum value to decode: $data');
          }
      }
    }
    return null;
  }

  /// Singleton [DeviceConfigPowerBalancingSensorEnumTypeTransformer] instance.
  static DeviceConfigPowerBalancingSensorEnumTypeTransformer? _instance;
}


