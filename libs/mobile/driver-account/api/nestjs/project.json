{"name": "driver-account-api-nestjs", "$schema": "../../../../../node_modules/nx/schemas/project-schema.json", "sourceRoot": "libs/mobile/driver-account/api/nestjs/src", "projectType": "library", "targets": {"generate-sources": {"executor": "nx:run-commands", "options": {"parallel": false, "cwd": "{projectRoot}", "commands": ["npx @openapitools/openapi-generator-cli generate -g typescript-nestjs -i ../contract/openapi3.yaml -o src --additional-properties=stringEnums=true", "rm openapitools.json", "npx prettier . --write"]}, "configurations": {"docker": {"cwd": "", "commands": ["docker run --rm -v $(pwd):/local -w /local openapitools/openapi-generator-cli generate -g typescript-nestjs -i libs/mobile/driver-account/api/contract/openapi3.yaml -o libs/mobile/driver-account/api/nestjs/src  --additional-properties=stringEnums=true", "npx prettier libs/mobile/driver-account/api/nestjs/src --write"]}}}}, "implicitDependencies": [], "tags": ["mobile"]}