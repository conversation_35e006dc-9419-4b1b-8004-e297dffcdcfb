import { MigrationInterface, QueryRunner, Table } from 'typeorm';

const SCHEMA = 'rewards';
const USER = 'rewards_api';
const TABLE = 'wallets';

export class CreateWalletsTableInRewardsSchema1746026006845
  implements MigrationInterface
{
  public async up(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.createTable(
      new Table({
        schema: SCHEMA,
        name: TABLE,
        columns: [
          {
            name: 'id',
            type: 'uuid',
            default: 'uuid_generate_v4()',
            isPrimary: true,
            primaryKeyConstraintName: 'reward_wallets_pk',
          },
          {
            name: 'type',
            type: 'varchar',
            isNullable: false,
          },
          {
            name: 'subscription_id',
            type: 'varchar',
            isNullable: true,
          },
          {
            name: 'created_at',
            type: 'timestamptz',
            default: 'clock_timestamp()',
            isNullable: false,
          },
          {
            name: 'updated_at',
            type: 'timestamptz',
            default: 'clock_timestamp()',
            isNullable: false,
          },
          {
            name: 'deleted_at',
            type: 'timestamptz',
            isNullable: true,
          },
        ],
      })
    );

    await queryRunner.query(`
      GRANT SELECT, INSERT, UPDATE, DELETE ON ${SCHEMA}.${TABLE} TO ${USER};
    `);
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    await queryRunner.query(`
      REVOKE SELECT, INSERT, UPDATE, DELETE ON ${SCHEMA}.${TABLE} FROM ${USER};
    `);

    await queryRunner.dropTable(
      new Table({
        schema: SCHEMA,
        name: TABLE,
      })
    );
  }
}
