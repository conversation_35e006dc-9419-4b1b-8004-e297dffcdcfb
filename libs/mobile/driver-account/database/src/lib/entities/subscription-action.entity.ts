import {
  <PERSON>umn,
  CreateDateColumn,
  DeleteDateColumn,
  Entity,
  JoinColumn,
  ManyToOne,
  PrimaryGeneratedColumn,
  UpdateDateColumn,
} from 'typeorm';
import { Misc } from 'ts-toolbelt';
import { Subscription } from './subscription.entity';

@Entity({ name: 'actions', schema: 'subscriptions' })
export class SubscriptionAction {
  @PrimaryGeneratedColumn('uuid', {
    primaryKeyConstraintName: 'actions_pk',
  })
  id: string;

  @JoinColumn({ name: 'subscription_id' })
  @ManyToOne(() => Subscription, (subscription) => subscription.actions)
  subscription: Subscription;

  @Column({ type: 'text', nullable: false })
  owner: string;

  @Column({ type: 'text', nullable: false })
  status: string;

  @Column({ type: 'text', nullable: false })
  type: string;

  @Column({ type: 'jsonb', nullable: false })
  data: typeof Misc.JSON;

  @Column('text', {
    array: true,
  })
  dependsOn: string[];

  @CreateDateColumn({ name: 'created_at' })
  createdAt: Date;

  @UpdateDateColumn({ name: 'updated_at' })
  updatedAt: Date;

  @DeleteDateColumn({ name: 'deleted_at' })
  deletedAt: Date | null;
}
