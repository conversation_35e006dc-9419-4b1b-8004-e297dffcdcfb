import { AxiosRequestConfig } from 'axios';
import { CLIENT_REF } from './device-config.constants';
import { ConfigModule, ConfigService } from '@nestjs/config';
import {
  Configuration,
  GetApi,
  GetRequestStatusApi,
  SetApi,
} from '@experience/shared/axios/assets-configuration-api-client';
import { DeviceConfigService } from './device-config.service';
import { DynamicModule, Module } from '@nestjs/common';

const baseOptions: AxiosRequestConfig = {
  validateStatus: (status) =>
    (status >= 200 && status < 300) || status === 404 || status === 410,
};

@Module({})
export class DeviceConfigModule {
  static register(clientRef: string): DynamicModule {
    return {
      module: DeviceConfigModule,
      imports: [ConfigModule],
      providers: [
        DeviceConfigService,
        {
          provide: CLIENT_REF,
          useValue: clientRef,
        },
        {
          provide: GetA<PERSON>,
          inject: [ConfigService],
          useFactory: (configService: ConfigService) =>
            new GetApi(
              new Configuration({
                basePath: configService.get(
                  'ASSETS_CONFIGURATION_API_BASE_URL'
                ),
                baseOptions,
              })
            ),
        },
        {
          provide: SetApi,
          inject: [ConfigService],
          useFactory: (configService: ConfigService) =>
            new SetApi(
              new Configuration({
                basePath: configService.get(
                  'ASSETS_CONFIGURATION_API_BASE_URL'
                ),
                baseOptions,
              })
            ),
        },
        {
          provide: GetRequestStatusApi,
          inject: [ConfigService],
          useFactory: (configService: ConfigService) =>
            new GetRequestStatusApi(
              new Configuration({
                basePath: configService.get(
                  'ASSETS_CONFIGURATION_API_BASE_URL'
                ),
                baseOptions,
              })
            ),
        },
      ],
      exports: [DeviceConfigService],
    };
  }
}
