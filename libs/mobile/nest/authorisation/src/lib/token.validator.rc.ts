import { Api3TokenService } from '@experience/mobile/nest/api3-token';
import { ConfigService } from '@nestjs/config';
import { HttpStatus, Injectable, UnauthorizedException } from '@nestjs/common';
import { THIRD_PARTY_CLAIM_KEY } from './constants';
import { TokenValidator } from './token.validator';
import { getAuth } from '@experience/shared/firebase/admin';

@Injectable()
export class TokenValidatorWithRemoteConfig extends TokenValidator {
  constructor(
    readonly config: ConfigService,
    readonly api3TokenService: Api3TokenService
  ) {
    super(config, api3TokenService);
  }

  public async validateGIPToken(token: string) {
    const decodedToken = await getAuth().verifyIdToken(token, true);

    if (decodedToken[THIRD_PARTY_CLAIM_KEY]) {
      // by default, do not allow tokens issued to third parties access
      throw new UnauthorizedException({
        statusCode: HttpStatus.UNAUTHORIZED,
        message: 'Unauthorized',
        cause: 'Third party token',
      });
    }

    if (
      decodedToken.email_verified !== undefined &&
      !decodedToken.email_verified
    ) {
      throw new UnauthorizedException({
        statusCode: HttpStatus.UNAUTHORIZED,
        message: 'Unauthorized',
        cause: 'Email is not verified',
      });
    }
  }
}
