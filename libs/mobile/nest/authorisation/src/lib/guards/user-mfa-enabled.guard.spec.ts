import { DeepMocked, createMock } from '@golevelup/ts-jest';
import { ExecutionContext } from '@nestjs/common';
import { FlagsService } from '@experience/shared/nest/remote-config';
import { Test } from '@nestjs/testing';
import { UserMFAEnabledGuard } from './user-mfa-enabled.guard';
import { mockAuth } from '@experience/mobile/test/mocking';

jest.mock('@experience/shared/firebase/admin', () => ({
  getAuth: () => mockAuth,
}));

describe('UserMFAEnabledGuard', () => {
  const TOKEN =
    'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.**************************************************************************.SflKxwRJSMeKKF2QT4fwpMeJf36POk6yJV_adQssw5c';

  let guard: UserMFAEnabledGuard;
  let flagsService: DeepMocked<FlagsService>;
  let executionContext: ExecutionContext;

  beforeEach(async () => {
    const module = await Test.createTestingModule({
      providers: [
        UserMFAEnabledGuard,
        {
          provide: FlagsService,
          useValue: createMock<FlagsService>(),
        },
      ],
    }).compile();

    guard = module.get(UserMFAEnabledGuard);
    flagsService = module.get(FlagsService);

    executionContext = createMock<ExecutionContext>({
      switchToHttp: () => ({
        getRequest: () => ({
          get: (header: string) =>
            header === 'Authorization' ? `Bearer ${TOKEN}` : undefined,
        }),
      }),
    });
  });

  describe('canActivate()', () => {
    it('returns true if the feature flag is disabled', async () => {
      flagsService.doCheckForRemoteFlag.mockResolvedValue(true);

      const res = await guard.canActivate(executionContext);

      expect(res).toBeTruthy();
    });

    it('returns false if there is no token', async () => {
      flagsService.doCheckForRemoteFlag.mockResolvedValue(false);

      const res = await guard.canActivate(createMock<ExecutionContext>());

      expect(res).toBeFalsy();
    });

    it("decodes the token and get's the user", async () => {
      flagsService.doCheckForRemoteFlag.mockResolvedValue(false);
      mockAuth.verifyIdToken.mockReturnValue({
        uid: 'example-user',
      });

      await guard.canActivate(executionContext);

      expect(mockAuth.verifyIdToken).toHaveBeenCalledWith(TOKEN);
      expect(mockAuth.getUser).toHaveBeenCalledWith('example-user');
    });

    it('returns true if the user has at least a MFA configured', async () => {
      flagsService.doCheckForRemoteFlag.mockResolvedValue(false);
      mockAuth.getUser.mockReturnValue({
        multiFactor: {
          enrolledFactors: [
            {
              uid: '46c53351-0289-46ce-ab33-00d657114b5b',
              factorId: 'phone',
              phoneNumber: '01234567890',
            },
          ],
        },
      });

      const res = await guard.canActivate(executionContext);

      expect(res).toBeTruthy();
    });

    it('returns false if the user has no MFA configured', async () => {
      flagsService.doCheckForRemoteFlag.mockResolvedValue(false);
      mockAuth.getUser.mockReturnValue({
        multiFactor: {
          enrolledFactors: [],
        },
      });

      const res = await guard.canActivate(executionContext);

      expect(res).toBeFalsy();
    });

    it('returns false if an error is thrown', async () => {
      flagsService.doCheckForRemoteFlag.mockResolvedValue(false);
      mockAuth.verifyIdToken.mockImplementation(() => {
        throw new Error();
      });

      const res = await guard.canActivate(executionContext);

      expect(res).toBeFalsy();
    });
  });
});
