import { Factor } from './retrieve-factor.types';
import { Injectable, Logger } from '@nestjs/common';
import { UserNotFound } from './user-not-found.error';
import { getAuth } from '@experience/shared/firebase/admin';
import type {
  MultiFactorInfo,
  PhoneMultiFactorInfo,
} from 'firebase-admin/lib/auth';
@Injectable()
export class RetrieveFactorService {
  private readonly logger = new Logger(RetrieveFactorService.name);

  async getFactors(authId: string): Promise<Factor[]> {
    try {
      const user = await this.getFirebaseUser(authId);

      return (
        user.multiFactor?.enrolledFactors
          .filter(RetrieveFactorService.isPhoneMFA)
          .map((factor) => ({
            id: factor.uid,
            phoneNumber: factor.phoneNumber,
            enrollmentTime: factor.enrollmentTime,
          })) ?? []
      );
    } catch (error) {
      this.logger.error({ authId, error }, 'unable to retrieve factors');

      throw error;
    }
  }

  private async getFirebaseUser(authId: string) {
    try {
      return await getAuth().getUser(authId);
    } catch (error) {
      this.logger.warn({ error, authId }, 'User not found in firebase auth');

      throw new UserNotFound();
    }
  }

  private static isPhoneMFA(
    factor: MultiFactorInfo
  ): factor is PhoneMultiFactorInfo {
    return factor.factorId === 'phone';
  }
}
