import { EmailTheme } from './constants';
import { getEmailThemeSenderString } from './auth.utils';

describe('Auth Utils', () => {
  describe('getCategorySenderString()', () => {
    it.each([
      {
        given: EmailTheme.DEFAULT,
        returns: '"Pod Point" <<EMAIL>>',
      },
      {
        given: EmailTheme.POD_ENERGY,
        returns: '"Pod" <<EMAIL>>',
      },
    ])('given $given it returns $returns', ({ given, returns }) => {
      expect(getEmailThemeSenderString(given)).toEqual(returns);
    });
  });
});
