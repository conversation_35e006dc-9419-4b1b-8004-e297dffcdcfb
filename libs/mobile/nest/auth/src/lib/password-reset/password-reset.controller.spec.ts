import {
  COMMON_CONTINUE_URL_ERROR,
  COMMON_EMAIL_MAX_LENGTH_ERROR,
  COMMON_INVALID_EMAIL_ERROR,
  COMMON_REQUIRED_ERROR,
} from '@experience/mobile/driver-account/typescript/domain-model-validation';
import { INestApplication } from '@nestjs/common';
import { PasswordResetController } from './password-reset.controller';
import { PasswordResetService } from './password-reset.service';
import {
  TEST_SEND_PASSWORD_RESET_REQUEST,
  TEST_SEND_PASSWORD_RESET_REQUEST_WITH_CONTINUE_URL,
  TEST_SEND_PASSWORD_RESET_REQUEST_WITH_INVALID_CONTINUE_URL,
} from '@experience/mobile/driver-account/domain/auth';
import { Test, TestingModule } from '@nestjs/testing';
import { v4 as uuid } from 'uuid';
import request from 'supertest';

jest.mock('./password-reset.service.ts');

describe('PasswordResetController', () => {
  let app: INestApplication;
  let passwordResetService: PasswordResetService;

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      controllers: [PasswordResetController],
      providers: [
        { provide: PasswordResetService, useClass: PasswordResetService },
      ],
    }).compile();

    passwordResetService =
      module.get<PasswordResetService>(PasswordResetService);

    app = module.createNestApplication();
    await app.init();
  });

  afterAll(async () => {
    await app.close();
  });

  describe('sendPasswordReset', () => {
    it('should send password reset', async () => {
      const mockSendPasswordReset = jest
        .spyOn(passwordResetService, 'sendPasswordReset')
        .mockResolvedValueOnce();

      const response = await request(app.getHttpServer())
        .post('/auth/password-reset')
        .set('x-app-name', 'flexdrive')
        .send(TEST_SEND_PASSWORD_RESET_REQUEST);

      expect(response.status).toEqual(202);
      expect(mockSendPasswordReset).toHaveBeenCalledWith(
        TEST_SEND_PASSWORD_RESET_REQUEST,
        'en',
        'flexdrive'
      );
    });

    it('should send password reset with language es', async () => {
      const mockSendPasswordReset = jest
        .spyOn(passwordResetService, 'sendPasswordReset')
        .mockResolvedValueOnce();

      const response = await request(app.getHttpServer())
        .post('/auth/password-reset')
        .set('Accept-Language', 'es')
        .set('x-app-name', 'flexdrive')
        .send(TEST_SEND_PASSWORD_RESET_REQUEST);

      expect(response.status).toEqual(202);
      expect(mockSendPasswordReset).toHaveBeenCalledWith(
        TEST_SEND_PASSWORD_RESET_REQUEST,
        'es',
        'flexdrive'
      );
    });

    it('should send password reset and pass a continue url', async () => {
      const mockSendPasswordReset = jest
        .spyOn(passwordResetService, 'sendPasswordReset')
        .mockResolvedValueOnce();

      const response = await request(app.getHttpServer())
        .post('/auth/password-reset')
        .set('x-app-name', 'flexdrive')
        .send(TEST_SEND_PASSWORD_RESET_REQUEST_WITH_CONTINUE_URL);

      expect(response.status).toEqual(202);
      expect(mockSendPasswordReset).toHaveBeenCalledWith(
        TEST_SEND_PASSWORD_RESET_REQUEST_WITH_CONTINUE_URL,
        'en',
        'flexdrive'
      );
    });

    it('should throw a validation error when continue url is invalid when sending password reset', () => {
      request(app.getHttpServer())
        .post('/auth/password-reset')
        .set('x-app-name', 'flexdrive')
        .send(TEST_SEND_PASSWORD_RESET_REQUEST_WITH_INVALID_CONTINUE_URL)
        .expect(400)
        .expect({
          statusCode: 400,
          message: [COMMON_CONTINUE_URL_ERROR],
          error: 'Bad Request',
        });
    });

    it('should throw a validation error when email address is missing when sending password reset', () => {
      request(app.getHttpServer())
        .post('/auth/password-reset')
        .set('x-app-name', 'flexdrive')
        .send({ email: undefined })
        .expect(400)
        .expect({
          statusCode: 400,
          message: [
            COMMON_REQUIRED_ERROR,
            COMMON_EMAIL_MAX_LENGTH_ERROR,
            COMMON_INVALID_EMAIL_ERROR,
          ],
          error: 'Bad Request',
        });
    });

    it('should throw a validation error when email address is invalid when sending password reset', () => {
      request(app.getHttpServer())
        .post('/auth/password-reset')
        .set('x-app-name', 'flexdrive')
        .send({ email: 'hello' })
        .expect(400)
        .expect({
          statusCode: 400,
          message: [COMMON_INVALID_EMAIL_ERROR],
          error: 'Bad Request',
        });
    });

    it('should throw a validation error when email address is too long when sending password reset', () => {
      request(app.getHttpServer())
        .post('/auth/password-reset')
        .set('x-app-name', 'flexdrive')
        .send({ email: uuid().repeat(10) + '@email.com' })
        .expect(400)
        .expect({
          statusCode: 400,
          message: [COMMON_EMAIL_MAX_LENGTH_ERROR, COMMON_INVALID_EMAIL_ERROR],
          error: 'Bad Request',
        });
    });
  });

  describe('sendPasswordResetAlert', () => {
    it('should call the password reset service successfully', async () => {
      const sendPasswordResetAlertMock = jest
        .spyOn(passwordResetService, 'sendPasswordResetAlert')
        .mockResolvedValue();

      const response = await request(app.getHttpServer())
        .post('/auth/password-reset-alert')
        .set('Accept-Language', 'es')
        .set('x-app-name', 'flexdrive')
        .send({
          email: '<EMAIL>',
        });

      expect(response.status).toEqual(200);
      expect(sendPasswordResetAlertMock).toHaveBeenCalledTimes(1);
      expect(sendPasswordResetAlertMock).toHaveBeenCalledWith(
        '<EMAIL>',
        'es',
        'flexdrive'
      );
    });
  });
});
