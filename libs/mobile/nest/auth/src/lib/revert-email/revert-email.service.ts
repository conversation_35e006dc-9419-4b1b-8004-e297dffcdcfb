import { EmailTheme } from '../constants';
import { I18nService } from 'nestjs-i18n';
import { Injectable } from '@nestjs/common';
import {
  SimpleEmailService,
  getTemplateStrings,
} from '@experience/shared/nest/aws/ses-module';
import { getEmailThemeBaseUrl, getEmailThemeSenderString } from '../auth.utils';
import { getLanguageFromCode } from '@experience/shared/nest/utils';
import { injectParametersIntoTemplateString } from '@experience/shared/typescript/utils';

interface RevertEmailParameters {
  emailAddress: string;
  theme: EmailTheme;
}

@Injectable()
export class RevertEmailService {
  constructor(
    private readonly simpleEmailService: SimpleEmailService,
    private readonly i18n: I18nService
  ) {}

  async sendRevertEmail(
    revertEmailParams: RevertEmailParameters,
    languageCode = 'en'
  ): Promise<void> {
    const { theme, ...params } = revertEmailParams;
    const lang = getLanguageFromCode(languageCode);
    const { htmlTemplateString, plainTextTemplateString } = getTemplateStrings(
      `./assets/mobile-account/email-templates/${lang}/${theme}/email-update-confirmation`
    );
    const imageUrl = getEmailThemeBaseUrl(theme);
    const subject = this.i18n.t('auth-emails.revert_email.subject', { lang });

    await this.simpleEmailService.sendEmail({
      bodyHtml: injectParametersIntoTemplateString(htmlTemplateString, {
        imageUrl,
        ...params,
      }),
      bodyText: injectParametersIntoTemplateString(plainTextTemplateString, {
        imageUrl,
        ...params,
      }),
      subject,
      to: params.emailAddress,
      attachment: [],
      cc: [],
      bcc: [],
      sender: getEmailThemeSenderString(theme),
    });
  }
}
