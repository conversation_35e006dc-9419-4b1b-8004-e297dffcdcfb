import { EmailTheme } from '../constants';
import { FlagsService } from '@experience/shared/nest/remote-config';
import { Injectable, Logger } from '@nestjs/common';
import { SubscriptionsApi } from '@experience/mobile/subscriptions-api/axios';
import { getAuth } from '@experience/shared/firebase/admin';

type ThemeSelectorParams = {
  appName?: string;
  userId?: string;
  email?: string;
};

@Injectable()
export class EmailThemeService {
  private readonly logger = new Logger(EmailThemeService.name);

  constructor(
    private readonly subscriptionApi: SubscriptionsApi,
    private readonly flagsService: FlagsService
  ) {}

  async pick({
    appName,
    userId,
    email,
  }: ThemeSelectorParams): Promise<EmailTheme> {
    return appName
      ? this.pickByAppName(appName)
      : userId
      ? await this.pickByUserId(userId)
      : email
      ? await this.pickByEmail(email)
      : EmailTheme.DEFAULT;
  }

  private pickByAppName(appName: ThemeSelectorParams['appName']): EmailTheme {
    switch (appName?.toLowerCase()) {
      case 'pod':
      case 'kapow':
      case 'flexdrive':
      case 'identity.podenergy.com':
        return EmailTheme.POD_ENERGY;
      default:
        return EmailTheme.DEFAULT;
    }
  }

  private async pickByUserId(
    userId: ThemeSelectorParams['userId']
  ): Promise<EmailTheme> {
    try {
      const isSubscriptionBasedThemingEnabled =
        await this.flagsService.doCheckForRemoteFlag(
          'ff_theme_based_on_subscription_status',
          'true'
        );

      if (isSubscriptionBasedThemingEnabled) {
        const {
          data: {
            subscriptions: [subscription],
          },
        } = await this.subscriptionApi.subscriptionsControllerSearch(userId);

        if (
          subscription &&
          ['PENDING', 'ACTIVE'].includes(subscription.status)
        ) {
          return EmailTheme.POD_ENERGY;
        }
      }
    } catch (error) {
      this.logger.error(
        { error, userId },
        'failed to retrieve user subscription'
      );
    }

    return EmailTheme.DEFAULT;
  }

  private async pickByEmail(email: string): Promise<EmailTheme> {
    try {
      const user = await getAuth().getUserByEmail(email);
      return await this.pickByUserId(user.uid);
    } catch (error) {
      this.logger.error({ error }, 'failed to retrieve user by email');

      return EmailTheme.DEFAULT;
    }
  }
}
