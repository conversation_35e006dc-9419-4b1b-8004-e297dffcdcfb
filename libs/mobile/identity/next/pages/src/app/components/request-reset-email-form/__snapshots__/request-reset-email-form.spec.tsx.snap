// Jest Snapshot v1, https://goo.gl/fbAQLP

exports[`RequestResetPasswordForm should match snapshot 1`] = `
<body>
  <div>
    <h1
      class="text-xxl font-bold"
    >
      Recover password
    </h1>
    <p
      class="text-md font-normal break-words"
    >
      Please enter your registered email to reset your password.
    </p>
    <form
      class="text-left"
      id="email-form"
      novalidate=""
    >
      <div
        class=""
      >
        <label
          class="text-md font-bold block mb-2"
          for="email"
        >
          Email address
        </label>
      </div>
      <input
        autocomplete="email"
        class="border-none pl-1.5 focus:outline-hidden focus:ring-2 focus:ring-primary/50 text-neutral placeholder-neutral disabled:bg-neutral/30 disabled:placeholder-neutral disabled:cursor-not-allowed py-2 rounded-sm bg-neutral/10 w-full"
        id="email"
        name="email"
        required=""
        type="email"
        value=""
      />
      <div
        class="pb-4"
      />
      <button
        class="border-2 border-solid rounded-sm font-bold text-center disabled:no-underline transition ease-in-out duration-200 cursor-pointer disabled:cursor-not-allowed bg-black border-black text-white hover:bg-charcoal hover:border-charcoal focus:bg-charcoal focus:border-charcoal active:bg-charcoal active:border-charcoal disabled:bg-neutral/20 disabled:border-neutral/20 disabled:text-neutral py-1.5 px-3.75 w-full"
        form="email-form"
        type="submit"
      >
        Confirm
      </button>
    </form>
  </div>
</body>
`;
