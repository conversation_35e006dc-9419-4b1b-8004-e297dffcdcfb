'use client';

import {
  AUTH_CONFIRM_NEW_PASSWORD_REQUIRED_ERROR,
  AUTH_NEW_PASSWORD_REQUIRED_ERROR,
  AUTH_PASSWORDS_MISMATCH_ERROR,
} from '../../../lib/constants';
import {
  Anchor,
  Button,
  CheckmarkCircleIcon,
  CrossCircleIcon,
  Heading,
  HeadingSizes,
  InputWidth,
  Paragraph,
} from '@experience/shared/react/design-system';
import {
  AuthError,
  confirmPasswordReset,
  verifyPasswordResetCode,
} from 'firebase/auth';
import { Controller, useForm } from 'react-hook-form';
import { PASSWORD_RESET } from '../../../lib/segment';
import { PasswordInput } from '../password-input/password-input';
import { VerticalSpacer } from '@experience/shared/react/layouts';
import { getAuth } from '../../../firebase/firebase';
import { pwnedPassword } from 'hibp';
import { use<PERSON><PERSON>back, useEffect, useState } from 'react';
import { use<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> } from '@experience/shared/react/hooks';
import { useFirebaseOptions } from '../../../lib/hooks/use-firebase-options';
import { useLocale, useTranslations } from 'next-intl';
import { useSegment } from '../../../lib/hooks/use-segment';

interface PasswordResetFormProps {
  oobCode: string;
  continueUrl: string;
}

interface PasswordResetFormValues {
  password: string;
  passwordConfirmation: string;
}

const PasswordResetForm = ({
  oobCode,
  continueUrl,
}: PasswordResetFormProps) => {
  const t = useTranslations('password_reset');

  const [passwordResetSuccess, setPasswordResetSuccess] = useState(false);
  const [passwordVerificationSuccess, setPasswordVerificationSuccess] =
    useState(true);
  const [verificationInProgress, setVerificationInProgress] =
    useState<boolean>(true);
  const [email, setEmail] = useState<string>();

  const { track } = useSegment();
  const locale = useLocale();

  useEffect(() => {
    track(PASSWORD_RESET, {
      email: email ?? 'unknown',
    });
  }, [email, track]);

  const firebaseOptions = useFirebaseOptions();

  const handleError = useErrorHandler();

  const {
    control,
    formState: { errors: formErrors, isSubmitting },
    handleSubmit,
    setError,
  } = useForm<PasswordResetFormValues>({
    defaultValues: { password: '', passwordConfirmation: '' },
    reValidateMode: 'onBlur',
  });

  const handleAuthError = useCallback(
    (error: AuthError): void => {
      switch (error.code) {
        case 'auth/password-does-not-meet-requirements':
        case 'auth/weak-password':
          setError('password', {
            type: 'manual',
            message: t('passwordCharError'),
          });
          track(PASSWORD_RESET, {
            requirementsFailure: true,
            email: email ?? 'unknown',
          });
          break;
        case 'auth/invalid-action-code':
        case 'auth/expired-action-code':
          setPasswordVerificationSuccess(false);
          track(PASSWORD_RESET, { expired: true, email: email ?? 'unknown' });
          break;
        default:
          handleError(error);
          track(PASSWORD_RESET, {
            unknownFailure: true,
            email: email ?? 'unknown',
          });
      }
    },
    [handleError, setError, track, t]
  );

  const initialVerify = useCallback(async () => {
    try {
      const auth = await getAuth(firebaseOptions);
      const email = await verifyPasswordResetCode(auth, oobCode);
      setEmail(email);
      setVerificationInProgress(false);
      track(PASSWORD_RESET, { valid: true, email });
    } catch (error) {
      setVerificationInProgress(false);
      setPasswordVerificationSuccess(false);
      handleAuthError(error as AuthError);
    }
  }, [firebaseOptions, handleAuthError, oobCode, track]);

  useEffect(() => {
    if (verificationInProgress) {
      initialVerify();
    }
  }, [initialVerify, verificationInProgress]);

  const formId = 'password-reset-form';

  const resetPassword = async ({
    password,
    passwordConfirmation,
  }: PasswordResetFormValues) => {
    if (passwordConfirmation !== password) {
      setError('passwordConfirmation', {
        message: AUTH_PASSWORDS_MISMATCH_ERROR,
        type: 'manual',
      });
      return;
    }

    try {
      if (await pwnedPassword(password).catch(() => 0)) {
        setError('password', {
          type: 'manual',
          message: t('pwnedPasswordError'),
        });
        track(PASSWORD_RESET, {
          requirementsFailure: true,
          email: email ?? 'unknown',
        });
        return;
      }
      const auth = await getAuth(firebaseOptions);
      await confirmPasswordReset(auth, oobCode, password);
      setPasswordResetSuccess(true);
      track(PASSWORD_RESET, { reset: true, email: email ?? 'unknown' });
      await fetch('/api/sendResetPasswordAlert', {
        method: 'POST',
        body: JSON.stringify({ email }),
        cache: 'no-cache',
        headers: {
          'Content-Type': 'application/json',
          accept: 'application/json',
          'Accept-Language': locale ?? 'en',
        },
      });
    } catch (error) {
      handleAuthError(error as AuthError);
    }
  };

  const passwordResetSuccessSection = (
    <>
      <Heading.H1 fontSize={HeadingSizes.L} className="font-bold">
        {t('header')}
      </Heading.H1>
      <div className="flex justify-center">
        <CheckmarkCircleIcon.SMALL className="w-16 h-16 text-white" />
      </div>
      <Paragraph>{t('successful')}</Paragraph>
      {continueUrl ? (
        <Button onClick={() => (window.location.href = continueUrl)}>
          {t('button')}
        </Button>
      ) : null}
    </>
  );

  const passwordResetFormSection = (
    <div className="text-left">
      <Heading.H1 fontSize={HeadingSizes.L} className="font-bold">
        {t('formHeader')}
      </Heading.H1>
      <Paragraph className="mt-4 mb-4">{t('formBody')}</Paragraph>
      <Paragraph className="mt-4 mb-4">
        {t('resettingFor')} <strong>{email}</strong>
      </Paragraph>
      <form
        className="text-left"
        id={formId}
        onSubmit={handleSubmit((data, event) => {
          event?.preventDefault();
          return resetPassword(data);
        })}
        noValidate
      >
        <Controller
          control={control}
          name="password"
          rules={{
            required: AUTH_NEW_PASSWORD_REQUIRED_ERROR,
          }}
          render={({ field: { name, onChange, value } }) => (
            <PasswordInput
              key={name}
              autoComplete="new-password"
              errorMessage={formErrors[name]?.message as string}
              label={t('formNewPasswordField')}
              id={name}
              isFocused={true}
              name={name}
              onChange={onChange}
              value={value}
              width={InputWidth.FULL}
            />
          )}
        />
        <VerticalSpacer />
        <Controller
          control={control}
          name="passwordConfirmation"
          rules={{
            required: AUTH_CONFIRM_NEW_PASSWORD_REQUIRED_ERROR,
          }}
          render={({ field: { name, onChange, value } }) => (
            <PasswordInput
              key={name}
              autoComplete="new-password"
              errorMessage={formErrors[name]?.message as string}
              label={t('formPasswordConfirmField')}
              id={name}
              name={name}
              onChange={onChange}
              value={value}
              width={InputWidth.FULL}
            />
          )}
        />
        <VerticalSpacer />
        <Button
          disabled={isSubmitting}
          form={formId}
          className="w-full"
          type="submit"
        >
          {t('confirm')}
        </Button>
      </form>
    </div>
  );

  const passwordVerificationFailureSection = (
    <>
      <div className="flex justify-center">
        <CrossCircleIcon.SMALL className="w-16 h-16 text-white" />
      </div>
      <Heading.H1 fontSize={HeadingSizes.M} className="font-bold">
        {t('formExpiredLinkHeader')}
      </Heading.H1>
      <Paragraph>{t('formExpiredLinkBody1')}</Paragraph>
      <Paragraph>{t('formExpiredLinkBody2')}</Paragraph>
      <Anchor
        href={`/request-reset${
          continueUrl ? `?continueUrl=${encodeURIComponent(continueUrl)}` : ''
        }`}
      >
        {t('formExpiredButton')}
      </Anchor>
    </>
  );

  if (verificationInProgress) {
    return (
      <Heading.H1 fontSize={HeadingSizes.M} className="font-bold">
        {t('verifying')}
      </Heading.H1>
    );
  }

  if (!passwordVerificationSuccess) {
    return passwordVerificationFailureSection;
  }

  return passwordResetSuccess
    ? passwordResetSuccessSection
    : passwordResetFormSection;
};

export default PasswordResetForm;
