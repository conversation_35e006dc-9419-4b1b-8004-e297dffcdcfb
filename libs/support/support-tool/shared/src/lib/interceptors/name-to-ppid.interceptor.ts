import {
  Call<PERSON><PERSON><PERSON>,
  ExecutionContext,
  Injectable,
  Logger,
  NestInterceptor,
} from '@nestjs/common';
import { Observable } from 'rxjs';
import { ChargerApi as SiteAdminApi } from '@experience/shared/axios/internal-site-admin-api-client';

@Injectable()
export class NameToPpidInterceptor implements NestInterceptor {
  private readonly logger = new Logger(NameToPpidInterceptor.name);

  constructor(private siteAdminApi: SiteAdminApi) {}

  async intercept(
    context: ExecutionContext,
    next: CallHandler
  ): Promise<Observable<unknown>> {
    const request = context.switchToHttp().getRequest();
    const nameRegex = /^[A-Za-z]{4}-[A-Za-z]{4}/;
    const isNameSearch = nameRegex.test(request.params.ppid);
    if (!isNameSearch) {
      return next.handle();
    }
    try {
      request.params.ppid = await this.siteAdminApi
        .chargerControllerFindPpidByChargerName(request.params.ppid)
        .then((response) => response.data);
    } catch (error) {
      this.logger.log(error);
    }
    return next.handle();
  }
}
