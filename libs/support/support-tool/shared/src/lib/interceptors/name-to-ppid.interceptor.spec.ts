import { AxiosResponse } from 'axios';
import { ExecutionContextHost } from '@nestjs/core/helpers/execution-context-host';
import { INestApplication } from '@nestjs/common';
import { NameToPpidInterceptor } from './name-to-ppid.interceptor';
import { ChargerApi as SiteAdminChargerApi } from '@experience/shared/axios/internal-site-admin-api-client';
import { Test, TestingModule } from '@nestjs/testing';

describe('NameToPpidInterceptor', () => {
  let app: INestApplication;
  let interceptor: NameToPpidInterceptor;
  let siteAdminApi: SiteAdminChargerApi;
  const mockHandle = jest.fn();
  const callHandler = {
    handle: mockHandle,
  };

  beforeAll(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [
        SiteAdminChargerApi,
        { provide: NameToPpidInterceptor, useClass: NameToPpidInterceptor },
      ],
    }).compile();

    interceptor = module.get<NameToPpidInterceptor>(NameToPpidInterceptor);
    siteAdminApi = module.get<SiteAdminChargerApi>(SiteAdminChargerApi);

    app = module.createNestApplication();
    await app.init();
  });

  it('should be defined', () => {
    expect(interceptor).toBeDefined();
    expect(siteAdminApi).toBeDefined();
  });

  it('should call the siteAdminApi and substitute the ppid if the search term matches charger name structure', async () => {
    const mockFindPpidByChargerName = jest
      .spyOn(siteAdminApi, 'chargerControllerFindPpidByChargerName')
      .mockResolvedValue({
        data: 'PSL-12345',
        status: 200,
      } as AxiosResponse);
    const executionContext = new ExecutionContextHost([
      { params: { ppid: 'John-Jake' } },
    ]);

    await interceptor.intercept(executionContext, callHandler);

    expect(mockFindPpidByChargerName).toHaveBeenCalledWith('John-Jake');
    expect(executionContext.switchToHttp().getRequest()).toEqual({
      params: { ppid: 'PSL-12345' },
    });
    expect(mockHandle).toHaveBeenCalled();
  });

  it('should not call the siteAdminApi nor substitute ppid if the search term does not match charger name structure', async () => {
    const mockFindPpidByChargerName = jest.spyOn(
      siteAdminApi,
      'chargerControllerFindPpidByChargerName'
    );
    const executionContext = new ExecutionContextHost([
      { params: { ppid: 'PSL-12345' } },
    ]);

    await interceptor.intercept(executionContext, callHandler);

    expect(mockFindPpidByChargerName).not.toHaveBeenCalled();
    expect(executionContext.switchToHttp().getRequest()).toEqual({
      params: { ppid: 'PSL-12345' },
    });
    expect(mockHandle).toHaveBeenCalled();
  });

  it('should log an error and continue if the siteAdminApi call returns an error', async () => {
    const mockFindPpidByChargerName = jest
      .spyOn(siteAdminApi, 'chargerControllerFindPpidByChargerName')
      .mockRejectedValue(new Error('oops'));
    const mockLog = jest.spyOn(interceptor['logger'], 'log');
    const executionContext = new ExecutionContextHost([
      { params: { ppid: 'John-Jake' } },
    ]);

    await interceptor.intercept(executionContext, callHandler);

    expect(mockFindPpidByChargerName).toHaveBeenCalledWith('John-Jake');
    expect(mockLog).toHaveBeenCalledWith(new Error('oops'));
    expect(executionContext.switchToHttp().getRequest()).toEqual({
      params: { ppid: 'John-Jake' },
    });
    expect(mockHandle).toHaveBeenCalled();
  });
});
